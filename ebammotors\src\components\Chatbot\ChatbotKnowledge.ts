import { SHIPPING_METHODS } from '@/lib/paymentConfig';
import {
  ELECTRONICS_CONFIG,
  FURNITURE_CONFIG,
  getAllCarModelsWithPrices,
  formatPriceRange
} from '@/lib/pricingConfig';

interface ResponsePattern {
  keywords: string[];
  responses: {
    en: string[] | (() => string[]);
    ja: string[] | (() => string[]);
  };
  priority: number;
}

interface PageKnowledge {
  [key: string]: {
    context: {
      en: string;
      ja: string;
    };
    patterns: ResponsePattern[];
  };
}

// Dynamic pricing functions
const getDynamicCarPricing = (locale: string): string => {
  const modelsWithPrices = getAllCarModelsWithPrices();
  const carList = modelsWithPrices.map(model => {
    const priceRange = formatPriceRange(model.priceRange.min, model.priceRange.max);
    return `• ${model.name} (${model.seats}): ${priceRange}`;
  }).join('\n');

  return locale === 'en'
    ? `🚗 **Popular Cars (FOB Japan):**\n${carList}`
    : `🚗 **人気車種（日本FOB価格）:**\n${carList}`;
};

const getDynamicShippingInfo = (locale: string): string => {
  const methods = SHIPPING_METHODS.map(method => {
    const weeks = Math.ceil(method.estimatedDays / 7);
    const timeRange = weeks === 1 ? '1-2 weeks' : `${Math.floor(weeks * 0.8)}-${weeks} weeks`;
    const timeRangeJa = weeks === 1 ? '1-2週間' : `${Math.floor(weeks * 0.8)}-${weeks}週間`;

    return locale === 'en'
      ? `• ${method.name}: ¥${method.basePrice.toLocaleString()} (${timeRange})`
      : `• ${method.name}: ¥${method.basePrice.toLocaleString()}（${timeRangeJa}）`;
  }).join('\n');

  return locale === 'en'
    ? `**Shipping to Ghana/Africa:**\n${methods}`
    : `**ガーナ・アフリカへの配送:**\n${methods}`;
};

const getDynamicStockInfo = (locale: string): string => {
  const modelsWithPrices = getAllCarModelsWithPrices();
  const stockList = modelsWithPrices.map(model => {
    const priceRange = formatPriceRange(model.priceRange.min, model.priceRange.max);
    return locale === 'en'
      ? `• ${model.name} (${model.seats}) - ${priceRange}`
      : `• ${model.name}（${model.seats}）- ${priceRange}`;
  }).join('\n');

  return locale === 'en'
    ? `🚗 **Popular Toyota Models:**\n${stockList}`
    : `🚗 **人気のトヨタモデル:**\n${stockList}`;
};

const getDynamicElectronicsPricing = (locale: string): string => {
  const examples = ELECTRONICS_CONFIG.examples.slice(0, 3).join(', ');
  const priceRange = formatPriceRange(ELECTRONICS_CONFIG.minPrice, ELECTRONICS_CONFIG.maxPrice);

  return locale === 'en'
    ? `📱 **Electronics:** ${priceRange}\n• ${examples}`
    : `📱 **電子機器:** ${priceRange}\n• ${examples}`;
};

const getDynamicFurniturePricing = (locale: string): string => {
  const examples = FURNITURE_CONFIG.examples.slice(0, 3).join(', ');
  const minPrice = `¥${FURNITURE_CONFIG.minPrice.toLocaleString()}+`;

  return locale === 'en'
    ? `🪑 **Furniture:** ${minPrice}\n• ${examples}`
    : `🪑 **家具:** ${minPrice}\n• ${examples}`;
};

export default class ChatbotKnowledge {
  private locale: string;
  private currentPage: string;
  private knowledge: PageKnowledge;
  private useAI: boolean = false;
  private aiInitialized: boolean = false;

  constructor(locale: string, currentPage: string) {
    this.locale = locale;
    this.currentPage = currentPage;
    this.knowledge = this.initializeKnowledge();
    this.initializeAI();
  }

  private async initializeAI() {
    try {
      // Check AI availability via API endpoint
      const response = await fetch('/api/chatbot', { method: 'GET' });
      const data = await response.json();

      this.useAI = data.aiAvailable || false;
      this.aiInitialized = true;

      if (this.useAI && data.provider) {
        console.log(`AI Chatbot enabled: ${data.provider.provider} (${data.provider.model})`);
      } else {
        console.log('AI service not available, using fallback system');
      }
    } catch (err) {
      console.log('AI service check failed, using fallback system:', err);
      this.useAI = false;
      this.aiInitialized = true;
    }
  }

  private initializeKnowledge(): PageKnowledge {
    return {
      // Global knowledge (applies to all pages)
      global: {
        context: {
          en: "EBAM Motors - Japanese used goods export to Ghana and Africa",
          ja: "EBAM Motors - 日本からガーナ・アフリカへの中古品輸出"
        },
        patterns: [
          {
            keywords: ["hello", "hi", "hey", "good morning", "good afternoon", "こんにちは", "はじめまして", "おはよう"],
            responses: {
              en: [
                "Hello! Welcome to EBAM Motors! 👋 I'm here to help you with information about our Japanese used goods export to Ghana and Africa. What can I help you with today?",
                "Hi there! I'm the EBAM Motors assistant. How can I help you find the perfect used goods from Japan today?"
              ],
              ja: [
                "こんにちは！EBAM Motorsへようこそ！👋 日本からガーナ・アフリカへの中古品輸出についてお手伝いします。何かご質問はありますか？",
                "はじめまして！EBAMモーターズのアシスタントです。日本の中古品についてどのようなお手伝いができますか？"
              ]
            },
            priority: 10
          },
          {
            keywords: ["price", "cost", "how much", "expensive", "cheap", "料金", "価格", "値段", "いくら", "安い", "高い"],
            responses: {
              en: () => [
                `**EBAM Motors Pricing Guide:**\n\n${getDynamicCarPricing('en')}\n\n${getDynamicElectronicsPricing('en')}\n\n${getDynamicFurniturePricing('en')}\n\n${getDynamicShippingInfo('en')}\n\nStock ID format: EBM000001. All prices FOB Japan. Contact us for detailed quotes!`,
                "We offer competitive pricing for quality Japanese goods! Prices are updated regularly based on market conditions. Contact us for current quotes on specific items."
              ],
              ja: () => [
                `**EBAMモーターズ価格ガイド:**\n\n${getDynamicCarPricing('ja')}\n\n${getDynamicElectronicsPricing('ja')}\n\n${getDynamicFurniturePricing('ja')}\n\n${getDynamicShippingInfo('ja')}\n\n在庫ID形式: EBM000001。すべて日本FOB価格。詳細見積もりはお問い合わせください！`,
                "高品質な日本製品を競争力のある価格で提供！価格は市場状況に基づいて定期的に更新されます。ご興味のある特定商品の現在の見積もりについてはお問い合わせください。"
              ]
            },
            priority: 9
          },
          {
            keywords: ["shipping", "delivery", "transport", "export", "import", "配送", "輸送", "輸出", "輸入", "送料"],
            responses: {
              en: () => [
                `**Shipping Options to Ghana & Africa:**\n\n${getDynamicShippingInfo('en')}\n\n**What We Handle:**\n✅ All export documentation\n✅ Customs clearance (Japan side)\n✅ Full shipment tracking\n✅ Insurance coverage\n\n**Your Responsibility:**\n• Import duties in destination country\n• Local customs clearance\n\n**Weight Limits:**\n• Sea Freight: Up to 2,000kg\n• Air Freight: Up to 500kg`,
                "We use established shipping routes with full tracking. Most customers choose sea freight for the best value. Need a shipping quote for specific items?"
              ],
              ja: () => [
                `**ガーナ・アフリカへの配送オプション:**\n\n${getDynamicShippingInfo('ja')}\n\n**当社対応事項:**\n✅ すべての輸出書類\n✅ 通関手続き（日本側）\n✅ 完全出荷追跡\n✅ 保険適用\n\n**お客様責任事項:**\n• 仕向地での輸入関税\n• 現地通関手続き\n\n**重量制限:**\n• 海上便: 最大2,000kg\n• 航空便: 最大500kg`,
                "確立された配送ルートで完全追跡を使用。ほとんどのお客様は最高の価値のために海上便を選択。特定商品の配送見積もりが必要ですか？"
              ]
            },
            priority: 8
          },
          {
            keywords: ["whatsapp", "contact", "phone", "email", "reach", "連絡", "電話", "メール", "問い合わせ"],
            responses: {
              en: [
                "You can reach us through:\nWhatsApp Channel: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G\n\nJapan Office: 080-6985-2864\nGhana Office: +233245375692\nEmail: <EMAIL>\n\nJapan: Saitama, Hanno City, Nagata\nGhana: Kumasi, Ghana",
                "Contact us anytime! Our WhatsApp channel is the fastest way to get responses. We also have local presence in Ghana for African customers. Would you like me to redirect you there?"
              ],
              ja: [
                "お問い合わせ方法：\nWhatsAppチャンネル: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G\n\n日本オフィス: 080-6985-2864\nガーナオフィス: +233245375692\nメール: <EMAIL>\n\n日本: 埼玉県、飯能市、永田\nガーナ: クマシ、ガーナ",
                "いつでもお問い合わせください！WhatsAppチャンネルが最も迅速な対応方法です。アフリカのお客様のためにガーナにも現地拠点があります。そちらにご案内しましょうか？"
              ]
            },
            priority: 8
          },
          {
            keywords: ["stock", "inventory", "available", "cars", "toyota", "honda", "nissan", "voxy", "noah", "sienta", "yaris", "vitz", "在庫", "利用可能", "車", "トヨタ", "ホンダ", "日産", "ヴォクシー", "ノア", "シエンタ", "ヤリス", "ヴィッツ"],
            responses: {
              en: () => [
                `**Current Stock Highlights:**\n\n${getDynamicStockInfo('en')}\n\n**Stock Features:**\n• All vehicles inspected & tested\n• Stock ID format: EBM000001\n• Multiple high-quality images\n• Detailed specifications\n• Mileage & condition reports\n• Chassis numbers provided\n\n**Status Options:**\n• Available - Ready for immediate purchase\n• Reserved - Pending sale completion\n\nBrowse our full stock online or ask about specific models!`,
                "We maintain fresh stock of quality Japanese vehicles. All cars come with detailed photos, specifications, and condition reports. What type of vehicle are you looking for?"
              ],
              ja: () => [
                `**現在の在庫ハイライト:**\n\n${getDynamicStockInfo('ja')}\n\n**在庫機能:**\n• すべての車両検査・テスト済み\n• 在庫ID形式: EBM000001\n• 複数の高品質画像\n• 詳細仕様\n• 走行距離・状態レポート\n• シャーシ番号提供\n\n**ステータスオプション:**\n• 利用可能 - 即座購入可能\n• 予約済み - 販売完了保留中\n\nオンラインで全在庫を閲覧するか、特定モデルについてお尋ねください！`,
                "高品質な日本車の新鮮な在庫を維持しています。すべての車に詳細写真、仕様、状態レポートが付属。どのような車両をお探しですか？"
              ]
            },
            priority: 8
          },
          {
            keywords: ["payment", "pay", "money", "bank", "transfer", "method", "支払い", "お金", "銀行", "振込", "方法"],
            responses: {
              en: [
                "**Payment Methods Available:**\n\n💳 **For International Customers:**\n• Bank wire transfer (most common)\n• PayPal (for smaller amounts)\n• Western Union\n• MoneyGram\n\n🏦 **For Japanese Customers:**\n• Bank transfer (銀行振込)\n• Cash payment\n• Credit card (selected items)\n\n**Payment Process:**\n1. Receive detailed quote\n2. Confirm order & payment method\n3. Make payment (we provide bank details)\n4. We confirm payment & begin export process\n5. Receive tracking information\n\n**Security:** All transactions are secure and documented. We provide payment confirmation and receipts.",
                "We accept various payment methods for your convenience. Bank transfer is most popular for international customers. Would you like specific payment details for your region?"
              ],
              ja: [
                "**利用可能な支払い方法:**\n\n💳 **海外のお客様向け:**\n• 銀行電信送金（最も一般的）\n• PayPal（少額の場合）\n• Western Union\n• MoneyGram\n\n🏦 **日本のお客様向け:**\n• 銀行振込\n• 現金支払い\n• クレジットカード（選択商品）\n\n**支払いプロセス:**\n1. 詳細見積もり受領\n2. 注文・支払い方法確認\n3. 支払い実行（銀行詳細提供）\n4. 支払い確認・輸出プロセス開始\n5. 追跡情報受領\n\n**セキュリティ:** すべての取引は安全で文書化されています。支払い確認書と領収書を提供します。",
                "お客様の便宜のため様々な支払い方法を受け付けています。海外のお客様には銀行振込が最も人気です。お客様の地域の具体的支払い詳細をお知りになりたいですか？"
              ]
            },
            priority: 7
          },
          {
            keywords: ["quality", "condition", "inspection", "guarantee", "warranty", "品質", "状態", "検査", "保証", "ワランティ"],
            responses: {
              en: [
                "**EBAM Motors Quality Assurance:**\n\n🔍 **Inspection Process:**\n• Every vehicle thoroughly inspected\n• Engine & transmission testing\n• Body condition assessment\n• Interior evaluation\n• Electrical systems check\n\n📋 **Condition Ratings:**\n• Excellent - Like new condition\n• Very Good - Minor wear only\n• Good - Normal wear, fully functional\n• Fair - Some wear, mechanically sound\n\n📸 **Documentation:**\n• Multiple high-resolution photos\n• Detailed condition reports\n• Mileage verification\n• Maintenance history (when available)\n\n✅ **Our Promise:**\n• Honest condition reporting\n• No hidden damages\n• Professional assessment\n• Quality guarantee",
                "We take quality seriously! Every item is carefully inspected and honestly described. Our reputation depends on customer satisfaction. What specific quality concerns do you have?"
              ],
              ja: [
                "**EBAMモーターズ品質保証:**\n\n🔍 **検査プロセス:**\n• すべての車両を徹底検査\n• エンジン・トランスミッションテスト\n• ボディ状態評価\n• 内装評価\n• 電気系統チェック\n\n📋 **状態評価:**\n• 優秀 - 新車同様の状態\n• 非常に良い - 軽微な摩耗のみ\n• 良い - 通常の摩耗、完全機能\n• 普通 - 一部摩耗、機械的に健全\n\n📸 **文書化:**\n• 複数の高解像度写真\n• 詳細状態レポート\n• 走行距離確認\n• メンテナンス履歴（利用可能時）\n\n✅ **当社の約束:**\n• 正直な状態報告\n• 隠れた損傷なし\n• プロフェッショナル評価\n• 品質保証",
                "品質を真剣に考えています！すべての商品は慎重に検査され、正直に説明されます。当社の評判は顧客満足にかかっています。具体的な品質に関する懸念はありますか？"
              ]
            },
            priority: 7
          },
          {
            keywords: ["faq", "frequently", "asked", "questions", "common", "help", "よくある", "質問", "FAQ", "ヘルプ"],
            responses: {
              en: [
                "**Frequently Asked Questions:**\n\n❓ **How long does shipping take?**\n• Sea freight: 4-6 weeks (standard) or 3-4 weeks (express)\n• Air freight: 1-2 weeks\n\n❓ **What's included in the price?**\n• FOB Japan price (vehicle + export documentation)\n• Shipping costs are separate\n\n❓ **Do you handle customs?**\n• We handle Japan export customs\n• You handle import customs in your country\n\n❓ **Can I see more photos?**\n• Yes! Each vehicle has multiple detailed photos\n• Contact us for additional angles if needed\n\n❓ **What about warranties?**\n• Vehicles sold as-is with honest condition reports\n• No warranties, but full transparency on condition\n\nMore questions? Ask me anything!",
                "I'm here to help with any questions! Common topics include shipping times, pricing, vehicle conditions, and payment methods. What would you like to know?"
              ],
              ja: [
                "**よくある質問:**\n\n❓ **配送にはどのくらいかかりますか？**\n• 海上便: 4-6週間（標準）または3-4週間（急行）\n• 航空便: 1-2週間\n\n❓ **価格に何が含まれますか？**\n• 日本FOB価格（車両+輸出書類）\n• 送料は別途\n\n❓ **通関手続きは対応しますか？**\n• 日本の輸出通関は当社対応\n• お客様の国での輸入通関はお客様対応\n\n❓ **もっと写真を見ることはできますか？**\n• はい！各車両に複数の詳細写真があります\n• 必要に応じて追加アングルはお問い合わせください\n\n❓ **保証はありますか？**\n• 車両は現状渡しで正直な状態レポート付き\n• 保証はありませんが、状態について完全透明性\n\nその他のご質問は？何でもお尋ねください！",
                "どんなご質問でもお手伝いします！一般的なトピックには配送時間、価格、車両状態、支払い方法があります。何をお知りになりたいですか？"
              ]
            },
            priority: 6
          },
          {
            keywords: ["problem", "issue", "trouble", "error", "not working", "broken", "問題", "トラブル", "エラー", "動かない", "壊れた"],
            responses: {
              en: [
                "**Having an issue? I'm here to help!**\n\n🔧 **Common Solutions:**\n• Website loading slowly? Try refreshing the page\n• Can't see vehicle images? Check your internet connection\n• Payment questions? Contact our finance team\n• Shipping delays? We'll provide tracking updates\n\n📞 **Need Direct Help?**\n• WhatsApp: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G\n• Japan Office: 080-6985-2864\n• Ghana Office: +233245375692\n• Email: <EMAIL>\n\n**Describe your specific issue and I'll do my best to help or connect you with the right person!**",
                "Sorry to hear you're having trouble! Please describe the specific issue you're experiencing, and I'll help you find a solution or connect you with our support team."
              ],
              ja: [
                "**問題が発生していますか？お手伝いします！**\n\n🔧 **一般的な解決策:**\n• ウェブサイトの読み込みが遅い？ページを更新してみてください\n• 車両画像が見えない？インターネット接続を確認してください\n• 支払いに関する質問？財務チームにお問い合わせください\n• 配送遅延？追跡更新を提供します\n\n📞 **直接サポートが必要？**\n• WhatsApp: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G\n• 日本オフィス: 080-6985-2864\n• ガーナオフィス: +233245375692\n• メール: <EMAIL>\n\n**具体的な問題を説明していただければ、最善を尽くしてお手伝いするか、適切な担当者におつなぎします！**",
                "トラブルが発生して申し訳ありません！具体的に発生している問題を説明していただければ、解決策を見つけるお手伝いをするか、サポートチームにおつなぎします。"
              ]
            },
            priority: 6
          },
          // Comprehensive automotive knowledge
          {
            keywords: ["engine", "transmission", "automatic", "manual", "CVT", "hybrid", "fuel", "consumption", "mileage", "エンジン", "トランスミッション", "オートマ", "マニュアル", "ハイブリッド", "燃費"],
            responses: {
              en: [
                "**Vehicle Technical Information:**\n\n🚗 **Engine Types Available:**\n• 1.3L - 2.0L petrol engines (most common)\n• Hybrid systems (Toyota Prius technology)\n• Diesel engines (selected models)\n\n⚙️ **Transmission Options:**\n• CVT (Continuously Variable Transmission) - smooth, fuel-efficient\n• Automatic - reliable, easy to drive\n• Manual - lower cost, better control\n\n⛽ **Fuel Efficiency:**\n• Compact cars: 15-20 km/L\n• Mid-size cars: 12-16 km/L\n• MPVs: 10-14 km/L\n• Hybrids: 20-25 km/L\n\n**All vehicles come with detailed engine condition reports and maintenance history when available.**",
                "Japanese vehicles are renowned for their reliable engines and efficient transmissions. Most of our Toyota models feature advanced CVT technology for optimal fuel economy. What specific technical details would you like to know?"
              ],
              ja: [
                "**車両技術情報:**\n\n🚗 **利用可能エンジンタイプ:**\n• 1.3L - 2.0Lガソリンエンジン（最も一般的）\n• ハイブリッドシステム（トヨタプリウス技術）\n• ディーゼルエンジン（選択モデル）\n\n⚙️ **トランスミッションオプション:**\n• CVT（無段変速機）- スムーズ、燃費効率\n• オートマチック - 信頼性、運転しやすい\n• マニュアル - 低コスト、より良いコントロール\n\n⛽ **燃費効率:**\n• コンパクトカー: 15-20 km/L\n• 中型車: 12-16 km/L\n• MPV: 10-14 km/L\n• ハイブリッド: 20-25 km/L\n\n**すべての車両に詳細なエンジン状態レポートと利用可能時のメンテナンス履歴が付属します。**",
                "日本車は信頼性の高いエンジンと効率的なトランスミッションで有名です。トヨタモデルの多くは最適な燃費のための先進CVT技術を搭載。どの具体的な技術詳細をお知りになりたいですか？"
              ]
            },
            priority: 8
          },
          {
            keywords: ["insurance", "registration", "documents", "paperwork", "title", "export certificate", "保険", "登録", "書類", "ペーパーワーク", "タイトル", "輸出証明書"],
            responses: {
              en: [
                "**Documentation & Paperwork:**\n\n📋 **What We Provide:**\n• Export Certificate (official Japanese export document)\n• Bill of Lading (shipping document)\n• Commercial Invoice (for customs)\n• Packing List (detailed item description)\n• Insurance Certificate (shipping insurance)\n• Vehicle Registration Copy (when applicable)\n• Inspection Reports (condition assessment)\n\n📄 **What You Need to Arrange:**\n• Import permit (in your country)\n• Customs clearance (destination port)\n• Local registration (after arrival)\n• Local insurance (for road use)\n• Import duty payment (varies by country)\n\n**We handle all Japanese export requirements. You handle import requirements in your country.**",
                "All necessary export documentation is included with your purchase. We ensure everything is properly prepared for smooth customs clearance. Need specific information about documentation for your country?"
              ],
              ja: [
                "**書類・ペーパーワーク:**\n\n📋 **当社提供書類:**\n• 輸出証明書（日本公式輸出書類）\n• 船荷証券（配送書類）\n• 商業送り状（税関用）\n• パッキングリスト（詳細商品説明）\n• 保険証明書（配送保険）\n• 車両登録証コピー（該当時）\n• 検査レポート（状態評価）\n\n📄 **お客様手配必要書類:**\n• 輸入許可（お客様の国）\n• 通関手続き（仕向港）\n• 現地登録（到着後）\n• 現地保険（道路使用用）\n• 輸入関税支払い（国により異なる）\n\n**すべての日本輸出要件を処理します。お客様の国での輸入要件はお客様が処理。**",
                "購入にはすべての必要な輸出書類が含まれます。スムーズな通関のためにすべてが適切に準備されることを保証します。お客様の国の書類について具体的な情報が必要ですか？"
              ]
            },
            priority: 7
          },
          {
            keywords: ["maintenance", "service", "repair", "parts", "spare parts", "warranty", "guarantee", "メンテナンス", "サービス", "修理", "部品", "スペアパーツ", "保証"],
            responses: {
              en: [
                "**Maintenance & Service Information:**\n\n🔧 **Vehicle Condition:**\n• All vehicles undergo comprehensive inspection\n• Recent maintenance records provided when available\n• Known issues clearly disclosed\n• Estimated remaining service life indicated\n\n🛠️ **Parts Availability:**\n• Toyota parts widely available globally\n• Honda/Nissan parts also well-supported\n• Common maintenance items easily sourced\n• We can provide parts supplier contacts\n\n⚠️ **Important Notes:**\n• Vehicles sold \"as-is\" with full disclosure\n• No warranty provided (honest condition reporting instead)\n• Recommend local inspection upon arrival\n• Maintenance history varies by previous owner\n\n**We believe in transparency - you get exactly what's described, nothing hidden.**",
                "Japanese vehicles are known for reliability and easy maintenance. Parts are widely available, and local mechanics familiar with these brands can be found in most countries. What specific maintenance concerns do you have?"
              ],
              ja: [
                "**メンテナンス・サービス情報:**\n\n🔧 **車両状態:**\n• すべての車両が包括的検査を受ける\n• 利用可能時は最近のメンテナンス記録を提供\n• 既知の問題を明確に開示\n• 推定残存サービス寿命を表示\n\n🛠️ **部品入手可能性:**\n• トヨタ部品は世界的に広く利用可能\n• ホンダ・日産部品もよくサポートされている\n• 一般的なメンテナンス項目は簡単に調達可能\n• 部品供給業者の連絡先を提供可能\n\n⚠️ **重要な注意事項:**\n• 車両は完全開示で「現状渡し」\n• 保証は提供されません（代わりに正直な状態報告）\n• 到着時の現地検査を推奨\n• メンテナンス履歴は前所有者により異なる\n\n**透明性を信じています - 説明された通りのものを、隠し事なしで提供。**",
                "日本車は信頼性と簡単なメンテナンスで知られています。部品は広く利用可能で、これらのブランドに精通した現地メカニックはほとんどの国で見つけることができます。具体的なメンテナンスの懸念はありますか？"
              ]
            },
            priority: 7
          },
          // Business and logistics knowledge
          {
            keywords: ["business", "wholesale", "bulk", "dealer", "reseller", "distributor", "partnership", "ビジネス", "卸売り", "大量", "ディーラー", "再販業者", "販売代理店", "パートナーシップ"],
            responses: {
              en: [
                "**Business & Wholesale Opportunities:**\n\n🤝 **Partnership Options:**\n• Authorized dealer programs\n• Bulk purchase discounts\n• Regular supply agreements\n• Exclusive territory rights (selected regions)\n\n📦 **Wholesale Benefits:**\n• Volume discounts (5-15% off retail)\n• Priority stock allocation\n• Extended payment terms\n• Marketing support materials\n\n💼 **Requirements:**\n• Established business registration\n• Minimum order quantities\n• Financial references\n• Local market presence\n\n**Interested in business partnership? Contact our business development team for detailed proposals and terms.**",
                "We welcome business partnerships! Whether you're looking to become a dealer, reseller, or bulk buyer, we have programs designed to support your business growth. What type of partnership interests you?"
              ],
              ja: [
                "**ビジネス・卸売り機会:**\n\n🤝 **パートナーシップオプション:**\n• 認定ディーラープログラム\n• 大量購入割引\n• 定期供給契約\n• 独占地域権（選択地域）\n\n📦 **卸売りメリット:**\n• ボリューム割引（小売価格の5-15%オフ）\n• 優先在庫配分\n• 延長支払い条件\n• マーケティングサポート資料\n\n💼 **要件:**\n• 確立された事業登録\n• 最小注文数量\n• 財務照会\n• 現地市場プレゼンス\n\n**ビジネスパートナーシップにご興味がありますか？詳細な提案と条件については、事業開発チームにお問い合わせください。**",
                "ビジネスパートナーシップを歓迎します！ディーラー、再販業者、大量購入者のいずれを目指す場合でも、ビジネス成長をサポートするプログラムがあります。どのようなパートナーシップにご興味がありますか？"
              ]
            },
            priority: 7
          },
          {
            keywords: ["customs", "duty", "tax", "import", "clearance", "port", "fees", "charges", "税関", "関税", "税金", "輸入", "通関", "港", "手数料", "料金"],
            responses: {
              en: [
                "**Customs & Import Information:**\n\n🏛️ **Import Duties (Varies by Country):**\n• Ghana: 5-35% depending on item type\n• Nigeria: 10-70% (vehicles typically 35%)\n• Kenya: 25-35% for vehicles\n• South Africa: 18-45% depending on category\n\n📋 **Additional Fees:**\n• Port handling charges: $200-500\n• Customs clearance: $100-300\n• Local transport from port: $50-200\n• Documentation fees: $50-150\n\n⚠️ **Important Notes:**\n• Duties calculated on CIF value (Cost + Insurance + Freight)\n• Age restrictions may apply (some countries limit vehicle age)\n• Environmental taxes may apply\n• We provide all necessary export documentation\n\n**Contact your local customs office for exact rates and requirements for your specific location.**",
                "Import duties vary significantly by country and item type. We provide all necessary export documentation, but you'll need to handle customs clearance and duty payment at your destination. Need specific information for your country?"
              ],
              ja: [
                "**税関・輸入情報:**\n\n🏛️ **輸入関税（国により異なる）:**\n• ガーナ: 商品タイプにより5-35%\n• ナイジェリア: 10-70%（車両は通常35%）\n• ケニア: 車両25-35%\n• 南アフリカ: カテゴリーにより18-45%\n\n📋 **追加手数料:**\n• 港湾取扱料金: $200-500\n• 通関手続き: $100-300\n• 港からの現地輸送: $50-200\n• 書類手数料: $50-150\n\n⚠️ **重要な注意事項:**\n• 関税はCIF価格で計算（コスト+保険+運賃）\n• 年式制限が適用される場合あり（一部の国では車両年式を制限）\n• 環境税が適用される場合あり\n• 必要なすべての輸出書類を提供\n\n**お客様の特定の場所の正確な税率と要件については、現地税関事務所にお問い合わせください。**",
                "輸入関税は国と商品タイプにより大きく異なります。必要なすべての輸出書類を提供しますが、仕向地での通関手続きと関税支払いはお客様が処理する必要があります。お客様の国の具体的な情報が必要ですか？"
              ]
            },
            priority: 8
          },
          {
            keywords: ["financing", "loan", "credit", "installment", "payment plan", "down payment", "ファイナンス", "ローン", "クレジット", "分割払い", "支払いプラン", "頭金"],
            responses: {
              en: [
                "**Payment & Financing Options:**\n\n💳 **Standard Payment Methods:**\n• Bank wire transfer (most secure)\n• PayPal (for amounts under $5,000)\n• Western Union / MoneyGram\n• Cryptocurrency (Bitcoin, USDT) - selected cases\n\n📅 **Payment Plans Available:**\n• 50% deposit + 50% before shipping\n• 30% deposit + 70% before shipping\n• Full payment upfront (5% discount)\n• Letter of Credit (for business customers)\n\n🏦 **Third-Party Financing:**\n• We can recommend financing partners in Ghana/Nigeria\n• Local banks may offer import financing\n• Microfinance institutions for smaller amounts\n• Trade finance companies for business customers\n\n**Note: We don't provide direct financing, but can connect you with trusted financial partners in your region.**",
                "We offer flexible payment options to make your purchase easier. While we don't provide direct financing, we work with financial partners who can help. What payment option works best for you?"
              ],
              ja: [
                "**支払い・ファイナンスオプション:**\n\n💳 **標準支払い方法:**\n• 銀行電信送金（最も安全）\n• PayPal（$5,000未満の金額）\n• Western Union / MoneyGram\n• 暗号通貨（Bitcoin、USDT）- 選択ケース\n\n📅 **利用可能な支払いプラン:**\n• 50%デポジット + 配送前50%\n• 30%デポジット + 配送前70%\n• 全額前払い（5%割引）\n• 信用状（ビジネス顧客向け）\n\n🏦 **第三者ファイナンス:**\n• ガーナ・ナイジェリアのファイナンスパートナーを推奨可能\n• 現地銀行が輸入ファイナンスを提供する場合あり\n• 少額のマイクロファイナンス機関\n• ビジネス顧客向け貿易金融会社\n\n**注意: 直接ファイナンスは提供しませんが、お客様の地域の信頼できる金融パートナーにおつなぎできます。**",
                "購入を容易にする柔軟な支払いオプションを提供します。直接ファイナンスは提供しませんが、サポートできる金融パートナーと協力しています。どの支払いオプションが最適ですか？"
              ]
            },
            priority: 7
          }
        ]
      },

      // Stock page specific knowledge
      stock: {
        context: {
          en: "Current stock of Japanese used goods including cars, electronics, and furniture",
          ja: "車、電子機器、家具を含む日本の中古品の現在の在庫"
        },
        patterns: [
          {
            keywords: ["toyota", "voxy", "noah", "sienta", "vitz", "yaris", "car", "vehicle", "トヨタ", "ヴォクシー", "ノア", "シエンタ", "ヴィッツ", "ヤリス", "車"],
            responses: {
              en: [
                "**🚗 Toyota Vehicles in Stock:**\n\n**Family MPVs:**\n• **Toyota Voxy (8-seater)**: ¥295,000-¥300,000\n  - 2.0L engine, CVT transmission\n  - Sliding doors, spacious interior\n  - Fuel efficiency: 13-15 km/L\n  - Perfect for large families\n\n• **Toyota Noah (8-seater)**: ¥350,000\n  - Similar to Voxy, slightly different styling\n  - Excellent safety ratings\n  - Reliable family transportation\n\n• **Toyota Sienta (7-seater)**: ¥320,000\n  - Compact MPV, easier to park\n  - 1.5L engine, great fuel economy\n  - Ideal for city driving\n\n**Compact Cars:**\n• **Toyota Vitz (5-seater)**: ¥325,000\n  - 1.0-1.3L engine options\n  - Excellent fuel efficiency: 18-20 km/L\n  - Perfect first car or city commuter\n\n• **Toyota Yaris (5-seater)**: ¥550,000\n  - Newer model, advanced features\n  - Hybrid options available\n  - Superior build quality\n\n**All vehicles include detailed inspection reports and multiple photos. Which model interests you most?**",
                "Our Toyota lineup covers everything from compact city cars to spacious family MPVs. Each vehicle comes with comprehensive documentation and honest condition reports. What type of vehicle best suits your needs?"
              ],
              ja: [
                "**🚗 在庫トヨタ車両:**\n\n**ファミリーMPV:**\n• **トヨタ ヴォクシー（8人乗り）**: ¥295,000-¥300,000\n  - 2.0Lエンジン、CVTトランスミッション\n  - スライドドア、広々とした内装\n  - 燃費効率: 13-15 km/L\n  - 大家族に最適\n\n• **トヨタ ノア（8人乗り）**: ¥350,000\n  - ヴォクシーに類似、スタイリングが若干異なる\n  - 優秀な安全評価\n  - 信頼できるファミリー輸送\n\n• **トヨタ シエンタ（7人乗り）**: ¥320,000\n  - コンパクトMPV、駐車しやすい\n  - 1.5Lエンジン、優れた燃費\n  - 市街地運転に理想的\n\n**コンパクトカー:**\n• **トヨタ ヴィッツ（5人乗り）**: ¥325,000\n  - 1.0-1.3Lエンジンオプション\n  - 優秀な燃費効率: 18-20 km/L\n  - 初回車または市街地通勤に最適\n\n• **トヨタ ヤリス（5人乗り）**: ¥550,000\n  - 新しいモデル、先進機能\n  - ハイブリッドオプション利用可能\n  - 優れた構築品質\n\n**すべての車両に詳細検査レポートと複数の写真が含まれます。どのモデルに最もご興味がありますか？**",
                "トヨタラインナップはコンパクトシティカーから広々としたファミリーMPVまですべてをカバーします。各車両には包括的な書類と正直な状態レポートが付属。どのような車両がお客様のニーズに最適ですか？"
              ]
            },
            priority: 10
          },
          {
            keywords: ["honda", "nissan", "mazda", "subaru", "mitsubishi", "suzuki", "ホンダ", "日産", "マツダ", "スバル", "三菱", "スズキ"],
            responses: {
              en: [
                "**Other Japanese Brands Available:**\n\n🚗 **Honda Models:**\n• Honda Fit/Jazz - Compact, reliable, 16-18 km/L\n• Honda Freed - 6-8 seater MPV, sliding doors\n• Honda Vezel - Compact SUV, hybrid available\n• Honda Civic - Sedan, sporty design\n\n🚗 **Nissan Models:**\n• Nissan Note - Compact car, CVT transmission\n• Nissan Serena - Large MPV, 8-seater\n• Nissan X-Trail - SUV, 4WD available\n• Nissan March - Economy car, great fuel efficiency\n\n🚗 **Other Brands:**\n• Mazda Demio - Stylish compact car\n• Subaru Impreza - AWD, reliable in all conditions\n• Mitsubishi Delica - Rugged MPV, 4WD\n• Suzuki Swift - Lightweight, economical\n\n**Prices range from ¥200,000 to ¥600,000 depending on model, year, and condition. Contact us for current availability and specific pricing!**",
                "We stock various Japanese brands beyond Toyota! Honda, Nissan, Mazda, and others offer excellent alternatives with their own unique strengths. What brand or type of vehicle are you most interested in?"
              ],
              ja: [
                "**利用可能な他の日本ブランド:**\n\n🚗 **ホンダモデル:**\n• ホンダ フィット/ジャズ - コンパクト、信頼性、16-18 km/L\n• ホンダ フリード - 6-8人乗りMPV、スライドドア\n• ホンダ ヴェゼル - コンパクトSUV、ハイブリッド利用可能\n• ホンダ シビック - セダン、スポーティデザイン\n\n🚗 **日産モデル:**\n• 日産 ノート - コンパクトカー、CVTトランスミッション\n• 日産 セレナ - 大型MPV、8人乗り\n• 日産 エクストレイル - SUV、4WD利用可能\n• 日産 マーチ - エコノミーカー、優れた燃費\n\n🚗 **他のブランド:**\n• マツダ デミオ - スタイリッシュコンパクトカー\n• スバル インプレッサ - AWD、すべての条件で信頼性\n• 三菱 デリカ - 頑丈なMPV、4WD\n• スズキ スイフト - 軽量、経済的\n\n**価格はモデル、年式、状態により¥200,000から¥600,000の範囲。現在の在庫状況と具体的な価格についてはお問い合わせください！**",
                "トヨタ以外の様々な日本ブランドを在庫しています！ホンダ、日産、マツダなどは独自の強みを持つ優秀な代替品を提供します。どのブランドまたは車両タイプに最もご興味がありますか？"
              ]
            },
            priority: 9
          },
          {
            keywords: ["electronics", "refrigerator", "air conditioner", "panasonic", "sharp", "電子機器", "冷蔵庫", "エアコン", "パナソニック", "シャープ"],
            responses: {
              en: [
                "We have quality Japanese electronics in stock! 📱\n• Panasonic Refrigerator (400L): ¥45,000\n• Sharp Air Conditioner (2.5HP): ¥28,000\n\nAll items are tested and in good working condition. Need more details about any specific appliance?",
                "Japanese electronics are known for their reliability and efficiency. Which type of electronic item are you looking for?"
              ],
              ja: [
                "高品質な日本の電子機器を在庫しています！📱\n• パナソニック冷蔵庫（400L）: ¥45,000\n• シャープエアコン（2.5HP）: ¥28,000\n\nすべての商品はテスト済みで良好な動作状態です。特定の家電について詳細が必要ですか？",
                "日本の電子機器は信頼性と効率性で知られています。どのような電子機器をお探しですか？"
              ]
            },
            priority: 9
          },
          {
            keywords: ["furniture", "dining", "table", "chair", "家具", "ダイニング", "テーブル", "椅子"],
            responses: {
              en: [
                "We have beautiful Japanese furniture available! 🪑\n• Japanese Dining Set (Table + 4 Chairs): ¥32,000\n• Solid wood construction, excellent condition\n\nJapanese furniture is known for its craftsmanship and durability. Interested in seeing more furniture options?",
                "Our furniture collection includes dining sets, storage solutions, and traditional Japanese pieces. What type of furniture are you looking for?"
              ],
              ja: [
                "美しい日本の家具をご用意しています！🪑\n• 日本のダイニングセット（テーブル+椅子4脚）: ¥32,000\n• 無垢材構造、優良状態\n\n日本の家具は職人技と耐久性で知られています。他の家具オプションもご覧になりますか？",
                "家具コレクションには、ダイニングセット、収納ソリューション、伝統的な日本の家具が含まれます。どのような家具をお探しですか？"
              ]
            },
            priority: 9
          }
        ]
      },

      // Services page knowledge
      services: {
        context: {
          en: "EBAM Motors services including sourcing, recycling, and exporting used goods",
          ja: "中古品の調達、リサイクル、輸出を含むEBAMモーターズのサービス"
        },
        patterns: [
          {
            keywords: ["service", "what do you do", "business", "company", "サービス", "何をしている", "事業", "会社"],
            responses: {
              en: [
                "EBAM Motors specializes in:\n🚗 Used Cars (Toyota, Honda, Nissan)\n📱 Electronics (appliances, phones, laptops)\n🪑 Furniture (dining sets, office furniture)\n🚲 Bicycles (city bikes, mountain bikes)\n🏗️ Heavy Equipment\n🏠 Household Items\n\nWe handle everything from sourcing to shipping to Ghana and Africa!",
                "We're a comprehensive trading company connecting Japan's quality used goods market with Ghana and Africa's consumer needs. Which service interests you most?"
              ],
              ja: [
                "EBAMモーターズの専門分野：\n🚗 中古車（トヨタ、ホンダ、日産）\n📱 電子機器（家電、スマートフォン、ノートパソコン）\n🪑 家具（ダイニングセット、オフィス家具）\n🚲 自転車（シティバイク、マウンテンバイク）\n🏗️ 重機\n🏠 家庭用品\n\n調達から配送まで、ガーナ・アフリカへのすべてを取り扱います！",
                "日本の高品質中古品市場とガーナ・アフリカの消費者ニーズを結ぶ総合商社です。どのサービスに最もご興味がありますか？"
              ]
            },
            priority: 10
          }
        ]
      },

      // Contact page knowledge
      contact: {
        context: {
          en: "Contact information and ways to reach EBAM Motors",
          ja: "EBAMモーターズの連絡先情報とお問い合わせ方法"
        },
        patterns: [
          {
            keywords: ["location", "address", "where", "office", "所在地", "住所", "どこ", "オフィス"],
            responses: {
              en: [
                "We have offices in two strategic locations:\n\n📍 Japan Office: Saitama Prefecture, Hanno City, Nagata\n📍 Ghana Office: Kumasi, Ghana\n\nOur Japan office handles sourcing and export operations, while our Ghana office provides local support for African customers. Would you like directions or more location details?",
                "We're strategically positioned with offices in both Japan and Ghana - perfect for accessing Tokyo's markets while providing local support in Africa for our customers."
              ],
              ja: [
                "戦略的な2つの拠点があります：\n\n📍 日本オフィス：埼玉県、飯能市、永田\n📍 ガーナオフィス：クマシ、ガーナ\n\n日本オフィスでは調達と輸出業務を、ガーナオフィスではアフリカのお客様への現地サポートを提供しています。道順や詳細な所在地情報が必要ですか？",
                "日本とガーナの両方にオフィスを戦略的に配置し、東京市場へのアクセスを保ちながら、アフリカのお客様に現地サポートを提供しています。"
              ]
            },
            priority: 10
          },
          {
            keywords: ["ghana", "africa", "kumasi", "local", "african", "ガーナ", "アフリカ", "クマシ", "現地", "アフリカ人"],
            responses: {
              en: [
                "Great! We have a local office in Kumasi, Ghana to better serve our African customers:\n\n📞 Ghana Office: +233245375692\n📍 Location: Kumasi, Ghana\n\nOur Ghana team can help with:\n• Local customer support\n• Delivery coordination\n• After-sales service\n• Product inquiries in local context\n\nWould you like to connect with our Ghana office directly?",
                "We're proud to have local presence in Ghana! Our Kumasi office ensures you get the best support for your purchases from Japan. Contact them at +233245375692 for personalized assistance."
              ],
              ja: [
                "素晴らしい！アフリカのお客様により良いサービスを提供するため、ガーナのクマシに現地オフィスがあります：\n\n📞 ガーナオフィス: +233245375692\n📍 所在地: クマシ、ガーナ\n\nガーナチームがお手伝いできること：\n• 現地カスタマーサポート\n• 配送調整\n• アフターサービス\n• 現地の文脈での製品お問い合わせ\n\nガーナオフィスに直接お繋ぎしましょうか？",
                "ガーナに現地拠点があることを誇りに思います！クマシオフィスが日本からの購入に最適なサポートを提供します。個別サポートについては+233245375692までお問い合わせください。"
              ]
            },
            priority: 9
          }
        ]
      },

      // Home page knowledge
      home: {
        context: {
          en: "EBAM Motors homepage - overview of services and company mission",
          ja: "EBAMモーターズホームページ - サービス概要と企業使命"
        },
        patterns: [
          {
            keywords: ["mission", "about", "company", "what", "why", "使命", "について", "会社", "なぜ"],
            responses: {
              en: [
                "EBAM Motors promotes sustainable trade by giving used goods a second life where they are needed most!\n\nOur mission:\n• Connect Japan's quality used goods with Ghana/Africa's consumer needs\n• Provide affordable, reliable products\n• Support environmental sustainability through reuse\n• Bridge cultural and economic gaps\n\nWe believe in creating value for both Japanese suppliers and African buyers!",
                "We're passionate about sustainable commerce! By exporting Japan's high-quality used goods to Ghana and Africa, we help reduce waste while providing affordable products to communities that need them most."
              ],
              ja: [
                "EBAMモーターズは、中古品を最も必要とされる場所で第二の人生を与えることで、持続可能な貿易を促進しています！🌍\n\n私たちの使命：\n• 日本の高品質中古品とガーナ・アフリカの消費者ニーズを結ぶ\n• 手頃で信頼性の高い製品を提供\n• 再利用による環境持続可能性の支援\n• 文化的・経済的格差の橋渡し\n\n日本の供給者とアフリカの購入者の両方に価値を創造することを信じています！",
                "持続可能な商業に情熱を注いでいます！日本の高品質中古品をガーナ・アフリカに輸出することで、廃棄物を削減しながら、最も必要とするコミュニティに手頃な製品を提供しています。"
              ]
            },
            priority: 10
          }
        ]
      },

      // About page knowledge
      about: {
        context: {
          en: "About EBAM Motors - company history, team, and values",
          ja: "EBAMモーターズについて - 会社の歴史、チーム、価値観"
        },
        patterns: [
          {
            keywords: ["history", "founded", "team", "values", "experience", "歴史", "設立", "チーム", "価値観", "経験"],
            responses: {
              en: [
                "EBAM Motors was founded with a vision to create sustainable trade connections between Japan and Africa. Our experienced team understands both markets deeply, ensuring quality products and reliable service.\n\nOur core values:\n✅ Quality assurance\n✅ Customer satisfaction\n✅ Environmental responsibility\n✅ Cultural bridge-building\n✅ Fair trade practices",
                "We're a dedicated team with extensive experience in international trade, logistics, and quality control. Our passion is connecting communities through sustainable commerce."
              ],
              ja: [
                "EBAMモーターズは、日本とアフリカ間の持続可能な貿易関係を築くビジョンで設立されました。経験豊富なチームが両市場を深く理解し、高品質な製品と信頼できるサービスを保証します。\n\n私たちの核となる価値観：\n✅ 品質保証\n✅ 顧客満足\n✅ 環境責任\n✅ 文化的橋渡し\n✅ 公正取引慣行",
                "国際貿易、物流、品質管理において豊富な経験を持つ専任チームです。持続可能な商業を通じてコミュニティを結ぶことが私たちの情熱です。"
              ]
            },
            priority: 10
          }
        ]
      },

      // How it works page knowledge
      "how-it-works": {
        context: {
          en: "Process explanation for buying and selling through EBAM Motors",
          ja: "EBAMモーターズでの売買プロセスの説明"
        },
        patterns: [
          {
            keywords: ["process", "how", "steps", "procedure", "buy", "sell", "プロセス", "どのように", "ステップ", "手順", "購入", "販売"],
            responses: {
              en: [
                "Here's how EBAM Motors works:\n\n🔍 **For Buyers (Ghana/Africa):**\n1. Browse our stock online\n2. Contact us with your requirements\n3. We provide quotes including shipping\n4. Make payment and we handle export\n5. Receive your goods in 4-6 weeks\n\n📦 **For Suppliers (Japan):**\n1. Contact us with items to sell\n2. We evaluate and make offers\n3. We handle pickup and logistics\n4. Get paid quickly and fairly\n\nWe manage everything in between!",
                "The process is simple! We act as the bridge between Japanese suppliers and African buyers, handling all logistics, quality control, and documentation. Want details about a specific step?"
              ],
              ja: [
                "EBAMモーターズの仕組み：\n\n🔍 **購入者向け（ガーナ・アフリカ）：**\n1. オンラインで在庫を閲覧\n2. ご要望をお問い合わせ\n3. 送料込みの見積もりを提供\n4. お支払い後、輸出手続きを代行\n5. 4-6週間で商品をお受け取り\n\n📦 **供給者向け（日本）：**\n1. 販売したい商品をお問い合わせ\n2. 評価とオファーを提供\n3. 集荷と物流を代行\n4. 迅速で公正な支払い\n\n間のすべてを管理します！",
                "プロセスは簡単です！日本の供給者とアフリカの購入者の橋渡しとして、すべての物流、品質管理、書類作成を処理します。特定のステップの詳細をお知りになりたいですか？"
              ]
            },
            priority: 10
          }
        ]
      },

      // Suppliers page knowledge
      suppliers: {
        context: {
          en: "Information for Japanese suppliers wanting to sell goods",
          ja: "商品を販売したい日本の供給者向け情報"
        },
        patterns: [
          {
            keywords: ["sell", "supplier", "offer", "pickup", "payment", "evaluation", "販売", "供給者", "オファー", "集荷", "支払い", "評価"],
            responses: {
              en: [
                "Want to sell your items to EBAM Motors? We're always looking for quality used goods!\n\n**We buy:**\n• Cars (especially Toyota, Honda, Nissan)\n• Electronics (appliances, phones, laptops)\n• Furniture (dining sets, office furniture)\n• Bicycles\n• Heavy equipment\n\n**Our process:**\n• Free evaluation\n• Fair market prices\n• Quick payment\n• We handle pickup\n\nContact us for a quote!",
                "We offer competitive prices for quality used goods and handle all the logistics. Whether you're moving, upgrading, or decluttering, we can help turn your items into cash!"
              ],
              ja: [
                "EBAMモーターズに商品を販売しませんか？高品質な中古品を常に探しています！\n\n**買取対象：**\n• 車（特にトヨタ、ホンダ、日産）\n• 電子機器（家電、スマートフォン、ノートパソコン）\n• 家具（ダイニングセット、オフィス家具）\n• 自転車\n• 重機\n\n**プロセス：**\n• 無料査定\n• 公正な市場価格\n• 迅速な支払い\n• 集荷代行\n\n見積もりをお問い合わせください！",
                "高品質な中古品に競争力のある価格を提供し、すべての物流を処理します。引っ越し、アップグレード、整理整頓の際に、商品を現金に変えるお手伝いをします！"
              ]
            },
            priority: 10
          }
        ]
      },

      // Buyers page knowledge
      buyers: {
        context: {
          en: "Information for buyers in Ghana and Africa",
          ja: "ガーナ・アフリカの購入者向け情報"
        },
        patterns: [
          {
            keywords: ["buy", "purchase", "import", "ghana", "africa", "shipping", "customs", "購入", "輸入", "ガーナ", "アフリカ", "配送", "税関"],
            responses: {
              en: [
                "Welcome, African buyers! 🌍 We specialize in exporting quality Japanese used goods to Ghana and across Africa.\n\n**Why choose Japanese goods?**\n✅ Superior quality and reliability\n✅ Well-maintained and tested\n✅ Affordable prices\n✅ Long-lasting value\n\n**We handle:**\n📋 Export documentation\n🚢 Shipping arrangements\n📦 Secure packaging\n💼 Insurance coverage\n\n**You handle:**\n🏛️ Import duties and customs clearance\n🚚 Local delivery from port\n\nReady to import quality goods from Japan?",
                "Japanese used goods offer exceptional value! Our cars are known for reliability, electronics for efficiency, and furniture for craftsmanship. We make importing from Japan simple and secure."
              ],
              ja: [
                "アフリカの購入者の皆様、ようこそ！🌍 日本の高品質中古品をガーナ・アフリカ全域に輸出することを専門としています。\n\n**なぜ日本の商品を選ぶのか？**\n✅ 優れた品質と信頼性\n✅ よく整備され、テスト済み\n✅ 手頃な価格\n✅ 長期的な価値\n\n**当社が処理：**\n📋 輸出書類\n🚢 配送手配\n📦 安全な梱包\n💼 保険適用\n\n**お客様が処理：**\n🏛️ 輸入関税と税関手続き\n🚚 港からの現地配送\n\n日本から高品質商品を輸入する準備はできていますか？",
                "日本の中古品は例外的な価値を提供します！車は信頼性、電子機器は効率性、家具は職人技で知られています。日本からの輸入をシンプルで安全にします。"
              ]
            },
            priority: 10
          }
        ]
      }
    };
  }

  getWelcomeMessage(): string {
    const welcomeMessages = {
      en: [
        `Hello! Welcome to EBAM Motors! I'm your AI assistant here to help with questions about our Japanese used goods export to Ghana and Africa.\n\nI can help you with:\n• Stock information and pricing\n• Shipping and logistics\n• Contact details\n• Service information\n\nWhat would you like to know?`,
        `Hi there! I'm here to assist you with EBAM Motors. Whether you're looking for cars, electronics, furniture, or have questions about our services, I'm here to help!\n\nWhat can I help you find today?`
      ],
      ja: [
        `こんにちは！EBAMモーターズへようこそ！日本からガーナ・アフリカへの中古品輸出についてお手伝いするAIアシスタントです。\n\n以下についてお手伝いできます：\n• 在庫情報と価格\n• 配送と物流\n• 連絡先詳細\n• サービス情報\n\n何についてお知りになりたいですか？`,
        `はじめまして！EBAMモーターズのお手伝いをします。車、電子機器、家具をお探しの方、またはサービスについてご質問がある方、お気軽にお尋ねください！\n\n今日は何をお探しですか？`
      ]
    };

    const messages = welcomeMessages[this.locale as 'en' | 'ja'] || welcomeMessages.en;
    return messages[Math.floor(Math.random() * messages.length)];
  }

  async getResponse(userInput: string): Promise<string> {
    // Wait for AI initialization if not done yet
    if (!this.aiInitialized) {
      await this.initializeAI();
    }

    // Try AI response first if available
    if (this.useAI) {
      try {
        const context = this.getPageContext();
        const response = await fetch('/api/chatbot', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: userInput,
            context: context,
            locale: this.locale
          }),
        });

        const data = await response.json();

        if (data.success && data.response) {
          return data.response;
        } else {
          console.log('AI response failed, falling back to rule-based system:', data.error);
          // If AI fails, disable it for this session to avoid repeated failures
          this.useAI = false;
        }
      } catch (error) {
        console.log('AI service error, falling back to rule-based system:', error);
        this.useAI = false;
      }
    }

    // Fallback to original rule-based system
    return this.getRuleBasedResponse(userInput);
  }

  private getRuleBasedResponse(userInput: string): string {
    const input = userInput.toLowerCase();

    // Get patterns from current page and global
    const currentPagePatterns = this.knowledge[this.currentPage]?.patterns || [];
    const globalPatterns = this.knowledge.global.patterns;
    const allPatterns = [...currentPagePatterns, ...globalPatterns];

    // Enhanced pattern matching with fuzzy logic and context analysis
    const matches = allPatterns
      .map(pattern => ({
        pattern,
        score: this.calculateEnhancedMatchScore(input, pattern.keywords)
      }))
      .filter(match => match.score > 0)
      .sort((a, b) => b.score - a.score || b.pattern.priority - a.pattern.priority);

    if (matches.length > 0) {
      const bestMatch = matches[0].pattern;
      const responseData = bestMatch.responses[this.locale as 'en' | 'ja'] || bestMatch.responses.en;

      // Handle function responses (dynamic content)
      const responses = typeof responseData === 'function' ? responseData() : responseData;
      return responses[Math.floor(Math.random() * responses.length)];
    }

    // Enhanced intelligent fallback system
    return this.getIntelligentFallback(input, this.locale as 'en' | 'ja');
  }

  private getPageContext(): string {
    const pageContexts: { [key: string]: { en: string; ja: string } } = {
      home: {
        en: "User is on the homepage. Focus on general company information, services overview, and welcoming new visitors.",
        ja: "ユーザーはホームページにいます。一般的な会社情報、サービス概要、新規訪問者への歓迎に焦点を当ててください。"
      },
      stock: {
        en: "User is viewing the stock/inventory page. Focus on available vehicles, pricing, and specific car models.",
        ja: "ユーザーは在庫ページを見ています。利用可能な車両、価格、特定の車種に焦点を当ててください。"
      },
      services: {
        en: "User is on the services page. Focus on export services, shipping methods, and business processes.",
        ja: "ユーザーはサービスページにいます。輸出サービス、配送方法、ビジネスプロセスに焦点を当ててください。"
      },
      contact: {
        en: "User is on the contact page. Focus on contact information, location details, and how to get in touch.",
        ja: "ユーザーは連絡先ページにいます。連絡先情報、場所の詳細、連絡方法に焦点を当ててください。"
      }
    };

    const context = pageContexts[this.currentPage];
    return context ? context[this.locale as 'en' | 'ja'] || context.en : '';
  }

  private calculateEnhancedMatchScore(input: string, keywords: string[]): number {
    let score = 0;
    const inputWords = input.split(/\s+/).filter(word => word.length > 2);

    for (const keyword of keywords) {
      const keywordLower = keyword.toLowerCase();

      // Exact phrase match (highest score)
      if (input.includes(keywordLower)) {
        score += 10;
        continue;
      }

      // Word-level matching
      for (const word of inputWords) {
        // Exact word match
        if (word === keywordLower) {
          score += 8;
        }
        // Fuzzy word match (for typos)
        else if (this.fuzzyMatch(word, keywordLower)) {
          score += 6;
        }
        // Partial word match
        else if (word.includes(keywordLower) || keywordLower.includes(word)) {
          score += 4;
        }
      }

      // Semantic similarity for common variations
      score += this.getSemanticScore(input, keywordLower);
    }

    return score;
  }

  private fuzzyMatch(word1: string, word2: string): boolean {
    if (Math.abs(word1.length - word2.length) > 2) return false;
    const distance = this.levenshteinDistance(word1, word2);
    return distance <= Math.max(1, Math.floor(Math.min(word1.length, word2.length) * 0.3));
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    return matrix[str2.length][str1.length];
  }

  private getSemanticScore(input: string, keyword: string): number {
    // Common automotive synonyms and variations
    const synonyms: { [key: string]: string[] } = {
      'car': ['vehicle', 'auto', 'automobile', 'motor'],
      'price': ['cost', 'fee', 'charge', 'amount', 'money'],
      'buy': ['purchase', 'order', 'get', 'acquire'],
      'ship': ['deliver', 'transport', 'send', 'freight'],
      'help': ['assist', 'support', 'aid', 'guide'],
      'info': ['information', 'details', 'data', 'facts'],
      '車': ['自動車', 'カー', 'ビークル'],
      '価格': ['値段', '料金', 'コスト', '金額'],
      '購入': ['買う', '注文', '取得'],
      '配送': ['輸送', '送る', 'デリバリー']
    };

    let score = 0;
    for (const [key, values] of Object.entries(synonyms)) {
      if (keyword.includes(key)) {
        for (const synonym of values) {
          if (input.includes(synonym)) {
            score += 2;
          }
        }
      }
    }
    return score;
  }

  private getIntelligentFallback(input: string, currentLang: 'en' | 'ja'): string {
    // Analyze input to provide contextual fallback
    const inputWords = input.split(/\s+/).filter(word => word.length > 2);

    // Category detection
    const categories = {
      automotive: ['car', 'vehicle', 'auto', 'motor', 'drive', 'wheel', 'engine', 'fuel', 'toyota', 'honda', 'nissan', '車', '自動車', 'エンジン', 'トヨタ', 'ホンダ', '日産'],
      business: ['business', 'buy', 'sell', 'price', 'cost', 'money', 'payment', 'order', 'wholesale', 'dealer', 'ビジネス', '購入', '販売', '価格', '支払い', '卸売り'],
      shipping: ['ship', 'delivery', 'transport', 'port', 'customs', 'import', 'export', 'freight', '配送', '輸送', '港', '税関', '輸入', '輸出'],
      technical: ['engine', 'transmission', 'fuel', 'mileage', 'specification', 'condition', 'maintenance', 'repair', 'エンジン', 'トランスミッション', '燃費', '仕様', '状態', 'メンテナンス'],
      contact: ['contact', 'phone', 'email', 'address', 'location', 'office', 'whatsapp', '連絡', '電話', 'メール', '住所', '場所', 'オフィス']
    };

    let detectedCategory = 'general';
    let maxMatches = 0;

    for (const [category, terms] of Object.entries(categories)) {
      const matches = inputWords.filter(word =>
        terms.some(term => word.includes(term) || term.includes(word))
      ).length;

      if (matches > maxMatches) {
        maxMatches = matches;
        detectedCategory = category;
      }
    }

    // Provide category-specific intelligent responses
    const intelligentResponses = {
      automotive: {
        en: [
          "**🚗 Vehicle Information Available:**\n\nI can provide detailed information about:\n\n• **Toyota Models**: Voxy (¥295,000-¥300,000), Noah (¥350,000), Sienta (¥320,000), Vitz (¥325,000), Yaris (¥550,000)\n• **Other Brands**: Honda, Nissan, Mazda, Subaru\n• **Technical Specs**: Engine types, transmission, fuel efficiency\n• **Vehicle Condition**: Inspection reports, maintenance history\n• **Features**: Seating capacity, safety ratings, special features\n\nWhat specific vehicle information would you like to know?",
          "I specialize in Japanese vehicle information! Whether you're looking for family MPVs, compact cars, or technical specifications, I can provide comprehensive details. What type of vehicle interests you most?"
        ],
        ja: [
          "**🚗 利用可能車両情報:**\n\n詳細情報を提供できること:\n\n• **トヨタモデル**: ヴォクシー（¥295,000-¥300,000）、ノア（¥350,000）、シエンタ（¥320,000）、ヴィッツ（¥325,000）、ヤリス（¥550,000）\n• **他のブランド**: ホンダ、日産、マツダ、スバル\n• **技術仕様**: エンジンタイプ、トランスミッション、燃費\n• **車両状態**: 検査レポート、メンテナンス履歴\n• **機能**: 座席数、安全評価、特別機能\n\nどの具体的な車両情報をお知りになりたいですか？",
          "日本車情報を専門としています！ファミリーMPV、コンパクトカー、技術仕様のいずれをお探しでも、包括的な詳細を提供できます。どのタイプの車両に最もご興味がありますか？"
        ]
      },
      business: {
        en: [
          "**💼 Business & Purchase Information:**\n\nI can help you with:\n\n• **Pricing**: Detailed quotes for all vehicles\n• **Payment Options**: Bank transfer, PayPal, payment plans\n• **Bulk Discounts**: 5-15% off for multiple vehicles\n• **Business Partnerships**: Dealer and reseller programs\n• **Financing**: Recommendations for import financing\n• **Documentation**: All required paperwork for purchase\n\nWhat specific business information do you need?",
          "Perfect! I can assist with all business and purchasing inquiries. Whether you're buying one vehicle or looking for wholesale opportunities, I have the information you need. What would you like to know?"
        ],
        ja: [
          "**💼 ビジネス・購入情報:**\n\nお手伝いできること:\n\n• **価格**: すべての車両の詳細見積もり\n• **支払いオプション**: 銀行送金、PayPal、支払いプラン\n• **大量割引**: 複数車両で5-15%オフ\n• **ビジネスパートナーシップ**: ディーラーと再販業者プログラム\n• **ファイナンス**: 輸入ファイナンスの推奨\n• **書類**: 購入に必要なすべての書類\n\nどの具体的なビジネス情報が必要ですか？",
          "完璧です！すべてのビジネスと購入に関するお問い合わせをお手伝いできます。1台の車両を購入するか卸売り機会をお探しかに関わらず、必要な情報があります。何をお知りになりたいですか？"
        ]
      },
      shipping: {
        en: [
          "**🚢 Shipping & Logistics Information:**\n\nComprehensive shipping details:\n\n• **Destinations**: Ghana ($800-1,200), Nigeria ($900-1,300), Kenya ($1,000-1,400)\n• **Timeframe**: 4-8 weeks door-to-port\n• **Documentation**: Export certificate, bill of lading, commercial invoice\n• **Insurance**: Full coverage during transit\n• **Tracking**: Real-time shipment monitoring\n• **Customs**: Guidance for import procedures\n\nWhich shipping aspect interests you most?",
          "I can provide complete shipping information for African destinations! From costs and timeframes to documentation and customs procedures, I have all the details you need."
        ],
        ja: [
          "**🚢 配送・物流情報:**\n\n包括的配送詳細:\n\n• **目的地**: ガーナ（$800-1,200）、ナイジェリア（$900-1,300）、ケニア（$1,000-1,400）\n• **期間**: ドアツーポート4-8週間\n• **書類**: 輸出証明書、船荷証券、商業送り状\n• **保険**: 輸送中の完全カバレッジ\n• **追跡**: リアルタイム出荷監視\n• **税関**: 輸入手続きのガイダンス\n\nどの配送面に最もご興味がありますか？",
          "アフリカの目的地への完全な配送情報を提供できます！コストと期間から書類と税関手続きまで、必要なすべての詳細があります。"
        ]
      },
      technical: {
        en: [
          "**🔧 Technical Information Available:**\n\nDetailed technical specs:\n\n• **Engines**: 1.0L-2.0L petrol, hybrid systems, diesel options\n• **Transmissions**: CVT, automatic, manual options\n• **Fuel Efficiency**: 10-25 km/L depending on model\n• **Safety Features**: Airbags, ABS, stability control\n• **Maintenance**: Service history, known issues, parts availability\n• **Condition Reports**: Detailed inspection results\n\nWhat technical details would you like to know?",
          "I can provide comprehensive technical information about all our vehicles! From engine specifications to maintenance history, I have detailed data available."
        ],
        ja: [
          "**🔧 利用可能技術情報:**\n\n詳細技術仕様:\n\n• **エンジン**: 1.0L-2.0Lガソリン、ハイブリッドシステム、ディーゼルオプション\n• **トランスミッション**: CVT、オートマチック、マニュアルオプション\n• **燃費**: モデルにより10-25 km/L\n• **安全機能**: エアバッグ、ABS、安定性制御\n• **メンテナンス**: サービス履歴、既知の問題、部品入手可能性\n• **状態レポート**: 詳細検査結果\n\nどの技術詳細をお知りになりたいですか？",
          "すべての車両の包括的な技術情報を提供できます！エンジン仕様からメンテナンス履歴まで、詳細なデータが利用可能です。"
        ]
      },
      contact: {
        en: [
          "**📞 Contact Information:**\n\n**Multiple Ways to Reach Us:**\n\n• **WhatsApp**: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G\n• **Japan Office**: 080-6985-2864\n• **Ghana Office**: +233245375692 (Kumasi)\n• **Email**: <EMAIL>\n• **Website**: Live chat (you're using it now!)\n\n**Office Hours:**\n• Japan: 9 AM - 6 PM JST\n• Ghana: 8 AM - 5 PM GMT\n\nHow would you prefer to get in touch?",
          "You can reach us through multiple channels! WhatsApp is often the fastest for quick questions, while email works great for detailed inquiries. What type of contact works best for you?"
        ],
        ja: [
          "**📞 連絡先情報:**\n\n**複数の連絡方法:**\n\n• **WhatsApp**: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G\n• **日本オフィス**: 080-6985-2864\n• **ガーナオフィス**: +233245375692（クマシ）\n• **メール**: <EMAIL>\n• **ウェブサイト**: ライブチャット（現在使用中！）\n\n**営業時間:**\n• 日本: 午前9時 - 午後6時 JST\n• ガーナ: 午前8時 - 午後5時 GMT\n\nどの方法でご連絡をご希望ですか？",
          "複数のチャンネルでご連絡いただけます！WhatsAppは迅速な質問に最も速く、メールは詳細なお問い合わせに最適です。どのタイプの連絡がお客様に最適ですか？"
        ]
      },
      general: {
        en: [
          "**🚗 Welcome to EBAM Motors! I'm your comprehensive automotive assistant.**\n\nI can provide detailed information about:\n\n• **🚙 Vehicles**: Toyota, Honda, Nissan models with specs and pricing\n• **💰 Business**: Pricing, payment plans, wholesale opportunities\n• **🚢 Shipping**: Costs, timeframes, documentation for African destinations\n• **🔧 Technical**: Engine specs, fuel efficiency, condition reports\n• **📞 Contact**: Multiple ways to reach our team\n\n**What specific information would you like to know?**",
          "I'm here to help with all your automotive needs! Whether you're looking for vehicle information, pricing, shipping details, or technical specifications, I have comprehensive knowledge to assist you. What would you like to explore?"
        ],
        ja: [
          "**🚗 EBAMモータースへようこそ！私はあなたの包括的な自動車アシスタントです。**\n\n詳細情報を提供できること:\n\n• **🚙 車両**: トヨタ、ホンダ、日産モデルの仕様と価格\n• **💰 ビジネス**: 価格、支払いプラン、卸売り機会\n• **🚢 配送**: アフリカの目的地へのコスト、期間、書類\n• **🔧 技術**: エンジン仕様、燃費、状態レポート\n• **📞 連絡**: チームに連絡する複数の方法\n\n**どの具体的な情報をお知りになりたいですか？**",
          "すべての自動車ニーズでお手伝いします！車両情報、価格、配送詳細、技術仕様のいずれをお探しでも、お手伝いする包括的な知識があります。何を探求したいですか？"
        ]
      }
    };

    const categoryResponses = intelligentResponses[detectedCategory as keyof typeof intelligentResponses];
    const responses = categoryResponses[currentLang] || categoryResponses.en;
    return responses[Math.floor(Math.random() * responses.length)];
  }

  // Keep the original calculateMatchScore for compatibility
  private calculateMatchScore(input: string, keywords: string[]): number {
    return this.calculateEnhancedMatchScore(input, keywords);
  }


  shouldTriggerLeadCapture(userInput: string): boolean {
    const input = userInput.toLowerCase();

    // Only trigger lead capture for very specific requests or complex queries
    const leadTriggers = [
      'get a quote', 'send me quote', 'detailed quote', 'custom quote',
      'speak to agent', 'talk to human', 'contact sales', 'sales team',
      'complex inquiry', 'special request', 'bulk order', 'wholesale',
      'financing options', 'payment plan', 'trade-in', 'exchange',
      'inspection report', 'detailed inspection', 'specific condition',
      'custom shipping', 'special delivery', 'urgent delivery',
      'business partnership', 'dealer inquiry', 'reseller',
      '詳細見積もり', 'カスタム見積もり', '営業担当', '人間と話したい',
      '複雑な問い合わせ', '特別な要求', '大量注文', '卸売り',
      'ファイナンス', '支払いプラン', '下取り', '交換',
      '検査レポート', '詳細検査', '特定の状態',
      'カスタム配送', '特別配送', '緊急配送',
      'ビジネスパートナーシップ', 'ディーラー問い合わせ', '再販業者'
    ];

    // Check for explicit form requests
    const formRequests = [
      'show form', 'lead form', 'contact form', 'inquiry form',
      'フォーム表示', 'リードフォーム', '問い合わせフォーム'
    ];

    // Check for very complex technical questions that need human expertise
    const complexQueries = [
      'engine problem', 'transmission issue', 'electrical fault',
      'custom modification', 'parts compatibility', 'technical specification',
      'warranty claim', 'insurance claim', 'legal question',
      'エンジン問題', 'トランスミッション問題', '電気系統故障',
      'カスタム改造', '部品互換性', '技術仕様',
      '保証請求', '保険請求', '法的質問'
    ];

    return formRequests.some(trigger => input.includes(trigger)) ||
           leadTriggers.some(trigger => input.includes(trigger)) ||
           (complexQueries.some(trigger => input.includes(trigger)) && input.length > 50);
  }

  extractProductInterest(userInput: string): string {
    const input = userInput.toLowerCase();

    // Car models
    if (input.includes('voxy')) return 'Toyota Voxy';
    if (input.includes('noah')) return 'Toyota Noah';
    if (input.includes('sienta')) return 'Toyota Sienta';
    if (input.includes('vitz')) return 'Toyota Vitz';
    if (input.includes('yaris')) return 'Toyota Yaris';
    if (input.includes('toyota') || input.includes('car')) return 'Toyota Vehicle';

    // Electronics
    if (input.includes('refrigerator') || input.includes('冷蔵庫')) return 'Refrigerator';
    if (input.includes('air conditioner') || input.includes('エアコン')) return 'Air Conditioner';
    if (input.includes('electronics') || input.includes('電子機器')) return 'Electronics';

    // Furniture
    if (input.includes('furniture') || input.includes('家具')) return 'Furniture';
    if (input.includes('dining') || input.includes('ダイニング')) return 'Dining Set';

    return 'General Inquiry';
  }
}
