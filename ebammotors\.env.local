# AI Chatbot Configuration
AI_PROVIDER=groq
GROQ_API_KEY=********************************************************
GROQ_MODEL=llama-3.1-8b-instant

# Admin Configuration
ADMIN_PASSWORD=RIyaj_2Req-1*A3@STOP

# Automation Settings
ENABLE_AUTOMATION=true

# JWT Secret for secure token authentication
JWT_SECRET=ebam-motors-jwt-secret-2024-secure-key-for-admin-authentication

# Email Configuration (Resend)
RESEND_API_KEY=re_GV4PkWSg_EPDQ1qvkRz8pEmfoSiEq4rCk
RESEND_FROM_EMAIL=EBAM Motors <<EMAIL>>
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
NO_REPLY_EMAIL=<EMAIL>

# Database Configuration (Neon/Vercel Postgres)
# Primary connection URL for @vercel/postgres
POSTGRES_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require

# Alternative connection URL (fallback)
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require