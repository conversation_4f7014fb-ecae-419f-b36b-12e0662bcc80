(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5957],{581:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eC});var a=t(5155),r=t(2115),l=t(5695),n=t(3783),i=t(7930),c=t(7809),d=t(7580),o=t(1497),x=t(2713),m=t(9397),u=t(5525),h=t(381),g=t(4416),p=t(4783),j=t(7924),b=t(3861),f=t(1243),y=t(4835),N=t(5339),v=t(8749),w=t(2657);function A(e){let{onAuthenticated:s}=e,[t,l]=(0,r.useState)(""),[n,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),[o,x]=(0,r.useState)(""),[m,h]=(0,r.useState)(0),g=async e=>{e.preventDefault(),d(!0),x("");try{let e=await fetch("/api/admin/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:t})}),a=await e.json();a.success?(localStorage.setItem("admin_token",a.token),s()):(x(a.message||"Invalid password"),h(e=>e+1),l(""))}catch(e){console.error("Auth error:",e),x("Authentication failed. Please try again."),h(e=>e+1)}finally{d(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(u.A,{className:"w-8 h-8 text-blue-600"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Admin Access"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Enter your password to access the admin dashboard"})]}),o&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-red-700 text-sm font-medium",children:o}),m>=3&&(0,a.jsx)("p",{className:"text-red-600 text-xs mt-1",children:"Multiple failed attempts detected. Please wait before trying again."})]})]}),(0,a.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Admin Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",type:n?"text":"password",value:t,onChange:e=>l(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"Enter admin password",required:!0,disabled:c}),(0,a.jsx)("button",{type:"button",onClick:()=>i(!n),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:c,children:n?(0,a.jsx)(v.A,{className:"w-5 h-5"}):(0,a.jsx)(w.A,{className:"w-5 h-5"})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:c||!t.trim(),className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Authenticating..."]}):"Access Dashboard"})]}),(0,a.jsx)("div",{className:"mt-8 p-4 bg-gray-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Security Notice"}),(0,a.jsx)("p",{children:"This is a secure admin area. All access attempts are logged and monitored."})]})]})})]}),(0,a.jsx)("div",{className:"text-center mt-6",children:(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"EBAM Motors Admin Dashboard v2.0"})})]})})}var k=t(646),C=t(4186),S=t(5868),I=t(3109),_=t(8500);function L(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0);(0,r.useEffect)(()=>{n()},[]);let n=async()=>{try{let e=localStorage.getItem("admin_token"),t=await fetch("/api/admin/dashboard-stats",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();s(e)}else s({totalCars:45,totalCustomers:128,totalOrders:23,pendingReviews:7,totalRevenue:245e4,monthlyGrowth:12.5,systemHealth:"healthy",recentActivity:[{id:"1",type:"order",message:"New order #ORD-001 received",timestamp:"2 minutes ago",status:"success"},{id:"2",type:"review",message:"Review pending approval",timestamp:"15 minutes ago",status:"warning"},{id:"3",type:"car",message:"Toyota Voxy added to inventory",timestamp:"1 hour ago",status:"success"}]})}catch(e){console.error("Failed to fetch dashboard stats:",e)}finally{l(!1)}};if(t)return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]},s))})});if(!e)return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsx)("p",{className:"text-gray-600",children:"Failed to load dashboard data."})});let x=[{title:"Total Cars",value:e.totalCars,icon:i.A,color:"blue",change:"+5 this month"},{title:"Customers",value:e.totalCustomers,icon:d.A,color:"green",change:"+12 this month"},{title:"Orders",value:e.totalOrders,icon:c.A,color:"purple",change:"+3 this week"},{title:"Pending Reviews",value:e.pendingReviews,icon:o.A,color:"orange",change:"Needs attention"}],u=e=>{switch(e){case"success":return(0,a.jsx)(k.A,{className:"w-4 h-4 text-green-500"});case"warning":return(0,a.jsx)(f.A,{className:"w-4 h-4 text-yellow-500"});case"error":return(0,a.jsx)(f.A,{className:"w-4 h-4 text-red-500"});default:return(0,a.jsx)(C.A,{className:"w-4 h-4 text-gray-500"})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:x.map((e,s)=>{let t=e.icon;return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.change})]}),(0,a.jsx)("div",{className:"p-3 rounded-full bg-".concat(e.color,"-100"),children:(0,a.jsx)(t,{className:"w-6 h-6 text-".concat(e.color,"-600")})})]})},s)})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Revenue Overview"}),(0,a.jsx)(S.A,{className:"w-5 h-5 text-green-600"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Revenue"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["\xa5",e.totalRevenue.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[e.monthlyGrowth>0?(0,a.jsx)(I.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,a.jsx)(_.A,{className:"w-4 h-4 text-red-500 mr-1"}),(0,a.jsxs)("span",{className:"text-sm font-medium ".concat(e.monthlyGrowth>0?"text-green-600":"text-red-600"),children:[e.monthlyGrowth>0?"+":"",e.monthlyGrowth,"% from last month"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Health"}),(0,a.jsx)(m.A,{className:"w-5 h-5 ".concat("healthy"===e.systemHealth?"text-green-600":"warning"===e.systemHealth?"text-yellow-600":"text-red-600")})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:["healthy"===e.systemHealth&&(0,a.jsx)(k.A,{className:"w-5 h-5 text-green-500 mr-2"}),"warning"===e.systemHealth&&(0,a.jsx)(f.A,{className:"w-5 h-5 text-yellow-500 mr-2"}),"error"===e.systemHealth&&(0,a.jsx)(f.A,{className:"w-5 h-5 text-red-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:e.systemHealth})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"All systems operational. Last check: 2 minutes ago"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"space-y-4",children:e.recentActivity.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[u(e.status),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.timestamp})]})]},e.id))}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{className:"flex items-center text-sm text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(w.A,{className:"w-4 h-4 mr-1"}),"View all activity"]})})]})]})]})}var D=t(3904),E=t(5487),P=t(6517),T=t(1539),F=t(5880),R=t(4213);function O(){var e,s,t,l,n;let[i,c]=(0,r.useState)(null),[d,o]=(0,r.useState)(!0),[x,u]=(0,r.useState)(!0),[h,g]=(0,r.useState)(30),p=(0,r.useRef)(null);(0,r.useEffect)(()=>(j(),x&&(p.current=setInterval(j,1e3*h)),()=>{p.current&&clearInterval(p.current)}),[x,h]);let j=async()=>{try{let e=localStorage.getItem("admin_token"),s=await fetch("/api/admin/health",{headers:{Authorization:"Bearer ".concat(e)}});if(s.ok){let e=await s.json();c(e)}}catch(e){console.error("Failed to fetch health data:",e)}finally{o(!1)}};return d?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):i?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat((e=>{switch(e){case"healthy":return"text-green-600 bg-green-100";case"degraded":return"text-yellow-600 bg-yellow-100";case"unhealthy":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(i.status)),children:(e=>{switch(e){case"healthy":return(0,a.jsx)(k.A,{className:"w-5 h-5"});case"degraded":return(0,a.jsx)(C.A,{className:"w-5 h-5"});case"unhealthy":return(0,a.jsx)(f.A,{className:"w-5 h-5"});default:return(0,a.jsx)(m.A,{className:"w-5 h-5"})}})(i.status)}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 capitalize",children:["System ",i.status]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Last updated: ",new Date(i.timestamp).toLocaleTimeString()]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("label",{className:"text-sm text-gray-600 mr-2",children:"Auto-refresh:"}),(0,a.jsx)("button",{onClick:()=>{u(!x),x||j()},className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(x?"bg-blue-600":"bg-gray-200"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(x?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("button",{onClick:j,className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-1"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(E.A,{className:"w-5 h-5 text-gray-400 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Uptime"}),(0,a.jsx)("p",{className:"font-semibold",children:(e=>{let s=Math.floor(e/86400),t=Math.floor(e%86400/3600),a=Math.floor(e%3600/60);return s>0?"".concat(s,"d ").concat(t,"h ").concat(a,"m"):t>0?"".concat(t,"h ").concat(a,"m"):"".concat(a,"m")})(i.uptime)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(P.A,{className:"w-5 h-5 text-gray-400 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Requests (5m)"}),(0,a.jsx)("p",{className:"font-semibold",children:i.stats.requests})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(T.A,{className:"w-5 h-5 text-gray-400 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Avg Response"}),(0,a.jsxs)("p",{className:"font-semibold",children:[i.stats.averageResponseTime,"ms"]})]})]})]}),i.issues.length>0&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800 mb-2",children:"Active Issues"}),(0,a.jsx)("ul",{className:"text-sm text-yellow-700 space-y-1",children:i.issues.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 flex-shrink-0"}),e]},s))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Metrics"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Error Rate"}),(0,a.jsxs)("div",{className:"flex items-center",children:[i.stats.errorRate>5?(0,a.jsx)(I.A,{className:"w-4 h-4 text-red-500 mr-1"}):(0,a.jsx)(_.A,{className:"w-4 h-4 text-green-500 mr-1"}),(0,a.jsxs)("span",{className:"font-semibold ".concat(i.stats.errorRate>5?"text-red-600":"text-green-600"),children:[i.stats.errorRate,"%"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Slow Requests"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:i.stats.slowRequests})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Total Requests"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:i.stats.requests})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Database Health"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(null==(e=i.database)?void 0:e.healthy)?(0,a.jsx)(k.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,a.jsx)(f.A,{className:"w-4 h-4 text-red-500 mr-1"}),(0,a.jsx)("span",{className:"font-semibold ".concat((null==(s=i.database)?void 0:s.healthy)?"text-green-600":"text-red-600"),children:(null==(t=i.database)?void 0:t.healthy)?"Healthy":"Error"})]})]}),(null==(l=i.database)?void 0:l.latency)&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Latency"}),(0,a.jsxs)("span",{className:"font-semibold text-gray-900",children:[i.database.latency,"ms"]})]}),(null==(n=i.database)?void 0:n.error)&&(0,a.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:i.database.error})})]})]})]}),i.memory&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Memory Usage"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(E.A,{className:"w-6 h-6 text-blue-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"RSS"}),(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:[i.memory.rss," MB"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(F.A,{className:"w-6 h-6 text-green-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Heap Total"}),(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:[i.memory.heapTotal," MB"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(R.A,{className:"w-6 h-6 text-purple-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Heap Used"}),(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:[i.memory.heapUsed," MB"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"w-6 h-6 text-orange-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"External"}),(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:[i.memory.external," MB"]})]})]})]})]}):(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(f.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Health Data Unavailable"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Unable to fetch system health information."}),(0,a.jsx)("button",{onClick:j,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Retry"})]})})}var M=t(9869),U=t(7108),z=t(8564),V=t(6932),B=t(4653),q=t(5968),G=t(7213),H=t(3717),J=t(2525),X=t(1284),W=t(1788),Y=t(7434),K=t(1154);function Q(e){let{isOpen:s,onClose:t,onImportComplete:l}=e,[n,i]=(0,r.useState)(null),[c,d]=(0,r.useState)(!1),[o,x]=(0,r.useState)(null),[m,u]=(0,r.useState)(!1),h=(0,r.useRef)(null);if(!s)return null;let p=e=>{"text/csv"===e.type||e.name.endsWith(".csv")?(i(e),x(null)):alert("Please select a CSV file")},j=async()=>{if(n){d(!0);try{let e=new FormData;e.append("csvFile",n);let s=localStorage.getItem("admin_password")||"NDAAA5@sons&Daughters";e.append("adminKey",s),e.append("importedBy","admin");let t=await fetch("/api/cars/import",{method:"POST",body:e}),a=await t.json();x(a),a.success&&l()}catch(e){console.error("Import error:",e),x({success:!1,batch_id:"",total_rows:0,successful_imports:0,failed_imports:0,errors:["Failed to process import: "+(e instanceof Error?e.message:"Unknown error")],imported_cars:[]})}finally{d(!1)}}},b=()=>{i(null),x(null),d(!1),t()};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Bulk Import Cars from CSV"}),(0,a.jsx)("button",{onClick:b,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(g.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"p-6",children:o?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 rounded-lg border ".concat(o.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:(0,a.jsxs)("div",{className:"flex items-center",children:[o.success?(0,a.jsx)(k.A,{className:"w-6 h-6 text-green-600 mr-3"}):(0,a.jsx)(N.A,{className:"w-6 h-6 text-red-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium ".concat(o.success?"text-green-800":"text-red-800"),children:o.success?"Import Completed":"Import Failed"}),(0,a.jsxs)("p",{className:"text-sm ".concat(o.success?"text-green-600":"text-red-600"),children:[o.successful_imports," of ",o.total_rows," cars imported successfully"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:o.total_rows}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Rows"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:o.successful_imports}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Successful"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:o.failed_imports}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Failed"})]})]}),o.errors.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Import Errors:"}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 max-h-40 overflow-y-auto",children:o.errors.map((e,s)=>(0,a.jsx)("p",{className:"text-sm text-red-700 mb-1",children:e},s))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:b,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Close"}),(0,a.jsx)("button",{onClick:()=>{i(null),x(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Import Another File"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(X.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-2",children:"How to import cars:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-1",children:[(0,a.jsx)("li",{children:"Download the CSV template below"}),(0,a.jsx)("li",{children:"Fill in your car data (required: car_id, make, model, year, price)"}),(0,a.jsx)("li",{children:"Upload the completed CSV file"}),(0,a.jsx)("li",{children:"Review the import results"})]})]})]})}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("button",{onClick:()=>{let e=new Blob(['car_id,make,model,year,title,price,original_price,mileage,fuel_type,transmission,engine_size,drive_type,seats,doors,body_type,body_condition,interior_condition,exterior_color,interior_color,main_image,images,image_folder,specs,features,status,stock_quantity,location,description,is_featured\ntoyota_voxy_2012_001,Toyota,Voxy,2012,Toyota Voxy 2012 - 8 Seater Family Van,300000,350000,85000,Gasoline,CVT,2.0L,2WD,8,5,Van,Good,Good,Silver,Black,/images/toyota/voxy/2012/main.jpg,"/images/toyota/voxy/2012/1.jpg,/images/toyota/voxy/2012/2.jpg",toyota/voxy/2012,"Air Conditioning,Power Steering,Electric Windows","Family Car,Spacious,Reliable",Available,1,Japan,Excellent condition Toyota Voxy with low mileage,false\nhonda_freed_2015_001,Honda,Freed,2015,Honda Freed 2015 - Compact Minivan,420000,450000,65000,Hybrid,CVT,1.5L,2WD,6,5,Minivan,Excellent,Excellent,White,Gray,/images/honda/freed/2015/main.jpg,"/images/honda/freed/2015/1.jpg,/images/honda/freed/2015/2.jpg",honda/freed/2015,"Hybrid Engine,Navigation System,Backup Camera","Fuel Efficient,Compact,Modern",Available,1,Japan,Low mileage Honda Freed hybrid in excellent condition,true'],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="car_import_template.csv",document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(s),document.body.removeChild(t)},className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)(W.A,{className:"w-4 h-4 mr-2"}),"Download CSV Template"]})}),(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-8 text-center transition-colors ".concat(m?"border-blue-400 bg-blue-50":n?"border-green-400 bg-green-50":"border-gray-300 hover:border-gray-400"),onDrop:e=>{e.preventDefault(),u(!1);let s=Array.from(e.dataTransfer.files);s.length>0&&p(s[0])},onDragOver:e=>{e.preventDefault(),u(!0)},onDragLeave:e=>{e.preventDefault(),u(!1)},children:[n?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)(Y.A,{className:"w-8 h-8 text-green-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-800",children:n.name}),(0,a.jsxs)("p",{className:"text-xs text-green-600",children:[(n.size/1024).toFixed(1)," KB"]})]})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)(M.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900 mb-2",children:"Drop your CSV file here"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"or click to browse files"}),(0,a.jsx)("button",{onClick:()=>{var e;return null==(e=h.current)?void 0:e.click()},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Select CSV File"})]}),(0,a.jsx)("input",{ref:h,type:"file",accept:".csv",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];t&&p(t)},className:"hidden"})]}),n&&(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)("button",{onClick:j,disabled:c,className:"flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Importing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(M.A,{className:"w-4 h-4 mr-2"}),"Import Cars"]})})})]})})]})})}function $(){let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!0),[n,c]=(0,r.useState)(""),[d,o]=(0,r.useState)({make:"",model:"",status:"",year_min:"",year_max:"",price_min:"",price_max:"",is_featured:"",location:""}),[x,m]=(0,r.useState)("grid"),[u,h]=(0,r.useState)([]),[g,p]=(0,r.useState)(!1),[b,y]=(0,r.useState)(1),[N,v]=(0,r.useState)(0),[A,C]=(0,r.useState)("created_at"),[S,I]=(0,r.useState)("desc"),[_,L]=(0,r.useState)(!1),[D,E]=(0,r.useState)({total:0,available:0,sold:0,featured:0,lowStock:0});(0,r.useEffect)(()=>{P(),T()},[b,n,d,A,S]);let P=async()=>{l(!0);try{let e=localStorage.getItem("admin_token"),t=new URLSearchParams({page:b.toString(),per_page:"20",search:n,sort_by:A,sort_order:S,...Object.fromEntries(Object.entries(d).filter(e=>{let[s,t]=e;return""!==t}))}),a=await fetch("/api/admin/cars?".concat(t),{headers:{Authorization:"Bearer ".concat(e)}});if(a.ok){let e=await a.json();s(e.cars||[]),v(e.total_count||0)}}catch(e){console.error("Error loading cars:",e)}finally{l(!1)}},T=async()=>{try{let e=localStorage.getItem("admin_token"),s=await fetch("/api/admin/cars/stats",{headers:{Authorization:"Bearer ".concat(e)}});if(s.ok){let e=await s.json();E(e)}}catch(e){console.error("Error loading stats:",e)}},F=e=>{c(e),y(1)},R=(e,s)=>{o(t=>({...t,[e]:s})),y(1)},O=e=>{h(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},X=async e=>{if(0!==u.length)try{let s=localStorage.getItem("admin_token");(await fetch("/api/admin/cars/bulk",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({action:e,car_ids:u})})).ok&&(await P(),await T(),h([]))}catch(e){console.error("Error performing bulk action:",e)}},W=e=>{switch(e.toLowerCase()){case"available":return"text-green-600 bg-green-100";case"sold":default:return"text-gray-600 bg-gray-100";case"reserved":return"text-yellow-600 bg-yellow-100";case"pending":return"text-blue-600 bg-blue-100"}},Y=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"JPY";return new Intl.NumberFormat("ja-JP",{style:"currency",currency:s,minimumFractionDigits:0}).format(e)};return t&&0===e.length?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Car Inventory"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage your vehicle inventory and listings"})]}),(0,a.jsxs)("button",{onClick:()=>L(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,a.jsx)(M.A,{className:"w-4 h-4 mr-2"}),"Bulk Import CSV"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(i.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D.total}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Cars"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(k.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D.available}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Available"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(U.A,{className:"w-6 h-6 text-gray-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D.sold}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Sold"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(z.A,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D.featured}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Featured"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(f.A,{className:"w-6 h-6 text-red-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D.lowStock}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Low Stock"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search cars by make, model, or ID...",value:n,onChange:e=>F(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>p(!g),className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(V.A,{className:"w-4 h-4 mr-2"}),"Filters"]}),(0,a.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[(0,a.jsx)("button",{onClick:()=>m("grid"),className:"p-2 ".concat("grid"===x?"bg-blue-50 text-blue-600":"text-gray-400"),children:(0,a.jsx)(B.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>m("list"),className:"p-2 ".concat("list"===x?"bg-blue-50 text-blue-600":"text-gray-400"),children:(0,a.jsx)(q.A,{className:"w-4 h-4"})})]})]})]}),g&&(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("select",{value:d.make,onChange:e=>R("make",e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Makes"}),(0,a.jsx)("option",{value:"Toyota",children:"Toyota"}),(0,a.jsx)("option",{value:"Honda",children:"Honda"}),(0,a.jsx)("option",{value:"Nissan",children:"Nissan"}),(0,a.jsx)("option",{value:"Mazda",children:"Mazda"})]}),(0,a.jsxs)("select",{value:d.status,onChange:e=>R("status",e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"Available",children:"Available"}),(0,a.jsx)("option",{value:"Sold",children:"Sold"}),(0,a.jsx)("option",{value:"Reserved",children:"Reserved"}),(0,a.jsx)("option",{value:"Pending",children:"Pending"})]}),(0,a.jsx)("input",{type:"number",placeholder:"Min Year",value:d.year_min,onChange:e=>R("year_min",e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)("input",{type:"number",placeholder:"Max Year",value:d.year_max,onChange:e=>R("year_max",e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("button",{onClick:()=>{o({make:"",model:"",status:"",year_min:"",year_max:"",price_min:"",price_max:"",is_featured:"",location:""}),y(1)},className:"text-sm text-gray-600 hover:text-gray-900",children:"Clear all filters"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",e.length," of ",N," cars"]})]})]})]}),u.length>0&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[u.length," car",u.length>1?"s":""," selected"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>X("feature"),className:"px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 transition-colors",children:"Feature"}),(0,a.jsx)("button",{onClick:()=>X("unfeature"),className:"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors",children:"Unfeature"}),(0,a.jsx)("button",{onClick:()=>X("delete"),className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors",children:"Delete"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===e.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(i.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No cars found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters"})]}):"grid"===x?(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow",children:[(0,a.jsxs)("div",{className:"relative",children:[e.main_image?(0,a.jsx)("img",{src:e.main_image,alt:e.title,className:"w-full h-48 object-cover"}):(0,a.jsx)("div",{className:"w-full h-48 bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(G.A,{className:"w-12 h-12 text-gray-400"})}),(0,a.jsx)("div",{className:"absolute top-2 left-2",children:(0,a.jsx)("input",{type:"checkbox",checked:u.includes(e.id),onChange:()=>O(e.id),className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"})}),(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(W(e.status)),children:e.status})}),e.is_featured&&(0,a.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,a.jsx)(z.A,{className:"w-5 h-5 text-yellow-500 fill-current"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:[e.make," ",e.model," ",e.year]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("span",{className:"text-lg font-bold text-blue-600",children:Y(e.price,e.currency)}),e.original_price&&e.original_price>e.price&&(0,a.jsx)("span",{className:"text-sm text-gray-500 line-through",children:Y(e.original_price,e.currency)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 mb-3",children:[(0,a.jsx)("span",{children:e.mileage?"".concat(e.mileage.toLocaleString()," km"):"N/A"}),(0,a.jsx)("span",{children:e.fuel_type||"N/A"}),(0,a.jsx)("span",{children:e.transmission||"N/A"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["Stock: ",e.stock_quantity]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"p-1 text-gray-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(H.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"p-1 text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(J.A,{className:"w-4 h-4"})})]})]})]})]},e.id))})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left",children:(0,a.jsx)("input",{type:"checkbox",checked:u.length===e.length&&e.length>0,onChange:()=>{u.length===e.length?h([]):h(e.map(e=>e.id))},className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("input",{type:"checkbox",checked:u.includes(e.id),onChange:()=>O(e.id),className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.main_image?(0,a.jsx)("img",{src:e.main_image,alt:e.title,className:"w-12 h-12 object-cover rounded-lg mr-4"}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-4",children:(0,a.jsx)(G.A,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.title}),e.is_featured&&(0,a.jsx)(z.A,{className:"w-4 h-4 text-yellow-500 fill-current ml-2"})]}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.make," ",e.model," ",e.year]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:Y(e.price,e.currency)}),e.original_price&&e.original_price>e.price&&(0,a.jsx)("div",{className:"text-sm text-gray-500 line-through",children:Y(e.original_price,e.currency)})]}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(W(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.stock_quantity}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(H.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(J.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})}),N>20&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Showing ",(b-1)*20+1," to ",Math.min(20*b,N)," of ",N," results"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>y(e=>Math.max(1,e-1)),disabled:1===b,className:"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("span",{className:"px-3 py-1 bg-blue-600 text-white rounded text-sm",children:b}),(0,a.jsx)("button",{onClick:()=>y(e=>e+1),disabled:20*b>=N,className:"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})}),(0,a.jsx)(Q,{isOpen:_,onClose:()=>L(!1),onImportComplete:()=>{L(!1),P(),T()}})]})}var Z=t(5196),ee=t(4516),es=t(8883),et=t(9074);function ea(){let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!0),[n,i]=(0,r.useState)(""),[c,d]=(0,r.useState)("all"),[x,m]=(0,r.useState)(null),[u,h]=(0,r.useState)([]),[p,b]=(0,r.useState)({total:0,pending:0,approved:0,rejected:0,averageRating:0,recentSubmissions:0});(0,r.useEffect)(()=>{f()},[]);let f=async()=>{l(!0);try{let e=localStorage.getItem("admin_token"),t=await fetch("/api/reviews",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();s(e.reviews||[]),y(e.reviews||[])}}catch(e){console.error("Error fetching reviews:",e)}finally{l(!1)}},y=e=>{let s=e.length,t=e.filter(e=>"pending"===e.status).length,a=e.filter(e=>"approved"===e.status).length,r=e.filter(e=>"rejected"===e.status).length,l=e.filter(e=>"approved"===e.status),n=l.length>0?l.reduce((e,s)=>e+s.rating,0)/l.length:0,i=new Date;i.setDate(i.getDate()-7),b({total:s,pending:t,approved:a,rejected:r,averageRating:Math.round(10*n)/10,recentSubmissions:e.filter(e=>new Date(e.submittedAt)>i).length})},N=async(e,s)=>{try{let t=localStorage.getItem("admin_token");(await fetch("/api/admin/reviews/moderate",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({reviewId:e,status:s})})).ok?await f():alert("Failed to moderate review")}catch(e){console.error("Error moderating review:",e),alert("Error moderating review")}},v=async e=>{if(0!==u.length)try{let s=localStorage.getItem("admin_token");(await fetch("/api/admin/reviews/bulk",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({action:e,reviewIds:u})})).ok&&(await f(),h([]))}catch(e){console.error("Error performing bulk action:",e)}},w=e=>{h(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},A=e.filter(e=>{let s=!n||e.name.toLowerCase().includes(n.toLowerCase())||e.location.toLowerCase().includes(n.toLowerCase())||e.review.toLowerCase().includes(n.toLowerCase()),t="all"===c||e.status===c,a=!x||e.rating===x;return s&&t&&a}),k=e=>{switch(e){case"approved":return"text-green-600 bg-green-100";case"rejected":return"text-red-600 bg-red-100";case"pending":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},S=e=>Array.from({length:5},(s,t)=>(0,a.jsx)(z.A,{className:"w-4 h-4 ".concat(t<e?"text-yellow-400 fill-current":"text-gray-300")},t));return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Review Moderation"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage customer reviews and feedback"})]}),(0,a.jsxs)("button",{onClick:f,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-6 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.total}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Reviews"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(C.A,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.pending}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Pending"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(Z.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.approved}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Approved"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(g.A,{className:"w-6 h-6 text-red-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.rejected}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Rejected"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(z.A,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.averageRating}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Avg Rating"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(I.A,{className:"w-6 h-6 text-indigo-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.recentSubmissions}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"This Week"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search reviews by name, location, or content...",value:n,onChange:e=>i(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"approved",children:"Approved"}),(0,a.jsx)("option",{value:"rejected",children:"Rejected"})]}),(0,a.jsxs)("select",{value:x||"",onChange:e=>m(e.target.value?parseInt(e.target.value):null),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Ratings"}),(0,a.jsx)("option",{value:"5",children:"5 Stars"}),(0,a.jsx)("option",{value:"4",children:"4 Stars"}),(0,a.jsx)("option",{value:"3",children:"3 Stars"}),(0,a.jsx)("option",{value:"2",children:"2 Stars"}),(0,a.jsx)("option",{value:"1",children:"1 Star"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["Showing ",A.length," of ",e.length," reviews"]}),n&&(0,a.jsx)("button",{onClick:()=>i(""),className:"text-blue-600 hover:text-blue-700",children:"Clear search"})]})]}),u.length>0&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[u.length," review",u.length>1?"s":""," selected"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>v("approve"),className:"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors",children:"Approve"}),(0,a.jsx)("button",{onClick:()=>v("reject"),className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors",children:"Reject"}),(0,a.jsx)("button",{onClick:()=>v("delete"),className:"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors",children:"Delete"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===A.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(o.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No reviews found"}),(0,a.jsx)("p",{className:"text-gray-600",children:n?"Try adjusting your search or filters":"No reviews have been submitted yet"})]}):(0,a.jsxs)("div",{className:"divide-y divide-gray-200",children:[(0,a.jsx)("div",{className:"p-4 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:u.length===A.length&&A.length>0,onChange:()=>{u.length===A.length?h([]):h(A.map(e=>e.id))},className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 mr-4"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Select All"})]})}),A.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("input",{type:"checkbox",checked:u.includes(e.id),onChange:()=>w(e.id),className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 mt-1"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mt-1",children:[(0,a.jsx)(ee.A,{className:"w-4 h-4 mr-1"}),e.location,(0,a.jsx)(es.A,{className:"w-4 h-4 ml-3 mr-1"}),e.email]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("div",{className:"flex items-center",children:[S(e.rating),(0,a.jsxs)("span",{className:"ml-2 text-sm font-medium text-gray-700",children:[e.rating,"/5"]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(k(e.status)),children:e.status}),"pending"===e.status&&(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>N(e.id,"approved"),className:"p-1 text-green-600 hover:bg-green-100 rounded transition-colors",title:"Approve",children:(0,a.jsx)(Z.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>N(e.id,"rejected"),className:"p-1 text-red-600 hover:bg-red-100 rounded transition-colors",title:"Reject",children:(0,a.jsx)(g.A,{className:"w-4 h-4"})})]})]})]}),e.title&&(0,a.jsx)("h5",{className:"font-medium text-gray-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-700 mb-3 leading-relaxed",children:e.review}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(et.A,{className:"w-4 h-4 mr-1"}),new Date(e.submittedAt).toLocaleDateString()]}),e.vehiclePurchased&&(0,a.jsxs)("span",{children:["Vehicle: ",e.vehiclePurchased]})]}),(0,a.jsxs)("div",{className:"text-xs",children:["Words: ",e.review.trim().split(/\s+/).length]})]})]})]})},e.id))]})})]})}var er=t(2318),el=t(6785),en=t(9420),ei=t(1007),ec=t(4229);function ed(e){let{isOpen:s,onClose:t,onCustomerAdded:l}=e,[n,i]=(0,r.useState)({personalInfo:{name:"",email:"",phone:""},address:{street:"",city:"",state:"",country:"Ghana",postalCode:""},preferences:{communicationPreference:"email",currency:"JPY",notifications:{email:!0,sms:!1,marketing:!1}},tags:[],notes:""}),[c,d]=(0,r.useState)(!1),[o,x]=(0,r.useState)("");if(!s)return null;let m=(e,s,t)=>{i(a=>({...a,[e]:{...a[e],[s]:t}}))},u=(e,s,t,a)=>{i(r=>({...r,[e]:{...r[e],[s]:{...r[e][s],[t]:a}}}))},h=()=>{o.trim()&&!n.tags.includes(o.trim())&&(i(e=>({...e,tags:[...e.tags,o.trim()]})),x(""))},p=e=>{i(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},j=async e=>{e.preventDefault(),d(!0);try{let e=await fetch("/api/customers",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...n,adminKey:"NDAAA5@sons&Daughters"})});if(e.ok)l(),t(),i({personalInfo:{name:"",email:"",phone:""},address:{street:"",city:"",state:"",country:"Ghana",postalCode:""},preferences:{communicationPreference:"email",currency:"JPY",notifications:{email:!0,sms:!1,marketing:!1}},tags:[],notes:""});else{let s=await e.json();alert("Error creating customer: "+s.message)}}catch(e){console.error("Error creating customer:",e),alert("Error creating customer. Please try again.")}finally{d(!1)}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Add New Customer"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(g.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("form",{onSubmit:j,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ei.A,{className:"w-5 h-5 mr-2"}),"Personal Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:n.personalInfo.name,onChange:e=>m("personalInfo","name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",required:!0,value:n.personalInfo.email,onChange:e=>m("personalInfo","email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter email address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",value:n.personalInfo.phone,onChange:e=>m("personalInfo","phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"+233 XX XXX XXXX"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ee.A,{className:"w-5 h-5 mr-2"}),"Address"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Street Address"}),(0,a.jsx)("input",{type:"text",value:n.address.street,onChange:e=>m("address","street",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter street address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City"}),(0,a.jsx)("input",{type:"text",value:n.address.city,onChange:e=>m("address","city",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter city"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Country"}),(0,a.jsxs)("select",{value:n.address.country,onChange:e=>m("address","country",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"Ghana",children:"Ghana"}),(0,a.jsx)("option",{value:"Nigeria",children:"Nigeria"}),(0,a.jsx)("option",{value:"Kenya",children:"Kenya"}),(0,a.jsx)("option",{value:"South Africa",children:"South Africa"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Preferences"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Communication"}),(0,a.jsxs)("select",{value:n.preferences.communicationPreference,onChange:e=>m("preferences","communicationPreference",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"email",children:"Email"}),(0,a.jsx)("option",{value:"phone",children:"Phone"}),(0,a.jsx)("option",{value:"sms",children:"SMS"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Currency"}),(0,a.jsxs)("select",{value:n.preferences.currency,onChange:e=>m("preferences","currency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"JPY",children:"Japanese Yen (\xa5)"}),(0,a.jsx)("option",{value:"USD",children:"US Dollar ($)"}),(0,a.jsx)("option",{value:"GHS",children:"Ghana Cedi (₵)"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notification Preferences"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:n.preferences.notifications.email,onChange:e=>u("preferences","notifications","email",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Email notifications"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:n.preferences.notifications.sms,onChange:e=>u("preferences","notifications","sms",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"SMS notifications"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:n.preferences.notifications.marketing,onChange:e=>u("preferences","notifications","marketing",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Marketing communications"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tags"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("input",{type:"text",value:o,onChange:e=>x(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),h()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add a tag and press Enter"}),(0,a.jsx)("button",{type:"button",onClick:h,className:"px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200",children:"Add"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:n.tags.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>p(e),className:"ml-1 text-blue-600 hover:text-blue-800",children:(0,a.jsx)(g.A,{className:"w-3 h-3"})})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes"}),(0,a.jsx)("textarea",{value:n.notes,onChange:e=>i(s=>({...s,notes:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add any additional notes about this customer..."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[(0,a.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec.A,{className:"w-4 h-4 mr-2"}),"Create Customer"]})})]})]})]})})}function eo(e){let{isOpen:s,onClose:t,onLeadCreated:l}=e,[n,i]=(0,r.useState)({personalInfo:{name:"",email:"",phone:""},leadInfo:{source:"website",status:"new",priority:"medium",interestedVehicle:"",budget:"",timeline:"",notes:""},tags:[]}),[c,d]=(0,r.useState)(!1),[o,x]=(0,r.useState)("");if(!s)return null;let m=(e,s,t)=>{i(a=>({...a,[e]:{...a[e],[s]:t}}))},u=()=>{o.trim()&&!n.tags.includes(o.trim())&&(i(e=>({...e,tags:[...e.tags,o.trim()]})),x(""))},h=e=>{i(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},p=async e=>{e.preventDefault(),d(!0);try{let e=await fetch("/api/leads",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...n,adminKey:"NDAAA5@sons&Daughters"})});if(e.ok)l(),t(),i({personalInfo:{name:"",email:"",phone:""},leadInfo:{source:"website",status:"new",priority:"medium",interestedVehicle:"",budget:"",timeline:"",notes:""},tags:[]});else{let s=await e.json();alert("Error creating lead: "+s.message)}}catch(e){console.error("Error creating lead:",e),alert("Error creating lead. Please try again.")}finally{d(!1)}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Create New Lead"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(g.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("form",{onSubmit:p,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ei.A,{className:"w-5 h-5 mr-2"}),"Contact Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:n.personalInfo.name,onChange:e=>m("personalInfo","name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",required:!0,value:n.personalInfo.email,onChange:e=>m("personalInfo","email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter email address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",value:n.personalInfo.phone,onChange:e=>m("personalInfo","phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"+233 XX XXX XXXX"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(el.A,{className:"w-5 h-5 mr-2"}),"Lead Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Lead Source"}),(0,a.jsxs)("select",{value:n.leadInfo.source,onChange:e=>m("leadInfo","source",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"website",children:"Website"}),(0,a.jsx)("option",{value:"social_media",children:"Social Media"}),(0,a.jsx)("option",{value:"referral",children:"Referral"}),(0,a.jsx)("option",{value:"phone_call",children:"Phone Call"}),(0,a.jsx)("option",{value:"email",children:"Email"}),(0,a.jsx)("option",{value:"walk_in",children:"Walk-in"}),(0,a.jsx)("option",{value:"advertisement",children:"Advertisement"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,a.jsxs)("select",{value:n.leadInfo.priority,onChange:e=>m("leadInfo","priority",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"low",children:"Low"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"urgent",children:"Urgent"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:n.leadInfo.status,onChange:e=>m("leadInfo","status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"new",children:"New"}),(0,a.jsx)("option",{value:"contacted",children:"Contacted"}),(0,a.jsx)("option",{value:"qualified",children:"Qualified"}),(0,a.jsx)("option",{value:"proposal",children:"Proposal Sent"}),(0,a.jsx)("option",{value:"negotiation",children:"In Negotiation"}),(0,a.jsx)("option",{value:"closed_won",children:"Closed Won"}),(0,a.jsx)("option",{value:"closed_lost",children:"Closed Lost"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Timeline"}),(0,a.jsxs)("select",{value:n.leadInfo.timeline,onChange:e=>m("leadInfo","timeline",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select timeline"}),(0,a.jsx)("option",{value:"immediate",children:"Immediate (within 1 week)"}),(0,a.jsx)("option",{value:"short_term",children:"Short term (1-4 weeks)"}),(0,a.jsx)("option",{value:"medium_term",children:"Medium term (1-3 months)"}),(0,a.jsx)("option",{value:"long_term",children:"Long term (3+ months)"}),(0,a.jsx)("option",{value:"just_browsing",children:"Just browsing"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Interested Vehicle"}),(0,a.jsx)("input",{type:"text",value:n.leadInfo.interestedVehicle,onChange:e=>m("leadInfo","interestedVehicle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Toyota Voxy 2012"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Budget Range"}),(0,a.jsxs)("select",{value:n.leadInfo.budget,onChange:e=>m("leadInfo","budget",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select budget range"}),(0,a.jsx)("option",{value:"under_200k",children:"Under \xa5200,000"}),(0,a.jsx)("option",{value:"200k_400k",children:"\xa5200,000 - \xa5400,000"}),(0,a.jsx)("option",{value:"400k_600k",children:"\xa5400,000 - \xa5600,000"}),(0,a.jsx)("option",{value:"600k_800k",children:"\xa5600,000 - \xa5800,000"}),(0,a.jsx)("option",{value:"800k_1m",children:"\xa5800,000 - \xa51,000,000"}),(0,a.jsx)("option",{value:"over_1m",children:"Over \xa51,000,000"}),(0,a.jsx)("option",{value:"flexible",children:"Flexible"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tags"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("input",{type:"text",value:o,onChange:e=>x(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),u()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add a tag and press Enter"}),(0,a.jsx)("button",{type:"button",onClick:u,className:"px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200",children:"Add"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:n.tags.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 text-sm rounded-full",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>h(e),className:"ml-1 text-purple-600 hover:text-purple-800",children:(0,a.jsx)(g.A,{className:"w-3 h-3"})})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes"}),(0,a.jsx)("textarea",{value:n.leadInfo.notes,onChange:e=>m("leadInfo","notes",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add any additional notes about this lead, their requirements, conversation details, etc."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[(0,a.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:c,className:"flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec.A,{className:"w-4 h-4 mr-2"}),"Create Lead"]})})]})]})]})})}function ex(e){let{isOpen:s,onClose:t,onFollowupScheduled:l}=e,[n,i]=(0,r.useState)([]),[c,d]=(0,r.useState)({customerId:"",type:"email",priority:"medium",title:"",description:"",scheduledDate:"",scheduledTime:"",notes:""}),[o,x]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(s){h();let e=new Date;e.setDate(e.getDate()+1),d(s=>({...s,scheduledDate:e.toISOString().split("T")[0],scheduledTime:"10:00"}))}},[s]);let h=async()=>{u(!0);try{let e=await fetch("/api/customers?adminKey=NDAAA5@sons&Daughters");if(e.ok){let s=await e.json();i(s.customers||[])}}catch(e){console.error("Error fetching customers:",e)}finally{u(!1)}};if(!s)return null;let p=(e,s)=>{d(t=>({...t,[e]:s}))},j=async e=>{e.preventDefault(),x(!0);try{let e=new Date("".concat(c.scheduledDate,"T").concat(c.scheduledTime)).toISOString(),s=await fetch("/api/followups",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({customerId:c.customerId,type:c.type,status:"pending",priority:c.priority,title:c.title,description:c.description,scheduledDate:e,notes:c.notes,createdBy:"admin",adminKey:"NDAAA5@sons&Daughters"})});if(s.ok)l(),t(),d({customerId:"",type:"email",priority:"medium",title:"",description:"",scheduledDate:"",scheduledTime:"",notes:""});else{let e=await s.json();alert("Error scheduling follow-up: "+e.message)}}catch(e){console.error("Error scheduling follow-up:",e),alert("Error scheduling follow-up. Please try again.")}finally{x(!1)}},b=n.find(e=>e.id===c.customerId);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Schedule Follow-up"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(g.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("form",{onSubmit:j,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ei.A,{className:"w-5 h-5 mr-2"}),"Customer"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Customer *"}),m?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(K.A,{className:"w-5 h-5 animate-spin text-gray-400"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading customers..."})]}):(0,a.jsxs)("select",{required:!0,value:c.customerId,onChange:e=>p("customerId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Choose a customer"}),n.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.personalInfo.name," (",e.personalInfo.email,")"]},e.id))]}),b&&(0,a.jsxs)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Selected:"})," ",b.personalInfo.name]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Email:"})," ",b.personalInfo.email]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(et.A,{className:"w-5 h-5 mr-2"}),"Follow-up Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Follow-up Type *"}),(0,a.jsxs)("select",{required:!0,value:c.type,onChange:e=>p("type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"email",children:"Email"}),(0,a.jsx)("option",{value:"phone",children:"Phone Call"}),(0,a.jsx)("option",{value:"meeting",children:"Meeting"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,a.jsxs)("select",{value:c.priority,onChange:e=>p("priority",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"low",children:"Low"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"urgent",children:"Urgent"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scheduled Date *"}),(0,a.jsx)("input",{type:"date",required:!0,value:c.scheduledDate,onChange:e=>p("scheduledDate",e.target.value),min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scheduled Time *"}),(0,a.jsx)("input",{type:"time",required:!0,value:c.scheduledTime,onChange:e=>p("scheduledTime",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),(0,a.jsx)("input",{type:"text",required:!0,value:c.title,onChange:e=>p("title",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Follow up on Toyota Voxy inquiry"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),(0,a.jsx)("textarea",{required:!0,value:c.description,onChange:e=>p("description",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe the purpose of this follow-up..."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Additional Notes"}),(0,a.jsx)("textarea",{value:c.notes,onChange:e=>p("notes",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Any additional notes or reminders for this follow-up..."})]}),c.type&&(0,a.jsx)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:["email"===c.type&&(0,a.jsx)(es.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),"phone"===c.type&&(0,a.jsx)(en.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),"meeting"===c.type&&(0,a.jsx)(et.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsxs)("p",{className:"font-medium mb-1",children:["email"===c.type&&"Email Follow-up","phone"===c.type&&"Phone Call Follow-up","meeting"===c.type&&"Meeting Follow-up"]}),(0,a.jsxs)("p",{children:["email"===c.type&&"An email reminder will be sent to you at the scheduled time.","phone"===c.type&&"A phone call reminder will be added to your tasks.","meeting"===c.type&&"A meeting reminder will be scheduled in your calendar."]})]})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[(0,a.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:o||!c.customerId,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Scheduling..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec.A,{className:"w-4 h-4 mr-2"}),"Schedule Follow-up"]})})]})]})]})})}function em(){let[e,s]=(0,r.useState)("overview"),[t,l]=(0,r.useState)([]),[n,i]=(0,r.useState)([]),[c,m]=(0,r.useState)(!0),[u,h]=(0,r.useState)(""),[g,p]=(0,r.useState)("all"),[b,f]=(0,r.useState)({customers:{total:0,active:0,new:0,vip:0},leads:{total:0,new:0,qualified:0,converted:0},revenue:{total:0,thisMonth:0,averageOrderValue:0},interactions:{total:0,thisWeek:0,responseRate:0}}),[y,N]=(0,r.useState)(!1),[v,A]=(0,r.useState)(!1),[k,I]=(0,r.useState)(!1);(0,r.useEffect)(()=>{_()},[]);let _=async()=>{m(!0);try{let e=localStorage.getItem("admin_token"),s=await fetch("/api/customers",{headers:{Authorization:"Bearer ".concat(e)}});if(s.ok){let e=await s.json();l(e.customers||[])}let t=await fetch("/api/leads",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();i(e.leads||[])}let a=await fetch("/api/admin/crm/stats",{headers:{Authorization:"Bearer ".concat(e)}});if(a.ok){let e=await a.json();f(e)}}catch(e){console.error("Error fetching CRM data:",e)}finally{m(!1)}},L=e=>{switch(e.toLowerCase()){case"active":case"converted":return"text-green-600 bg-green-100";case"new":return"text-blue-600 bg-blue-100";case"qualified":return"text-purple-600 bg-purple-100";case"lost":return"text-red-600 bg-red-100";case"nurturing":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},E=e=>{switch(e.toLowerCase()){case"urgent":return"text-red-600 bg-red-100";case"high":return"text-orange-600 bg-orange-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},P=e=>new Intl.NumberFormat("ja-JP",{style:"currency",currency:"JPY",minimumFractionDigits:0}).format(e),T=t.filter(e=>{let s=!u||e.personalInfo.name.toLowerCase().includes(u.toLowerCase())||e.personalInfo.email.toLowerCase().includes(u.toLowerCase()),t="all"===g||e.status===g;return s&&t}),F=n.filter(e=>{let s=!u||e.customerInfo.name.toLowerCase().includes(u.toLowerCase())||e.customerInfo.email&&e.customerInfo.email.toLowerCase().includes(u.toLowerCase()),t="all"===g||e.status===g;return s&&t});return c?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"CRM Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage customers, leads, and relationships"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{onClick:_,className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-2"}),"Refresh"]}),(0,a.jsxs)("button",{onClick:()=>N(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(er.A,{className:"w-4 h-4 mr-2"}),"Add Customer"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[{id:"overview",label:"Overview",icon:x.A},{id:"customers",label:"Customers",icon:d.A},{id:"leads",label:"Leads",icon:el.A},{id:"interactions",label:"Interactions",icon:o.A}].map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(e===t.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(r,{className:"w-4 h-4 mr-2"}),t.label]},t.id)})})]}),"overview"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:b.customers.total}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["+",b.customers.new," new this month"]})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(d.A,{className:"w-6 h-6 text-blue-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Leads"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:b.leads.total}),(0,a.jsxs)("p",{className:"text-sm text-purple-600",children:[b.leads.qualified," qualified"]})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(el.A,{className:"w-6 h-6 text-purple-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:P(b.revenue.total)}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["Avg: ",P(b.revenue.averageOrderValue)]})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(S.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Interactions"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:b.interactions.total}),(0,a.jsxs)("p",{className:"text-sm text-blue-600",children:[b.interactions.thisWeek," this week"]})]}),(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-orange-600"})})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>N(!0),className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(er.A,{className:"w-5 h-5 text-blue-600 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Add New Customer"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Create a new customer profile"})]})]}),(0,a.jsxs)("button",{onClick:()=>A(!0),className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(el.A,{className:"w-5 h-5 text-purple-600 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Create Lead"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Add a new sales lead"})]})]}),(0,a.jsxs)("button",{onClick:()=>I(!0),className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(et.A,{className:"w-5 h-5 text-green-600 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Schedule Follow-up"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Set reminder for customer contact"})]})]})]})]})]}),"customers"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search customers...",value:u,onChange:e=>h(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:g,onChange:e=>p(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"new",children:"New"}),(0,a.jsx)("option",{value:"vip",children:"VIP"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===T.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(d.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No customers found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Orders"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Spent"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tier"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:T.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.personalInfo.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.personalInfo.email}),e.address.city&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,a.jsx)(ee.A,{className:"w-3 h-3 mr-1"}),e.address.city,", ",e.address.country]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(L(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.totalOrders}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:P(e.totalSpent)}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(z.A,{className:"w-4 h-4 text-yellow-400 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:e.membershipTier})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(H.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-purple-600 transition-colors",children:(0,a.jsx)(es.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]}),"leads"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search leads...",value:u,onChange:e=>h(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:g,onChange:e=>p(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"new",children:"New"}),(0,a.jsx)("option",{value:"contacted",children:"Contacted"}),(0,a.jsx)("option",{value:"qualified",children:"Qualified"}),(0,a.jsx)("option",{value:"converted",children:"Converted"}),(0,a.jsx)("option",{value:"lost",children:"Lost"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===F.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(el.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No leads found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:F.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:e.customerInfo.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(L(e.status)),children:e.status}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(E(e.priority)),children:e.priority})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mb-3",children:[e.customerInfo.email&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(es.A,{className:"w-4 h-4 mr-1"}),e.customerInfo.email]}),e.customerInfo.phone&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(en.A,{className:"w-4 h-4 mr-1"}),e.customerInfo.phone]}),e.customerInfo.location&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(ee.A,{className:"w-4 h-4 mr-1"}),e.customerInfo.location]})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-3",children:e.inquiry.message}),e.inquiry.productInterest&&(0,a.jsxs)("div",{className:"text-sm text-blue-600 mb-2",children:["Interest: ",e.inquiry.productInterest]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,a.jsx)(C.A,{className:"w-4 h-4 mr-1"}),"Created ",new Date(e.createdAt).toLocaleDateString(),(0,a.jsx)("span",{className:"mx-2",children:"•"}),"Source: ",e.source]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(H.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-purple-600 transition-colors",children:(0,a.jsx)(es.A,{className:"w-4 h-4"})})]})]})},e.id))})})]}),"interactions"===e&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Interactions Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Interaction tracking and communication history coming soon"})]})}),(0,a.jsx)(ed,{isOpen:y,onClose:()=>N(!1),onCustomerAdded:_}),(0,a.jsx)(eo,{isOpen:v,onClose:()=>A(!1),onLeadCreated:_}),(0,a.jsx)(ex,{isOpen:k,onClose:()=>I(!1),onFollowupScheduled:_})]})}function eu(){let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!0),[n,i]=(0,r.useState)(""),[d,o]=(0,r.useState)("all"),[x,m]=(0,r.useState)("all"),[u,h]=(0,r.useState)([]),[g,p]=(0,r.useState)({total:0,pending:0,processing:0,completed:0,cancelled:0,totalRevenue:0,averageOrderValue:0,recentOrders:0});(0,r.useEffect)(()=>{b()},[]);let b=async()=>{l(!0);try{let e=localStorage.getItem("admin_token"),t=await fetch("/api/orders",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();s(e.orders||[]),y(e.orders||[])}}catch(e){console.error("Error fetching orders:",e)}finally{l(!1)}},y=e=>{let s=e.length,t=e.filter(e=>"pending_payment"===e.status||"pending"===e.status).length,a=e.filter(e=>"processing"===e.status||"confirmed"===e.status).length,r=e.filter(e=>"completed"===e.status||"delivered"===e.status).length,l=e.filter(e=>"cancelled"===e.status).length,n=e.filter(e=>"completed"===e.payment.status).reduce((e,s)=>e+s.totalAmount,0),i=new Date;i.setDate(i.getDate()-7),p({total:s,pending:t,processing:a,completed:r,cancelled:l,totalRevenue:n,averageOrderValue:s>0?n/s:0,recentOrders:e.filter(e=>new Date(e.createdAt)>i).length})},N=async e=>{try{let s=localStorage.getItem("admin_token"),t=await fetch("/api/admin/orders/".concat(e,"/invoice"),{method:"POST",headers:{Authorization:"Bearer ".concat(s)}});if(t.ok){let s=await t.blob(),a=window.URL.createObjectURL(s),r=document.createElement("a");r.href=a,r.download="invoice-".concat(e,".pdf"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}}catch(e){console.error("Error generating invoice:",e)}},v=e=>{switch(e.toLowerCase()){case"completed":case"delivered":case"paid":return"text-green-600 bg-green-100";case"processing":case"confirmed":case"shipped":return"text-blue-600 bg-blue-100";case"pending":case"pending_payment":return"text-yellow-600 bg-yellow-100";case"cancelled":case"failed":case"refunded":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},A=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"JPY";return new Intl.NumberFormat("ja-JP",{style:"currency",currency:s,minimumFractionDigits:0}).format(e)},_=e.filter(e=>{let s=!n||e.orderNumber.toLowerCase().includes(n.toLowerCase())||e.customerInfo.name.toLowerCase().includes(n.toLowerCase())||e.customerInfo.email.toLowerCase().includes(n.toLowerCase())||e.vehicle.title.toLowerCase().includes(n.toLowerCase()),t="all"===d||e.status===d,a="all"===x||e.payment.status===x;return s&&t&&a});return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Order Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track and manage customer orders"})]}),(0,a.jsxs)("button",{onClick:b,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(c.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.total}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Orders"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(C.A,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.pending}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Pending"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(U.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.processing}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Processing"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(k.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.completed}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Completed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(f.A,{className:"w-6 h-6 text-red-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.cancelled}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Cancelled"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(S.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A(g.totalRevenue)}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Revenue"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(I.A,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.recentOrders}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"This Week"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search orders by number, customer, or vehicle...",value:n,onChange:e=>i(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:d,onChange:e=>o(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"pending_payment",children:"Pending Payment"}),(0,a.jsx)("option",{value:"processing",children:"Processing"}),(0,a.jsx)("option",{value:"confirmed",children:"Confirmed"}),(0,a.jsx)("option",{value:"shipped",children:"Shipped"}),(0,a.jsx)("option",{value:"delivered",children:"Delivered"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,a.jsxs)("select",{value:x,onChange:e=>m(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Payments"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"processing",children:"Processing"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"refunded",children:"Refunded"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-4 text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["Showing ",_.length," of ",e.length," orders"]}),n&&(0,a.jsx)("button",{onClick:()=>i(""),className:"text-blue-600 hover:text-blue-700",children:"Clear search"})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===_.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(c.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No orders found"}),(0,a.jsx)("p",{className:"text-gray-600",children:n?"Try adjusting your search or filters":"No orders have been placed yet"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Vehicle"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:_.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["#",e.orderNumber]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.id.slice(0,8),"..."]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.customerInfo.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.customerInfo.email}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,a.jsx)(ee.A,{className:"w-3 h-3 mr-1"}),e.customerInfo.address.city,", ",e.customerInfo.address.country]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.vehicle.images[0]&&(0,a.jsx)("img",{src:e.vehicle.images[0],alt:e.vehicle.title,className:"w-12 h-12 object-cover rounded-lg mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.vehicle.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:A(e.vehicle.price,e.vehicle.currency)})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:A(e.totalAmount,e.currency)}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Shipping: ",A(e.shipping.cost,e.currency)]})]}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(v(e.payment.status)),children:e.payment.status}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:e.payment.method.name})]}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(v(e.status)),children:e.status}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:e.shipping.status})]}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>N(e.id),className:"text-gray-400 hover:text-blue-600 transition-colors",title:"Download Invoice",children:(0,a.jsx)(W.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",title:"View Details",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-purple-600 transition-colors",title:"Edit Order",children:(0,a.jsx)(H.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-orange-600 transition-colors",title:"Contact Customer",children:(0,a.jsx)(es.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]})}var eh=t(4870),eg=t(8515);function ep(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0),[n,o]=(0,r.useState)("30d"),[u,h]=(0,r.useState)("overview");(0,r.useEffect)(()=>{g()},[n]);let g=async()=>{l(!0);try{let e=localStorage.getItem("admin_token"),t=await fetch("/api/admin/analytics?range=".concat(n),{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();s(e)}}catch(e){console.error("Error fetching analytics:",e)}finally{l(!1)}},p=async e=>{try{let s=localStorage.getItem("admin_token"),t=await fetch("/api/admin/analytics/export?type=".concat(e,"&range=").concat(n),{headers:{Authorization:"Bearer ".concat(s)}});if(t.ok){let s=await t.blob(),a=window.URL.createObjectURL(s),r=document.createElement("a");r.href=a,r.download="analytics-".concat(e,"-").concat(n,".csv"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}}catch(e){console.error("Error exporting report:",e)}},j=e=>new Intl.NumberFormat("ja-JP",{style:"currency",currency:"JPY",minimumFractionDigits:0}).format(e),b=e=>"".concat(e>0?"+":"").concat(e.toFixed(1),"%"),f=e=>e>0?(0,a.jsx)(eh.A,{className:"w-4 h-4 text-green-500"}):e<0?(0,a.jsx)(eg.A,{className:"w-4 h-4 text-red-500"}):null,y=e=>e>0?"text-green-600":e<0?"text-red-600":"text-gray-600";return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"System Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Comprehensive business insights and reports"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("select",{value:n,onChange:e=>o(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"7d",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30d",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90d",children:"Last 90 days"}),(0,a.jsx)("option",{value:"1y",children:"Last year"})]}),(0,a.jsxs)("button",{onClick:g,className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-2"}),"Refresh"]}),(0,a.jsxs)("button",{onClick:()=>p("overview"),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(W.A,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[{id:"overview",label:"Overview",icon:x.A},{id:"sales",label:"Sales",icon:S.A},{id:"customers",label:"Customers",icon:d.A},{id:"products",label:"Products",icon:i.A},{id:"performance",label:"Performance",icon:el.A}].map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>h(e.id),className:"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(u===e.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(s,{className:"w-4 h-4 mr-2"}),e.label]},e.id)})})]}),"overview"===u&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:j(e.overview.totalRevenue)}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[f(e.overview.revenueGrowth),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(y(e.overview.revenueGrowth)),children:b(e.overview.revenueGrowth)})]})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(S.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.overview.totalOrders}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[f(e.overview.orderGrowth),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(y(e.overview.orderGrowth)),children:b(e.overview.orderGrowth)})]})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(c.A,{className:"w-6 h-6 text-blue-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.overview.totalCustomers}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[f(e.overview.customerGrowth),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(y(e.overview.customerGrowth)),children:b(e.overview.customerGrowth)})]})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(d.A,{className:"w-6 h-6 text-purple-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Conversion Rate"}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:[e.overview.conversionRate.toFixed(1),"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Visitors to customers"})]}),(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(el.A,{className:"w-6 h-6 text-orange-600"})})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Sales Trends"}),(0,a.jsx)("div",{className:"space-y-4",children:e.salesTrends.slice(-6).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e.month}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:j(e.revenue)}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.orders," orders"]})]})]},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Top Products"}),(0,a.jsx)("div",{className:"space-y-4",children:e.topProducts.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-500 w-6",children:["#",s+1]}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.sales," sales"]})]})]}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:j(e.revenue)})]},e.id))})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Geographic Distribution"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:e.geographicData.slice(0,6).map((e,s)=>(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.customers}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.country}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.orders," orders • ",j(e.revenue)]})]},s))})]})]}),"performance"===u&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Average Order Value"}),(0,a.jsx)(S.A,{className:"w-5 h-5 text-green-600"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:j(e.performanceMetrics.averageOrderValue)}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Per transaction"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Customer Lifetime Value"}),(0,a.jsx)(d.A,{className:"w-5 h-5 text-purple-600"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:j(e.performanceMetrics.customerLifetimeValue)}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Average per customer"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Repeat Customer Rate"}),(0,a.jsx)(m.A,{className:"w-5 h-5 text-blue-600"})]}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:[e.performanceMetrics.repeatCustomerRate.toFixed(1),"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Return customers"})]})]})}),"overview"!==u&&"performance"!==u&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(x.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:[u.charAt(0).toUpperCase()+u.slice(1)," Analytics"]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Detailed ",u," analytics coming soon"]})]})})]}):(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(x.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Analytics Unavailable"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Unable to load analytics data."}),(0,a.jsx)("button",{onClick:g,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Retry"})]})})}var ej=t(9803),eb=t(2919),ef=t(4861);function ey(){let[e,s]=(0,r.useState)("overview"),[t,l]=(0,r.useState)([]),[n,i]=(0,r.useState)([]),[c,o]=(0,r.useState)(null),[x,g]=(0,r.useState)(!0),[p,b]=(0,r.useState)(""),[y,N]=(0,r.useState)("all");(0,r.useEffect)(()=>{v()},[]);let v=async()=>{g(!0);try{let e=localStorage.getItem("admin_token"),s=await fetch("/api/admin/security/logs",{headers:{Authorization:"Bearer ".concat(e)}});if(s.ok){let e=await s.json();l(e.logs||[])}let t=await fetch("/api/admin/security/users",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();i(e.users||[])}let a=await fetch("/api/admin/security/settings",{headers:{Authorization:"Bearer ".concat(e)}});if(a.ok){let e=await a.json();o(e.settings)}}catch(e){console.error("Error fetching security data:",e)}finally{g(!1)}},w=e=>{switch(e){case"success":return"text-green-600 bg-green-100";case"failed":return"text-red-600 bg-red-100";case"warning":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},A=e=>{switch(e){case"super_admin":return"text-purple-600 bg-purple-100";case"admin":return"text-blue-600 bg-blue-100";case"moderator":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},C=e=>{switch(e.toLowerCase()){case"login":return(0,a.jsx)(ej.A,{className:"w-4 h-4"});case"logout":return(0,a.jsx)(eb.A,{className:"w-4 h-4"});case"failed_login":return(0,a.jsx)(ef.A,{className:"w-4 h-4"});case"password_change":return(0,a.jsx)(u.A,{className:"w-4 h-4"});case"user_created":return(0,a.jsx)(er.A,{className:"w-4 h-4"});case"user_deleted":return(0,a.jsx)(J.A,{className:"w-4 h-4"});case"settings_changed":return(0,a.jsx)(h.A,{className:"w-4 h-4"});default:return(0,a.jsx)(m.A,{className:"w-4 h-4"})}},S=t.filter(e=>{let s=!p||e.user.toLowerCase().includes(p.toLowerCase())||e.action.toLowerCase().includes(p.toLowerCase())||e.ipAddress.includes(p),t="all"===y||e.status===y;return s&&t}),I={totalLogs:t.length,failedLogins:t.filter(e=>"failed_login"===e.action).length,activeUsers:n.filter(e=>"active"===e.status).length,suspendedUsers:n.filter(e=>"suspended"===e.status).length,recentAlerts:t.filter(e=>{let s=new Date(e.timestamp),t=new Date;return t.setDate(t.getDate()-1),s>t&&"failed"===e.status}).length};return x?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Security & Access Control"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage system security, users, and access permissions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{onClick:v,className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-2"}),"Refresh"]}),(0,a.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(W.A,{className:"w-4 h-4 mr-2"}),"Export Logs"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[{id:"overview",label:"Overview",icon:u.A},{id:"users",label:"Admin Users",icon:d.A},{id:"logs",label:"Security Logs",icon:m.A},{id:"settings",label:"Settings",icon:h.A}].map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(e===t.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(r,{className:"w-4 h-4 mr-2"}),t.label]},t.id)})})]}),"overview"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Logs"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:I.totalLogs})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(m.A,{className:"w-6 h-6 text-blue-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Failed Logins"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:I.failedLogins})]}),(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-full",children:(0,a.jsx)(ef.A,{className:"w-6 h-6 text-red-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Users"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:I.activeUsers})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(k.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Suspended"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:I.suspendedUsers})]}),(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-full",children:(0,a.jsx)(f.A,{className:"w-6 h-6 text-yellow-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Recent Alerts"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:I.recentAlerts})]}),(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(f.A,{className:"w-6 h-6 text-orange-600"})})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Security Events"}),(0,a.jsx)("div",{className:"space-y-4",children:t.slice(0,10).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"p-2 rounded-full ".concat(w(e.status)),children:C(e.action)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.action.replace("_"," ")}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.user," • ",e.ipAddress]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900",children:new Date(e.timestamp).toLocaleString()}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(w(e.status)),children:e.status})]})]},e.id))})]})]}),"users"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Admin Users"}),(0,a.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(er.A,{className:"w-4 h-4 mr-2"}),"Add User"]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Login"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:n.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.username}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(A(e.role)),children:e.role.replace("_"," ")})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("active"===e.status?"text-green-600 bg-green-100":"suspended"===e.status?"text-red-600 bg-red-100":"text-gray-600 bg-gray-100"),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.lastLogin?new Date(e.lastLogin).toLocaleDateString():"Never"}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(H.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(J.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})]})}),"logs"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search logs by user, action, or IP address...",value:p,onChange:e=>b(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:y,onChange:e=>N(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"success",children:"Success"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"warning",children:"Warning"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"IP Address"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Details"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:S.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:new Date(e.timestamp).toLocaleString()}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[C(e.action),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-900",children:e.action.replace("_"," ")})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.user}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.ipAddress}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(w(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.details})]},e.id))})]})})})]}),"settings"===e&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Security Settings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Advanced security configuration coming soon"})]})})]})}var eN=t(3127);function ev(){let[e,s]=(0,r.useState)("general"),[t,l]=(0,r.useState)({general:{siteName:"EBAM Motors",siteDescription:"Premium Japanese Cars for Ghana and Africa",contactEmail:"<EMAIL>",contactPhone:"+233245375692",businessAddress:"Kumasi, Ghana",currency:"JPY",timezone:"Africa/Accra"},email:{provider:"resend",smtpHost:"smtp.resend.com",smtpPort:587,smtpUser:"resend",smtpPassword:"",fromEmail:"<EMAIL>",fromName:"EBAM Motors"},notifications:{emailNotifications:!0,orderNotifications:!0,reviewNotifications:!0,systemAlerts:!0,dailySummary:!1},security:{sessionTimeout:60,maxLoginAttempts:5,passwordMinLength:8,requireTwoFactor:!1,allowedIPs:[]},appearance:{theme:"light",primaryColor:"#2563eb",logoUrl:"/logo.png",faviconUrl:"/favicon.ico"},automation:{followupEnabled:!0,followupDelayDays:3,emailFollowups:!0,smsFollowups:!0,leadFollowups:!0,customerFollowups:!0}}),[n,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)("idle"),o=[{id:"general",label:"General",icon:h.A},{id:"email",label:"Email",icon:es.A},{id:"notifications",label:"Notifications",icon:b.A},{id:"security",label:"Security",icon:u.A},{id:"appearance",label:"Appearance",icon:eN.A},{id:"automation",label:"Automation",icon:D.A}],x=async()=>{d("saving");try{let e=localStorage.getItem("admin_token");(await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)})).ok?d("success"):d("error"),setTimeout(()=>d("idle"),3e3)}catch(e){console.error("Error saving settings:",e),d("error"),setTimeout(()=>d("idle"),3e3)}},m=(e,s,t)=>{l(a=>({...a,[e]:{...a[e],[s]:t}}))};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"System Settings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Configure your admin dashboard and system preferences"})]}),(0,a.jsx)("button",{onClick:x,disabled:"saving"===c,className:"flex items-center px-4 py-2 rounded-lg transition-colors ".concat("success"===c?"bg-green-600 text-white":"error"===c?"bg-red-600 text-white":"bg-blue-600 text-white hover:bg-blue-700"," disabled:opacity-50"),children:"saving"===c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Saving..."]}):"success"===c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Saved"]}):"error"===c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Error"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec.A,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"flex space-x-8 px-6",children:o.map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:"flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(e===t.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,a.jsx)(r,{className:"w-4 h-4 mr-2"}),t.label]},t.id)})})}),(0,a.jsxs)("div",{className:"p-6",children:["general"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Site Name"}),(0,a.jsx)("input",{type:"text",value:t.general.siteName,onChange:e=>m("general","siteName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Email"}),(0,a.jsx)("input",{type:"email",value:t.general.contactEmail,onChange:e=>m("general","contactEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Phone"}),(0,a.jsx)("input",{type:"text",value:t.general.contactPhone,onChange:e=>m("general","contactPhone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Currency"}),(0,a.jsxs)("select",{value:t.general.currency,onChange:e=>m("general","currency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"JPY",children:"Japanese Yen (\xa5)"}),(0,a.jsx)("option",{value:"USD",children:"US Dollar ($)"}),(0,a.jsx)("option",{value:"GHS",children:"Ghana Cedi (₵)"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Site Description"}),(0,a.jsx)("textarea",{value:t.general.siteDescription,onChange:e=>m("general","siteDescription",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Business Address"}),(0,a.jsx)("textarea",{value:t.general.businessAddress,onChange:e=>m("general","businessAddress",e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),"email"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(X.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Email Configuration"}),(0,a.jsx)("p",{children:"Configure your email settings for sending notifications, order confirmations, and system alerts."})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Provider"}),(0,a.jsxs)("select",{value:t.email.provider,onChange:e=>m("email","provider",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"resend",children:"Resend"}),(0,a.jsx)("option",{value:"smtp",children:"Custom SMTP"}),(0,a.jsx)("option",{value:"gmail",children:"Gmail"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"From Email"}),(0,a.jsx)("input",{type:"email",value:t.email.fromEmail,onChange:e=>m("email","fromEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"From Name"}),(0,a.jsx)("input",{type:"text",value:t.email.fromName,onChange:e=>m("email","fromName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"SMTP Host"}),(0,a.jsx)("input",{type:"text",value:t.email.smtpHost,onChange:e=>m("email","smtpHost",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),"notifications"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"space-y-4",children:Object.entries(t.notifications).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["emailNotifications"===s&&"Enable email notifications for all events","orderNotifications"===s&&"Get notified when new orders are placed","reviewNotifications"===s&&"Get notified when new reviews are submitted","systemAlerts"===s&&"Receive system health and security alerts","dailySummary"===s&&"Receive daily summary reports"]})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t,onChange:e=>m("notifications",s,e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]},s)})})}),"security"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(f.A,{className:"w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Security Settings"}),(0,a.jsx)("p",{children:"These settings affect the security of your admin panel. Change with caution."})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Session Timeout (minutes)"}),(0,a.jsx)("input",{type:"number",value:t.security.sessionTimeout,onChange:e=>m("security","sessionTimeout",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Login Attempts"}),(0,a.jsx)("input",{type:"number",value:t.security.maxLoginAttempts,onChange:e=>m("security","maxLoginAttempts",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minimum Password Length"}),(0,a.jsx)("input",{type:"number",value:t.security.passwordMinLength,onChange:e=>m("security","passwordMinLength",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("label",{className:"flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.security.requireTwoFactor,onChange:e=>m("security","requireTwoFactor",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-700",children:"Require Two-Factor Authentication"})]})})]})]}),"appearance"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Theme"}),(0,a.jsxs)("select",{value:t.appearance.theme,onChange:e=>m("appearance","theme",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"light",children:"Light"}),(0,a.jsx)("option",{value:"dark",children:"Dark"}),(0,a.jsx)("option",{value:"auto",children:"Auto"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Primary Color"}),(0,a.jsx)("input",{type:"color",value:t.appearance.primaryColor,onChange:e=>m("appearance","primaryColor",e.target.value),className:"w-full h-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logo URL"}),(0,a.jsx)("input",{type:"url",value:t.appearance.logoUrl,onChange:e=>m("appearance","logoUrl",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Favicon URL"}),(0,a.jsx)("input",{type:"url",value:t.appearance.faviconUrl,onChange:e=>m("appearance","faviconUrl",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})}),"automation"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(D.A,{className:"w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-green-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Automated Follow-up System"}),(0,a.jsx)("p",{children:"Automatically send follow-up emails and SMS to customers and leads after a specified delay. This helps improve customer engagement and conversion rates."})]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Enable Automated Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Master switch for all automated follow-up features"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.followupEnabled,onChange:e=>m("automation","followupEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Follow-up Delay (Days)"}),(0,a.jsx)("input",{type:"number",min:"1",max:"30",value:t.automation.followupDelayDays,onChange:e=>m("automation","followupDelayDays",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:!t.automation.followupEnabled}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Number of days to wait before sending follow-up"})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:"Follow-up Methods"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:"Email Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Send automated follow-up emails"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.emailFollowups,onChange:e=>m("automation","emailFollowups",e.target.checked),disabled:!t.automation.followupEnabled,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:"SMS Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Send automated SMS messages"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.smsFollowups,onChange:e=>m("automation","smsFollowups",e.target.checked),disabled:!t.automation.followupEnabled,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:"Target Audiences"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:"Customer Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Follow up with new customers"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.customerFollowups,onChange:e=>m("automation","customerFollowups",e.target.checked),disabled:!t.automation.followupEnabled,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:"Lead Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Follow up with new leads"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.leadFollowups,onChange:e=>m("automation","leadFollowups",e.target.checked),disabled:!t.automation.followupEnabled,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4 pt-6 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:"System Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("button",{type:"button",onClick:async()=>{try{(await fetch("/api/admin/followups/process",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin_token"))}})).ok?alert("Follow-up processing triggered successfully!"):alert("Failed to trigger follow-up processing")}catch(e){alert("Error triggering follow-up processing")}},className:"flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-2"}),"Process Pending Follow-ups"]}),(0,a.jsxs)("button",{type:"button",onClick:async()=>{try{(await fetch("/api/admin/system/init",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin_token"))}})).ok?alert("System services initialized successfully!"):alert("Failed to initialize system services")}catch(e){alert("Error initializing system services")}},className:"flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)(E.A,{className:"w-4 h-4 mr-2"}),"Initialize System"]})]})]})]})]})]})]})]})}let ew=[{id:"overview",label:"Overview",icon:n.A,path:"/admin/dashboard"},{id:"cars",label:"Car Management",icon:i.A,path:"/admin/cars"},{id:"orders",label:"Orders",icon:c.A,path:"/admin/orders"},{id:"customers",label:"CRM",icon:d.A,path:"/admin/customers"},{id:"reviews",label:"Reviews",icon:o.A,path:"/admin/reviews"},{id:"analytics",label:"Analytics",icon:x.A,path:"/admin/analytics"},{id:"health",label:"System Health",icon:m.A,path:"/admin/health"},{id:"security",label:"Security",icon:u.A,path:"/admin/security"},{id:"settings",label:"Settings",icon:h.A,path:"/admin/settings"}];function eA(){var e,s;let[t,n]=(0,r.useState)(!1),[i,d]=(0,r.useState)(!1),[x,m]=(0,r.useState)(!1),[u,h]=(0,r.useState)("overview"),[N,v]=(0,r.useState)(3),[w,k]=(0,r.useState)(!1),C=(0,l.useRouter)(),S=(0,l.useSearchParams)(),I=(0,r.useRef)(null);(0,r.useEffect)(()=>{n(!0),localStorage.getItem("admin_token")&&d(!0);let e=S.get("section");e&&ew.find(s=>s.id===e)&&h(e)},[S]),(0,r.useEffect)(()=>{function e(e){I.current&&!I.current.contains(e.target)&&k(!1)}if(w)return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[w]);let _=(e,s)=>{h(e),m(!1);let t="/admin/dashboard?section=".concat(e);C.push(t)},D=(e,s)=>{switch(k(!1),e){case"order":_("orders","/admin/orders");break;case"review":_("reviews","/admin/reviews");break;case"system":_("health","/admin/health");break;case"car":_("cars","/admin/cars");break;default:_("overview","/admin/dashboard")}N>0&&v(e=>e-1)};return t?i?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[(0,a.jsxs)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ".concat(x?"translate-x-0":"-translate-x-full"," transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"EBAM Admin"}),(0,a.jsx)("button",{onClick:()=>m(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,a.jsx)(g.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("nav",{className:"mt-6 px-3",children:ew.map(e=>{let s=e.icon,t=u===e.id;return(0,a.jsxs)("button",{onClick:()=>_(e.id,e.path),className:"w-full flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ".concat(t?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,a.jsx)(s,{className:"w-5 h-5 mr-3"}),e.label,e.badge&&(0,a.jsx)("span",{className:"ml-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full",children:e.badge})]},e.id)})})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col lg:ml-0",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{onClick:()=>m(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,a.jsx)(p.A,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:"ml-4 lg:ml-0",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:(null==(e=ew.find(e=>e.id===u))?void 0:e.label)||"Dashboard"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"hidden md:block relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"relative",ref:I,children:[(0,a.jsxs)("button",{onClick:()=>k(!w),className:"relative p-2 text-gray-400 hover:text-gray-600",children:[(0,a.jsx)(b.A,{className:"w-5 h-5"}),N>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:N})]}),w&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Notifications"})}),(0,a.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[(0,a.jsx)("button",{onClick:()=>D("order","ORD-001"),className:"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"w-4 h-4 text-blue-600"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"New order received"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Order #ORD-001 from John Doe"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"2 minutes ago"})]})]})}),(0,a.jsx)("button",{onClick:()=>D("review","REV-001"),className:"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"w-4 h-4 text-green-600"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"New review submitted"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"5-star review for Toyota Voxy"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"15 minutes ago"})]})]})}),(0,a.jsx)("button",{onClick:()=>D("system","SYS-001"),className:"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(f.A,{className:"w-4 h-4 text-yellow-600"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"System alert"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Low inventory warning for Honda Freed"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"1 hour ago"})]})]})})]}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsx)("button",{onClick:()=>k(!1),className:"w-full text-center text-sm text-blue-600 hover:text-blue-800",children:"View all notifications"})})]})]}),(0,a.jsxs)("button",{onClick:()=>{localStorage.removeItem("admin_token"),d(!1)},className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})}),(0,a.jsxs)("main",{className:"flex-1 p-6 overflow-auto",children:["overview"===u&&(0,a.jsx)(L,{}),"health"===u&&(0,a.jsx)(O,{}),"cars"===u&&(0,a.jsx)($,{}),"reviews"===u&&(0,a.jsx)(ea,{}),"customers"===u&&(0,a.jsx)(em,{}),"orders"===u&&(0,a.jsx)(eu,{}),"analytics"===u&&(0,a.jsx)(ep,{}),"security"===u&&(0,a.jsx)(ey,{}),"settings"===u&&(0,a.jsx)(ev,{}),"overview"!==u&&"health"!==u&&"cars"!==u&&"reviews"!==u&&"customers"!==u&&"orders"!==u&&"analytics"!==u&&"security"!==u&&"settings"!==u&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:null==(s=ew.find(e=>e.id===u))?void 0:s.label}),(0,a.jsx)("p",{className:"text-gray-600",children:"This section is under development. Please check back soon."})]})]})]}),x&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>m(!1)})]}):(0,a.jsx)(A,{onAuthenticated:()=>d(!0)}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading dashboard..."})]})})}function ek(){return(0,a.jsx)(eA,{})}function eC(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading dashboard..."})]})}),children:(0,a.jsx)(ek,{})})}},4939:(e,s,t)=>{Promise.resolve().then(t.bind(t,581))}},e=>{var s=s=>e(e.s=s);e.O(0,[9563,8441,1684,7358],()=>s(4939)),_N_E=e.O()}]);