"use strict";exports.id=2273,exports.ids=[2273,4654],exports.modules={62273:(e,t,o)=>{o.d(t,{OW:()=>m,cz:()=>p,scheduleAutoFollowupForCustomer:()=>l,scheduleAutoFollowupForLead:()=>n});var a=o(53190),r=o(16967);let s={delayDays:3,enableEmail:!0,enableSMS:!0,emailTemplate:"customer_followup",smsTemplate:"customer_followup_sms"};async function l(e,t){try{let o=new Date;o.setDate(o.getDate()+s.delayDays),s.enableEmail&&await (0,a.XL)({type:"email",status:"pending",priority:"medium",customerId:e,title:"3-Day Customer Follow-up (Email)",description:"Automated follow-up email to check customer satisfaction and offer assistance",scheduledDate:o.toISOString(),automationRule:{trigger:"customer_created",delay:24*s.delayDays,conditions:{type:"email",template:s.emailTemplate}},createdBy:"system"}),s.enableSMS&&t.personalInfo?.phone&&await (0,a.XL)({type:"sms",status:"pending",priority:"medium",customerId:e,title:"3-Day Customer Follow-up (SMS)",description:"Automated SMS follow-up to check customer satisfaction",scheduledDate:o.toISOString(),automationRule:{trigger:"customer_created",delay:24*s.delayDays,conditions:{type:"sms",template:s.smsTemplate,phone:t.personalInfo.phone}},createdBy:"system"})}catch(e){console.error("Error scheduling auto follow-up for customer:",e)}}async function n(e,t){try{let o=new Date;o.setDate(o.getDate()+s.delayDays),s.enableEmail&&t.customerInfo?.email&&await (0,a.XL)({type:"email",status:"pending",priority:"high",leadId:e,title:"3-Day Lead Follow-up (Email)",description:"Automated follow-up email for lead nurturing and conversion",scheduledDate:o.toISOString(),automationRule:{trigger:"lead_created",delay:24*s.delayDays,conditions:{type:"email",template:"lead_followup",leadSource:t.source,productInterest:t.inquiry?.productInterest}},createdBy:"system"}),s.enableSMS&&t.customerInfo?.phone&&await (0,a.XL)({type:"sms",status:"pending",priority:"high",leadId:e,title:"3-Day Lead Follow-up (SMS)",description:"Automated SMS follow-up for lead conversion",scheduledDate:o.toISOString(),automationRule:{trigger:"lead_created",delay:24*s.delayDays,conditions:{type:"sms",template:"lead_followup_sms",phone:t.customerInfo.phone,productInterest:t.inquiry?.productInterest}},createdBy:"system"})}catch(e){console.error("Error scheduling auto follow-up for lead:",e)}}async function i(){try{let{getAllFollowUps:e,updateFollowUp:t,getCustomerById:a,getLeadById:r}=await Promise.resolve().then(o.bind(o,53190)),s=await e(),l=new Date;for(let e of s.filter(e=>"pending"===e.status&&e.automationRule&&new Date(e.scheduledDate)<=l))try{await u(e),await t(e.id,{status:"completed",completedAt:new Date().toISOString(),notes:`${e.notes||""}

Automatically processed on ${new Date().toLocaleString()}`})}catch(o){console.error(`Error processing followup ${e.id}:`,o),await t(e.id,{status:"failed",notes:`${e.notes||""}

Failed to process: ${o instanceof Error?o.message:"Unknown error"}`})}}catch(e){console.error("Error processing automated follow-ups:",e)}}async function u(e){let{getCustomerById:t,getLeadById:r}=await Promise.resolve().then(o.bind(o,53190)),s=null,l="";if(e.customerId?(s=await t(e.customerId),l="customer"):e.leadId&&(s=await r(e.leadId),l="lead"),!s)throw Error(`Recipient not found for followup ${e.id}`);let n=e.automationRule?.conditions||{};"email"===e.type?await c(s,l,n,e):"sms"===e.type&&await d(s,l,n,e),await (0,a.createInteraction)({customerId:e.customerId,leadId:e.leadId,type:"email"===e.type?"email":"sms",direction:"outbound",channel:"automation",content:`Automated ${e.type} follow-up sent: ${e.title}`,subject:e.title,tags:["automated","follow_up",e.type],createdBy:"system"})}async function c(e,t,o,a){let s="customer"===t?e.personalInfo?.email:e.customerInfo?.email;if(!s)throw Error("No email address found for recipient");let l={to:s,customerName:"customer"===t?e.personalInfo?.name:e.customerInfo?.name,followupType:a.title,description:a.description};"lead_followup"===o.template&&(l={...l,productInterest:o.productInterest||"our vehicles",leadSource:o.leadSource||"website",inquiryDetails:e.inquiry?.message||""}),await r.gm.sendFollowUpEmail(l)}async function d(e,t,o,a){if(!o.phone)throw Error("No phone number found for recipient");"customer"===t?e.personalInfo?.name:e.customerInfo?.name;"lead_followup_sms"===o.template&&o.productInterest}function m(){setInterval(i,36e5),setTimeout(i,5e3)}async function p(){try{return await i(),{processed:1,errors:0}}catch(e){return console.error("Error in manual follow-up processing:",e),{processed:0,errors:1}}}}};