import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File path for storing push subscriptions
const SUBSCRIPTIONS_FILE = path.join(process.cwd(), 'data', 'push-subscriptions.json');

// Load existing subscriptions
async function loadSubscriptions() {
  try {
    const data = await fs.readFile(SUBSCRIPTIONS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

// Save subscriptions
async function saveSubscriptions(subscriptions: any[]) {
  const dataDir = path.dirname(SUBSCRIPTIONS_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
  await fs.writeFile(SUBSCRIPTIONS_FILE, JSON.stringify(subscriptions, null, 2));
}

// POST - Unsubscribe from push notifications
export async function POST(request: NextRequest) {
  try {
    const { endpoint } = await request.json();
    
    if (!endpoint) {
      return NextResponse.json(
        { success: false, message: 'Endpoint is required' },
        { status: 400 }
      );
    }

    // Load existing subscriptions
    const subscriptions = await loadSubscriptions();
    
    // Remove subscription with matching endpoint
    const filteredSubscriptions = subscriptions.filter(
      (sub: any) => sub.endpoint !== endpoint
    );
    
    // Save updated subscriptions
    await saveSubscriptions(filteredSubscriptions);

    return NextResponse.json({
      success: true,
      message: 'Unsubscribed successfully',
    });
    
  } catch (error) {
    return NextResponse.json(
      { success: false, message: 'Failed to unsubscribe' },
      { status: 500 }
    );
  }
}
