import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get security logs (mock data for now - in production this would come from a security logging system)
    const logs = await getSecurityLogs();

    return NextResponse.json({
      success: true,
      logs
    });
  } catch (error) {
    console.error('Error fetching security logs:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch security logs' },
      { status: 500 }
    );
  }
}

async function getSecurityLogs() {
  // In a real implementation, this would fetch from a security logging system
  // For now, we'll generate some mock data based on recent activity
  
  const mockLogs = [
    {
      id: '1',
      timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
      action: 'login',
      user: 'admin',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Tokyo, Japan',
      status: 'success' as const,
      details: 'Successful admin login'
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
      action: 'failed_login',
      user: 'unknown',
      ipAddress: '************',
      userAgent: 'curl/7.68.0',
      location: 'Unknown',
      status: 'failed' as const,
      details: 'Invalid credentials provided'
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      action: 'password_change',
      user: 'admin',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Tokyo, Japan',
      status: 'success' as const,
      details: 'Password successfully changed'
    },
    {
      id: '4',
      timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
      action: 'settings_changed',
      user: 'admin',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Tokyo, Japan',
      status: 'success' as const,
      details: 'Security settings updated'
    },
    {
      id: '5',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      action: 'failed_login',
      user: 'admin',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
      location: 'Unknown',
      status: 'failed' as const,
      details: 'Multiple failed login attempts detected'
    },
    {
      id: '6',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
      action: 'logout',
      user: 'admin',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Tokyo, Japan',
      status: 'success' as const,
      details: 'User logged out successfully'
    },
    {
      id: '7',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
      action: 'login',
      user: 'admin',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Tokyo, Japan',
      status: 'success' as const,
      details: 'Successful admin login'
    },
    {
      id: '8',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(), // 12 hours ago
      action: 'user_created',
      user: 'admin',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Tokyo, Japan',
      status: 'success' as const,
      details: 'New admin user created: moderator1'
    },
    {
      id: '9',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      action: 'failed_login',
      user: 'unknown',
      ipAddress: '************',
      userAgent: 'python-requests/2.25.1',
      location: 'Unknown',
      status: 'failed' as const,
      details: 'Automated attack attempt detected'
    },
    {
      id: '10',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 days ago
      action: 'settings_changed',
      user: 'admin',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Tokyo, Japan',
      status: 'success' as const,
      details: 'Password policy updated'
    }
  ];

  return mockLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action, user, ipAddress, userAgent, details, status = 'success' } = await request.json();

    if (!action || !user || !ipAddress) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // In a real implementation, this would save to a security logging system
    const logEntry = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      action,
      user,
      ipAddress,
      userAgent: userAgent || 'Unknown',
      location: 'Unknown', // Would be resolved from IP in real implementation
      status,
      details: details || `${action} performed by ${user}`
    };

    console.log('Security log entry:', logEntry);

    return NextResponse.json({
      success: true,
      message: 'Security log entry created',
      logEntry
    });

  } catch (error) {
    console.error('Error creating security log:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create security log' },
      { status: 500 }
    );
  }
}
