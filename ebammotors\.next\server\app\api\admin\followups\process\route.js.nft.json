{"version": 1, "files": ["../../../../../../../data/customers.json", "../../../../../../../data/followups.json", "../../../../../../../data/interactions.json", "../../../../../../../data/invoices.json", "../../../../../../../data/leads.json", "../../../../../../../data/orders.json", "../../../../../../../data/reviews.json", "../../../../../../../node_modules/next/dist/client/components/app-router-headers.js", "../../../../../../../node_modules/next/dist/compiled/@opentelemetry/api/index.js", "../../../../../../../node_modules/next/dist/compiled/@opentelemetry/api/package.json", "../../../../../../../node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js", "../../../../../../../node_modules/next/dist/compiled/next-server/app-route.runtime.prod.js", "../../../../../../../node_modules/next/dist/server/app-render/action-async-storage-instance.js", "../../../../../../../node_modules/next/dist/server/app-render/action-async-storage.external.js", "../../../../../../../node_modules/next/dist/server/app-render/after-task-async-storage-instance.js", "../../../../../../../node_modules/next/dist/server/app-render/after-task-async-storage.external.js", "../../../../../../../node_modules/next/dist/server/app-render/async-local-storage.js", "../../../../../../../node_modules/next/dist/server/app-render/clean-async-snapshot-instance.js", "../../../../../../../node_modules/next/dist/server/app-render/clean-async-snapshot.external.js", "../../../../../../../node_modules/next/dist/server/app-render/work-async-storage-instance.js", "../../../../../../../node_modules/next/dist/server/app-render/work-async-storage.external.js", "../../../../../../../node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js", "../../../../../../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js", "../../../../../../../node_modules/next/dist/server/lib/incremental-cache/tags-manifest.external.js", "../../../../../../../node_modules/next/dist/server/lib/trace/constants.js", "../../../../../../../node_modules/next/dist/server/lib/trace/tracer.js", "../../../../../../../node_modules/next/dist/shared/lib/is-thenable.js", "../../../../../../../node_modules/next/package.json", "../../../../../../../node_modules/prettier/package.json", "../../../../../../../node_modules/prettier/plugins/html.js", "../../../../../../../node_modules/prettier/plugins/html.mjs", "../../../../../../../node_modules/prettier/standalone.js", "../../../../../../../node_modules/prettier/standalone.mjs", "../../../../../../../package.json", "../../../../../../package.json", "../../../../../chunks/2938.js", "../../../../../chunks/3794.js", "../../../../../chunks/4447.js", "../../../../../chunks/580.js", "../../../../../chunks/6967.js", "../../../../../chunks/7696.js", "../../../../../webpack-runtime.js", "route_client-reference-manifest.js"]}