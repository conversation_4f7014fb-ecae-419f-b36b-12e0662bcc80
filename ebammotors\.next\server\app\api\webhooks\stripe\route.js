(()=>{var t={};t.id=4024,t.ids=[4024],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7263:(t,e,r)=>{"use strict";r.d(e,{LP:()=>s,U1:()=>c,de:()=>u,g5:()=>n.g5});var i=r(80137),n=r(30358);async function a(t){let e=function(t){let e=[{id:"1",description:t.vehicle.title,quantity:1,unitPrice:t.vehicle.price,total:t.vehicle.price}];return t.shipping.cost>0&&e.push({id:"2",description:`Shipping (${t.shipping.method.name})`,quantity:1,unitPrice:t.shipping.cost,total:t.shipping.cost}),e}(t),{subtotal:r,tax:i,total:a}=function(t){let e=t.reduce((t,e)=>t+e.total,0);return{subtotal:e,tax:0,total:e+0}}(e),s={orderId:t.id,issuedDate:new Date().toISOString().split("T")[0],dueDate:t.payment.dueDate||new Date(Date.now()+2592e6).toISOString().split("T")[0],status:"sent",items:e,subtotal:r,tax:i,shipping:t.shipping.cost,total:a,currency:t.currency,paymentTerms:"Payment due within 30 days of invoice date",notes:`Order: ${t.orderNumber}
Vehicle: ${t.vehicle.title}
Shipping to: ${t.shipping.address.city}, ${t.shipping.address.country}`};return await (0,n.iO)(s)}function s(t,e){let r=new i.Ay,n=r.internal.pageSize.width;r.setFontSize(24),r.setFont("helvetica","bold"),r.text("EBAM MOTORS",20,30),r.setFontSize(12),r.setFont("helvetica","normal"),r.text("Premium Japanese Vehicles Export",20,40),r.text("Japan Office: Tokyo, Japan",20,50),r.text("Ghana Office: Kumasi, Ghana",20,60),r.text("Phone: +233245375692",20,70),r.text("Email: <EMAIL>",20,80),r.setFontSize(20),r.setFont("helvetica","bold"),r.text("INVOICE",n-20-40,30),r.setFontSize(12),r.setFont("helvetica","normal"),r.text(`Invoice #: ${t.invoiceNumber}`,n-20-60,45),r.text(`Date: ${t.issuedDate}`,n-20-60,55),r.text(`Due Date: ${t.dueDate}`,n-20-60,65),r.text(`Order #: ${e.orderNumber}`,n-20-60,75),r.setFontSize(14),r.setFont("helvetica","bold"),r.text("Bill To:",20,110),r.setFontSize(12),r.setFont("helvetica","normal"),r.text(e.customerInfo.name,20,125),r.text(e.customerInfo.email,20,135),r.text(e.customerInfo.phone,20,145),r.text(e.customerInfo.address.street,20,155),r.text(`${e.customerInfo.address.city}, ${e.customerInfo.address.state}`,20,165),r.text(`${e.customerInfo.address.country} ${e.customerInfo.address.postalCode}`,20,175),r.setFontSize(14),r.setFont("helvetica","bold"),r.text("Ship To:",n-20-80,110),r.setFontSize(12),r.setFont("helvetica","normal"),r.text(e.shipping.address.street,n-20-80,125),r.text(`${e.shipping.address.city}, ${e.shipping.address.state}`,n-20-80,135),r.text(`${e.shipping.address.country} ${e.shipping.address.postalCode}`,n-20-80,145),r.setFontSize(12),r.setFont("helvetica","bold"),r.text("Description",20,200),r.text("Qty",n-120,200),r.text("Unit Price",n-80,200),r.text("Total",n-40,200),r.line(20,205,n-20,205),r.setFont("helvetica","normal");let a=215;if(t.items.forEach((t,e)=>{r.text(t.description,20,a),r.text(t.quantity.toString(),n-120,a),r.text(`\xa5${t.unitPrice.toLocaleString()}`,n-80,a),r.text(`\xa5${t.total.toLocaleString()}`,n-40,a),a+=20}),r.line(20,a,n-20,a),a+=10,r.setFont("helvetica","normal"),r.text("Subtotal:",n-80,a),r.text(`\xa5${t.subtotal.toLocaleString()}`,n-40,a),a+=15,t.tax>0&&(r.text("Tax:",n-80,a),r.text(`\xa5${t.tax.toLocaleString()}`,n-40,a),a+=15),r.setFont("helvetica","bold"),r.text("Total:",n-80,a),r.text(`\xa5${t.total.toLocaleString()}`,n-40,a),a+=30,r.setFontSize(10),r.setFont("helvetica","normal"),r.text("Payment Terms:",20,a),a+=10,r.text(t.paymentTerms,20,a),t.notes){a+=20,r.text("Notes:",20,a),a+=10;let e=r.splitTextToSize(t.notes,n-40);r.text(e,20,a)}let s=r.internal.pageSize.height-30;return r.setFontSize(8),r.text("Thank you for your business!",20,s),r.text("For questions about this invoice, please contact <NAME_EMAIL>",20,s+10),r}async function o(t,e){s(t,e).output("blob");let r=`/invoices/${t.invoiceNumber}.pdf`;return await (0,n.Dq)(t.id,{pdfUrl:r}),r}async function c(t){let e=await a(t);return await o(e,t),e}async function u(t){return await (0,n.Dq)(t,{status:"paid"})}},8086:t=>{"use strict";t.exports=require("module")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30358:(t,e,r)=>{"use strict";r.d(e,{Dq:()=>E,HQ:()=>F,Q4:()=>v,Vd:()=>g,ee:()=>b,fS:()=>w,g5:()=>O,getOrderById:()=>S,iO:()=>$,iY:()=>I});var i=r(29021),n=r(33873),a=r.n(n),s=r(23870);let o=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,c=a().join(process.cwd(),"data"),u=a().join(c,"orders.json"),d=a().join(c,"invoices.json"),p=[],l=[];async function m(){if(!o)try{await i.promises.access(c)}catch{await i.promises.mkdir(c,{recursive:!0})}}async function f(){if(o)return p;try{await m();let t=await i.promises.readFile(u,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function h(t){if(o){p=t;return}await m(),await i.promises.writeFile(u,JSON.stringify(t,null,2))}async function x(){if(o)return l;try{await m();let t=await i.promises.readFile(d,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function y(t){if(o){l=t;return}await m(),await i.promises.writeFile(d,JSON.stringify(t,null,2))}async function w(t){let e=await f(),r={...t,id:(0,s.A)(),orderNumber:function(){let t=Date.now().toString(),e=Math.random().toString(36).substr(2,4).toUpperCase();return`EB${t.slice(-6)}${e}`}(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await h(e),r}async function g(){return await f()}async function S(t){return(await f()).find(e=>e.id===t)||null}async function v(t){return(await f()).filter(e=>e.customerId===t)}async function I(t,e){let r=await f(),i=r.findIndex(e=>e.id===t);return -1!==i&&(r[i]={...r[i],...e,updatedAt:new Date().toISOString()},await h(r),!0)}async function b(t,e){let r=await S(t);if(!r)return!1;let i={...r.payment,...e};return await I(t,{payment:i})}async function F(t,e){let r=await S(t);if(!r)return!1;let i={...e,id:(0,s.A)()},n={...r.shipping,updates:[...r.shipping.updates,i]};return await I(t,{shipping:n})}async function $(t){let e=await x(),r={...t,id:(0,s.A)(),invoiceNumber:function(){let t=Date.now().toString(),e=Math.random().toString(36).substr(2,3).toUpperCase();return`INV-${t.slice(-6)}-${e}`}()};return e.push(r),await y(e),r}async function O(t){return(await x()).find(e=>e.orderId===t)||null}async function E(t,e){let r=await x(),i=r.findIndex(e=>e.id===t);return -1!==i&&(r[i]={...r[i],...e},await y(r),!0)}},33873:t=>{"use strict";t.exports=require("path")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66151:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>v,routeModule:()=>y,serverHooks:()=>S,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{POST:()=>m});var n=r(96559),a=r(48088),s=r(37719),o=r(32190),c=r(97877),u=r(30358),d=r(7263);let p=process.env.STRIPE_SECRET_KEY?new c.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia"}):null,l=process.env.STRIPE_WEBHOOK_SECRET;async function m(t){if(!p||!l)return o.NextResponse.json({error:"Stripe not configured"},{status:500});try{let e,r=await t.text(),i=t.headers.get("stripe-signature");if(!i)return o.NextResponse.json({error:"Missing stripe signature"},{status:400});try{e=p.webhooks.constructEvent(r,i,l)}catch(t){return console.error("Webhook signature verification failed:",t),o.NextResponse.json({error:"Invalid signature"},{status:400})}switch(e.type){case"payment_intent.succeeded":await f(e.data.object);break;case"payment_intent.payment_failed":await h(e.data.object);break;case"payment_intent.processing":await x(e.data.object)}return o.NextResponse.json({received:!0})}catch(t){return console.error("Webhook error:",t),o.NextResponse.json({error:"Webhook handler failed"},{status:500})}}async function f(t){let e=t.metadata.orderId;if(!e)return void console.error("No orderId in payment intent metadata");try{await (0,u.ee)(e,{status:"completed",transactionId:t.id,paidAt:new Date().toISOString()});let r=await (0,d.g5)(e);r&&await (0,d.de)(r.id),await (0,u.HQ)(e,{timestamp:new Date().toISOString(),status:"Payment Confirmed",location:"EBAM Motors Office",description:"Payment received and confirmed via Stripe. Vehicle preparation will begin shortly."})}catch(t){console.error(`Error handling payment success for order ${e}:`,t)}}async function h(t){let e=t.metadata.orderId;if(!e)return void console.error("No orderId in payment intent metadata");try{await (0,u.ee)(e,{status:"failed",transactionId:t.id}),await (0,u.HQ)(e,{timestamp:new Date().toISOString(),status:"Payment Failed",location:"Payment Processor",description:"Payment failed. Please try again or contact support."})}catch(t){console.error(`Error handling payment failure for order ${e}:`,t)}}async function x(t){let e=t.metadata.orderId;if(!e)return void console.error("No orderId in payment intent metadata");try{await (0,u.ee)(e,{status:"processing",transactionId:t.id})}catch(t){console.error(`Error handling payment processing for order ${e}:`,t)}}let y=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/webhooks/stripe/route",pathname:"/api/webhooks/stripe",filename:"route",bundlePath:"app/api/webhooks/stripe/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\webhooks\\stripe\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:S}=y;function v(){return(0,s.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},78335:()=>{},79646:t=>{"use strict";t.exports=require("child_process")},81630:t=>{"use strict";t.exports=require("http")},94735:t=>{"use strict";t.exports=require("events")},96487:()=>{}};var e=require("../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),i=e.X(0,[4447,580,4406,7877],()=>r(66151));module.exports=i})();