import Link from 'next/link';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';
import WhatsAppChannel from '@/components/WhatsAppChannel';
import StockWrapper from './StockWrapper';
import { generateStockFromFileSystem } from './utils/stockGenerator';

export default async function StockPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  // Generate stock data dynamically from file system
  const stockItems = await generateStockFromFileSystem();

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Stock Grid with Sidebar Layout */}
      <section className="pt-20">
        {/* Stock Grid with Client Components */}
        <StockWrapper items={stockItems} locale={locale} />
      </section>

      {/* Pricing Summary - Below the main stock area */}
      <section className="py-16 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <h3 className="text-2xl font-heading font-bold text-center mb-8">
              {messages.stock?.carModelPricingGuide || (locale === 'en' ? 'Car Model Pricing Guide' : '車種別価格ガイド')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <div className="bg-neutral-50 rounded-lg p-6 text-center">
                <h4 className="font-semibold text-lg mb-2">Toyota Voxy</h4>
                <p className="text-sm text-neutral-600 mb-2">8-seater MPV</p>
                <p className="text-xl font-bold text-primary-600">¥295,000-¥300,000</p>
              </div>
              <div className="bg-neutral-50 rounded-lg p-6 text-center">
                <h4 className="font-semibold text-lg mb-2">Toyota Noah</h4>
                <p className="text-sm text-neutral-600 mb-2">8-seater MPV</p>
                <p className="text-xl font-bold text-primary-600">¥350,000</p>
              </div>
              <div className="bg-neutral-50 rounded-lg p-6 text-center">
                <h4 className="font-semibold text-lg mb-2">Toyota Sienta</h4>
                <p className="text-sm text-neutral-600 mb-2">7-seater Compact MPV</p>
                <p className="text-xl font-bold text-primary-600">¥320,000</p>
              </div>
              <div className="bg-neutral-50 rounded-lg p-6 text-center">
                <h4 className="font-semibold text-lg mb-2">Toyota Vitz</h4>
                <p className="text-sm text-neutral-600 mb-2">5-seater Compact</p>
                <p className="text-xl font-bold text-primary-600">¥325,000</p>
              </div>
              <div className="bg-neutral-50 rounded-lg p-6 text-center">
                <h4 className="font-semibold text-lg mb-2">Toyota Yaris</h4>
                <p className="text-sm text-neutral-600 mb-2">5-seater Compact</p>
                <p className="text-xl font-bold text-primary-600">¥550,000</p>
              </div>
            </div>
            <div className="mt-6 text-center">
              <p className="text-sm text-neutral-600">
                {messages.stock?.allPricesInYen || (locale === 'en'
                  ? 'All prices in Japanese Yen (¥). Shipping and import duties not included.'
                  : '全ての価格は日本円（¥）表示です。送料・輸入関税は含まれません。'
                )}
              </p>
            </div>
          </div>

          {/* WhatsApp Channel */}
          <div className="mt-12">
            <WhatsAppChannel locale={locale} variant="compact" showQR={true} />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-heading font-bold mb-6">
            {locale === 'en' ? "Don't See What You're Looking For?" : 'お探しの商品が見つかりませんか？'}
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
            {locale === 'en' 
              ? 'Contact us with your specific requirements. We source new stock regularly and can help you find exactly what you need.'
              : 'ご希望の商品についてお問い合わせください。定期的に新しい在庫を調達しており、お客様のニーズに合った商品を見つけるお手伝いをいたします。'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href={`/${locale}/contact`}
              className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-8 py-4 rounded-lg font-semibold transition-colors duration-200"
            >
              {messages.navigation.contact}
            </Link>
            <Link
              href={`/${locale}/buyers`}
              className="border-2 border-white text-white hover:bg-white hover:text-primary-800 px-8 py-4 rounded-lg font-semibold transition-colors duration-200"
            >
              {messages.navigation.buyers}
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">EM</span>
                </div>
                <span className="font-heading font-bold text-xl">EBAM MOTORS</span>
              </div>
              <p className="text-neutral-300">
                {messages.about?.mission || 'Promote sustainable trade by giving used goods a second life where they are needed most.'}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">{messages.contact.contactInfo}</h3>
              <div className="space-y-2 text-neutral-300">
                <div>
                  <p>Japan: {messages.about?.phone || '080-6985-2864'}</p>
                  <p>Ghana: {messages.about?.ghanaPhone || '+233245375692'}</p>
                </div>
                <p>{messages.about?.email || '<EMAIL>'}</p>
                <div>
                  <p>Japan: {messages.about?.location || 'Japan, Saitama, Hanno City, Nagata'}</p>
                  <p>Ghana: {messages.about?.ghanaLocation || 'Kumasi, Ghana'}</p>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">{messages.contact?.quickLinks || 'Quick Links'}</h3>
              <div className="space-y-2">
                <Link href={`/${locale}/services`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.services}
                </Link>
                <Link href={`/${locale}/how-it-works`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.howItWorks}
                </Link>
                <Link href={`/${locale}/contact`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.contact}
                </Link>
              </div>
            </div>
          </div>
          <div className="border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 EBAM MOTORS. All rights reserved.</p>
          </div>
        </div>
      </footer>


    </div>
  );
}
