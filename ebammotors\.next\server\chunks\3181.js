"use strict";exports.id=3181,exports.ids=[3181],exports.modules={3181:(e,s,t)=>{t.d(s,{default:()=>M});var a=t(60687),r=t(43210),l=t(85814),n=t.n(l),i=t(16189),c=t(78272),o=t(58869),d=t(98712),m=t(11860),x=t(12941),u=t(54907),h=t(63213),p=t(41550),f=t(64021),j=t(12597),b=t(13861),N=t(43125),y=t(48340),g=t(97992);function v({isOpen:e,onClose:s,initialMode:t="login",locale:l}){let[n,i]=(0,r.useState)(t),[c,d]=(0,r.useState)(!1),[x,u]=(0,r.useState)(!1),[v,w]=(0,r.useState)(""),{login:A,register:k}=(0,h.A)(),[S,C]=(0,r.useState)({email:"",password:""}),[P,E]=(0,r.useState)({name:"",email:"",password:"",confirmPassword:"",phone:"",location:""});if(!e)return null;let D=async e=>{e.preventDefault(),u(!0),w("");try{await A(S.email,S.password)?(s(),C({email:"",password:""})):w("en"===l?"Invalid email or password":"メールアドレスまたはパスワードが無効です")}catch(e){w("en"===l?"Login failed. Please try again.":"ログインに失敗しました。もう一度お試しください。")}finally{u(!1)}},$=async e=>{if(e.preventDefault(),u(!0),w(""),P.password!==P.confirmPassword){w("en"===l?"Passwords do not match":"パスワードが一致しません"),u(!1);return}try{await k({name:P.name,email:P.email,password:P.password,phone:P.phone,location:P.location})?(s(),E({name:"",email:"",password:"",confirmPassword:"",phone:"",location:""})):w("en"===l?"Registration failed. Please try again.":"登録に失敗しました。もう一度お試しください。")}catch(e){w("en"===l?"Registration failed. Please try again.":"登録に失敗しました。もう一度お試しください。")}finally{u(!1)}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-neutral-800",children:"login"===n?"en"===l?"Sign In":"サインイン":"en"===l?"Create Account":"アカウント作成"}),(0,a.jsx)("button",{onClick:s,className:"text-neutral-600 hover:text-neutral-800 transition-colors",children:(0,a.jsx)(m.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[v&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:v}),"login"===n?(0,a.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===l?"Email Address":"メールアドレス"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{type:"email",value:S.email,onChange:e=>C(s=>({...s,email:e.target.value})),className:"w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===l?"Enter your email":"メールアドレスを入力",required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===l?"Password":"パスワード"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{type:c?"text":"password",value:S.password,onChange:e=>C(s=>({...s,password:e.target.value})),className:"w-full pl-10 pr-12 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===l?"Enter your password":"パスワードを入力",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>d(!c),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600",children:c?(0,a.jsx)(j.A,{className:"w-5 h-5"}):(0,a.jsx)(b.A,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("button",{type:"submit",disabled:x,className:"w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2",children:[x&&(0,a.jsx)(N.A,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"en"===l?"Sign In":"サインイン"})]})]}):(0,a.jsxs)("form",{onSubmit:$,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===l?"Full Name":"氏名"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",value:P.name,onChange:e=>E(s=>({...s,name:e.target.value})),className:"w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===l?"Enter your full name":"氏名を入力",required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===l?"Email Address":"メールアドレス"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{type:"email",value:P.email,onChange:e=>E(s=>({...s,email:e.target.value})),className:"w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===l?"Enter your email":"メールアドレスを入力",required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:["en"===l?"Phone Number":"電話番号"," (","en"===l?"Optional":"任意",")"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{type:"tel",value:P.phone,onChange:e=>E(s=>({...s,phone:e.target.value})),className:"w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===l?"Enter your phone number":"電話番号を入力"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:["en"===l?"Location":"所在地"," (","en"===l?"Optional":"任意",")"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",value:P.location,onChange:e=>E(s=>({...s,location:e.target.value})),className:"w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===l?"Enter your location":"所在地を入力"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===l?"Password":"パスワード"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{type:c?"text":"password",value:P.password,onChange:e=>E(s=>({...s,password:e.target.value})),className:"w-full pl-10 pr-12 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===l?"Create a password":"パスワードを作成",required:!0,minLength:6}),(0,a.jsx)("button",{type:"button",onClick:()=>d(!c),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600",children:c?(0,a.jsx)(j.A,{className:"w-5 h-5"}):(0,a.jsx)(b.A,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===l?"Confirm Password":"パスワード確認"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{type:c?"text":"password",value:P.confirmPassword,onChange:e=>E(s=>({...s,confirmPassword:e.target.value})),className:"w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===l?"Confirm your password":"パスワードを確認",required:!0})]})]}),(0,a.jsxs)("button",{type:"submit",disabled:x,className:"w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2",children:[x&&(0,a.jsx)(N.A,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"en"===l?"Create Account":"アカウント作成"})]})]}),(0,a.jsxs)("div",{className:"mt-6 text-center",children:[(0,a.jsx)("p",{className:"text-neutral-600",children:"login"===n?"en"===l?"Don't have an account?":"アカウントをお持ちでない方は":"en"===l?"Already have an account?":"既にアカウントをお持ちの方は"}),(0,a.jsx)("button",{onClick:()=>{i("login"===n?"register":"login"),w("")},className:"text-primary-600 hover:text-primary-700 font-semibold transition-colors",children:"login"===n?"en"===l?"Create Account":"アカウント作成":"en"===l?"Sign In":"サインイン"})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-primary-50 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-primary-800 mb-2",children:"en"===l?"Member Benefits":"会員特典"}),(0,a.jsxs)("ul",{className:"text-sm text-primary-700 space-y-1",children:[(0,a.jsxs)("li",{children:["• ","en"===l?"Save favorite vehicles":"お気に入り車両の保存"]}),(0,a.jsxs)("li",{children:["• ","en"===l?"Track purchase history":"購入履歴の追跡"]}),(0,a.jsxs)("li",{children:["• ","en"===l?"Earn loyalty points":"ロイヤルティポイントの獲得"]}),(0,a.jsxs)("li",{children:["• ","en"===l?"Save custom searches":"カスタム検索の保存"]}),(0,a.jsxs)("li",{children:["• ","en"===l?"Get exclusive offers":"限定オファーの取得"]})]})]})]})]})})}var w=t(67760),A=t(99270),k=t(71057),S=t(64398),C=t(84027),P=t(40083),E=t(63143),D=t(8819),$=t(97051);function L({locale:e,onClose:s}){let{user:t,logout:l,updateProfile:n,favorites:i,savedSearches:c,purchaseHistory:d}=(0,h.A)(),[x,u]=(0,r.useState)("profile"),[p,f]=(0,r.useState)(!1),[j,b]=(0,r.useState)({name:t?.name||"",phone:t?.phone||"",location:t?.location||""});if(!t)return null;let N=async()=>{await n(j)&&f(!1)},y=[{id:"profile",label:"en"===e?"Profile":"プロフィール",icon:o.A},{id:"favorites",label:"en"===e?"Favorites":"お気に入り",icon:w.A,count:i.length},{id:"searches",label:"en"===e?"Saved Searches":"保存済み検索",icon:A.A,count:c.length},{id:"orders",label:"en"===e?"Orders":"注文履歴",icon:k.A,count:d.length},{id:"loyalty",label:"en"===e?"Loyalty":"ロイヤルティ",icon:S.A},{id:"settings",label:"en"===e?"Settings":"設定",icon:C.A}],g=e=>{switch(e){case"Bronze":return"text-amber-600 bg-amber-100";case"Silver":default:return"text-gray-600 bg-gray-100";case"Gold":return"text-yellow-600 bg-yellow-100";case"Platinum":return"text-purple-600 bg-purple-100"}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b bg-gradient-to-r from-primary-600 to-primary-700 text-white",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-bold",children:t.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${g(t.membershipTier)}`,children:t.membershipTier}),(0,a.jsxs)("span",{className:"text-primary-100 text-sm",children:[t.loyaltyPoints," ","en"===e?"points":"ポイント"]})]})]})]}),(0,a.jsx)("button",{onClick:s,className:"text-white/80 hover:text-white transition-colors",children:(0,a.jsx)(m.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-64 border-r bg-neutral-50 p-4",children:(0,a.jsxs)("nav",{className:"space-y-2",children:[y.map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>u(e.id),className:`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${x===e.id?"bg-primary-100 text-primary-700":"text-neutral-600 hover:bg-neutral-100"}`,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(s,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"font-medium",children:e.label})]}),void 0!==e.count&&(0,a.jsx)("span",{className:"bg-primary-600 text-white text-xs px-2 py-1 rounded-full",children:e.count})]},e.id)}),(0,a.jsxs)("button",{onClick:l,className:"w-full flex items-center space-x-3 p-3 rounded-lg text-left text-red-600 hover:bg-red-50 transition-colors mt-4",children:[(0,a.jsx)(P.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"font-medium",children:"en"===e?"Sign Out":"サインアウト"})]})]})}),(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:["profile"===x&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:"en"===e?"Profile Information":"プロフィール情報"}),p?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:N,className:"flex items-center space-x-2 bg-primary-600 text-white px-3 py-1 rounded-lg hover:bg-primary-700 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===e?"Save":"保存"})]}),(0,a.jsx)("button",{onClick:()=>{f(!1),b({name:t.name,phone:t.phone||"",location:t.location||""})},className:"text-neutral-600 hover:text-neutral-800 transition-colors",children:(0,a.jsx)(m.A,{className:"w-4 h-4"})})]}):(0,a.jsxs)("button",{onClick:()=>f(!0),className:"flex items-center space-x-2 text-primary-600 hover:text-primary-700 transition-colors",children:[(0,a.jsx)(E.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===e?"Edit":"編集"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===e?"Full Name":"氏名"}),p?(0,a.jsx)("input",{type:"text",value:j.name,onChange:e=>b(s=>({...s,name:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"}):(0,a.jsx)("p",{className:"text-neutral-800",children:t.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===e?"Email Address":"メールアドレス"}),(0,a.jsx)("p",{className:"text-neutral-800",children:t.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===e?"Phone Number":"電話番号"}),p?(0,a.jsx)("input",{type:"tel",value:j.phone,onChange:e=>b(s=>({...s,phone:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===e?"Enter phone number":"電話番号を入力"}):(0,a.jsx)("p",{className:"text-neutral-800",children:t.phone||("en"===e?"Not provided":"未設定")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===e?"Location":"所在地"}),p?(0,a.jsx)("input",{type:"text",value:j.location,onChange:e=>b(s=>({...s,location:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===e?"Enter location":"所在地を入力"}):(0,a.jsx)("p",{className:"text-neutral-800",children:t.location||("en"===e?"Not provided":"未設定")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===e?"Member Since":"会員登録日"}),(0,a.jsx)("p",{className:"text-neutral-800",children:new Date(t.joinDate).toLocaleDateString("en"===e?"en-US":"ja-JP")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===e?"Membership Tier":"会員ランク"}),(0,a.jsx)("span",{className:`inline-flex px-3 py-1 rounded-full text-sm font-medium ${g(t.membershipTier)}`,children:t.membershipTier})]})]})]}),"favorites"===x&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:"en"===e?"Favorite Vehicles":"お気に入り車両"}),0===i.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(w.A,{className:"w-12 h-12 text-neutral-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===e?"No favorites yet":"お気に入りはまだありません"})]}):(0,a.jsx)("div",{className:"space-y-3",children:i.map(s=>(0,a.jsx)("div",{className:"p-4 border rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium text-neutral-800",children:["Vehicle ID: ",s.vehicleId]}),(0,a.jsxs)("p",{className:"text-sm text-neutral-600",children:["en"===e?"Added":"追加日",": ",new Date(s.addedAt).toLocaleDateString()]}),s.notes&&(0,a.jsx)("p",{className:"text-sm text-neutral-600 mt-1",children:s.notes})]}),(0,a.jsx)("button",{className:"text-red-600 hover:text-red-700 transition-colors",children:(0,a.jsx)(w.A,{className:"w-5 h-5 fill-current"})})]})},s.id))})]}),"searches"===x&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:"en"===e?"Saved Searches":"保存済み検索"}),0===c.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(A.A,{className:"w-12 h-12 text-neutral-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===e?"No saved searches yet":"保存済み検索はまだありません"})]}):(0,a.jsx)("div",{className:"space-y-3",children:c.map(s=>(0,a.jsx)("div",{className:"p-4 border rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-neutral-800",children:s.name}),(0,a.jsxs)("p",{className:"text-sm text-neutral-600",children:["en"===e?"Created":"作成日",": ",new Date(s.createdAt).toLocaleDateString()]})]}),(0,a.jsx)("button",{className:"text-primary-600 hover:text-primary-700 transition-colors",children:"en"===e?"Use Search":"検索を使用"})]})},s.id))})]}),"orders"===x&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:"en"===e?"Purchase History":"購入履歴"}),0===d.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(k.A,{className:"w-12 h-12 text-neutral-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===e?"No orders yet":"注文履歴はまだありません"})]}):(0,a.jsx)("div",{className:"space-y-3",children:d.map(s=>(0,a.jsx)("div",{className:"p-4 border rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-neutral-800",children:s.vehicleTitle}),(0,a.jsx)("p",{className:"text-lg font-bold text-primary-600",children:s.price}),(0,a.jsxs)("p",{className:"text-sm text-neutral-600",children:["en"===e?"Ordered":"注文日",": ",new Date(s.orderDate).toLocaleDateString()]})]}),(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"delivered"===s.status?"bg-green-100 text-green-800":"shipped"===s.status?"bg-blue-100 text-blue-800":"confirmed"===s.status?"bg-yellow-100 text-yellow-800":"cancelled"===s.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:s.status})]})},s.id))})]}),"loyalty"===x&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:"en"===e?"Loyalty Program":"ロイヤルティプログラム"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-primary-600 mb-2",children:t.loyaltyPoints}),(0,a.jsx)("div",{className:"text-primary-700 font-medium",children:"en"===e?"Available Points":"利用可能ポイント"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-800 mb-2",children:"en"===e?"Earn Points":"ポイント獲得"}),(0,a.jsxs)("ul",{className:"text-sm text-neutral-600 space-y-1",children:[(0,a.jsxs)("li",{children:["• ","en"===e?"1 point per \xa51,000 spent":"\xa51,000につき1ポイント"]}),(0,a.jsxs)("li",{children:["• ","en"===e?"5 points for adding favorites":"お気に入り追加で5ポイント"]}),(0,a.jsxs)("li",{children:["• ","en"===e?"100 points for referrals":"紹介で100ポイント"]})]})]}),(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-800 mb-2",children:"en"===e?"Redeem Points":"ポイント交換"}),(0,a.jsxs)("ul",{className:"text-sm text-neutral-600 space-y-1",children:[(0,a.jsxs)("li",{children:["• ","en"===e?"1000 points = \xa51,000 discount":"1000ポイント = \xa51,000割引"]}),(0,a.jsxs)("li",{children:["• ","en"===e?"500 points = Free inspection":"500ポイント = 無料検査"]}),(0,a.jsxs)("li",{children:["• ","en"===e?"2000 points = Priority shipping":"2000ポイント = 優先配送"]})]})]})]})]}),"settings"===x&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:"en"===e?"Account Settings":"アカウント設定"}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("h4",{className:"font-semibold text-neutral-800 mb-3 flex items-center space-x-2",children:[(0,a.jsx)($.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"en"===e?"Notification Preferences":"通知設定"})]}),(0,a.jsx)("div",{className:"space-y-3",children:Object.entries(t.preferences.notifications).map(([s,r])=>(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-neutral-700",children:"email"===s?"en"===e?"Email Notifications":"メール通知":"sms"===s?"en"===e?"SMS Notifications":"SMS通知":"push"===s?"en"===e?"Push Notifications":"プッシュ通知":"marketing"===s?"en"===e?"Marketing Emails":"マーケティングメール":s}),(0,a.jsx)("input",{type:"checkbox",checked:r,onChange:()=>{n({preferences:{...t.preferences,notifications:{...t.preferences.notifications,[s]:!r}}})},className:"w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"})]},s))})]})})]})]})]})]})})}function M(){let[e,s]=(0,r.useState)(!1),[t,l]=(0,r.useState)(!1),[p,f]=(0,r.useState)(!1),[j,b]=(0,r.useState)(!1),N=(0,i.usePathname)(),{user:y}=(0,h.A)(),g=N.split("/")[1]||"en",w=(0,u.c)(g),A=[{name:w.navigation.home,href:`/${g}`},{name:w.navigation.services,href:`/${g}/services`},{name:w.navigation.inventory,href:`/${g}/stock`},{name:w.navigation.about,href:`/${g}/about`},{name:w.navigation.contact,href:`/${g}/contact`}];return(0,a.jsxs)("nav",{className:"bg-white shadow-lg fixed top-0 left-0 right-0 z-50 transition-all duration-300",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"EM"})}),(0,a.jsx)("span",{className:"font-heading font-bold text-xl text-neutral-800",children:"EBAM MOTORS"})]})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[A.map(e=>e.href===`/${g}/stock`?(0,a.jsxs)("div",{className:"relative",onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),children:[(0,a.jsxs)(n(),{href:e.href,className:"text-neutral-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200 flex items-center gap-1",children:[e.name,(0,a.jsx)(c.A,{className:"w-4 h-4"})]}),t&&(0,a.jsxs)("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 py-2 z-50",children:[(0,a.jsx)(n(),{href:`/${g}/stock#filter`,className:"block px-4 py-2 text-sm text-neutral-600 hover:text-primary-600 hover:bg-neutral-50 transition-colors duration-200",children:"en"===g?"Filter by Category":"カテゴリーで絞り込み"}),(0,a.jsx)(n(),{href:`/${g}/stock#search`,className:"block px-4 py-2 text-sm text-neutral-600 hover:text-primary-600 hover:bg-neutral-50 transition-colors duration-200",children:"en"===g?"Search Stock":"在庫検索"})]})]},e.name):(0,a.jsx)(n(),{href:e.href,className:"text-neutral-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200",children:e.name},e.name)),(0,a.jsx)(n(),{href:"en"===g?"/ja":"/en",className:"text-neutral-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200",children:"en"===g?w.navigation.japanese:"English"}),y?(0,a.jsxs)("button",{onClick:()=>b(!0),className:"flex items-center space-x-2 text-neutral-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden lg:inline",children:y.name})]}):(0,a.jsxs)("button",{onClick:()=>f(!0),className:"flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===g?"Sign In":"サインイン"})]})]}),(0,a.jsx)("div",{className:"md:hidden flex items-center",children:(0,a.jsx)("button",{onClick:()=>s(!e),className:"text-neutral-600 hover:text-primary-600 p-2",children:e?(0,a.jsx)(m.A,{className:"w-6 h-6"}):(0,a.jsx)(x.A,{className:"w-6 h-6"})})})]}),e&&(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",children:[A.map(e=>(0,a.jsx)(n(),{href:e.href,className:"text-neutral-600 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200",onClick:()=>s(!1),children:e.name},e.name)),(0,a.jsx)(n(),{href:"en"===g?"/ja":"/en",className:"text-neutral-600 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200",onClick:()=>s(!1),children:"en"===g?w.navigation.japanese:"English"}),(0,a.jsx)("div",{className:"border-t pt-3 mt-3",children:y?(0,a.jsxs)("button",{onClick:()=>{b(!0),s(!1)},className:"flex items-center space-x-2 text-neutral-600 hover:text-primary-600 px-3 py-2 text-base font-medium transition-colors duration-200 w-full text-left",children:[(0,a.jsx)(o.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:y.name})]}):(0,a.jsxs)("button",{onClick:()=>{f(!0),s(!1)},className:"flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 mx-3 justify-center",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===g?"Sign In":"サインイン"})]})})]})})]}),p&&(0,a.jsx)(v,{isOpen:p,onClose:()=>f(!1),locale:g}),j&&y&&(0,a.jsx)(L,{locale:g,onClose:()=>b(!1)})]})}}};