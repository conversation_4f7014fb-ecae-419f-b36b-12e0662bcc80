// Push Notifications utility for EBAM Motors
// Handles subscription, sending notifications, and managing user preferences

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  data?: any;
  actions?: NotificationAction[];
  tag?: string;
  requireInteraction?: boolean;
  silent?: boolean;
  vibrate?: number[];
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export interface PushSubscription {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
  userId?: string;
  preferences?: NotificationPreferences;
  createdAt: string;
  lastUsed: string;
}

export interface NotificationPreferences {
  orderUpdates: boolean;
  newStock: boolean;
  promotions: boolean;
  priceDrops: boolean;
  newsletter: boolean;
  reminders: boolean;
}

// Default notification preferences
export const DEFAULT_PREFERENCES: NotificationPreferences = {
  orderUpdates: true,
  newStock: true,
  promotions: false,
  priceDrops: true,
  newsletter: false,
  reminders: true,
};

// Check if push notifications are supported
export const isPushSupported = (): boolean => {
  return (
    typeof window !== 'undefined' &&
    'serviceWorker' in navigator &&
    'PushManager' in window &&
    'Notification' in window
  );
};

// Request notification permission
export const requestNotificationPermission = async (): Promise<NotificationPermission> => {
  if (!isPushSupported()) {
    throw new Error('Push notifications are not supported');
  }

  const permission = await Notification.requestPermission();
  return permission;
};

// Subscribe to push notifications
export const subscribeToPush = async (userId?: string): Promise<PushSubscription | null> => {
  if (!isPushSupported()) {
    throw new Error('Push notifications are not supported');
  }

  try {
    // Request permission first
    const permission = await requestNotificationPermission();
    if (permission !== 'granted') {
      throw new Error('Notification permission denied');
    }

    // Get service worker registration
    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) {
      throw new Error('Service worker not registered');
    }

    // Subscribe to push manager
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(
        process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || ''
      ),
    });

    // Convert to our format
    const pushSubscription: PushSubscription = {
      endpoint: subscription.endpoint,
      keys: {
        p256dh: arrayBufferToBase64(subscription.getKey('p256dh')!),
        auth: arrayBufferToBase64(subscription.getKey('auth')!),
      },
      userId,
      preferences: DEFAULT_PREFERENCES,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
    };

    // Save subscription to server
    await saveSubscription(pushSubscription);

    return pushSubscription;
  } catch (error) {
    return null;
  }
};

// Unsubscribe from push notifications
export const unsubscribeFromPush = async (): Promise<boolean> => {
  if (!isPushSupported()) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) {
      return false;
    }

    const subscription = await registration.pushManager.getSubscription();
    if (!subscription) {
      return false;
    }

    // Unsubscribe from push manager
    const success = await subscription.unsubscribe();
    
    if (success) {
      // Remove subscription from server
      await removeSubscription(subscription.endpoint);
    }

    return success;
  } catch (error) {
    return false;
  }
};

// Get current subscription status
export const getSubscriptionStatus = async (): Promise<{
  isSubscribed: boolean;
  subscription: PushSubscription | null;
  permission: NotificationPermission;
}> => {
  if (!isPushSupported()) {
    return {
      isSubscribed: false,
      subscription: null,
      permission: 'denied',
    };
  }

  try {
    const permission = Notification.permission;
    const registration = await navigator.serviceWorker.getRegistration();
    
    if (!registration) {
      return {
        isSubscribed: false,
        subscription: null,
        permission,
      };
    }

    const subscription = await registration.pushManager.getSubscription();
    
    return {
      isSubscribed: !!subscription,
      subscription: subscription ? {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: arrayBufferToBase64(subscription.getKey('auth')!),
        },
        createdAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
      } : null,
      permission,
    };
  } catch (error) {
    return {
      isSubscribed: false,
      subscription: null,
      permission: 'denied',
    };
  }
};

// Update notification preferences
export const updateNotificationPreferences = async (
  preferences: Partial<NotificationPreferences>
): Promise<boolean> => {
  try {
    const response = await fetch('/api/notifications/preferences', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(preferences),
    });

    return response.ok;
  } catch (error) {
    return false;
  }
};

// Send test notification
export const sendTestNotification = async (): Promise<boolean> => {
  try {
    const response = await fetch('/api/notifications/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.ok;
  } catch (error) {
    return false;
  }
};

// Utility functions
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }

  return outputArray;
}

function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

// Save subscription to server
async function saveSubscription(subscription: PushSubscription): Promise<void> {
  try {
    const response = await fetch('/api/notifications/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscription),
    });

    if (!response.ok) {
      throw new Error('Failed to save subscription');
    }
  } catch (error) {
    throw error;
  }
}

// Remove subscription from server
async function removeSubscription(endpoint: string): Promise<void> {
  try {
    const response = await fetch('/api/notifications/unsubscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ endpoint }),
    });

    if (!response.ok) {
      throw new Error('Failed to remove subscription');
    }
  } catch (error) {
    throw error;
  }
}

// Predefined notification templates
export const NOTIFICATION_TEMPLATES = {
  ORDER_CONFIRMED: (orderNumber: string): NotificationPayload => ({
    title: 'Order Confirmed',
    body: `Your order #${orderNumber} has been confirmed and is being processed.`,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: `order-${orderNumber}`,
    data: { type: 'order_update', orderNumber },
    actions: [
      { action: 'view', title: 'View Order', icon: '/icons/view.png' },
      { action: 'track', title: 'Track', icon: '/icons/track.png' },
    ],
  }),

  ORDER_SHIPPED: (orderNumber: string, trackingNumber: string): NotificationPayload => ({
    title: 'Order Shipped',
    body: `Your order #${orderNumber} has been shipped. Tracking: ${trackingNumber}`,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: `order-${orderNumber}`,
    data: { type: 'order_update', orderNumber, trackingNumber },
    actions: [
      { action: 'track', title: 'Track Package', icon: '/icons/track.png' },
    ],
  }),

  NEW_STOCK: (carModel: string, price: number): NotificationPayload => ({
    title: 'New Car Available',
    body: `${carModel} now available for ¥${price.toLocaleString()}`,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'new-stock',
    data: { type: 'new_stock', carModel, price },
    actions: [
      { action: 'view', title: 'View Car', icon: '/icons/car.png' },
    ],
  }),

  PRICE_DROP: (carModel: string, oldPrice: number, newPrice: number): NotificationPayload => ({
    title: 'Price Drop Alert',
    body: `${carModel} price reduced from ¥${oldPrice.toLocaleString()} to ¥${newPrice.toLocaleString()}`,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'price-drop',
    data: { type: 'price_drop', carModel, oldPrice, newPrice },
    actions: [
      { action: 'view', title: 'View Deal', icon: '/icons/deal.png' },
    ],
  }),

  PROMOTION: (title: string, description: string): NotificationPayload => ({
    title: title,
    body: description,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'promotion',
    data: { type: 'promotion' },
    actions: [
      { action: 'view', title: 'Learn More', icon: '/icons/info.png' },
    ],
  }),
};
