import Link from 'next/link';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';

export default async function SuppliersPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  const supplierTypes = [
    {
      title: locale === 'en' ? 'Moving Companies' : '引越し会社',
      description: locale === 'en' 
        ? 'Partner with us to give your clients\' unwanted items a second life while earning additional revenue.'
        : 'お客様の不要品に第二の人生を与えながら、追加収益を得るために当社とパートナーシップを結びませんか。',
      icon: '🚚',
      benefits: [
        locale === 'en' ? 'Additional revenue stream' : '追加収益源',
        locale === 'en' ? 'Eco-friendly disposal solution' : '環境に優しい処分ソリューション',
        locale === 'en' ? 'Professional pickup service' : 'プロフェッショナルな回収サービス',
        locale === 'en' ? 'Competitive pricing' : '競争力のある価格設定'
      ]
    },
    {
      title: locale === 'en' ? 'Junk Removal Services' : '不用品回収業者',
      description: locale === 'en'
        ? 'Transform disposal costs into profit by partnering with our export business.'
        : '当社の輸出事業とのパートナーシップにより、処分コストを利益に変えましょう。',
      icon: '♻️',
      benefits: [
        locale === 'en' ? 'Turn waste into revenue' : '廃棄物を収益に変換',
        locale === 'en' ? 'Reduce landfill costs' : '埋立地コストの削減',
        locale === 'en' ? 'Sustainable business model' : '持続可能なビジネスモデル',
        locale === 'en' ? 'Regular pickup schedules' : '定期回収スケジュール'
      ]
    },
    {
      title: locale === 'en' ? 'Car Dealers' : '自動車ディーラー',
      description: locale === 'en'
        ? 'We purchase trade-ins, accident cars, and inventory that doesn\'t sell locally.'
        : '下取り車、事故車、地元で売れない在庫を購入いたします。',
      icon: '',
      benefits: [
        locale === 'en' ? 'Quick inventory turnover' : '迅速な在庫回転',
        locale === 'en' ? 'Cash payment on collection' : '回収時の現金支払い',
        locale === 'en' ? 'Handle all paperwork' : 'すべての書類手続きを代行',
        locale === 'en' ? 'Accept various conditions' : '様々な状態の車両を受け入れ'
      ]
    },
    {
      title: locale === 'en' ? 'Individuals' : '個人の方',
      description: locale === 'en'
        ? 'Sell your unwanted items directly to us for fair prices and convenient pickup.'
        : '不要品を適正価格で直接当社に売却し、便利な回収サービスをご利用ください。',
      icon: '👤',
      benefits: [
        locale === 'en' ? 'Fair market pricing' : '適正な市場価格',
        locale === 'en' ? 'Free item evaluation' : '無料商品査定',
        locale === 'en' ? 'Convenient pickup service' : '便利な回収サービス',
        locale === 'en' ? 'Immediate payment' : '即座のお支払い'
      ]
    }
  ];

  const partnershipProcess = [
    {
      step: 1,
      title: locale === 'en' ? 'Initial Contact' : '初回お問い合わせ',
      description: locale === 'en'
        ? 'Contact us to discuss your business and the types of items you handle.'
        : 'お客様のビジネスと取り扱い商品について相談するためにお問い合わせください。'
    },
    {
      step: 2,
      title: locale === 'en' ? 'Partnership Agreement' : 'パートナーシップ契約',
      description: locale === 'en'
        ? 'We establish terms, pricing, and pickup schedules that work for both parties.'
        : '双方にとって有効な条件、価格設定、回収スケジュールを確立いたします。'
    },
    {
      step: 3,
      title: locale === 'en' ? 'Start Trading' : '取引開始',
      description: locale === 'en'
        ? 'Begin regular collections and payments according to our agreed schedule.'
        : '合意したスケジュールに従って定期回収とお支払いを開始いたします。'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-heading font-bold mb-6">
            {messages.suppliers.title}
          </h1>
          <p className="text-xl lg:text-2xl text-primary-100 max-w-4xl mx-auto">
            {locale === 'en'
              ? 'Turn your unwanted inventory into revenue while promoting sustainable trade'
              : '不要在庫を収益に変え、持続可能な貿易を促進しましょう'
            }
          </p>
        </div>
      </section>

      {/* Supplier Types */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {locale === 'en' ? 'Who We Work With' : 'お取引先'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'We partner with various businesses and individuals across Japan'
                : '日本全国の様々な企業・個人の皆様とパートナーシップを結んでいます'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {supplierTypes.map((type, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-3xl">{type.icon}</span>
                  </div>
                  <h3 className="text-2xl font-heading font-bold text-neutral-800">
                    {type.title}
                  </h3>
                </div>
                
                <p className="text-neutral-600 mb-6 leading-relaxed">
                  {type.description}
                </p>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-neutral-800">
                    {locale === 'en' ? 'Benefits:' : 'メリット：'}
                  </h4>
                  <ul className="space-y-2">
                    {type.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center text-neutral-600">
                        <div className="w-2 h-2 bg-primary-600 rounded-full mr-3 flex-shrink-0"></div>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Process */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {locale === 'en' ? 'How to Become a Partner' : 'パートナーになる方法'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'Simple steps to start a profitable partnership with EBAM Motors'
                : 'EBAM Motorsとの収益性の高いパートナーシップを始めるための簡単なステップ'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {partnershipProcess.map((step) => (
              <div key={step.step} className="relative text-center">
                <div className="bg-primary-50 rounded-2xl p-8 h-full">
                  <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span className="text-white font-bold text-2xl">{step.step}</span>
                  </div>
                  <h3 className="text-xl font-heading font-bold text-primary-800 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-neutral-700 leading-relaxed">
                    {step.description}
                  </p>
                </div>
                {step.step < partnershipProcess.length && (
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                    <div className="w-8 h-0.5 bg-primary-300"></div>
                    <div className="w-0 h-0 border-l-4 border-l-primary-300 border-t-2 border-t-transparent border-b-2 border-b-transparent absolute right-0 top-1/2 transform -translate-y-1/2"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Current Partners */}
      <section className="py-20 bg-primary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {messages.suppliers.partnerships}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white rounded-xl p-8 text-center shadow-lg">
              <h3 className="text-2xl font-heading font-bold text-neutral-800 mb-4">
                {messages.suppliers.toyotaHonda}
              </h3>
              <p className="text-neutral-600 leading-relaxed">
                {locale === 'en'
                  ? 'We work with authorized Toyota and Honda dealers to source quality vehicles for export.'
                  : '輸出用の高品質車両を調達するため、正規トヨタ・ホンダディーラーと提携しています。'
                }
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 text-center shadow-lg">
              <div className="w-20 h-20 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-4xl">🚚</span>
              </div>
              <h3 className="text-2xl font-heading font-bold text-neutral-800 mb-4">
                {messages.suppliers.movingCompanies}
              </h3>
              <p className="text-neutral-600 leading-relaxed">
                {locale === 'en'
                  ? 'Our network of moving company partners helps us source furniture and household items.'
                  : '引越し会社パートナーのネットワークにより、家具や家庭用品を調達しています。'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-heading font-bold mb-6">
            {locale === 'en' ? 'Ready to Partner With Us?' : 'パートナーシップを始めませんか？'}
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
            {locale === 'en'
              ? 'Join our network of suppliers and turn your unwanted inventory into a profitable revenue stream.'
              : '当社のサプライヤーネットワークに参加し、不要在庫を収益性の高い収入源に変えましょう。'
            }
          </p>
          
          <div className="bg-white/10 rounded-2xl p-8 max-w-2xl mx-auto mb-8">
            <h3 className="text-2xl font-heading font-bold mb-4">
              {locale === 'en' ? 'Contact Information' : 'お問い合わせ先'}
            </h3>
            <div className="space-y-3 text-primary-100">
              <div className="text-center">
                <p className="font-semibold mb-2">{locale === 'en' ? 'Japan Office:' : '日本オフィス：'}</p>
                <p className="flex items-center justify-center">
                  {messages.about?.phone || '080-6985-2864'}
                </p>
                <p className="flex items-center justify-center">
                  {messages.about?.location || 'Japan, Saitama, Hanno City, Nagata'}
                </p>
              </div>
              <div className="text-center pt-4">
                <p className="font-semibold mb-2">{locale === 'en' ? 'Ghana Office:' : 'ガーナオフィス：'}</p>
                <p className="flex items-center justify-center">
                  {messages.about?.ghanaPhone || '+233245375692'}
                </p>
                <p className="flex items-center justify-center">
                  {messages.about?.ghanaLocation || 'Kumasi, Ghana'}
                </p>
              </div>
              <div className="text-center pt-4">
                <p className="flex items-center justify-center">
                  {messages.about?.email || '<EMAIL>'}
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href={`/${locale}/contact`}
              className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {messages.navigation.contact}
            </Link>
            <Link
              href={`/${locale}/how-it-works`}
              className="border-2 border-white text-white hover:bg-white hover:text-primary-800 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {messages.navigation.howItWorks}
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">EM</span>
                </div>
                <span className="font-heading font-bold text-xl">EBAM MOTORS</span>
              </div>
              <p className="text-neutral-300">
                {messages.about.mission}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">Contact Info</h3>
              <div className="space-y-2 text-neutral-300">
                <div>
                  <p className="font-medium text-neutral-200">Japan: {messages.about?.phone || '080-6985-2864'}</p>
                  <p className="font-medium text-neutral-200">Ghana: {messages.about?.ghanaPhone || '+233245375692'}</p>
                </div>
                <p>{messages.about?.email || '<EMAIL>'}</p>
                <p>{messages.about?.location || 'Japan, Saitama, Hanno City, Nagata'}</p>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">{messages.contact?.quickLinks || 'Quick Links'}</h3>
              <div className="space-y-2">
                <Link href={`/${locale}/services`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.services}
                </Link>
                <Link href={`/${locale}/stock`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.inventory}
                </Link>
                <Link href={`/${locale}/buyers`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.buyers}
                </Link>
              </div>
            </div>
          </div>
          <div className="border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 EBAM MOTORS. {messages.ui?.allRightsReserved || 'All rights reserved.'}</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
