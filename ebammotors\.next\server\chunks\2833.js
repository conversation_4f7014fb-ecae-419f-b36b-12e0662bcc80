"use strict";exports.id=2833,exports.ids=[2833],exports.modules={92833:(e,t,i)=>{i.d(t,{B9:()=>c,BH:()=>l,Eb:()=>s,Tf:()=>o,Ww:()=>u,getSecurityHeaders:()=>m,h4:()=>g,o2:()=>a});let n={auth:{maxAttempts:5,windowMs:9e5},api:{maxRequests:100,windowMs:6e4},contact:{maxSubmissions:3,windowMs:36e5},review:{maxSubmissions:2,windowMs:36e5}},r=new Map;function s(e,t){let i=n[t],s=Date.now(),o=`${t}:${e}`,a=r.get(o);if(!a||s>a.resetTime){let e={count:1,resetTime:s+i.windowMs};return r.set(o,e),{allowed:!0,remaining:i.maxAttempts-1,resetTime:e.resetTime}}return a.count>=i.maxAttempts?{allowed:!1,remaining:0,resetTime:a.resetTime}:(a.count++,r.set(o,a),{allowed:!0,remaining:i.maxAttempts-a.count,resetTime:a.resetTime})}function o(e){let t=e.headers.get("x-forwarded-for"),i=e.headers.get("x-real-ip"),n=e.headers.get("remote-addr");return t?t.split(",")[0].trim():i||n||"unknown"}function a(e){return"string"!=typeof e?"":e.trim().replace(/[<>]/g,"").replace(/javascript:/gi,"").replace(/on\w+=/gi,"").substring(0,1e3)}function c(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)&&e.length<=254}function l(e){return/^[\+]?[1-9][\d]{0,15}$/.test(e.replace(/[\s\-\(\)]/g,""))}function u(e){return[/script/gi,/javascript/gi,/vbscript/gi,/onload/gi,/onerror/gi,/onclick/gi,/<iframe/gi,/<object/gi,/<embed/gi,/eval\(/gi,/document\.cookie/gi,/window\.location/gi].some(t=>t.test(e))}function m(){return{"X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY","X-XSS-Protection":"1; mode=block","Referrer-Policy":"strict-origin-when-cross-origin","Content-Security-Policy":"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"}}function g(e,t,i){let n=new Date().toISOString();console.warn(`[SECURITY] ${n} - ${e}`,{ip:o(i),userAgent:i.headers.get("user-agent")||"unknown",details:t,url:i.url})}setInterval(function(){let e=Date.now();for(let[t,i]of r.entries())e>i.resetTime&&r.delete(t)},3e5)}};