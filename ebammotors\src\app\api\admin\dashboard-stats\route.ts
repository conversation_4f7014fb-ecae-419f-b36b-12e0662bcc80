import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { sql } from '@vercel/postgres';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get dashboard statistics
    const stats = await getDashboardStats();

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch dashboard stats' },
      { status: 500 }
    );
  }
}

async function getDashboardStats() {
  try {
    // Get car count
    const carsResult = await sql`SELECT COUNT(*) as count FROM cars`;
    const totalCars = parseInt(carsResult.rows[0]?.count || '0');

    // Get customer count (from orders and reviews)
    const customersResult = await sql`
      SELECT COUNT(DISTINCT email) as count 
      FROM (
        SELECT customer_email as email FROM orders
        UNION
        SELECT customer_email as email FROM reviews WHERE customer_email IS NOT NULL
      ) as customers
    `;
    const totalCustomers = parseInt(customersResult.rows[0]?.count || '0');

    // Get order count
    const ordersResult = await sql`SELECT COUNT(*) as count FROM orders`;
    const totalOrders = parseInt(ordersResult.rows[0]?.count || '0');

    // Get pending reviews count
    const reviewsResult = await sql`SELECT COUNT(*) as count FROM reviews WHERE status = 'pending'`;
    const pendingReviews = parseInt(reviewsResult.rows[0]?.count || '0');

    // Calculate total revenue
    const revenueResult = await sql`
      SELECT SUM(total_amount) as total 
      FROM orders 
      WHERE status IN ('completed', 'shipped', 'delivered')
    `;
    const totalRevenue = parseFloat(revenueResult.rows[0]?.total || '0');

    // Calculate monthly growth (mock calculation for now)
    const monthlyGrowth = 12.5; // This would be calculated based on historical data

    // Get recent activity
    const recentActivity = await getRecentActivity();

    // System health check
    const systemHealth = await checkSystemHealth();

    return {
      totalCars,
      totalCustomers,
      totalOrders,
      pendingReviews,
      totalRevenue,
      monthlyGrowth,
      systemHealth,
      recentActivity
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    
    // Return fallback data if database queries fail
    return {
      totalCars: 0,
      totalCustomers: 0,
      totalOrders: 0,
      pendingReviews: 0,
      totalRevenue: 0,
      monthlyGrowth: 0,
      systemHealth: 'error' as const,
      recentActivity: []
    };
  }
}

async function getRecentActivity() {
  try {
    const activities = [];

    // Get recent orders
    const recentOrders = await sql`
      SELECT id, customer_name, total_amount, created_at, status
      FROM orders 
      ORDER BY created_at DESC 
      LIMIT 3
    `;

    recentOrders.rows.forEach(order => {
      activities.push({
        id: `order-${order.id}`,
        type: 'order',
        message: `New order from ${order.customer_name} - ¥${order.total_amount}`,
        timestamp: formatTimestamp(order.created_at),
        status: order.status === 'completed' ? 'success' : 'warning'
      });
    });

    // Get recent reviews
    const recentReviews = await sql`
      SELECT id, customer_name, rating, status, created_at
      FROM reviews 
      ORDER BY created_at DESC 
      LIMIT 2
    `;

    recentReviews.rows.forEach(review => {
      activities.push({
        id: `review-${review.id}`,
        type: 'review',
        message: `${review.status === 'pending' ? 'Review pending approval' : 'Review approved'} - ${review.rating} stars`,
        timestamp: formatTimestamp(review.created_at),
        status: review.status === 'approved' ? 'success' : 'warning'
      });
    });

    // Get recent cars added
    const recentCars = await sql`
      SELECT car_id, make, model, year, created_at
      FROM cars 
      ORDER BY created_at DESC 
      LIMIT 2
    `;

    recentCars.rows.forEach(car => {
      activities.push({
        id: `car-${car.car_id}`,
        type: 'car',
        message: `${car.make} ${car.model} ${car.year} added to inventory`,
        timestamp: formatTimestamp(car.created_at),
        status: 'success'
      });
    });

    // Sort by timestamp and return top 5
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 5);

  } catch (error) {
    console.error('Error fetching recent activity:', error);
    return [
      {
        id: '1',
        type: 'system',
        message: 'Dashboard initialized',
        timestamp: 'Just now',
        status: 'success'
      }
    ];
  }
}

async function checkSystemHealth(): Promise<'healthy' | 'warning' | 'error'> {
  try {
    // Test database connection
    await sql`SELECT 1`;
    
    // Add more health checks here as needed
    // - Check external API connections
    // - Check file system access
    // - Check memory usage
    
    return 'healthy';
  } catch (error) {
    console.error('System health check failed:', error);
    return 'error';
  }
}

function formatTimestamp(timestamp: string | Date): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  
  return date.toLocaleDateString();
}
