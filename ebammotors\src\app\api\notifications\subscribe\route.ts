import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File path for storing push subscriptions
const SUBSCRIPTIONS_FILE = path.join(process.cwd(), 'data', 'push-subscriptions.json');

// Ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = path.dirname(SUBSCRIPTIONS_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// Load existing subscriptions
async function loadSubscriptions() {
  try {
    await ensureDataDirectory();
    const data = await fs.readFile(SUBSCRIPTIONS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // File doesn't exist or is invalid, return empty array
    return [];
  }
}

// Save subscriptions
async function saveSubscriptions(subscriptions: any[]) {
  await ensureDataDirectory();
  await fs.writeFile(SUBSCRIPTIONS_FILE, JSON.stringify(subscriptions, null, 2));
}

// POST - Subscribe to push notifications
export async function POST(request: NextRequest) {
  try {
    const subscription = await request.json();
    
    // Validate subscription data
    if (!subscription.endpoint || !subscription.keys?.p256dh || !subscription.keys?.auth) {
      return NextResponse.json(
        { success: false, message: 'Invalid subscription data' },
        { status: 400 }
      );
    }

    // Load existing subscriptions
    const subscriptions = await loadSubscriptions();
    
    // Check if subscription already exists
    const existingIndex = subscriptions.findIndex(
      (sub: any) => sub.endpoint === subscription.endpoint
    );
    
    if (existingIndex >= 0) {
      // Update existing subscription
      subscriptions[existingIndex] = {
        ...subscriptions[existingIndex],
        ...subscription,
        lastUsed: new Date().toISOString(),
      };
    } else {
      // Add new subscription
      subscriptions.push({
        ...subscription,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
      });
    }
    
    // Save updated subscriptions
    await saveSubscriptions(subscriptions);

    return NextResponse.json({
      success: true,
      message: 'Subscription saved successfully',
    });
    
  } catch (error) {
    return NextResponse.json(
      { success: false, message: 'Failed to save subscription' },
      { status: 500 }
    );
  }
}

// GET - Get subscription status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint');
    
    if (!endpoint) {
      return NextResponse.json(
        { success: false, message: 'Endpoint parameter required' },
        { status: 400 }
      );
    }
    
    const subscriptions = await loadSubscriptions();
    const subscription = subscriptions.find((sub: any) => sub.endpoint === endpoint);
    
    return NextResponse.json({
      success: true,
      subscription: subscription || null,
      isSubscribed: !!subscription,
    });
    
  } catch (error) {
    return NextResponse.json(
      { success: false, message: 'Failed to get subscription status' },
      { status: 500 }
    );
  }
}
