{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/admin/orders/[orderId]/invoice", "regex": "^/api/admin/orders/([^/]+?)/invoice(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/api/admin/orders/(?<nxtPorderId>[^/]+?)/invoice(?:/)?$"}, {"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/about", "regex": "^/([^/]+?)/about(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/about(?:/)?$"}, {"page": "/[locale]/buyers", "regex": "^/([^/]+?)/buyers(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/buyers(?:/)?$"}, {"page": "/[locale]/contact", "regex": "^/([^/]+?)/contact(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/contact(?:/)?$"}, {"page": "/[locale]/how-it-works", "regex": "^/([^/]+?)/how\\-it\\-works(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/how\\-it\\-works(?:/)?$"}, {"page": "/[locale]/inventory", "regex": "^/([^/]+?)/inventory(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/inventory(?:/)?$"}, {"page": "/[locale]/leave-review", "regex": "^/([^/]+?)/leave\\-review(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/leave\\-review(?:/)?$"}, {"page": "/[locale]/reviews", "regex": "^/([^/]+?)/reviews(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reviews(?:/)?$"}, {"page": "/[locale]/services", "regex": "^/([^/]+?)/services(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/services(?:/)?$"}, {"page": "/[locale]/stock", "regex": "^/([^/]+?)/stock(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/stock(?:/)?$"}, {"page": "/[locale]/stock/[vehicleId]", "regex": "^/([^/]+?)/stock/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPvehicleId": "nxtPvehicleId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/stock/(?<nxtPvehicleId>[^/]+?)(?:/)?$"}, {"page": "/[locale]/suppliers", "regex": "^/([^/]+?)/suppliers(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/suppliers(?:/)?$"}, {"page": "/[locale]/tracking", "regex": "^/([^/]+?)/tracking(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/tracking(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/analytics", "regex": "^/admin/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/analytics(?:/)?$"}, {"page": "/admin/cars", "regex": "^/admin/cars(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/cars(?:/)?$"}, {"page": "/admin/crm", "regex": "^/admin/crm(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/crm(?:/)?$"}, {"page": "/admin/customers", "regex": "^/admin/customers(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/customers(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/health", "regex": "^/admin/health(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/health(?:/)?$"}, {"page": "/admin/orders", "regex": "^/admin/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/orders(?:/)?$"}, {"page": "/admin/reviews", "regex": "^/admin/reviews(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/reviews(?:/)?$"}, {"page": "/admin/security", "regex": "^/admin/security(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/security(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/test-features", "regex": "^/test\\-features(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-features(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}