'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminHealthPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to main dashboard with health section active
    router.replace('/admin/dashboard?section=health');
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to system health monitoring...</p>
      </div>
    </div>
  );
}
