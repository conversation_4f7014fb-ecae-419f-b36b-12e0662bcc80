exports.id=5887,exports.ids=[5887],exports.modules={4161:(e,t,a)=>{"use strict";a.d(t,{Uw:()=>o,od:()=>r});let i={"en-ja":{excellent:"素晴らしい",great:"素晴らしい",good:"良い",amazing:"驚くべき",perfect:"完璧",wonderful:"素晴らしい",fantastic:"素晴らしい",outstanding:"優秀",professional:"プロフェッショナル",service:"サービス",quality:"品質",car:"車",vehicle:"車両",delivery:"配送",shipping:"配送",fast:"速い",quick:"迅速",reliable:"信頼できる",trustworthy:"信頼できる",recommend:"おすすめ",satisfied:"満足",happy:"満足",experience:"体験",customer:"顧客",team:"チーム",staff:"スタッフ",price:"価格",value:"価値",condition:"状態","thank you":"ありがとうございます",thanks:"ありがとう"},"ja-en":{素晴らしい:"excellent",良い:"good",驚くべき:"amazing",完璧:"perfect",優秀:"outstanding",プロフェッショナル:"professional",サービス:"service",品質:"quality",車:"car",車両:"vehicle",配送:"delivery",速い:"fast",迅速:"quick",信頼できる:"reliable",おすすめ:"recommend",満足:"satisfied",体験:"experience",顧客:"customer",チーム:"team",スタッフ:"staff",価格:"price",価値:"value",状態:"condition",ありがとうございます:"thank you",ありがとう:"thanks"}},n={"en-ja":{"excellent service":"優れたサービス","great experience":"素晴らしい体験","highly recommend":"強くお勧めします","professional team":"プロフェッショナルなチーム","quality vehicle":"高品質な車両","fast delivery":"迅速な配送","good condition":"良い状態","satisfied customer":"満足した顧客","will use again":"また利用します","thank you":"ありがとうございます"},"ja-en":{優れたサービス:"excellent service",素晴らしい体験:"great experience",強くお勧めします:"highly recommend",プロフェッショナルなチーム:"professional team",高品質な車両:"quality vehicle",迅速な配送:"fast delivery",良い状態:"good condition",満足した顧客:"satisfied customer",また利用します:"will use again",ありがとうございます:"thank you"}};function r(e){return/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(e)?"ja":"en"}async function s(e,t,a){let s=a||r(e);if(s===t)return{translatedText:e,sourceLanguage:s,targetLanguage:t};let o=`${s}-${t}`,c=i[o]||{},l=n[o]||{},d=e.toLowerCase();for(let[e,t]of Object.entries(l)){let a=RegExp(e.toLowerCase(),"gi");d=d.replace(a,t)}for(let[e,t]of Object.entries(c)){let a=RegExp(`\\b${e.toLowerCase()}\\b`,"gi");d=d.replace(a,t)}return{translatedText:d=d.charAt(0).toUpperCase()+d.slice(1),sourceLanguage:s,targetLanguage:t}}async function o(e,t){let a=await s(e.review,t),i=e.title;return e.title&&(i=(await s(e.title,t)).translatedText),{name:e.name,location:e.location,review:a.translatedText,title:i}}},12909:(e,t,a)=>{"use strict";a.d(t,{Qq:()=>h,Tq:()=>p,bS:()=>d,fF:()=>u,mU:()=>l});var i=a(85663),n=a(43205),r=a.n(n);let s=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function c(e,t){try{return await i.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function l(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),a=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await c(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return r().sign(t,s,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,a=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:a,expiresAt:a+864e5,lastActivity:a}),function(){let e=Date.now();for(let[t,a]of o.entries())e>a.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function u(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=r().verify(e,s);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let a=Date.now();return a>t.expiresAt?(o.delete(e),null):(t.lastActivity=a,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let m=new Map;function h(e){let t=Date.now(),a=m.get(e);return!a||t-a.lastAttempt>9e5?(m.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):a.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-a.lastAttempt)}:(a.count++,a.lastAttempt=t,m.set(e,a),{allowed:!0,remainingAttempts:5-a.count})}function p(e){m.delete(e)}},67462:(e,t,a)=>{"use strict";a.d(t,{CE:()=>p,RV:()=>v,Ve:()=>g,WD:()=>y,kD:()=>f,zu:()=>h});var i=a(29021),n=a(33873),r=a.n(n);let s=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,o=r().join(process.cwd(),"data","reviews.json"),c=r().join(process.cwd(),"data"),l=[],d=[{id:"sample-1",name:"Kwame Mensah",location:"Accra, Ghana",email:"<EMAIL>",rating:5,title:"Excellent Service and Quality Vehicle",review:"I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!",vehiclePurchased:"Toyota Voxy 2015",purchaseDate:"2024-01",locale:"en",submittedAt:"2024-01-15T10:30:00.000Z",status:"approved",images:[],titleEn:"Excellent Service and Quality Vehicle",titleJa:"優れたサービスと高品質な車両",reviewEn:"I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!",reviewJa:"EBAM Motorsを通じてトヨタ ヴォクシーを購入しましたが、全プロセスがスムーズでプロフェッショナルでした。車は説明通りの優れた状態で到着しました。チームは私のすべての質問に迅速に対応し、配送プロセスを手間なく進めてくれました。強くお勧めします！"},{id:"sample-2",name:"Akosua Boateng",location:"Kumasi, Ghana",email:"<EMAIL>",rating:5,title:"Professional and Trustworthy",review:"EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!",vehiclePurchased:"Honda Fit 2016",purchaseDate:"2024-02",locale:"en",submittedAt:"2024-02-10T14:20:00.000Z",status:"approved",images:[],titleEn:"Professional and Trustworthy",titleJa:"プロフェッショナルで信頼できる",reviewEn:"EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!",reviewJa:"EBAM Motorsは私の日常通勤に最適なホンダ フィットを見つけるのを手伝ってくれました。彼らのチームは車両について非常に知識が豊富で、各車について詳細な情報を提供してくれました。配送は迅速で、車は完璧な状態で到着しました。全体的に素晴らしい体験でした！"}];async function u(){try{await i.promises.access(c)}catch{await i.promises.mkdir(c,{recursive:!0})}}async function m(){try{await i.promises.access(o)}catch{await u(),await i.promises.writeFile(o,JSON.stringify(d,null,2),"utf8")}}async function h(){if(s)return 0===l.length&&(l=[...d]),l;try{await m();let e=await i.promises.readFile(o,"utf8");return JSON.parse(e)}catch(e){return console.error("Error reading reviews:",e),d}}async function p(e){if(s){l=[...e];return}try{await u(),await i.promises.writeFile(o,JSON.stringify(e,null,2),"utf8")}catch(e){throw console.error("Error saving reviews:",e),Error("Failed to save reviews")}}async function f(e){let t=await h(),a={...e,id:Date.now().toString()+"-"+Math.random().toString(36).substr(2,9)};return t.push(a),await p(t),a}async function y(e,t){let a=await h(),i=a.findIndex(t=>t.id===e);return -1!==i&&(a[i].status=t,await p(a),!0)}async function w(e){return(await h()).filter(t=>t.status===e)}async function g(){return w("approved")}async function v(e){return(await h()).find(t=>t.id===e)||null}},77268:(e,t,a)=>{"use strict";a.d(t,{iY:()=>n}),a(32190);var i=a(12909);function n(e,t){let a=e.headers.get("authorization"),n=e.cookies.get("admin_session")?.value,r=(0,i.fF)(a,n);if(r.isValid)return{isValid:!0,adminId:r.adminId,method:"token/session"};let s=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return s&&s===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},96487:()=>{}};