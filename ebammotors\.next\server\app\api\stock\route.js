(()=>{var e={};e.id=5548,e.ids=[5548],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11762:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>y,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var r={};a.r(r),a.d(r,{GET:()=>l});var s=a(96559),o=a(48088),n=a(37719),i=a(32190),c=a(17198);async function l(){try{let e=await (0,c.l)();return i.NextResponse.json(e)}catch(e){return console.error("Error fetching stock data:",e),i.NextResponse.json({error:"Failed to fetch stock data"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/stock/route",pathname:"/api/stock",filename:"route",bundlePath:"app/api/stock/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\stock\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:y}=u;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}},17198:(e,t,a)=>{"use strict";a.d(t,{l:()=>c});var r=a(33873),s=a.n(r);let o={"toyota-voxy":{name:"Toyota Voxy",basePrice:3e5,seats:"8-seater",transmission:"Automatic",fuelType:"Gasoline"},"toyota-noah":{name:"Toyota Noah",basePrice:35e4,seats:"8-seater",transmission:"Automatic",fuelType:"Gasoline"},"toyota-sienta":{name:"Toyota Sienta",basePrice:32e4,seats:"7-seater",transmission:"CVT",fuelType:"Hybrid"},"toyota-vitz":{name:"Toyota Vitz",basePrice:325e3,seats:"5-seater",transmission:"Manual",fuelType:"Gasoline"},"toyota-yaris":{name:"Toyota Yaris",basePrice:55e4,seats:"5-seater",transmission:"CVT",fuelType:"Hybrid"}},n={yearAdjustment:{old:-5e3,new:5e3}};function i(){return[{id:1,category:"cars",title:"Toyota Voxy 2015",price:"\xa5450,000",location:"Japan",status:"Available",image:"/car-models/voxy/voxy-2015-001/1.jpg",images:["/car-models/voxy/voxy-2015-001/1.jpg","/car-models/voxy/voxy-2015-001/2.jpg"],specs:["8 seats","CVT","85k km","Gasoline","Excellent"],carId:"voxy-2015-001",year:2015,mileage:85e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Excellent"},{id:2,category:"cars",title:"Toyota Noah 2016",price:"\xa5480,000",location:"Japan",status:"Available",image:"/car-models/noah/noah-2016-001/1.jpg",images:["/car-models/noah/noah-2016-001/1.jpg","/car-models/noah/noah-2016-001/2.jpg"],specs:["8 seats","CVT","72k km","Gasoline","Very Good"],carId:"noah-2016-001",year:2016,mileage:72e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Very Good"},{id:3,category:"cars",title:"Toyota Sienta 2017",price:"\xa5520,000",location:"Japan",status:"Available",image:"/car-models/sienta/sienta-2017-001/1.jpg",images:["/car-models/sienta/sienta-2017-001/1.jpg","/car-models/sienta/sienta-2017-001/2.jpg"],specs:["7 seats","CVT","58k km","Gasoline","Excellent"],carId:"sienta-2017-001",year:2017,mileage:58e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Excellent"}]}async function c(){let e=[],t=s().join(process.cwd(),"public","car-models");try{let r=await Promise.resolve().then(a.t.bind(a,29021,23));if(!r.existsSync(t))return console.warn("Car models directory not found:",t),i();let c=r.readdirSync(t),l=1;for(let a of c){let i=s().join(t,a);if(!r.statSync(i).isDirectory())continue;let c=o[a];if(c)for(let t of r.readdirSync(i)){let u=s().join(i,t);if(!r.statSync(u).isDirectory())continue;let d=r.readdirSync(u).filter(e=>[".jpg",".jpeg",".JPG",".JPEG",".png",".PNG"].includes(s().extname(e))).sort();if(0===d.length)continue;let p=function(e){let t=e.match(/(\d{4})/);return t?parseInt(t[1]):2010}(t),y=function(e){let t=0;for(let a=0;a<e.length;a++)t=(t<<5)-t+e.charCodeAt(a),t&=t;return Math.floor(Math.abs(t)/0x7fffffff*9e4+3e4)}(t),m=function(e){let t=["Excellent","Very Good","Good","Fair","Needs Work"],a=0;for(let t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t),a&=a;return t[Math.abs(a)%t.length]}(t),f=function(e,t){let a=o[e];if(!a)return 0;let r=a.basePrice;return t<=2010?r+=n.yearAdjustment.old:t>=2012&&(r+=n.yearAdjustment.new),Math.max(r,0)}(a,p),g=d.map(e=>`/car-models/${a}/${t}/${e}`),x=t.split("").reduce((e,t)=>e+t.charCodeAt(0),0),h=x%4==0,v=x%5==0,T=h?x%7:x%30+7,k=new Date;k.setDate(k.getDate()-T);let j={id:l++,category:"cars",title:`${c.name} ${p}`,price:`\xa5${f.toLocaleString()}`,location:"Japan",status:t.length%10==0?"Reserved":"Available",image:g[0],images:g,specs:[c.seats,c.transmission,`${Math.floor(y/1e3)}k km`,c.fuelType,m],carId:t,year:p,mileage:y,fuelType:c.fuelType,transmission:c.transmission,bodyCondition:m,addedDate:k.toISOString(),originalPrice:v?`\xa5${(f+2e4).toLocaleString()}`:void 0,stockQuantity:x%3+1,popularity:x%100+1};e.push(j)}}return e}catch(e){return console.error("Error generating stock from file system:",e),i()}}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580],()=>a(11762));module.exports=r})();