import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { updateReviewStatus, getReviewById } from '@/lib/reviewStorage';
import { emailService, ReviewNotificationData } from '@/lib/resendService';

export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { reviewId, status } = await request.json();

    if (!reviewId || !status) {
      return NextResponse.json(
        { success: false, message: 'Review ID and status are required' },
        { status: 400 }
      );
    }

    if (!['approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { success: false, message: 'Invalid status. Must be "approved" or "rejected"' },
        { status: 400 }
      );
    }

    // Get review data before updating (for email notification)
    const review = await getReviewById(reviewId);
    if (!review) {
      return NextResponse.json(
        { success: false, message: 'Review not found' },
        { status: 404 }
      );
    }

    // Update review status
    const updatedReview = await updateReviewStatus(reviewId, status);
    if (!updatedReview) {
      return NextResponse.json(
        { success: false, message: 'Failed to update review status' },
        { status: 500 }
      );
    }

    // Send email notification if review is approved
    if (status === 'approved' && review.email) {
      try {
        const reviewNotificationData: ReviewNotificationData = {
          customerName: review.name,
          vehicleTitle: review.vehiclePurchased || 'Vehicle',
          rating: review.rating,
          review: review.review,
          reviewDate: new Date(review.submittedAt).toLocaleDateString(),
          isApproval: true
        };

        await emailService.sendReviewApprovalNotification(review.email, reviewNotificationData);
        console.log('Review approval email sent to customer');
      } catch (emailError) {
        console.error('Failed to send review approval email:', emailError);
        // Don't fail the approval process if email fails
      }
    }

    return NextResponse.json({
      success: true,
      message: `Review ${status} successfully`,
      review: updatedReview
    });

  } catch (error) {
    console.error('Error moderating review:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to moderate review' },
      { status: 500 }
    );
  }
}
