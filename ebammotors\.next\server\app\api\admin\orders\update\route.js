(()=>{var e={};e.id=7695,e.ids=[7695],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>m,Tq:()=>f,bS:()=>d,fF:()=>l,mU:()=>c});var s=r(85663),n=r(43205),i=r.n(n);let a=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await s.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),r=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(t,a,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let e=Date.now();for(let[t,r]of o.entries())e>r.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function l(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=i().verify(e,a);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let r=Date.now();return r>t.expiresAt?(o.delete(e),null):(t.lastActivity=r,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),r=p.get(e);return!r||t-r.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-r.lastAttempt)}:(r.count++,r.lastAttempt=t,p.set(e,r),{allowed:!0,remainingAttempts:5-r.count})}function f(e){p.delete(e)}},23870:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(55511);let n={randomUUID:s.randomUUID},i=new Uint8Array(256),a=i.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(n.randomUUID&&!t&&!e)return n.randomUUID();let u=(e=e||{}).random??e.rng?.()??(a>i.length-16&&((0,s.randomFillSync)(i),a=0),i.slice(a,a+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=u[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(u)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30358:(e,t,r)=>{"use strict";r.d(t,{Dq:()=>U,HQ:()=>N,Q4:()=>v,Vd:()=>A,ee:()=>I,fS:()=>y,g5:()=>b,getOrderById:()=>x,iO:()=>D,iY:()=>S});var s=r(29021),n=r(33873),i=r.n(n),a=r(23870);let o=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,u=i().join(process.cwd(),"data"),c=i().join(u,"orders.json"),d=i().join(u,"invoices.json"),l=[],p=[];async function m(){if(!o)try{await s.promises.access(u)}catch{await s.promises.mkdir(u,{recursive:!0})}}async function f(){if(o)return l;try{await m();let e=await s.promises.readFile(c,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function g(e){if(o){l=e;return}await m(),await s.promises.writeFile(c,JSON.stringify(e,null,2))}async function h(){if(o)return p;try{await m();let e=await s.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function w(e){if(o){p=e;return}await m(),await s.promises.writeFile(d,JSON.stringify(e,null,2))}async function y(e){let t=await f(),r={...e,id:(0,a.A)(),orderNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`EB${e.slice(-6)}${t}`}(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await g(t),r}async function A(){return await f()}async function x(e){return(await f()).find(t=>t.id===e)||null}async function v(e){return(await f()).filter(t=>t.customerId===e)}async function S(e,t){let r=await f(),s=r.findIndex(t=>t.id===e);return -1!==s&&(r[s]={...r[s],...t,updatedAt:new Date().toISOString()},await g(r),!0)}async function I(e,t){let r=await x(e);if(!r)return!1;let s={...r.payment,...t};return await S(e,{payment:s})}async function N(e,t){let r=await x(e);if(!r)return!1;let s={...t,id:(0,a.A)()},n={...r.shipping,updates:[...r.shipping.updates,s]};return await S(e,{shipping:n})}async function D(e){let t=await h(),r={...e,id:(0,a.A)(),invoiceNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,3).toUpperCase();return`INV-${e.slice(-6)}-${t}`}()};return t.push(r),await w(t),r}async function b(e){return(await h()).find(t=>t.orderId===e)||null}async function U(e,t){let r=await h(),s=r.findIndex(t=>t.id===e);return -1!==s&&(r[s]={...r[s],...t},await w(r),!0)}},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66936:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{PATCH:()=>l});var n=r(96559),i=r(48088),a=r(37719),o=r(32190),u=r(77268),c=r(30358),d=r(16967);async function l(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{orderId:t,status:r,shippingUpdate:s,notes:n}=await e.json();if(!t||!r)return o.NextResponse.json({success:!1,message:"Order ID and status are required"},{status:400});let i=await (0,c.getOrderById)(t);if(!i)return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});let a={status:r,updatedAt:new Date().toISOString()};if(n&&(a.notes=n),s&&(a.shipping={...i.shipping,status:s.status||i.shipping.status,trackingNumber:s.trackingNumber||i.shipping.trackingNumber,updates:[...i.shipping.updates,{status:s.status||r,message:s.message||`Order status updated to ${r}`,timestamp:new Date().toISOString(),location:s.location||null}]}),!await (0,c.iY)(t,a))return o.NextResponse.json({success:!1,message:"Failed to update order"},{status:500});try{let e=await (0,c.getOrderById)(t);if(e){let t={customerName:e.customerInfo.name,orderNumber:e.orderNumber,vehicleTitle:e.vehicle.title,newStatus:r,trackingNumber:e.shipping.trackingNumber,estimatedDelivery:e.shipping.estimatedDelivery,orderUrl:`${process.env.NEXT_PUBLIC_BASE_URL}/tracking?order=${e.orderNumber}`};await d.gm.sendOrderStatusUpdate(e.customerInfo.email,t)}}catch(e){console.error("Failed to send status update email:",e)}return o.NextResponse.json({success:!0,message:"Order updated successfully"})}catch(e){return console.error("Error updating order:",e),o.NextResponse.json({success:!1,message:"Failed to update order"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/orders/update/route",pathname:"/api/admin/orders/update",filename:"route",bundlePath:"app/api/admin/orders/update/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\orders\\update\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:g}=p;function h(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},77268:(e,t,r)=>{"use strict";r.d(t,{iY:()=>n}),r(32190);var s=r(12909);function n(e,t){let r=e.headers.get("authorization"),n=e.cookies.get("admin_session")?.value,i=(0,s.fF)(r,n);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let a=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return a&&a===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7696,6967],()=>r(66936));module.exports=s})();