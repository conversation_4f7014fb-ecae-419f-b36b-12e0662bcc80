import Link from 'next/link';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';

export default async function HowItWorksPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  const sellerSteps = [
    {
      step: 1,
      title: messages.howItWorks.sellStep1,
      description: locale === 'en' 
        ? 'Reach out to us via phone, WhatsApp, or email with details about your items. We handle cars, furniture, electronics, and more.'
        : 'お電話、WhatsApp、またはメールで商品の詳細をお知らせください。車、家具、電化製品など幅広く対応いたします。',
      icon: '📞'
    },
    {
      step: 2,
      title: messages.howItWorks.sellStep2,
      description: locale === 'en'
        ? 'Our team will inspect your items or discuss pricing based on photos and descriptions you provide.'
        : '当社のチームが商品を査定するか、お客様からの写真や説明に基づいて価格を相談いたします。',
      icon: '🔍'
    },
    {
      step: 3,
      title: messages.howItWorks.sellStep3,
      description: locale === 'en'
        ? 'We arrange collection at your convenience and provide immediate payment via cash or bank transfer.'
        : 'お客様のご都合に合わせて回収を手配し、現金または銀行振込で即座にお支払いいたします。',
      icon: '💰'
    }
  ];

  const buyerSteps = [
    {
      step: 1,
      title: messages.howItWorks.buyStep1,
      description: locale === 'en'
        ? 'Browse our inventory online or contact our agents in Ghana for current available items and pricing.'
        : 'オンラインで在庫をご覧いただくか、ガーナの代理店に現在の在庫と価格についてお問い合わせください。',
      icon: '🔍'
    },
    {
      step: 2,
      title: messages.howItWorks.buyStep2,
      description: locale === 'en'
        ? 'Make your selection and complete the purchase process. We accept various payment methods for your convenience.'
        : '商品を選択し、購入手続きを完了してください。お客様の利便性のため、様々な支払い方法を受け付けています。',
      icon: '💳'
    },
    {
      step: 3,
      title: messages.howItWorks.buyStep3,
      description: locale === 'en'
        ? 'Your items are shipped from Japan and available for pickup at our designated locations in Ghana or across Africa.'
        : '商品は日本から発送され、ガーナまたはアフリカ各地の指定場所でお受け取りいただけます。',
      icon: '🚢'
    },
    {
      step: 4,
      title: messages.howItWorks.buyStep4,
      description: locale === 'en'
        ? 'We provide ongoing support after your purchase to ensure your complete satisfaction with our service.'
        : 'ご購入後も継続的なサポートを提供し、当社のサービスにご満足いただけるよう努めています。',
      icon: '🤝'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-heading font-bold mb-6">
            {messages.navigation.howItWorks}
          </h1>
          <p className="text-xl lg:text-2xl text-primary-100 max-w-4xl mx-auto">
            {locale === 'en'
              ? 'Simple, transparent process for both sellers in Japan and buyers in Ghana & Africa'
              : '日本の売り手とガーナ・アフリカの買い手の両方にとってシンプルで透明なプロセス'
            }
          </p>
        </div>
      </section>

      {/* For Sellers Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {messages.howItWorks.forSellers}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'Turn your unwanted items into cash with our hassle-free collection service'
                : '手間のかからない回収サービスで不要品を現金に変えましょう'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {sellerSteps.map((step) => (
              <div key={step.step} className="relative">
                <div className="bg-primary-50 rounded-2xl p-8 text-center h-full">
                  <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span className="text-3xl">{step.icon}</span>
                  </div>
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-secondary-500 rounded-full flex items-center justify-center">
                    <span className="text-neutral-900 font-bold text-sm">{step.step}</span>
                  </div>
                  <h3 className="text-xl font-heading font-bold text-primary-800 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-neutral-700 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href={`/${locale}/suppliers`}
              className="inline-block bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {locale === 'en' ? 'Start Selling Today' : '今すぐ売却を開始'}
            </Link>
          </div>
        </div>
      </section>

      {/* For Buyers Section */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {messages.howItWorks.forBuyers}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'Access quality Japanese goods with reliable shipping to Ghana and across Africa'
                : 'ガーナ・アフリカ全域への信頼できる配送で高品質な日本製品にアクセス'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {buyerSteps.map((step) => (
              <div key={step.step} className="relative">
                <div className="bg-white rounded-2xl p-8 text-center h-full shadow-lg">
                  <div className="w-16 h-16 bg-secondary-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span className="text-3xl">{step.icon}</span>
                  </div>
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{step.step}</span>
                  </div>
                  <h3 className="text-xl font-heading font-bold text-neutral-800 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-neutral-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href={`/${locale}/stock`}
                className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
              >
                {messages.homepage.viewInventory}
              </Link>
              <Link
                href={`/${locale}/buyers`}
                className="border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
              >
                {messages.navigation.buyers}
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {locale === 'en' ? 'Frequently Asked Questions' : 'よくある質問'}
            </h2>
          </div>

          <div className="space-y-8">
            <div className="bg-neutral-50 rounded-xl p-8">
              <h3 className="text-xl font-heading font-bold text-neutral-800 mb-4">
                {locale === 'en' ? 'How long does shipping take?' : '配送にはどのくらい時間がかかりますか？'}
              </h3>
              <p className="text-neutral-600 leading-relaxed">
                {locale === 'en'
                  ? 'Shipping from Japan to Ghana typically takes 4-6 weeks depending on the items and shipping method. We provide tracking information for all shipments.'
                  : '日本からガーナへの配送は、商品や配送方法によって通常4〜6週間かかります。すべての出荷に追跡情報を提供いたします。'
                }
              </p>
            </div>

            <div className="bg-neutral-50 rounded-xl p-8">
              <h3 className="text-xl font-heading font-bold text-neutral-800 mb-4">
                {locale === 'en' ? 'What payment methods do you accept?' : 'どのような支払い方法を受け付けていますか？'}
              </h3>
              <p className="text-neutral-600 leading-relaxed">
                {locale === 'en'
                  ? 'We accept bank transfers, mobile money, and cash payments. For sellers in Japan, we offer immediate cash payment or bank transfer upon collection.'
                  : '銀行振込、モバイルマネー、現金支払いを受け付けています。日本の売り手の方には、回収時に即座に現金支払いまたは銀行振込を提供いたします。'
                }
              </p>
            </div>

            <div className="bg-neutral-50 rounded-xl p-8">
              <h3 className="text-xl font-heading font-bold text-neutral-800 mb-4">
                {locale === 'en' ? 'Do you handle customs and import procedures?' : '税関や輸入手続きは対応していますか？'}
              </h3>
              <p className="text-neutral-600 leading-relaxed">
                {locale === 'en'
                  ? 'Yes, we handle all export documentation from Japan. For imports to Ghana, we work with local partners to assist with customs clearance.'
                  : 'はい、日本からの輸出書類はすべて当社で対応いたします。ガーナへの輸入については、現地パートナーと協力して通関手続きをサポートいたします。'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-heading font-bold mb-6">
            {locale === 'en' ? 'Ready to Get Started?' : '始める準備はできましたか？'}
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
            {locale === 'en'
              ? 'Whether you want to sell your items or buy quality goods, we make the process simple and reliable.'
              : '商品を売りたい方も、高品質な商品を購入したい方も、当社がシンプルで信頼できるプロセスを提供いたします。'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href={`/${locale}/contact`}
              className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {messages.navigation.contact}
            </Link>
            <Link
              href={`/${locale}/about`}
              className="border-2 border-white text-white hover:bg-white hover:text-primary-800 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {messages.navigation.about}
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">EM</span>
                </div>
                <span className="font-heading font-bold text-xl">EBAM MOTORS</span>
              </div>
              <p className="text-neutral-300">
                {messages.about?.mission || 'Promote sustainable trade by giving used goods a second life where they are needed most.'}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">Contact Info</h3>
              <div className="space-y-2 text-neutral-300">
                <div>
                  <p>Japan: {messages.about?.phone || '080-6985-2864'}</p>
                  <p>Ghana: {messages.about?.ghanaPhone || '+233245375692'}</p>
                </div>
                <p>{messages.about?.email || '<EMAIL>'}</p>
                <div>
                  <p>Japan: {messages.about?.location || 'Japan, Saitama, Hanno City, Nagata'}</p>
                  <p>Ghana: {messages.about?.ghanaLocation || 'Kumasi, Ghana'}</p>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">{messages.contact?.quickLinks || 'Quick Links'}</h3>
              <div className="space-y-2">
                <Link href={`/${locale}/services`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.services}
                </Link>
                <Link href={`/${locale}/inventory`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.inventory}
                </Link>
                <Link href={`/${locale}/suppliers`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.suppliers}
                </Link>
              </div>
            </div>
          </div>
          <div className="border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 EBAM MOTORS. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
