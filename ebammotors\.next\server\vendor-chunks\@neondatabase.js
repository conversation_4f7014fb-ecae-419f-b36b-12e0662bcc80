"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@neondatabase";
exports.ids = ["vendor-chunks/@neondatabase"];
exports.modules = {

/***/ "(rsc)/./node_modules/@neondatabase/serverless/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@neondatabase/serverless/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ vn),\n/* harmony export */   ClientBase: () => (/* binding */ export_ClientBase),\n/* harmony export */   Connection: () => (/* binding */ export_Connection),\n/* harmony export */   DatabaseError: () => (/* binding */ export_DatabaseError),\n/* harmony export */   NeonDbError: () => (/* binding */ Ae),\n/* harmony export */   Pool: () => (/* binding */ Zs),\n/* harmony export */   Query: () => (/* binding */ export_Query),\n/* harmony export */   defaults: () => (/* binding */ export_defaults),\n/* harmony export */   neon: () => (/* binding */ Ys),\n/* harmony export */   neonConfig: () => (/* binding */ _e),\n/* harmony export */   types: () => (/* binding */ export_types)\n/* harmony export */ });\nvar to=Object.create;var Ce=Object.defineProperty;var ro=Object.getOwnPropertyDescriptor;var no=Object.getOwnPropertyNames;var io=Object.getPrototypeOf,so=Object.prototype.hasOwnProperty;var oo=(r,e,t)=>e in r?Ce(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):\nr[e]=t;var a=(r,e)=>Ce(r,\"name\",{value:e,configurable:!0});var z=(r,e)=>()=>(r&&(e=r(r=0)),e);var I=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),ie=(r,e)=>{for(var t in e)\nCe(r,t,{get:e[t],enumerable:!0})},An=(r,e,t,n)=>{if(e&&typeof e==\"object\"||typeof e==\n\"function\")for(let i of no(e))!so.call(r,i)&&i!==t&&Ce(r,i,{get:()=>e[i],enumerable:!(n=\nro(e,i))||n.enumerable});return r};var Te=(r,e,t)=>(t=r!=null?to(io(r)):{},An(e||!r||!r.__esModule?Ce(t,\"default\",{\nvalue:r,enumerable:!0}):t,r)),N=r=>An(Ce({},\"__esModule\",{value:!0}),r);var _=(r,e,t)=>oo(r,typeof e!=\"symbol\"?e+\"\":e,t);var In=I(nt=>{\"use strict\";p();nt.byteLength=uo;nt.toByteArray=ho;nt.fromByteArray=\npo;var ae=[],te=[],ao=typeof Uint8Array<\"u\"?Uint8Array:Array,Pt=\"ABCDEFGHIJKLMNO\\\nPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";for(ve=0,Cn=Pt.length;ve<Cn;++ve)\nae[ve]=Pt[ve],te[Pt.charCodeAt(ve)]=ve;var ve,Cn;te[45]=62;te[95]=63;function Tn(r){\nvar e=r.length;if(e%4>0)throw new Error(\"Invalid string. Length must be a multip\\\nle of 4\");var t=r.indexOf(\"=\");t===-1&&(t=e);var n=t===e?0:4-t%4;return[t,n]}a(Tn,\n\"getLens\");function uo(r){var e=Tn(r),t=e[0],n=e[1];return(t+n)*3/4-n}a(uo,\"byte\\\nLength\");function co(r,e,t){return(e+t)*3/4-t}a(co,\"_byteLength\");function ho(r){\nvar e,t=Tn(r),n=t[0],i=t[1],s=new ao(co(r,n,i)),o=0,u=i>0?n-4:n,c;for(c=0;c<u;c+=\n4)e=te[r.charCodeAt(c)]<<18|te[r.charCodeAt(c+1)]<<12|te[r.charCodeAt(c+2)]<<6|te[r.\ncharCodeAt(c+3)],s[o++]=e>>16&255,s[o++]=e>>8&255,s[o++]=e&255;return i===2&&(e=\nte[r.charCodeAt(c)]<<2|te[r.charCodeAt(c+1)]>>4,s[o++]=e&255),i===1&&(e=te[r.charCodeAt(\nc)]<<10|te[r.charCodeAt(c+1)]<<4|te[r.charCodeAt(c+2)]>>2,s[o++]=e>>8&255,s[o++]=\ne&255),s}a(ho,\"toByteArray\");function lo(r){return ae[r>>18&63]+ae[r>>12&63]+ae[r>>\n6&63]+ae[r&63]}a(lo,\"tripletToBase64\");function fo(r,e,t){for(var n,i=[],s=e;s<t;s+=\n3)n=(r[s]<<16&16711680)+(r[s+1]<<8&65280)+(r[s+2]&255),i.push(lo(n));return i.join(\n\"\")}a(fo,\"encodeChunk\");function po(r){for(var e,t=r.length,n=t%3,i=[],s=16383,o=0,\nu=t-n;o<u;o+=s)i.push(fo(r,o,o+s>u?u:o+s));return n===1?(e=r[t-1],i.push(ae[e>>2]+\nae[e<<4&63]+\"==\")):n===2&&(e=(r[t-2]<<8)+r[t-1],i.push(ae[e>>10]+ae[e>>4&63]+ae[e<<\n2&63]+\"=\")),i.join(\"\")}a(po,\"fromByteArray\")});var Pn=I(Bt=>{p();Bt.read=function(r,e,t,n,i){var s,o,u=i*8-n-1,c=(1<<u)-1,h=c>>\n1,l=-7,d=t?i-1:0,b=t?-1:1,C=r[e+d];for(d+=b,s=C&(1<<-l)-1,C>>=-l,l+=u;l>0;s=s*256+\nr[e+d],d+=b,l-=8);for(o=s&(1<<-l)-1,s>>=-l,l+=n;l>0;o=o*256+r[e+d],d+=b,l-=8);if(s===\n0)s=1-h;else{if(s===c)return o?NaN:(C?-1:1)*(1/0);o=o+Math.pow(2,n),s=s-h}return(C?\n-1:1)*o*Math.pow(2,s-n)};Bt.write=function(r,e,t,n,i,s){var o,u,c,h=s*8-i-1,l=(1<<\nh)-1,d=l>>1,b=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,C=n?0:s-1,B=n?1:-1,W=e<0||\ne===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,o=l):(o=Math.\nfloor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-o))<1&&(o--,c*=2),o+d>=1?e+=b/c:e+=\nb*Math.pow(2,1-d),e*c>=2&&(o++,c/=2),o+d>=l?(u=0,o=l):o+d>=1?(u=(e*c-1)*Math.pow(\n2,i),o=o+d):(u=e*Math.pow(2,d-1)*Math.pow(2,i),o=0));i>=8;r[t+C]=u&255,C+=B,u/=256,\ni-=8);for(o=o<<i|u,h+=i;h>0;r[t+C]=o&255,C+=B,o/=256,h-=8);r[t+C-B]|=W*128}});var $n=I(Le=>{\"use strict\";p();var Lt=In(),Pe=Pn(),Bn=typeof Symbol==\"function\"&&\ntypeof Symbol.for==\"function\"?Symbol.for(\"nodejs.util.inspect.custom\"):null;Le.Buffer=\nf;Le.SlowBuffer=So;Le.INSPECT_MAX_BYTES=50;var it=2147483647;Le.kMaxLength=it;f.\nTYPED_ARRAY_SUPPORT=yo();!f.TYPED_ARRAY_SUPPORT&&typeof console<\"u\"&&typeof console.\nerror==\"function\"&&console.error(\"This browser lacks typed array (Uint8Array) su\\\npport which is required by `buffer` v5.x. Use `buffer` v4.x if you require old b\\\nrowser support.\");function yo(){try{let r=new Uint8Array(1),e={foo:a(function(){\nreturn 42},\"foo\")};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(\nr,e),r.foo()===42}catch{return!1}}a(yo,\"typedArraySupport\");Object.defineProperty(\nf.prototype,\"parent\",{enumerable:!0,get:a(function(){if(f.isBuffer(this))return this.\nbuffer},\"get\")});Object.defineProperty(f.prototype,\"offset\",{enumerable:!0,get:a(\nfunction(){if(f.isBuffer(this))return this.byteOffset},\"get\")});function fe(r){if(r>\nit)throw new RangeError('The value \"'+r+'\" is invalid for option \"size\"');let e=new Uint8Array(\nr);return Object.setPrototypeOf(e,f.prototype),e}a(fe,\"createBuffer\");function f(r,e,t){\nif(typeof r==\"number\"){if(typeof e==\"string\")throw new TypeError('The \"string\" a\\\nrgument must be of type string. Received type number');return Dt(r)}return Mn(r,\ne,t)}a(f,\"Buffer\");f.poolSize=8192;function Mn(r,e,t){if(typeof r==\"string\")return go(\nr,e);if(ArrayBuffer.isView(r))return wo(r);if(r==null)throw new TypeError(\"The f\\\nirst argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-l\\\nike Object. Received type \"+typeof r);if(ue(r,ArrayBuffer)||r&&ue(r.buffer,ArrayBuffer)||\ntypeof SharedArrayBuffer<\"u\"&&(ue(r,SharedArrayBuffer)||r&&ue(r.buffer,SharedArrayBuffer)))\nreturn Ft(r,e,t);if(typeof r==\"number\")throw new TypeError('The \"value\" argument\\\n must not be of type number. Received type number');let n=r.valueOf&&r.valueOf();\nif(n!=null&&n!==r)return f.from(n,e,t);let i=bo(r);if(i)return i;if(typeof Symbol<\n\"u\"&&Symbol.toPrimitive!=null&&typeof r[Symbol.toPrimitive]==\"function\")return f.\nfrom(r[Symbol.toPrimitive](\"string\"),e,t);throw new TypeError(\"The first argumen\\\nt must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. \\\nReceived type \"+typeof r)}a(Mn,\"from\");f.from=function(r,e,t){return Mn(r,e,t)};\nObject.setPrototypeOf(f.prototype,Uint8Array.prototype);Object.setPrototypeOf(f,\nUint8Array);function Dn(r){if(typeof r!=\"number\")throw new TypeError('\"size\" arg\\\nument must be of type number');if(r<0)throw new RangeError('The value \"'+r+'\" is\\\n invalid for option \"size\"')}a(Dn,\"assertSize\");function mo(r,e,t){return Dn(r),\nr<=0?fe(r):e!==void 0?typeof t==\"string\"?fe(r).fill(e,t):fe(r).fill(e):fe(r)}a(mo,\n\"alloc\");f.alloc=function(r,e,t){return mo(r,e,t)};function Dt(r){return Dn(r),fe(\nr<0?0:kt(r)|0)}a(Dt,\"allocUnsafe\");f.allocUnsafe=function(r){return Dt(r)};f.allocUnsafeSlow=\nfunction(r){return Dt(r)};function go(r,e){if((typeof e!=\"string\"||e===\"\")&&(e=\"\\\nutf8\"),!f.isEncoding(e))throw new TypeError(\"Unknown encoding: \"+e);let t=kn(r,e)|\n0,n=fe(t),i=n.write(r,e);return i!==t&&(n=n.slice(0,i)),n}a(go,\"fromString\");function Rt(r){\nlet e=r.length<0?0:kt(r.length)|0,t=fe(e);for(let n=0;n<e;n+=1)t[n]=r[n]&255;return t}\na(Rt,\"fromArrayLike\");function wo(r){if(ue(r,Uint8Array)){let e=new Uint8Array(r);\nreturn Ft(e.buffer,e.byteOffset,e.byteLength)}return Rt(r)}a(wo,\"fromArrayView\");\nfunction Ft(r,e,t){if(e<0||r.byteLength<e)throw new RangeError('\"offset\" is outs\\\nide of buffer bounds');if(r.byteLength<e+(t||0))throw new RangeError('\"length\" i\\\ns outside of buffer bounds');let n;return e===void 0&&t===void 0?n=new Uint8Array(\nr):t===void 0?n=new Uint8Array(r,e):n=new Uint8Array(r,e,t),Object.setPrototypeOf(\nn,f.prototype),n}a(Ft,\"fromArrayBuffer\");function bo(r){if(f.isBuffer(r)){let e=kt(\nr.length)|0,t=fe(e);return t.length===0||r.copy(t,0,0,e),t}if(r.length!==void 0)\nreturn typeof r.length!=\"number\"||Ot(r.length)?fe(0):Rt(r);if(r.type===\"Buffer\"&&\nArray.isArray(r.data))return Rt(r.data)}a(bo,\"fromObject\");function kt(r){if(r>=\nit)throw new RangeError(\"Attempt to allocate Buffer larger than maximum size: 0x\"+\nit.toString(16)+\" bytes\");return r|0}a(kt,\"checked\");function So(r){return+r!=r&&\n(r=0),f.alloc(+r)}a(So,\"SlowBuffer\");f.isBuffer=a(function(e){return e!=null&&e.\n_isBuffer===!0&&e!==f.prototype},\"isBuffer\");f.compare=a(function(e,t){if(ue(e,Uint8Array)&&\n(e=f.from(e,e.offset,e.byteLength)),ue(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),\n!f.isBuffer(e)||!f.isBuffer(t))throw new TypeError('The \"buf1\", \"buf2\" arguments\\\n must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,i=t.\nlength;for(let s=0,o=Math.min(n,i);s<o;++s)if(e[s]!==t[s]){n=e[s],i=t[s];break}return n<\ni?-1:i<n?1:0},\"compare\");f.isEncoding=a(function(e){switch(String(e).toLowerCase()){case\"\\\nhex\":case\"utf8\":case\"utf-8\":case\"ascii\":case\"latin1\":case\"binary\":case\"base64\":case\"\\\nucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return!0;default:return!1}},\"isEn\\\ncoding\");f.concat=a(function(e,t){if(!Array.isArray(e))throw new TypeError('\"lis\\\nt\" argument must be an Array of Buffers');if(e.length===0)return f.alloc(0);let n;\nif(t===void 0)for(t=0,n=0;n<e.length;++n)t+=e[n].length;let i=f.allocUnsafe(t),s=0;\nfor(n=0;n<e.length;++n){let o=e[n];if(ue(o,Uint8Array))s+o.length>i.length?(f.isBuffer(\no)||(o=f.from(o)),o.copy(i,s)):Uint8Array.prototype.set.call(i,o,s);else if(f.isBuffer(\no))o.copy(i,s);else throw new TypeError('\"list\" argument must be an Array of Buf\\\nfers');s+=o.length}return i},\"concat\");function kn(r,e){if(f.isBuffer(r))return r.\nlength;if(ArrayBuffer.isView(r)||ue(r,ArrayBuffer))return r.byteLength;if(typeof r!=\n\"string\")throw new TypeError('The \"string\" argument must be one of type string, \\\nBuffer, or ArrayBuffer. Received type '+typeof r);let t=r.length,n=arguments.length>\n2&&arguments[2]===!0;if(!n&&t===0)return 0;let i=!1;for(;;)switch(e){case\"ascii\":case\"\\\nlatin1\":case\"binary\":return t;case\"utf8\":case\"utf-8\":return Mt(r).length;case\"uc\\\ns2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return t*2;case\"hex\":return t>>>1;case\"\\\nbase64\":return Gn(r).length;default:if(i)return n?-1:Mt(r).length;e=(\"\"+e).toLowerCase(),\ni=!0}}a(kn,\"byteLength\");f.byteLength=kn;function xo(r,e,t){let n=!1;if((e===void 0||\ne<0)&&(e=0),e>this.length||((t===void 0||t>this.length)&&(t=this.length),t<=0)||\n(t>>>=0,e>>>=0,t<=e))return\"\";for(r||(r=\"utf8\");;)switch(r){case\"hex\":return Lo(\nthis,e,t);case\"utf8\":case\"utf-8\":return On(this,e,t);case\"ascii\":return Po(this,\ne,t);case\"latin1\":case\"binary\":return Bo(this,e,t);case\"base64\":return To(this,e,\nt);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return Ro(this,e,t);default:\nif(n)throw new TypeError(\"Unknown encoding: \"+r);r=(r+\"\").toLowerCase(),n=!0}}a(\nxo,\"slowToString\");f.prototype._isBuffer=!0;function Ee(r,e,t){let n=r[e];r[e]=r[t],\nr[t]=n}a(Ee,\"swap\");f.prototype.swap16=a(function(){let e=this.length;if(e%2!==0)\nthrow new RangeError(\"Buffer size must be a multiple of 16-bits\");for(let t=0;t<\ne;t+=2)Ee(this,t,t+1);return this},\"swap16\");f.prototype.swap32=a(function(){let e=this.\nlength;if(e%4!==0)throw new RangeError(\"Buffer size must be a multiple of 32-bit\\\ns\");for(let t=0;t<e;t+=4)Ee(this,t,t+3),Ee(this,t+1,t+2);return this},\"swap32\");\nf.prototype.swap64=a(function(){let e=this.length;if(e%8!==0)throw new RangeError(\n\"Buffer size must be a multiple of 64-bits\");for(let t=0;t<e;t+=8)Ee(this,t,t+7),\nEe(this,t+1,t+6),Ee(this,t+2,t+5),Ee(this,t+3,t+4);return this},\"swap64\");f.prototype.\ntoString=a(function(){let e=this.length;return e===0?\"\":arguments.length===0?On(\nthis,0,e):xo.apply(this,arguments)},\"toString\");f.prototype.toLocaleString=f.prototype.\ntoString;f.prototype.equals=a(function(e){if(!f.isBuffer(e))throw new TypeError(\n\"Argument must be a Buffer\");return this===e?!0:f.compare(this,e)===0},\"equals\");\nf.prototype.inspect=a(function(){let e=\"\",t=Le.INSPECT_MAX_BYTES;return e=this.toString(\n\"hex\",0,t).replace(/(.{2})/g,\"$1 \").trim(),this.length>t&&(e+=\" ... \"),\"<Buffer \"+\ne+\">\"},\"inspect\");Bn&&(f.prototype[Bn]=f.prototype.inspect);f.prototype.compare=\na(function(e,t,n,i,s){if(ue(e,Uint8Array)&&(e=f.from(e,e.offset,e.byteLength)),!f.\nisBuffer(e))throw new TypeError('The \"target\" argument must be one of type Buffe\\\nr or Uint8Array. Received type '+typeof e);if(t===void 0&&(t=0),n===void 0&&(n=e?\ne.length:0),i===void 0&&(i=0),s===void 0&&(s=this.length),t<0||n>e.length||i<0||\ns>this.length)throw new RangeError(\"out of range index\");if(i>=s&&t>=n)return 0;\nif(i>=s)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,i>>>=0,s>>>=0,this===e)return 0;\nlet o=s-i,u=n-t,c=Math.min(o,u),h=this.slice(i,s),l=e.slice(t,n);for(let d=0;d<c;++d)\nif(h[d]!==l[d]){o=h[d],u=l[d];break}return o<u?-1:u<o?1:0},\"compare\");function Un(r,e,t,n,i){\nif(r.length===0)return-1;if(typeof t==\"string\"?(n=t,t=0):t>2147483647?t=2147483647:\nt<-2147483648&&(t=-2147483648),t=+t,Ot(t)&&(t=i?0:r.length-1),t<0&&(t=r.length+t),\nt>=r.length){if(i)return-1;t=r.length-1}else if(t<0)if(i)t=0;else return-1;if(typeof e==\n\"string\"&&(e=f.from(e,n)),f.isBuffer(e))return e.length===0?-1:Ln(r,e,t,n,i);if(typeof e==\n\"number\")return e=e&255,typeof Uint8Array.prototype.indexOf==\"function\"?i?Uint8Array.\nprototype.indexOf.call(r,e,t):Uint8Array.prototype.lastIndexOf.call(r,e,t):Ln(r,\n[e],t,n,i);throw new TypeError(\"val must be string, number or Buffer\")}a(Un,\"bid\\\nirectionalIndexOf\");function Ln(r,e,t,n,i){let s=1,o=r.length,u=e.length;if(n!==\nvoid 0&&(n=String(n).toLowerCase(),n===\"ucs2\"||n===\"ucs-2\"||n===\"utf16le\"||n===\"\\\nutf-16le\")){if(r.length<2||e.length<2)return-1;s=2,o/=2,u/=2,t/=2}function c(l,d){\nreturn s===1?l[d]:l.readUInt16BE(d*s)}a(c,\"read\");let h;if(i){let l=-1;for(h=t;h<\no;h++)if(c(r,h)===c(e,l===-1?0:h-l)){if(l===-1&&(l=h),h-l+1===u)return l*s}else l!==\n-1&&(h-=h-l),l=-1}else for(t+u>o&&(t=o-u),h=t;h>=0;h--){let l=!0;for(let d=0;d<u;d++)\nif(c(r,h+d)!==c(e,d)){l=!1;break}if(l)return h}return-1}a(Ln,\"arrayIndexOf\");f.prototype.\nincludes=a(function(e,t,n){return this.indexOf(e,t,n)!==-1},\"includes\");f.prototype.\nindexOf=a(function(e,t,n){return Un(this,e,t,n,!0)},\"indexOf\");f.prototype.lastIndexOf=\na(function(e,t,n){return Un(this,e,t,n,!1)},\"lastIndexOf\");function vo(r,e,t,n){\nt=Number(t)||0;let i=r.length-t;n?(n=Number(n),n>i&&(n=i)):n=i;let s=e.length;n>\ns/2&&(n=s/2);let o;for(o=0;o<n;++o){let u=parseInt(e.substr(o*2,2),16);if(Ot(u))\nreturn o;r[t+o]=u}return o}a(vo,\"hexWrite\");function Eo(r,e,t,n){return st(Mt(e,\nr.length-t),r,t,n)}a(Eo,\"utf8Write\");function _o(r,e,t,n){return st(ko(e),r,t,n)}\na(_o,\"asciiWrite\");function Ao(r,e,t,n){return st(Gn(e),r,t,n)}a(Ao,\"base64Write\");\nfunction Co(r,e,t,n){return st(Uo(e,r.length-t),r,t,n)}a(Co,\"ucs2Write\");f.prototype.\nwrite=a(function(e,t,n,i){if(t===void 0)i=\"utf8\",n=this.length,t=0;else if(n===void 0&&\ntypeof t==\"string\")i=t,n=this.length,t=0;else if(isFinite(t))t=t>>>0,isFinite(n)?\n(n=n>>>0,i===void 0&&(i=\"utf8\")):(i=n,n=void 0);else throw new Error(\"Buffer.wri\\\nte(string, encoding, offset[, length]) is no longer supported\");let s=this.length-\nt;if((n===void 0||n>s)&&(n=s),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError(\n\"Attempt to write outside buffer bounds\");i||(i=\"utf8\");let o=!1;for(;;)switch(i){case\"\\\nhex\":return vo(this,e,t,n);case\"utf8\":case\"utf-8\":return Eo(this,e,t,n);case\"asc\\\nii\":case\"latin1\":case\"binary\":return _o(this,e,t,n);case\"base64\":return Ao(this,\ne,t,n);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return Co(this,e,t,n);default:\nif(o)throw new TypeError(\"Unknown encoding: \"+i);i=(\"\"+i).toLowerCase(),o=!0}},\"\\\nwrite\");f.prototype.toJSON=a(function(){return{type:\"Buffer\",data:Array.prototype.\nslice.call(this._arr||this,0)}},\"toJSON\");function To(r,e,t){return e===0&&t===r.\nlength?Lt.fromByteArray(r):Lt.fromByteArray(r.slice(e,t))}a(To,\"base64Slice\");function On(r,e,t){\nt=Math.min(r.length,t);let n=[],i=e;for(;i<t;){let s=r[i],o=null,u=s>239?4:s>223?\n3:s>191?2:1;if(i+u<=t){let c,h,l,d;switch(u){case 1:s<128&&(o=s);break;case 2:c=\nr[i+1],(c&192)===128&&(d=(s&31)<<6|c&63,d>127&&(o=d));break;case 3:c=r[i+1],h=r[i+\n2],(c&192)===128&&(h&192)===128&&(d=(s&15)<<12|(c&63)<<6|h&63,d>2047&&(d<55296||\nd>57343)&&(o=d));break;case 4:c=r[i+1],h=r[i+2],l=r[i+3],(c&192)===128&&(h&192)===\n128&&(l&192)===128&&(d=(s&15)<<18|(c&63)<<12|(h&63)<<6|l&63,d>65535&&d<1114112&&\n(o=d))}}o===null?(o=65533,u=1):o>65535&&(o-=65536,n.push(o>>>10&1023|55296),o=56320|\no&1023),n.push(o),i+=u}return Io(n)}a(On,\"utf8Slice\");var Rn=4096;function Io(r){\nlet e=r.length;if(e<=Rn)return String.fromCharCode.apply(String,r);let t=\"\",n=0;\nfor(;n<e;)t+=String.fromCharCode.apply(String,r.slice(n,n+=Rn));return t}a(Io,\"d\\\necodeCodePointsArray\");function Po(r,e,t){let n=\"\";t=Math.min(r.length,t);for(let i=e;i<\nt;++i)n+=String.fromCharCode(r[i]&127);return n}a(Po,\"asciiSlice\");function Bo(r,e,t){\nlet n=\"\";t=Math.min(r.length,t);for(let i=e;i<t;++i)n+=String.fromCharCode(r[i]);\nreturn n}a(Bo,\"latin1Slice\");function Lo(r,e,t){let n=r.length;(!e||e<0)&&(e=0),\n(!t||t<0||t>n)&&(t=n);let i=\"\";for(let s=e;s<t;++s)i+=Oo[r[s]];return i}a(Lo,\"he\\\nxSlice\");function Ro(r,e,t){let n=r.slice(e,t),i=\"\";for(let s=0;s<n.length-1;s+=\n2)i+=String.fromCharCode(n[s]+n[s+1]*256);return i}a(Ro,\"utf16leSlice\");f.prototype.\nslice=a(function(e,t){let n=this.length;e=~~e,t=t===void 0?n:~~t,e<0?(e+=n,e<0&&\n(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);let i=this.subarray(\ne,t);return Object.setPrototypeOf(i,f.prototype),i},\"slice\");function q(r,e,t){if(r%\n1!==0||r<0)throw new RangeError(\"offset is not uint\");if(r+e>t)throw new RangeError(\n\"Trying to access beyond buffer length\")}a(q,\"checkOffset\");f.prototype.readUintLE=\nf.prototype.readUIntLE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||q(e,t,this.length);let i=this[e],\ns=1,o=0;for(;++o<t&&(s*=256);)i+=this[e+o]*s;return i},\"readUIntLE\");f.prototype.\nreadUintBE=f.prototype.readUIntBE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||q(e,t,this.\nlength);let i=this[e+--t],s=1;for(;t>0&&(s*=256);)i+=this[e+--t]*s;return i},\"re\\\nadUIntBE\");f.prototype.readUint8=f.prototype.readUInt8=a(function(e,t){return e=\ne>>>0,t||q(e,1,this.length),this[e]},\"readUInt8\");f.prototype.readUint16LE=f.prototype.\nreadUInt16LE=a(function(e,t){return e=e>>>0,t||q(e,2,this.length),this[e]|this[e+\n1]<<8},\"readUInt16LE\");f.prototype.readUint16BE=f.prototype.readUInt16BE=a(function(e,t){\nreturn e=e>>>0,t||q(e,2,this.length),this[e]<<8|this[e+1]},\"readUInt16BE\");f.prototype.\nreadUint32LE=f.prototype.readUInt32LE=a(function(e,t){return e=e>>>0,t||q(e,4,this.\nlength),(this[e]|this[e+1]<<8|this[e+2]<<16)+this[e+3]*16777216},\"readUInt32LE\");\nf.prototype.readUint32BE=f.prototype.readUInt32BE=a(function(e,t){return e=e>>>0,\nt||q(e,4,this.length),this[e]*16777216+(this[e+1]<<16|this[e+2]<<8|this[e+3])},\"\\\nreadUInt32BE\");f.prototype.readBigUInt64LE=ge(a(function(e){e=e>>>0,Be(e,\"offset\");\nlet t=this[e],n=this[e+7];(t===void 0||n===void 0)&&We(e,this.length-8);let i=t+\nthis[++e]*2**8+this[++e]*2**16+this[++e]*2**24,s=this[++e]+this[++e]*2**8+this[++e]*\n2**16+n*2**24;return BigInt(i)+(BigInt(s)<<BigInt(32))},\"readBigUInt64LE\"));f.prototype.\nreadBigUInt64BE=ge(a(function(e){e=e>>>0,Be(e,\"offset\");let t=this[e],n=this[e+7];\n(t===void 0||n===void 0)&&We(e,this.length-8);let i=t*2**24+this[++e]*2**16+this[++e]*\n2**8+this[++e],s=this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n;return(BigInt(\ni)<<BigInt(32))+BigInt(s)},\"readBigUInt64BE\"));f.prototype.readIntLE=a(function(e,t,n){\ne=e>>>0,t=t>>>0,n||q(e,t,this.length);let i=this[e],s=1,o=0;for(;++o<t&&(s*=256);)\ni+=this[e+o]*s;return s*=128,i>=s&&(i-=Math.pow(2,8*t)),i},\"readIntLE\");f.prototype.\nreadIntBE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||q(e,t,this.length);let i=t,s=1,o=this[e+\n--i];for(;i>0&&(s*=256);)o+=this[e+--i]*s;return s*=128,o>=s&&(o-=Math.pow(2,8*t)),\no},\"readIntBE\");f.prototype.readInt8=a(function(e,t){return e=e>>>0,t||q(e,1,this.\nlength),this[e]&128?(255-this[e]+1)*-1:this[e]},\"readInt8\");f.prototype.readInt16LE=\na(function(e,t){e=e>>>0,t||q(e,2,this.length);let n=this[e]|this[e+1]<<8;return n&\n32768?n|4294901760:n},\"readInt16LE\");f.prototype.readInt16BE=a(function(e,t){e=e>>>\n0,t||q(e,2,this.length);let n=this[e+1]|this[e]<<8;return n&32768?n|4294901760:n},\n\"readInt16BE\");f.prototype.readInt32LE=a(function(e,t){return e=e>>>0,t||q(e,4,this.\nlength),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},\"readInt32LE\");f.prototype.\nreadInt32BE=a(function(e,t){return e=e>>>0,t||q(e,4,this.length),this[e]<<24|this[e+\n1]<<16|this[e+2]<<8|this[e+3]},\"readInt32BE\");f.prototype.readBigInt64LE=ge(a(function(e){\ne=e>>>0,Be(e,\"offset\");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&We(e,\nthis.length-8);let i=this[e+4]+this[e+5]*2**8+this[e+6]*2**16+(n<<24);return(BigInt(\ni)<<BigInt(32))+BigInt(t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24)},\"readB\\\nigInt64LE\"));f.prototype.readBigInt64BE=ge(a(function(e){e=e>>>0,Be(e,\"offset\");\nlet t=this[e],n=this[e+7];(t===void 0||n===void 0)&&We(e,this.length-8);let i=(t<<\n24)+this[++e]*2**16+this[++e]*2**8+this[++e];return(BigInt(i)<<BigInt(32))+BigInt(\nthis[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n)},\"readBigInt64BE\"));f.prototype.\nreadFloatLE=a(function(e,t){return e=e>>>0,t||q(e,4,this.length),Pe.read(this,e,\n!0,23,4)},\"readFloatLE\");f.prototype.readFloatBE=a(function(e,t){return e=e>>>0,\nt||q(e,4,this.length),Pe.read(this,e,!1,23,4)},\"readFloatBE\");f.prototype.readDoubleLE=\na(function(e,t){return e=e>>>0,t||q(e,8,this.length),Pe.read(this,e,!0,52,8)},\"r\\\neadDoubleLE\");f.prototype.readDoubleBE=a(function(e,t){return e=e>>>0,t||q(e,8,this.\nlength),Pe.read(this,e,!1,52,8)},\"readDoubleBE\");function Y(r,e,t,n,i,s){if(!f.isBuffer(\nr))throw new TypeError('\"buffer\" argument must be a Buffer instance');if(e>i||e<\ns)throw new RangeError('\"value\" argument is out of bounds');if(t+n>r.length)throw new RangeError(\n\"Index out of range\")}a(Y,\"checkInt\");f.prototype.writeUintLE=f.prototype.writeUIntLE=\na(function(e,t,n,i){if(e=+e,t=t>>>0,n=n>>>0,!i){let u=Math.pow(2,8*n)-1;Y(this,e,\nt,n,u,0)}let s=1,o=0;for(this[t]=e&255;++o<n&&(s*=256);)this[t+o]=e/s&255;return t+\nn},\"writeUIntLE\");f.prototype.writeUintBE=f.prototype.writeUIntBE=a(function(e,t,n,i){\nif(e=+e,t=t>>>0,n=n>>>0,!i){let u=Math.pow(2,8*n)-1;Y(this,e,t,n,u,0)}let s=n-1,\no=1;for(this[t+s]=e&255;--s>=0&&(o*=256);)this[t+s]=e/o&255;return t+n},\"writeUI\\\nntBE\");f.prototype.writeUint8=f.prototype.writeUInt8=a(function(e,t,n){return e=\n+e,t=t>>>0,n||Y(this,e,t,1,255,0),this[t]=e&255,t+1},\"writeUInt8\");f.prototype.writeUint16LE=\nf.prototype.writeUInt16LE=a(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,e,t,2,\n65535,0),this[t]=e&255,this[t+1]=e>>>8,t+2},\"writeUInt16LE\");f.prototype.writeUint16BE=\nf.prototype.writeUInt16BE=a(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,e,t,2,\n65535,0),this[t]=e>>>8,this[t+1]=e&255,t+2},\"writeUInt16BE\");f.prototype.writeUint32LE=\nf.prototype.writeUInt32LE=a(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,e,t,4,\n4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=e&255,t+\n4},\"writeUInt32LE\");f.prototype.writeUint32BE=f.prototype.writeUInt32BE=a(function(e,t,n){\nreturn e=+e,t=t>>>0,n||Y(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,\nthis[t+2]=e>>>8,this[t+3]=e&255,t+4},\"writeUInt32BE\");function Nn(r,e,t,n,i){Hn(\ne,n,i,r,t,7);let s=Number(e&BigInt(4294967295));r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,\nr[t++]=s,s=s>>8,r[t++]=s;let o=Number(e>>BigInt(32)&BigInt(4294967295));return r[t++]=\no,o=o>>8,r[t++]=o,o=o>>8,r[t++]=o,o=o>>8,r[t++]=o,t}a(Nn,\"wrtBigUInt64LE\");function qn(r,e,t,n,i){\nHn(e,n,i,r,t,7);let s=Number(e&BigInt(4294967295));r[t+7]=s,s=s>>8,r[t+6]=s,s=s>>\n8,r[t+5]=s,s=s>>8,r[t+4]=s;let o=Number(e>>BigInt(32)&BigInt(4294967295));return r[t+\n3]=o,o=o>>8,r[t+2]=o,o=o>>8,r[t+1]=o,o=o>>8,r[t]=o,t+8}a(qn,\"wrtBigUInt64BE\");f.\nprototype.writeBigUInt64LE=ge(a(function(e,t=0){return Nn(this,e,t,BigInt(0),BigInt(\n\"0xffffffffffffffff\"))},\"writeBigUInt64LE\"));f.prototype.writeBigUInt64BE=ge(a(function(e,t=0){\nreturn qn(this,e,t,BigInt(0),BigInt(\"0xffffffffffffffff\"))},\"writeBigUInt64BE\"));\nf.prototype.writeIntLE=a(function(e,t,n,i){if(e=+e,t=t>>>0,!i){let c=Math.pow(2,\n8*n-1);Y(this,e,t,n,c-1,-c)}let s=0,o=1,u=0;for(this[t]=e&255;++s<n&&(o*=256);)e<\n0&&u===0&&this[t+s-1]!==0&&(u=1),this[t+s]=(e/o>>0)-u&255;return t+n},\"writeIntL\\\nE\");f.prototype.writeIntBE=a(function(e,t,n,i){if(e=+e,t=t>>>0,!i){let c=Math.pow(\n2,8*n-1);Y(this,e,t,n,c-1,-c)}let s=n-1,o=1,u=0;for(this[t+s]=e&255;--s>=0&&(o*=\n256);)e<0&&u===0&&this[t+s+1]!==0&&(u=1),this[t+s]=(e/o>>0)-u&255;return t+n},\"w\\\nriteIntBE\");f.prototype.writeInt8=a(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,\ne,t,1,127,-128),e<0&&(e=255+e+1),this[t]=e&255,t+1},\"writeInt8\");f.prototype.writeInt16LE=\na(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,e,t,2,32767,-32768),this[t]=e&255,\nthis[t+1]=e>>>8,t+2},\"writeInt16LE\");f.prototype.writeInt16BE=a(function(e,t,n){\nreturn e=+e,t=t>>>0,n||Y(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=e&255,\nt+2},\"writeInt16BE\");f.prototype.writeInt32LE=a(function(e,t,n){return e=+e,t=t>>>\n0,n||Y(this,e,t,4,2147483647,-2147483648),this[t]=e&255,this[t+1]=e>>>8,this[t+2]=\ne>>>16,this[t+3]=e>>>24,t+4},\"writeInt32LE\");f.prototype.writeInt32BE=a(function(e,t,n){\nreturn e=+e,t=t>>>0,n||Y(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+\n1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4},\"writeIn\\\nt32BE\");f.prototype.writeBigInt64LE=ge(a(function(e,t=0){return Nn(this,e,t,-BigInt(\n\"0x8000000000000000\"),BigInt(\"0x7fffffffffffffff\"))},\"writeBigInt64LE\"));f.prototype.\nwriteBigInt64BE=ge(a(function(e,t=0){return qn(this,e,t,-BigInt(\"0x8000000000000\\\n000\"),BigInt(\"0x7fffffffffffffff\"))},\"writeBigInt64BE\"));function Qn(r,e,t,n,i,s){\nif(t+n>r.length)throw new RangeError(\"Index out of range\");if(t<0)throw new RangeError(\n\"Index out of range\")}a(Qn,\"checkIEEE754\");function Wn(r,e,t,n,i){return e=+e,t=\nt>>>0,i||Qn(r,e,t,4,34028234663852886e22,-34028234663852886e22),Pe.write(r,e,t,n,\n23,4),t+4}a(Wn,\"writeFloat\");f.prototype.writeFloatLE=a(function(e,t,n){return Wn(\nthis,e,t,!0,n)},\"writeFloatLE\");f.prototype.writeFloatBE=a(function(e,t,n){return Wn(\nthis,e,t,!1,n)},\"writeFloatBE\");function jn(r,e,t,n,i){return e=+e,t=t>>>0,i||Qn(\nr,e,t,8,17976931348623157e292,-17976931348623157e292),Pe.write(r,e,t,n,52,8),t+8}\na(jn,\"writeDouble\");f.prototype.writeDoubleLE=a(function(e,t,n){return jn(this,e,\nt,!0,n)},\"writeDoubleLE\");f.prototype.writeDoubleBE=a(function(e,t,n){return jn(\nthis,e,t,!1,n)},\"writeDoubleBE\");f.prototype.copy=a(function(e,t,n,i){if(!f.isBuffer(\ne))throw new TypeError(\"argument should be a Buffer\");if(n||(n=0),!i&&i!==0&&(i=\nthis.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<n&&(i=n),i===n||e.length===\n0||this.length===0)return 0;if(t<0)throw new RangeError(\"targetStart out of boun\\\nds\");if(n<0||n>=this.length)throw new RangeError(\"Index out of range\");if(i<0)throw new RangeError(\n\"sourceEnd out of bounds\");i>this.length&&(i=this.length),e.length-t<i-n&&(i=e.length-\nt+n);let s=i-n;return this===e&&typeof Uint8Array.prototype.copyWithin==\"functio\\\nn\"?this.copyWithin(t,n,i):Uint8Array.prototype.set.call(e,this.subarray(n,i),t),\ns},\"copy\");f.prototype.fill=a(function(e,t,n,i){if(typeof e==\"string\"){if(typeof t==\n\"string\"?(i=t,t=0,n=this.length):typeof n==\"string\"&&(i=n,n=this.length),i!==void 0&&\ntypeof i!=\"string\")throw new TypeError(\"encoding must be a string\");if(typeof i==\n\"string\"&&!f.isEncoding(i))throw new TypeError(\"Unknown encoding: \"+i);if(e.length===\n1){let o=e.charCodeAt(0);(i===\"utf8\"&&o<128||i===\"latin1\")&&(e=o)}}else typeof e==\n\"number\"?e=e&255:typeof e==\"boolean\"&&(e=Number(e));if(t<0||this.length<t||this.\nlength<n)throw new RangeError(\"Out of range index\");if(n<=t)return this;t=t>>>0,\nn=n===void 0?this.length:n>>>0,e||(e=0);let s;if(typeof e==\"number\")for(s=t;s<n;++s)\nthis[s]=e;else{let o=f.isBuffer(e)?e:f.from(e,i),u=o.length;if(u===0)throw new TypeError(\n'The value \"'+e+'\" is invalid for argument \"value\"');for(s=0;s<n-t;++s)this[s+t]=\no[s%u]}return this},\"fill\");var Ie={};function Ut(r,e,t){var n;Ie[r]=(n=class extends t{constructor(){\nsuper(),Object.defineProperty(this,\"message\",{value:e.apply(this,arguments),writable:!0,\nconfigurable:!0}),this.name=`${this.name} [${r}]`,this.stack,delete this.name}get code(){\nreturn r}set code(s){Object.defineProperty(this,\"code\",{configurable:!0,enumerable:!0,\nvalue:s,writable:!0})}toString(){return`${this.name} [${r}]: ${this.message}`}},\na(n,\"NodeError\"),n)}a(Ut,\"E\");Ut(\"ERR_BUFFER_OUT_OF_BOUNDS\",function(r){return r?\n`${r} is outside of buffer bounds`:\"Attempt to access memory outside buffer boun\\\nds\"},RangeError);Ut(\"ERR_INVALID_ARG_TYPE\",function(r,e){return`The \"${r}\" argum\\\nent must be of type number. Received type ${typeof e}`},TypeError);Ut(\"ERR_OUT_O\\\nF_RANGE\",function(r,e,t){let n=`The value of \"${r}\" is out of range.`,i=t;return Number.\nisInteger(t)&&Math.abs(t)>2**32?i=Fn(String(t)):typeof t==\"bigint\"&&(i=String(t),\n(t>BigInt(2)**BigInt(32)||t<-(BigInt(2)**BigInt(32)))&&(i=Fn(i)),i+=\"n\"),n+=` It\\\n must be ${e}. Received ${i}`,n},RangeError);function Fn(r){let e=\"\",t=r.length,\nn=r[0]===\"-\"?1:0;for(;t>=n+4;t-=3)e=`_${r.slice(t-3,t)}${e}`;return`${r.slice(0,\nt)}${e}`}a(Fn,\"addNumericalSeparator\");function Fo(r,e,t){Be(e,\"offset\"),(r[e]===\nvoid 0||r[e+t]===void 0)&&We(e,r.length-(t+1))}a(Fo,\"checkBounds\");function Hn(r,e,t,n,i,s){\nif(r>t||r<e){let o=typeof e==\"bigint\"?\"n\":\"\",u;throw s>3?e===0||e===BigInt(0)?u=\n`>= 0${o} and < 2${o} ** ${(s+1)*8}${o}`:u=`>= -(2${o} ** ${(s+1)*8-1}${o}) and \\\n< 2 ** ${(s+1)*8-1}${o}`:u=`>= ${e}${o} and <= ${t}${o}`,new Ie.ERR_OUT_OF_RANGE(\n\"value\",u,r)}Fo(n,i,s)}a(Hn,\"checkIntBI\");function Be(r,e){if(typeof r!=\"number\")\nthrow new Ie.ERR_INVALID_ARG_TYPE(e,\"number\",r)}a(Be,\"validateNumber\");function We(r,e,t){\nthrow Math.floor(r)!==r?(Be(r,t),new Ie.ERR_OUT_OF_RANGE(t||\"offset\",\"an integer\",\nr)):e<0?new Ie.ERR_BUFFER_OUT_OF_BOUNDS:new Ie.ERR_OUT_OF_RANGE(t||\"offset\",`>= ${t?\n1:0} and <= ${e}`,r)}a(We,\"boundsError\");var Mo=/[^+/0-9A-Za-z-_]/g;function Do(r){\nif(r=r.split(\"=\")[0],r=r.trim().replace(Mo,\"\"),r.length<2)return\"\";for(;r.length%\n4!==0;)r=r+\"=\";return r}a(Do,\"base64clean\");function Mt(r,e){e=e||1/0;let t,n=r.\nlength,i=null,s=[];for(let o=0;o<n;++o){if(t=r.charCodeAt(o),t>55295&&t<57344){if(!i){\nif(t>56319){(e-=3)>-1&&s.push(239,191,189);continue}else if(o+1===n){(e-=3)>-1&&\ns.push(239,191,189);continue}i=t;continue}if(t<56320){(e-=3)>-1&&s.push(239,191,\n189),i=t;continue}t=(i-55296<<10|t-56320)+65536}else i&&(e-=3)>-1&&s.push(239,191,\n189);if(i=null,t<128){if((e-=1)<0)break;s.push(t)}else if(t<2048){if((e-=2)<0)break;\ns.push(t>>6|192,t&63|128)}else if(t<65536){if((e-=3)<0)break;s.push(t>>12|224,t>>\n6&63|128,t&63|128)}else if(t<1114112){if((e-=4)<0)break;s.push(t>>18|240,t>>12&63|\n128,t>>6&63|128,t&63|128)}else throw new Error(\"Invalid code point\")}return s}a(\nMt,\"utf8ToBytes\");function ko(r){let e=[];for(let t=0;t<r.length;++t)e.push(r.charCodeAt(\nt)&255);return e}a(ko,\"asciiToBytes\");function Uo(r,e){let t,n,i,s=[];for(let o=0;o<\nr.length&&!((e-=2)<0);++o)t=r.charCodeAt(o),n=t>>8,i=t%256,s.push(i),s.push(n);return s}\na(Uo,\"utf16leToBytes\");function Gn(r){return Lt.toByteArray(Do(r))}a(Gn,\"base64T\\\noBytes\");function st(r,e,t,n){let i;for(i=0;i<n&&!(i+t>=e.length||i>=r.length);++i)\ne[i+t]=r[i];return i}a(st,\"blitBuffer\");function ue(r,e){return r instanceof e||\nr!=null&&r.constructor!=null&&r.constructor.name!=null&&r.constructor.name===e.name}\na(ue,\"isInstance\");function Ot(r){return r!==r}a(Ot,\"numberIsNaN\");var Oo=function(){\nlet r=\"0123456789abcdef\",e=new Array(256);for(let t=0;t<16;++t){let n=t*16;for(let i=0;i<\n16;++i)e[n+i]=r[t]+r[i]}return e}();function ge(r){return typeof BigInt>\"u\"?No:r}\na(ge,\"defineBigIntMethod\");function No(){throw new Error(\"BigInt not supported\")}\na(No,\"BufferBigIntNotDefined\")});var S,x,v,g,y,m,p=z(()=>{\"use strict\";S=globalThis,x=globalThis.setImmediate??(r=>setTimeout(\nr,0)),v=globalThis.clearImmediate??(r=>clearTimeout(r)),g=globalThis.crypto??{};\ng.subtle??(g.subtle={});y=typeof globalThis.Buffer==\"function\"&&typeof globalThis.\nBuffer.allocUnsafe==\"function\"?globalThis.Buffer:$n().Buffer,m=globalThis.process??\n{};m.env??(m.env={});try{m.nextTick(()=>{})}catch{let e=Promise.resolve();m.nextTick=\ne.then.bind(e)}});var we=I((Xc,Nt)=>{\"use strict\";p();var Re=typeof Reflect==\"object\"?Reflect:null,\nVn=Re&&typeof Re.apply==\"function\"?Re.apply:a(function(e,t,n){return Function.prototype.\napply.call(e,t,n)},\"ReflectApply\"),ot;Re&&typeof Re.ownKeys==\"function\"?ot=Re.ownKeys:\nObject.getOwnPropertySymbols?ot=a(function(e){return Object.getOwnPropertyNames(\ne).concat(Object.getOwnPropertySymbols(e))},\"ReflectOwnKeys\"):ot=a(function(e){return Object.\ngetOwnPropertyNames(e)},\"ReflectOwnKeys\");function qo(r){console&&console.warn&&\nconsole.warn(r)}a(qo,\"ProcessEmitWarning\");var zn=Number.isNaN||a(function(e){return e!==\ne},\"NumberIsNaN\");function L(){L.init.call(this)}a(L,\"EventEmitter\");Nt.exports=\nL;Nt.exports.once=Ho;L.EventEmitter=L;L.prototype._events=void 0;L.prototype._eventsCount=\n0;L.prototype._maxListeners=void 0;var Kn=10;function at(r){if(typeof r!=\"functi\\\non\")throw new TypeError('The \"listener\" argument must be of type Function. Recei\\\nved type '+typeof r)}a(at,\"checkListener\");Object.defineProperty(L,\"defaultMaxLi\\\nsteners\",{enumerable:!0,get:a(function(){return Kn},\"get\"),set:a(function(r){if(typeof r!=\n\"number\"||r<0||zn(r))throw new RangeError('The value of \"defaultMaxListeners\" is\\\n out of range. It must be a non-negative number. Received '+r+\".\");Kn=r},\"set\")});\nL.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this).\n_events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=\nthis._maxListeners||void 0};L.prototype.setMaxListeners=a(function(e){if(typeof e!=\n\"number\"||e<0||zn(e))throw new RangeError('The value of \"n\" is out of range. It \\\nmust be a non-negative number. Received '+e+\".\");return this._maxListeners=e,this},\n\"setMaxListeners\");function Yn(r){return r._maxListeners===void 0?L.defaultMaxListeners:\nr._maxListeners}a(Yn,\"_getMaxListeners\");L.prototype.getMaxListeners=a(function(){\nreturn Yn(this)},\"getMaxListeners\");L.prototype.emit=a(function(e){for(var t=[],\nn=1;n<arguments.length;n++)t.push(arguments[n]);var i=e===\"error\",s=this._events;\nif(s!==void 0)i=i&&s.error===void 0;else if(!i)return!1;if(i){var o;if(t.length>\n0&&(o=t[0]),o instanceof Error)throw o;var u=new Error(\"Unhandled error.\"+(o?\" (\"+\no.message+\")\":\"\"));throw u.context=o,u}var c=s[e];if(c===void 0)return!1;if(typeof c==\n\"function\")Vn(c,this,t);else for(var h=c.length,l=ti(c,h),n=0;n<h;++n)Vn(l[n],this,\nt);return!0},\"emit\");function Zn(r,e,t,n){var i,s,o;if(at(t),s=r._events,s===void 0?\n(s=r._events=Object.create(null),r._eventsCount=0):(s.newListener!==void 0&&(r.emit(\n\"newListener\",e,t.listener?t.listener:t),s=r._events),o=s[e]),o===void 0)o=s[e]=\nt,++r._eventsCount;else if(typeof o==\"function\"?o=s[e]=n?[t,o]:[o,t]:n?o.unshift(\nt):o.push(t),i=Yn(r),i>0&&o.length>i&&!o.warned){o.warned=!0;var u=new Error(\"Po\\\nssible EventEmitter memory leak detected. \"+o.length+\" \"+String(e)+\" listeners a\\\ndded. Use emitter.setMaxListeners() to increase limit\");u.name=\"MaxListenersExce\\\nededWarning\",u.emitter=r,u.type=e,u.count=o.length,qo(u)}return r}a(Zn,\"_addList\\\nener\");L.prototype.addListener=a(function(e,t){return Zn(this,e,t,!1)},\"addListe\\\nner\");L.prototype.on=L.prototype.addListener;L.prototype.prependListener=a(function(e,t){\nreturn Zn(this,e,t,!0)},\"prependListener\");function Qo(){if(!this.fired)return this.\ntarget.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?\nthis.listener.call(this.target):this.listener.apply(this.target,arguments)}a(Qo,\n\"onceWrapper\");function Jn(r,e,t){var n={fired:!1,wrapFn:void 0,target:r,type:e,\nlistener:t},i=Qo.bind(n);return i.listener=t,n.wrapFn=i,i}a(Jn,\"_onceWrap\");L.prototype.\nonce=a(function(e,t){return at(t),this.on(e,Jn(this,e,t)),this},\"once\");L.prototype.\nprependOnceListener=a(function(e,t){return at(t),this.prependListener(e,Jn(this,\ne,t)),this},\"prependOnceListener\");L.prototype.removeListener=a(function(e,t){var n,\ni,s,o,u;if(at(t),i=this._events,i===void 0)return this;if(n=i[e],n===void 0)return this;\nif(n===t||n.listener===t)--this._eventsCount===0?this._events=Object.create(null):\n(delete i[e],i.removeListener&&this.emit(\"removeListener\",e,n.listener||t));else if(typeof n!=\n\"function\"){for(s=-1,o=n.length-1;o>=0;o--)if(n[o]===t||n[o].listener===t){u=n[o].\nlistener,s=o;break}if(s<0)return this;s===0?n.shift():Wo(n,s),n.length===1&&(i[e]=\nn[0]),i.removeListener!==void 0&&this.emit(\"removeListener\",e,u||t)}return this},\n\"removeListener\");L.prototype.off=L.prototype.removeListener;L.prototype.removeAllListeners=\na(function(e){var t,n,i;if(n=this._events,n===void 0)return this;if(n.removeListener===\nvoid 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=\n0):n[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete n[e]),\nthis;if(arguments.length===0){var s=Object.keys(n),o;for(i=0;i<s.length;++i)o=s[i],\no!==\"removeListener\"&&this.removeAllListeners(o);return this.removeAllListeners(\n\"removeListener\"),this._events=Object.create(null),this._eventsCount=0,this}if(t=\nn[e],typeof t==\"function\")this.removeListener(e,t);else if(t!==void 0)for(i=t.length-\n1;i>=0;i--)this.removeListener(e,t[i]);return this},\"removeAllListeners\");function Xn(r,e,t){\nvar n=r._events;if(n===void 0)return[];var i=n[e];return i===void 0?[]:typeof i==\n\"function\"?t?[i.listener||i]:[i]:t?jo(i):ti(i,i.length)}a(Xn,\"_listeners\");L.prototype.\nlisteners=a(function(e){return Xn(this,e,!0)},\"listeners\");L.prototype.rawListeners=\na(function(e){return Xn(this,e,!1)},\"rawListeners\");L.listenerCount=function(r,e){\nreturn typeof r.listenerCount==\"function\"?r.listenerCount(e):ei.call(r,e)};L.prototype.\nlistenerCount=ei;function ei(r){var e=this._events;if(e!==void 0){var t=e[r];if(typeof t==\n\"function\")return 1;if(t!==void 0)return t.length}return 0}a(ei,\"listenerCount\");\nL.prototype.eventNames=a(function(){return this._eventsCount>0?ot(this._events):\n[]},\"eventNames\");function ti(r,e){for(var t=new Array(e),n=0;n<e;++n)t[n]=r[n];\nreturn t}a(ti,\"arrayClone\");function Wo(r,e){for(;e+1<r.length;e++)r[e]=r[e+1];r.\npop()}a(Wo,\"spliceOne\");function jo(r){for(var e=new Array(r.length),t=0;t<e.length;++t)\ne[t]=r[t].listener||r[t];return e}a(jo,\"unwrapListeners\");function Ho(r,e){return new Promise(\nfunction(t,n){function i(o){r.removeListener(e,s),n(o)}a(i,\"errorListener\");function s(){\ntypeof r.removeListener==\"function\"&&r.removeListener(\"error\",i),t([].slice.call(\narguments))}a(s,\"resolver\"),ri(r,e,s,{once:!0}),e!==\"error\"&&Go(r,i,{once:!0})})}\na(Ho,\"once\");function Go(r,e,t){typeof r.on==\"function\"&&ri(r,\"error\",e,t)}a(Go,\n\"addErrorHandlerIfEventEmitter\");function ri(r,e,t,n){if(typeof r.on==\"function\")\nn.once?r.once(e,t):r.on(e,t);else if(typeof r.addEventListener==\"function\")r.addEventListener(\ne,a(function i(s){n.once&&r.removeEventListener(e,i),t(s)},\"wrapListener\"));else\nthrow new TypeError('The \"emitter\" argument must be of type EventEmitter. Receiv\\\ned type '+typeof r)}a(ri,\"eventTargetAgnosticAddListener\")});var je={};ie(je,{default:()=>$o});var $o,He=z(()=>{\"use strict\";p();$o={}});function Ge(r){let e=1779033703,t=3144134277,n=1013904242,i=2773480762,s=1359893119,\no=2600822924,u=528734635,c=1541459225,h=0,l=0,d=[1116352408,1899447441,3049323471,\n3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,\n1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,\n604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,\n3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,\n1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,\n3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,\n883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,\n2361852424,2428436474,2756734187,3204031479,3329325298],b=a((A,w)=>A>>>w|A<<32-w,\n\"rrot\"),C=new Uint32Array(64),B=new Uint8Array(64),W=a(()=>{for(let R=0,G=0;R<16;R++,\nG+=4)C[R]=B[G]<<24|B[G+1]<<16|B[G+2]<<8|B[G+3];for(let R=16;R<64;R++){let G=b(C[R-\n15],7)^b(C[R-15],18)^C[R-15]>>>3,he=b(C[R-2],17)^b(C[R-2],19)^C[R-2]>>>10;C[R]=C[R-\n16]+G+C[R-7]+he|0}let A=e,w=t,P=n,V=i,k=s,j=o,ce=u,ee=c;for(let R=0;R<64;R++){let G=b(\nk,6)^b(k,11)^b(k,25),he=k&j^~k&ce,ye=ee+G+he+d[R]+C[R]|0,xe=b(A,2)^b(A,13)^b(A,22),\nme=A&w^A&P^w&P,se=xe+me|0;ee=ce,ce=j,j=k,k=V+ye|0,V=P,P=w,w=A,A=ye+se|0}e=e+A|0,\nt=t+w|0,n=n+P|0,i=i+V|0,s=s+k|0,o=o+j|0,u=u+ce|0,c=c+ee|0,l=0},\"process\"),X=a(A=>{\ntypeof A==\"string\"&&(A=new TextEncoder().encode(A));for(let w=0;w<A.length;w++)B[l++]=\nA[w],l===64&&W();h+=A.length},\"add\"),de=a(()=>{if(B[l++]=128,l==64&&W(),l+8>64){\nfor(;l<64;)B[l++]=0;W()}for(;l<58;)B[l++]=0;let A=h*8;B[l++]=A/1099511627776&255,\nB[l++]=A/4294967296&255,B[l++]=A>>>24,B[l++]=A>>>16&255,B[l++]=A>>>8&255,B[l++]=\nA&255,W();let w=new Uint8Array(32);return w[0]=e>>>24,w[1]=e>>>16&255,w[2]=e>>>8&\n255,w[3]=e&255,w[4]=t>>>24,w[5]=t>>>16&255,w[6]=t>>>8&255,w[7]=t&255,w[8]=n>>>24,\nw[9]=n>>>16&255,w[10]=n>>>8&255,w[11]=n&255,w[12]=i>>>24,w[13]=i>>>16&255,w[14]=\ni>>>8&255,w[15]=i&255,w[16]=s>>>24,w[17]=s>>>16&255,w[18]=s>>>8&255,w[19]=s&255,\nw[20]=o>>>24,w[21]=o>>>16&255,w[22]=o>>>8&255,w[23]=o&255,w[24]=u>>>24,w[25]=u>>>\n16&255,w[26]=u>>>8&255,w[27]=u&255,w[28]=c>>>24,w[29]=c>>>16&255,w[30]=c>>>8&255,\nw[31]=c&255,w},\"digest\");return r===void 0?{add:X,digest:de}:(X(r),de())}var ni=z(\n()=>{\"use strict\";p();a(Ge,\"sha256\")});var O,$e,ii=z(()=>{\"use strict\";p();O=class O{constructor(){_(this,\"_dataLength\",\n0);_(this,\"_bufferLength\",0);_(this,\"_state\",new Int32Array(4));_(this,\"_buffer\",\nnew ArrayBuffer(68));_(this,\"_buffer8\");_(this,\"_buffer32\");this._buffer8=new Uint8Array(\nthis._buffer,0,68),this._buffer32=new Uint32Array(this._buffer,0,17),this.start()}static hashByteArray(e,t=!1){\nreturn this.onePassHasher.start().appendByteArray(e).end(t)}static hashStr(e,t=!1){\nreturn this.onePassHasher.start().appendStr(e).end(t)}static hashAsciiStr(e,t=!1){\nreturn this.onePassHasher.start().appendAsciiStr(e).end(t)}static _hex(e){let t=O.\nhexChars,n=O.hexOut,i,s,o,u;for(u=0;u<4;u+=1)for(s=u*8,i=e[u],o=0;o<8;o+=2)n[s+1+\no]=t.charAt(i&15),i>>>=4,n[s+0+o]=t.charAt(i&15),i>>>=4;return n.join(\"\")}static _md5cycle(e,t){\nlet n=e[0],i=e[1],s=e[2],o=e[3];n+=(i&s|~i&o)+t[0]-680876936|0,n=(n<<7|n>>>25)+i|\n0,o+=(n&i|~n&s)+t[1]-389564586|0,o=(o<<12|o>>>20)+n|0,s+=(o&n|~o&i)+t[2]+606105819|\n0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+t[3]-1044525330|0,i=(i<<22|i>>>10)+s|0,n+=(i&\ns|~i&o)+t[4]-176418897|0,n=(n<<7|n>>>25)+i|0,o+=(n&i|~n&s)+t[5]+1200080426|0,o=(o<<\n12|o>>>20)+n|0,s+=(o&n|~o&i)+t[6]-1473231341|0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+\nt[7]-45705983|0,i=(i<<22|i>>>10)+s|0,n+=(i&s|~i&o)+t[8]+1770035416|0,n=(n<<7|n>>>\n25)+i|0,o+=(n&i|~n&s)+t[9]-1958414417|0,o=(o<<12|o>>>20)+n|0,s+=(o&n|~o&i)+t[10]-\n42063|0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+t[11]-1990404162|0,i=(i<<22|i>>>10)+s|\n0,n+=(i&s|~i&o)+t[12]+1804603682|0,n=(n<<7|n>>>25)+i|0,o+=(n&i|~n&s)+t[13]-40341101|\n0,o=(o<<12|o>>>20)+n|0,s+=(o&n|~o&i)+t[14]-1502002290|0,s=(s<<17|s>>>15)+o|0,i+=\n(s&o|~s&n)+t[15]+1236535329|0,i=(i<<22|i>>>10)+s|0,n+=(i&o|s&~o)+t[1]-165796510|\n0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&~s)+t[6]-1069501632|0,o=(o<<9|o>>>23)+n|0,s+=(o&\ni|n&~i)+t[11]+643717713|0,s=(s<<14|s>>>18)+o|0,i+=(s&n|o&~n)+t[0]-373897302|0,i=\n(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[5]-701558691|0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&\n~s)+t[10]+38016083|0,o=(o<<9|o>>>23)+n|0,s+=(o&i|n&~i)+t[15]-660478335|0,s=(s<<14|\ns>>>18)+o|0,i+=(s&n|o&~n)+t[4]-405537848|0,i=(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[9]+\n568446438|0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&~s)+t[14]-1019803690|0,o=(o<<9|o>>>23)+\nn|0,s+=(o&i|n&~i)+t[3]-187363961|0,s=(s<<14|s>>>18)+o|0,i+=(s&n|o&~n)+t[8]+1163531501|\n0,i=(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[13]-1444681467|0,n=(n<<5|n>>>27)+i|0,o+=(n&\ns|i&~s)+t[2]-51403784|0,o=(o<<9|o>>>23)+n|0,s+=(o&i|n&~i)+t[7]+1735328473|0,s=(s<<\n14|s>>>18)+o|0,i+=(s&n|o&~n)+t[12]-1926607734|0,i=(i<<20|i>>>12)+s|0,n+=(i^s^o)+\nt[5]-378558|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[8]-2022574463|0,o=(o<<11|o>>>21)+\nn|0,s+=(o^n^i)+t[11]+1839030562|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+t[14]-35309556|\n0,i=(i<<23|i>>>9)+s|0,n+=(i^s^o)+t[1]-1530992060|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+\nt[4]+1272893353|0,o=(o<<11|o>>>21)+n|0,s+=(o^n^i)+t[7]-155497632|0,s=(s<<16|s>>>\n16)+o|0,i+=(s^o^n)+t[10]-1094730640|0,i=(i<<23|i>>>9)+s|0,n+=(i^s^o)+t[13]+681279174|\n0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[0]-358537222|0,o=(o<<11|o>>>21)+n|0,s+=(o^n^i)+\nt[3]-722521979|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+t[6]+76029189|0,i=(i<<23|i>>>9)+\ns|0,n+=(i^s^o)+t[9]-640364487|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[12]-421815835|0,\no=(o<<11|o>>>21)+n|0,s+=(o^n^i)+t[15]+530742520|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+\nt[2]-995338651|0,i=(i<<23|i>>>9)+s|0,n+=(s^(i|~o))+t[0]-198630844|0,n=(n<<6|n>>>\n26)+i|0,o+=(i^(n|~s))+t[7]+1126891415|0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+t[14]-\n1416354905|0,s=(s<<15|s>>>17)+o|0,i+=(o^(s|~n))+t[5]-57434055|0,i=(i<<21|i>>>11)+\ns|0,n+=(s^(i|~o))+t[12]+1700485571|0,n=(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[3]-1894986606|\n0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+t[10]-1051523|0,s=(s<<15|s>>>17)+o|0,i+=(o^\n(s|~n))+t[1]-2054922799|0,i=(i<<21|i>>>11)+s|0,n+=(s^(i|~o))+t[8]+1873313359|0,n=\n(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[15]-30611744|0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+\nt[6]-1560198380|0,s=(s<<15|s>>>17)+o|0,i+=(o^(s|~n))+t[13]+1309151649|0,i=(i<<21|\ni>>>11)+s|0,n+=(s^(i|~o))+t[4]-145523070|0,n=(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[11]-\n1120210379|0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+t[2]+718787259|0,s=(s<<15|s>>>17)+\no|0,i+=(o^(s|~n))+t[9]-343485551|0,i=(i<<21|i>>>11)+s|0,e[0]=n+e[0]|0,e[1]=i+e[1]|\n0,e[2]=s+e[2]|0,e[3]=o+e[3]|0}start(){return this._dataLength=0,this._bufferLength=\n0,this._state.set(O.stateIdentity),this}appendStr(e){let t=this._buffer8,n=this.\n_buffer32,i=this._bufferLength,s,o;for(o=0;o<e.length;o+=1){if(s=e.charCodeAt(o),\ns<128)t[i++]=s;else if(s<2048)t[i++]=(s>>>6)+192,t[i++]=s&63|128;else if(s<55296||\ns>56319)t[i++]=(s>>>12)+224,t[i++]=s>>>6&63|128,t[i++]=s&63|128;else{if(s=(s-55296)*\n1024+(e.charCodeAt(++o)-56320)+65536,s>1114111)throw new Error(\"Unicode standard\\\n supports code points up to U+10FFFF\");t[i++]=(s>>>18)+240,t[i++]=s>>>12&63|128,\nt[i++]=s>>>6&63|128,t[i++]=s&63|128}i>=64&&(this._dataLength+=64,O._md5cycle(this.\n_state,n),i-=64,n[0]=n[16])}return this._bufferLength=i,this}appendAsciiStr(e){let t=this.\n_buffer8,n=this._buffer32,i=this._bufferLength,s,o=0;for(;;){for(s=Math.min(e.length-\no,64-i);s--;)t[i++]=e.charCodeAt(o++);if(i<64)break;this._dataLength+=64,O._md5cycle(\nthis._state,n),i=0}return this._bufferLength=i,this}appendByteArray(e){let t=this.\n_buffer8,n=this._buffer32,i=this._bufferLength,s,o=0;for(;;){for(s=Math.min(e.length-\no,64-i);s--;)t[i++]=e[o++];if(i<64)break;this._dataLength+=64,O._md5cycle(this._state,\nn),i=0}return this._bufferLength=i,this}getState(){let e=this._state;return{buffer:String.\nfromCharCode.apply(null,Array.from(this._buffer8)),buflen:this._bufferLength,length:this.\n_dataLength,state:[e[0],e[1],e[2],e[3]]}}setState(e){let t=e.buffer,n=e.state,i=this.\n_state,s;for(this._dataLength=e.length,this._bufferLength=e.buflen,i[0]=n[0],i[1]=\nn[1],i[2]=n[2],i[3]=n[3],s=0;s<t.length;s+=1)this._buffer8[s]=t.charCodeAt(s)}end(e=!1){\nlet t=this._bufferLength,n=this._buffer8,i=this._buffer32,s=(t>>2)+1;this._dataLength+=\nt;let o=this._dataLength*8;if(n[t]=128,n[t+1]=n[t+2]=n[t+3]=0,i.set(O.buffer32Identity.\nsubarray(s),s),t>55&&(O._md5cycle(this._state,i),i.set(O.buffer32Identity)),o<=4294967295)\ni[14]=o;else{let u=o.toString(16).match(/(.*?)(.{0,8})$/);if(u===null)return;let c=parseInt(\nu[2],16),h=parseInt(u[1],16)||0;i[14]=c,i[15]=h}return O._md5cycle(this._state,i),\ne?this._state:O._hex(this._state)}};a(O,\"Md5\"),_(O,\"stateIdentity\",new Int32Array(\n[1732584193,-271733879,-1732584194,271733878])),_(O,\"buffer32Identity\",new Int32Array(\n[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),_(O,\"hexChars\",\"0123456789abcdef\"),_(O,\"hexO\\\nut\",[]),_(O,\"onePassHasher\",new O);$e=O});var qt={};ie(qt,{createHash:()=>Ko,createHmac:()=>zo,randomBytes:()=>Vo});function Vo(r){\nreturn g.getRandomValues(y.alloc(r))}function Ko(r){if(r===\"sha256\")return{update:a(\nfunction(e){return{digest:a(function(){return y.from(Ge(e))},\"digest\")}},\"update\")};\nif(r===\"md5\")return{update:a(function(e){return{digest:a(function(){return typeof e==\n\"string\"?$e.hashStr(e):$e.hashByteArray(e)},\"digest\")}},\"update\")};throw new Error(\n`Hash type '${r}' not supported`)}function zo(r,e){if(r!==\"sha256\")throw new Error(\n`Only sha256 is supported (requested: '${r}')`);return{update:a(function(t){return{\ndigest:a(function(){typeof e==\"string\"&&(e=new TextEncoder().encode(e)),typeof t==\n\"string\"&&(t=new TextEncoder().encode(t));let n=e.length;if(n>64)e=Ge(e);else if(n<\n64){let c=new Uint8Array(64);c.set(e),e=c}let i=new Uint8Array(64),s=new Uint8Array(\n64);for(let c=0;c<64;c++)i[c]=54^e[c],s[c]=92^e[c];let o=new Uint8Array(t.length+\n64);o.set(i,0),o.set(t,64);let u=new Uint8Array(96);return u.set(s,0),u.set(Ge(o),\n64),y.from(Ge(u))},\"digest\")}},\"update\")}}var Qt=z(()=>{\"use strict\";p();ni();ii();\na(Vo,\"randomBytes\");a(Ko,\"createHash\");a(zo,\"createHmac\")});var jt=I(si=>{\"use strict\";p();si.parse=function(r,e){return new Wt(r,e).parse()};\nvar ut=class ut{constructor(e,t){this.source=e,this.transform=t||Yo,this.position=\n0,this.entries=[],this.recorded=[],this.dimension=0}isEof(){return this.position>=\nthis.source.length}nextCharacter(){var e=this.source[this.position++];return e===\n\"\\\\\"?{value:this.source[this.position++],escaped:!0}:{value:e,escaped:!1}}record(e){\nthis.recorded.push(e)}newEntry(e){var t;(this.recorded.length>0||e)&&(t=this.recorded.\njoin(\"\"),t===\"NULL\"&&!e&&(t=null),t!==null&&(t=this.transform(t)),this.entries.push(\nt),this.recorded=[])}consumeDimensions(){if(this.source[0]===\"[\")for(;!this.isEof();){\nvar e=this.nextCharacter();if(e.value===\"=\")break}}parse(e){var t,n,i;for(this.consumeDimensions();!this.\nisEof();)if(t=this.nextCharacter(),t.value===\"{\"&&!i)this.dimension++,this.dimension>\n1&&(n=new ut(this.source.substr(this.position-1),this.transform),this.entries.push(\nn.parse(!0)),this.position+=n.position-2);else if(t.value===\"}\"&&!i){if(this.dimension--,\n!this.dimension&&(this.newEntry(),e))return this.entries}else t.value==='\"'&&!t.\nescaped?(i&&this.newEntry(!0),i=!i):t.value===\",\"&&!i?this.newEntry():this.record(\nt.value);if(this.dimension!==0)throw new Error(\"array dimension not balanced\");return this.\nentries}};a(ut,\"ArrayParser\");var Wt=ut;function Yo(r){return r}a(Yo,\"identity\")});var Ht=I((mh,oi)=>{p();var Zo=jt();oi.exports={create:a(function(r,e){return{parse:a(\nfunction(){return Zo.parse(r,e)},\"parse\")}},\"create\")}});var ci=I((bh,ui)=>{\"use strict\";p();var Jo=/(\\d{1,})-(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2}):(\\d{2})(\\.\\d{1,})?.*?( BC)?$/,\nXo=/^(\\d{1,})-(\\d{2})-(\\d{2})( BC)?$/,ea=/([Z+-])(\\d{2})?:?(\\d{2})?:?(\\d{2})?/,ta=/^-?infinity$/;\nui.exports=a(function(e){if(ta.test(e))return Number(e.replace(\"i\",\"I\"));var t=Jo.\nexec(e);if(!t)return ra(e)||null;var n=!!t[8],i=parseInt(t[1],10);n&&(i=ai(i));var s=parseInt(\nt[2],10)-1,o=t[3],u=parseInt(t[4],10),c=parseInt(t[5],10),h=parseInt(t[6],10),l=t[7];\nl=l?1e3*parseFloat(l):0;var d,b=na(e);return b!=null?(d=new Date(Date.UTC(i,s,o,\nu,c,h,l)),Gt(i)&&d.setUTCFullYear(i),b!==0&&d.setTime(d.getTime()-b)):(d=new Date(\ni,s,o,u,c,h,l),Gt(i)&&d.setFullYear(i)),d},\"parseDate\");function ra(r){var e=Xo.\nexec(r);if(e){var t=parseInt(e[1],10),n=!!e[4];n&&(t=ai(t));var i=parseInt(e[2],\n10)-1,s=e[3],o=new Date(t,i,s);return Gt(t)&&o.setFullYear(t),o}}a(ra,\"getDate\");\nfunction na(r){if(r.endsWith(\"+00\"))return 0;var e=ea.exec(r.split(\" \")[1]);if(e){\nvar t=e[1];if(t===\"Z\")return 0;var n=t===\"-\"?-1:1,i=parseInt(e[2],10)*3600+parseInt(\ne[3]||0,10)*60+parseInt(e[4]||0,10);return i*n*1e3}}a(na,\"timeZoneOffset\");function ai(r){\nreturn-(r-1)}a(ai,\"bcYearToNegativeYear\");function Gt(r){return r>=0&&r<100}a(Gt,\n\"is0To99\")});var li=I((vh,hi)=>{p();hi.exports=sa;var ia=Object.prototype.hasOwnProperty;function sa(r){\nfor(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)ia.call(t,\nn)&&(r[n]=t[n])}return r}a(sa,\"extend\")});var di=I((Ah,pi)=>{\"use strict\";p();var oa=li();pi.exports=Fe;function Fe(r){if(!(this instanceof\nFe))return new Fe(r);oa(this,wa(r))}a(Fe,\"PostgresInterval\");var aa=[\"seconds\",\"\\\nminutes\",\"hours\",\"days\",\"months\",\"years\"];Fe.prototype.toPostgres=function(){var r=aa.\nfilter(this.hasOwnProperty,this);return this.milliseconds&&r.indexOf(\"seconds\")<\n0&&r.push(\"seconds\"),r.length===0?\"0\":r.map(function(e){var t=this[e]||0;return e===\n\"seconds\"&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/\\.?0+$/,\n\"\")),t+\" \"+e},this).join(\" \")};var ua={years:\"Y\",months:\"M\",days:\"D\",hours:\"H\",minutes:\"\\\nM\",seconds:\"S\"},ca=[\"years\",\"months\",\"days\"],ha=[\"hours\",\"minutes\",\"seconds\"];Fe.\nprototype.toISOString=Fe.prototype.toISO=function(){var r=ca.map(t,this).join(\"\"),\ne=ha.map(t,this).join(\"\");return\"P\"+r+\"T\"+e;function t(n){var i=this[n]||0;return n===\n\"seconds\"&&this.milliseconds&&(i=(i+this.milliseconds/1e3).toFixed(6).replace(/0+$/,\n\"\")),i+ua[n]}};var $t=\"([+-]?\\\\d+)\",la=$t+\"\\\\s+years?\",fa=$t+\"\\\\s+mons?\",pa=$t+\"\\\n\\\\s+days?\",da=\"([+-])?([\\\\d]*):(\\\\d\\\\d):(\\\\d\\\\d)\\\\.?(\\\\d{1,6})?\",ya=new RegExp([\nla,fa,pa,da].map(function(r){return\"(\"+r+\")?\"}).join(\"\\\\s*\")),fi={years:2,months:4,\ndays:6,hours:9,minutes:10,seconds:11,milliseconds:12},ma=[\"hours\",\"minutes\",\"sec\\\nonds\",\"milliseconds\"];function ga(r){var e=r+\"000000\".slice(r.length);return parseInt(\ne,10)/1e3}a(ga,\"parseMilliseconds\");function wa(r){if(!r)return{};var e=ya.exec(\nr),t=e[8]===\"-\";return Object.keys(fi).reduce(function(n,i){var s=fi[i],o=e[s];return!o||\n(o=i===\"milliseconds\"?ga(o):parseInt(o,10),!o)||(t&&~ma.indexOf(i)&&(o*=-1),n[i]=\no),n},{})}a(wa,\"parse\")});var mi=I((Ih,yi)=>{\"use strict\";p();yi.exports=a(function(e){if(/^\\\\x/.test(e))return new y(\ne.substr(2),\"hex\");for(var t=\"\",n=0;n<e.length;)if(e[n]!==\"\\\\\")t+=e[n],++n;else if(/[0-7]{3}/.\ntest(e.substr(n+1,3)))t+=String.fromCharCode(parseInt(e.substr(n+1,3),8)),n+=4;else{\nfor(var i=1;n+i<e.length&&e[n+i]===\"\\\\\";)i++;for(var s=0;s<Math.floor(i/2);++s)t+=\n\"\\\\\";n+=Math.floor(i/2)*2}return new y(t,\"binary\")},\"parseBytea\")});var Ei=I((Lh,vi)=>{p();var Ve=jt(),Ke=Ht(),ct=ci(),wi=di(),bi=mi();function ht(r){\nreturn a(function(t){return t===null?t:r(t)},\"nullAllowed\")}a(ht,\"allowNull\");function Si(r){\nreturn r===null?r:r===\"TRUE\"||r===\"t\"||r===\"true\"||r===\"y\"||r===\"yes\"||r===\"on\"||\nr===\"1\"}a(Si,\"parseBool\");function ba(r){return r?Ve.parse(r,Si):null}a(ba,\"pars\\\neBoolArray\");function Sa(r){return parseInt(r,10)}a(Sa,\"parseBaseTenInt\");function Vt(r){\nreturn r?Ve.parse(r,ht(Sa)):null}a(Vt,\"parseIntegerArray\");function xa(r){return r?\nVe.parse(r,ht(function(e){return xi(e).trim()})):null}a(xa,\"parseBigIntegerArray\");\nvar va=a(function(r){if(!r)return null;var e=Ke.create(r,function(t){return t!==\nnull&&(t=Zt(t)),t});return e.parse()},\"parsePointArray\"),Kt=a(function(r){if(!r)\nreturn null;var e=Ke.create(r,function(t){return t!==null&&(t=parseFloat(t)),t});\nreturn e.parse()},\"parseFloatArray\"),re=a(function(r){if(!r)return null;var e=Ke.\ncreate(r);return e.parse()},\"parseStringArray\"),zt=a(function(r){if(!r)return null;\nvar e=Ke.create(r,function(t){return t!==null&&(t=ct(t)),t});return e.parse()},\"\\\nparseDateArray\"),Ea=a(function(r){if(!r)return null;var e=Ke.create(r,function(t){\nreturn t!==null&&(t=wi(t)),t});return e.parse()},\"parseIntervalArray\"),_a=a(function(r){\nreturn r?Ve.parse(r,ht(bi)):null},\"parseByteAArray\"),Yt=a(function(r){return parseInt(\nr,10)},\"parseInteger\"),xi=a(function(r){var e=String(r);return/^\\d+$/.test(e)?e:\nr},\"parseBigInteger\"),gi=a(function(r){return r?Ve.parse(r,ht(JSON.parse)):null},\n\"parseJsonArray\"),Zt=a(function(r){return r[0]!==\"(\"?null:(r=r.substring(1,r.length-\n1).split(\",\"),{x:parseFloat(r[0]),y:parseFloat(r[1])})},\"parsePoint\"),Aa=a(function(r){\nif(r[0]!==\"<\"&&r[1]!==\"(\")return null;for(var e=\"(\",t=\"\",n=!1,i=2;i<r.length-1;i++){\nif(n||(e+=r[i]),r[i]===\")\"){n=!0;continue}else if(!n)continue;r[i]!==\",\"&&(t+=r[i])}\nvar s=Zt(e);return s.radius=parseFloat(t),s},\"parseCircle\"),Ca=a(function(r){r(20,\nxi),r(21,Yt),r(23,Yt),r(26,Yt),r(700,parseFloat),r(701,parseFloat),r(16,Si),r(1082,\nct),r(1114,ct),r(1184,ct),r(600,Zt),r(651,re),r(718,Aa),r(1e3,ba),r(1001,_a),r(1005,\nVt),r(1007,Vt),r(1028,Vt),r(1016,xa),r(1017,va),r(1021,Kt),r(1022,Kt),r(1231,Kt),\nr(1014,re),r(1015,re),r(1008,re),r(1009,re),r(1040,re),r(1041,re),r(1115,zt),r(1182,\nzt),r(1185,zt),r(1186,wi),r(1187,Ea),r(17,bi),r(114,JSON.parse.bind(JSON)),r(3802,\nJSON.parse.bind(JSON)),r(199,gi),r(3807,gi),r(3907,re),r(2951,re),r(791,re),r(1183,\nre),r(1270,re)},\"init\");vi.exports={init:Ca}});var Ai=I((Mh,_i)=>{\"use strict\";p();var Z=1e6;function Ta(r){var e=r.readInt32BE(\n0),t=r.readUInt32BE(4),n=\"\";e<0&&(e=~e+(t===0),t=~t+1>>>0,n=\"-\");var i=\"\",s,o,u,\nc,h,l;{if(s=e%Z,e=e/Z>>>0,o=4294967296*s+t,t=o/Z>>>0,u=\"\"+(o-Z*t),t===0&&e===0)return n+\nu+i;for(c=\"\",h=6-u.length,l=0;l<h;l++)c+=\"0\";i=c+u+i}{if(s=e%Z,e=e/Z>>>0,o=4294967296*\ns+t,t=o/Z>>>0,u=\"\"+(o-Z*t),t===0&&e===0)return n+u+i;for(c=\"\",h=6-u.length,l=0;l<\nh;l++)c+=\"0\";i=c+u+i}{if(s=e%Z,e=e/Z>>>0,o=4294967296*s+t,t=o/Z>>>0,u=\"\"+(o-Z*t),\nt===0&&e===0)return n+u+i;for(c=\"\",h=6-u.length,l=0;l<h;l++)c+=\"0\";i=c+u+i}return s=\ne%Z,o=4294967296*s+t,u=\"\"+o%Z,n+u+i}a(Ta,\"readInt8\");_i.exports=Ta});var Bi=I((Uh,Pi)=>{p();var Ia=Ai(),F=a(function(r,e,t,n,i){t=t||0,n=n||!1,i=i||function(C,B,W){\nreturn C*Math.pow(2,W)+B};var s=t>>3,o=a(function(C){return n?~C&255:C},\"inv\"),u=255,\nc=8-t%8;e<c&&(u=255<<8-e&255,c=e),t&&(u=u>>t%8);var h=0;t%8+e>=8&&(h=i(0,o(r[s])&\nu,c));for(var l=e+t>>3,d=s+1;d<l;d++)h=i(h,o(r[d]),8);var b=(e+t)%8;return b>0&&\n(h=i(h,o(r[l])>>8-b,b)),h},\"parseBits\"),Ii=a(function(r,e,t){var n=Math.pow(2,t-\n1)-1,i=F(r,1),s=F(r,t,1);if(s===0)return 0;var o=1,u=a(function(h,l,d){h===0&&(h=\n1);for(var b=1;b<=d;b++)o/=2,(l&1<<d-b)>0&&(h+=o);return h},\"parsePrecisionBits\"),\nc=F(r,e,t+1,!1,u);return s==Math.pow(2,t+1)-1?c===0?i===0?1/0:-1/0:NaN:(i===0?1:\n-1)*Math.pow(2,s-n)*c},\"parseFloatFromBits\"),Pa=a(function(r){return F(r,1)==1?-1*\n(F(r,15,1,!0)+1):F(r,15,1)},\"parseInt16\"),Ci=a(function(r){return F(r,1)==1?-1*(F(\nr,31,1,!0)+1):F(r,31,1)},\"parseInt32\"),Ba=a(function(r){return Ii(r,23,8)},\"pars\\\neFloat32\"),La=a(function(r){return Ii(r,52,11)},\"parseFloat64\"),Ra=a(function(r){\nvar e=F(r,16,32);if(e==49152)return NaN;for(var t=Math.pow(1e4,F(r,16,16)),n=0,i=[],\ns=F(r,16),o=0;o<s;o++)n+=F(r,16,64+16*o)*t,t/=1e4;var u=Math.pow(10,F(r,16,48));\nreturn(e===0?1:-1)*Math.round(n*u)/u},\"parseNumeric\"),Ti=a(function(r,e){var t=F(\ne,1),n=F(e,63,1),i=new Date((t===0?1:-1)*n/1e3+9466848e5);return r||i.setTime(i.\ngetTime()+i.getTimezoneOffset()*6e4),i.usec=n%1e3,i.getMicroSeconds=function(){return this.\nusec},i.setMicroSeconds=function(s){this.usec=s},i.getUTCMicroSeconds=function(){\nreturn this.usec},i},\"parseDate\"),ze=a(function(r){for(var e=F(r,32),t=F(r,32,32),\nn=F(r,32,64),i=96,s=[],o=0;o<e;o++)s[o]=F(r,32,i),i+=32,i+=32;var u=a(function(h){\nvar l=F(r,32,i);if(i+=32,l==4294967295)return null;var d;if(h==23||h==20)return d=\nF(r,l*8,i),i+=l*8,d;if(h==25)return d=r.toString(this.encoding,i>>3,(i+=l<<3)>>3),\nd;console.log(\"ERROR: ElementType not implemented: \"+h)},\"parseElement\"),c=a(function(h,l){\nvar d=[],b;if(h.length>1){var C=h.shift();for(b=0;b<C;b++)d[b]=c(h,l);h.unshift(\nC)}else for(b=0;b<h[0];b++)d[b]=u(l);return d},\"parse\");return c(s,n)},\"parseArr\\\nay\"),Fa=a(function(r){return r.toString(\"utf8\")},\"parseText\"),Ma=a(function(r){return r===\nnull?null:F(r,8)>0},\"parseBool\"),Da=a(function(r){r(20,Ia),r(21,Pa),r(23,Ci),r(26,\nCi),r(1700,Ra),r(700,Ba),r(701,La),r(16,Ma),r(1114,Ti.bind(null,!1)),r(1184,Ti.bind(\nnull,!0)),r(1e3,ze),r(1007,ze),r(1016,ze),r(1008,ze),r(1009,ze),r(25,Fa)},\"init\");\nPi.exports={init:Da}});var Ri=I((qh,Li)=>{p();Li.exports={BOOL:16,BYTEA:17,CHAR:18,INT8:20,INT2:21,INT4:23,\nREGPROC:24,TEXT:25,OID:26,TID:27,XID:28,CID:29,JSON:114,XML:142,PG_NODE_TREE:194,\nSMGR:210,PATH:602,POLYGON:604,CIDR:650,FLOAT4:700,FLOAT8:701,ABSTIME:702,RELTIME:703,\nTINTERVAL:704,CIRCLE:718,MACADDR8:774,MONEY:790,MACADDR:829,INET:869,ACLITEM:1033,\nBPCHAR:1042,VARCHAR:1043,DATE:1082,TIME:1083,TIMESTAMP:1114,TIMESTAMPTZ:1184,INTERVAL:1186,\nTIMETZ:1266,BIT:1560,VARBIT:1562,NUMERIC:1700,REFCURSOR:1790,REGPROCEDURE:2202,REGOPER:2203,\nREGOPERATOR:2204,REGCLASS:2205,REGTYPE:2206,UUID:2950,TXID_SNAPSHOT:2970,PG_LSN:3220,\nPG_NDISTINCT:3361,PG_DEPENDENCIES:3402,TSVECTOR:3614,TSQUERY:3615,GTSVECTOR:3642,\nREGCONFIG:3734,REGDICTIONARY:3769,JSONB:3802,REGNAMESPACE:4089,REGROLE:4096}});var Je=I(Ze=>{p();var ka=Ei(),Ua=Bi(),Oa=Ht(),Na=Ri();Ze.getTypeParser=qa;Ze.setTypeParser=\nQa;Ze.arrayParser=Oa;Ze.builtins=Na;var Ye={text:{},binary:{}};function Fi(r){return String(\nr)}a(Fi,\"noParse\");function qa(r,e){return e=e||\"text\",Ye[e]&&Ye[e][r]||Fi}a(qa,\n\"getTypeParser\");function Qa(r,e,t){typeof e==\"function\"&&(t=e,e=\"text\"),Ye[e][r]=\nt}a(Qa,\"setTypeParser\");ka.init(function(r,e){Ye.text[r]=e});Ua.init(function(r,e){\nYe.binary[r]=e})});var Xe=I((Gh,Jt)=>{\"use strict\";p();Jt.exports={host:\"localhost\",user:m.platform===\n\"win32\"?m.env.USERNAME:m.env.USER,database:void 0,password:null,connectionString:void 0,\nport:5432,rows:0,binary:!1,max:10,idleTimeoutMillis:3e4,client_encoding:\"\",ssl:!1,\napplication_name:void 0,fallback_application_name:void 0,options:void 0,parseInputDatesAsUTC:!1,\nstatement_timeout:!1,lock_timeout:!1,idle_in_transaction_session_timeout:!1,query_timeout:!1,\nconnect_timeout:0,keepalives:1,keepalives_idle:0};var Me=Je(),Wa=Me.getTypeParser(\n20,\"text\"),ja=Me.getTypeParser(1016,\"text\");Jt.exports.__defineSetter__(\"parseIn\\\nt8\",function(r){Me.setTypeParser(20,\"text\",r?Me.getTypeParser(23,\"text\"):Wa),Me.\nsetTypeParser(1016,\"text\",r?Me.getTypeParser(1007,\"text\"):ja)})});var et=I((Vh,Di)=>{\"use strict\";p();var Ha=(Qt(),N(qt)),Ga=Xe();function $a(r){var e=r.\nreplace(/\\\\/g,\"\\\\\\\\\").replace(/\"/g,'\\\\\"');return'\"'+e+'\"'}a($a,\"escapeElement\");\nfunction Mi(r){for(var e=\"{\",t=0;t<r.length;t++)t>0&&(e=e+\",\"),r[t]===null||typeof r[t]>\n\"u\"?e=e+\"NULL\":Array.isArray(r[t])?e=e+Mi(r[t]):r[t]instanceof y?e+=\"\\\\\\\\x\"+r[t].\ntoString(\"hex\"):e+=$a(lt(r[t]));return e=e+\"}\",e}a(Mi,\"arrayString\");var lt=a(function(r,e){\nif(r==null)return null;if(r instanceof y)return r;if(ArrayBuffer.isView(r)){var t=y.\nfrom(r.buffer,r.byteOffset,r.byteLength);return t.length===r.byteLength?t:t.slice(\nr.byteOffset,r.byteOffset+r.byteLength)}return r instanceof Date?Ga.parseInputDatesAsUTC?\nza(r):Ka(r):Array.isArray(r)?Mi(r):typeof r==\"object\"?Va(r,e):r.toString()},\"pre\\\npareValue\");function Va(r,e){if(r&&typeof r.toPostgres==\"function\"){if(e=e||[],e.\nindexOf(r)!==-1)throw new Error('circular reference detected while preparing \"'+\nr+'\" for query');return e.push(r),lt(r.toPostgres(lt),e)}return JSON.stringify(r)}\na(Va,\"prepareObject\");function H(r,e){for(r=\"\"+r;r.length<e;)r=\"0\"+r;return r}a(\nH,\"pad\");function Ka(r){var e=-r.getTimezoneOffset(),t=r.getFullYear(),n=t<1;n&&\n(t=Math.abs(t)+1);var i=H(t,4)+\"-\"+H(r.getMonth()+1,2)+\"-\"+H(r.getDate(),2)+\"T\"+\nH(r.getHours(),2)+\":\"+H(r.getMinutes(),2)+\":\"+H(r.getSeconds(),2)+\".\"+H(r.getMilliseconds(),\n3);return e<0?(i+=\"-\",e*=-1):i+=\"+\",i+=H(Math.floor(e/60),2)+\":\"+H(e%60,2),n&&(i+=\n\" BC\"),i}a(Ka,\"dateToString\");function za(r){var e=r.getUTCFullYear(),t=e<1;t&&(e=\nMath.abs(e)+1);var n=H(e,4)+\"-\"+H(r.getUTCMonth()+1,2)+\"-\"+H(r.getUTCDate(),2)+\"\\\nT\"+H(r.getUTCHours(),2)+\":\"+H(r.getUTCMinutes(),2)+\":\"+H(r.getUTCSeconds(),2)+\".\"+\nH(r.getUTCMilliseconds(),3);return n+=\"+00:00\",t&&(n+=\" BC\"),n}a(za,\"dateToStrin\\\ngUTC\");function Ya(r,e,t){return r=typeof r==\"string\"?{text:r}:r,e&&(typeof e==\"\\\nfunction\"?r.callback=e:r.values=e),t&&(r.callback=t),r}a(Ya,\"normalizeQueryConfi\\\ng\");var Xt=a(function(r){return Ha.createHash(\"md5\").update(r,\"utf-8\").digest(\"h\\\nex\")},\"md5\"),Za=a(function(r,e,t){var n=Xt(e+r),i=Xt(y.concat([y.from(n),t]));return\"\\\nmd5\"+i},\"postgresMd5PasswordHash\");Di.exports={prepareValue:a(function(e){return lt(\ne)},\"prepareValueWrapper\"),normalizeQueryConfig:Ya,postgresMd5PasswordHash:Za,md5:Xt}});var qi=I((Yh,Ni)=>{\"use strict\";p();var er=(Qt(),N(qt));function Ja(r){if(r.indexOf(\n\"SCRAM-SHA-256\")===-1)throw new Error(\"SASL: Only mechanism SCRAM-SHA-256 is cur\\\nrently supported\");let e=er.randomBytes(18).toString(\"base64\");return{mechanism:\"\\\nSCRAM-SHA-256\",clientNonce:e,response:\"n,,n=*,r=\"+e,message:\"SASLInitialResponse\"}}\na(Ja,\"startSession\");function Xa(r,e,t){if(r.message!==\"SASLInitialResponse\")throw new Error(\n\"SASL: Last message was not SASLInitialResponse\");if(typeof e!=\"string\")throw new Error(\n\"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\");if(typeof t!=\n\"string\")throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: serverData must be a\\\n string\");let n=ru(t);if(n.nonce.startsWith(r.clientNonce)){if(n.nonce.length===\nr.clientNonce.length)throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: server n\\\nonce is too short\")}else throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: serv\\\ner nonce does not start with client nonce\");var i=y.from(n.salt,\"base64\"),s=su(e,\ni,n.iteration),o=De(s,\"Client Key\"),u=iu(o),c=\"n=*,r=\"+r.clientNonce,h=\"r=\"+n.nonce+\n\",s=\"+n.salt+\",i=\"+n.iteration,l=\"c=biws,r=\"+n.nonce,d=c+\",\"+h+\",\"+l,b=De(u,d),C=Oi(\no,b),B=C.toString(\"base64\"),W=De(s,\"Server Key\"),X=De(W,d);r.message=\"SASLRespon\\\nse\",r.serverSignature=X.toString(\"base64\"),r.response=l+\",p=\"+B}a(Xa,\"continueSe\\\nssion\");function eu(r,e){if(r.message!==\"SASLResponse\")throw new Error(\"SASL: La\\\nst message was not SASLResponse\");if(typeof e!=\"string\")throw new Error(\"SASL: S\\\nCRAM-SERVER-FINAL-MESSAGE: serverData must be a string\");let{serverSignature:t}=nu(\ne);if(t!==r.serverSignature)throw new Error(\"SASL: SCRAM-SERVER-FINAL-MESSAGE: s\\\nerver signature does not match\")}a(eu,\"finalizeSession\");function tu(r){if(typeof r!=\n\"string\")throw new TypeError(\"SASL: text must be a string\");return r.split(\"\").map(\n(e,t)=>r.charCodeAt(t)).every(e=>e>=33&&e<=43||e>=45&&e<=126)}a(tu,\"isPrintableC\\\nhars\");function ki(r){return/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.\ntest(r)}a(ki,\"isBase64\");function Ui(r){if(typeof r!=\"string\")throw new TypeError(\n\"SASL: attribute pairs text must be a string\");return new Map(r.split(\",\").map(e=>{\nif(!/^.=/.test(e))throw new Error(\"SASL: Invalid attribute pair entry\");let t=e[0],\nn=e.substring(2);return[t,n]}))}a(Ui,\"parseAttributePairs\");function ru(r){let e=Ui(\nr),t=e.get(\"r\");if(t){if(!tu(t))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAG\\\nE: nonce must only contain printable characters\")}else throw new Error(\"SASL: SC\\\nRAM-SERVER-FIRST-MESSAGE: nonce missing\");let n=e.get(\"s\");if(n){if(!ki(n))throw new Error(\n\"SASL: SCRAM-SERVER-FIRST-MESSAGE: salt must be base64\")}else throw new Error(\"S\\\nASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing\");let i=e.get(\"i\");if(i){if(!/^[1-9][0-9]*$/.\ntest(i))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: invalid iteration cou\\\nnt\")}else throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: iteration missing\");\nlet s=parseInt(i,10);return{nonce:t,salt:n,iteration:s}}a(ru,\"parseServerFirstMe\\\nssage\");function nu(r){let t=Ui(r).get(\"v\");if(t){if(!ki(t))throw new Error(\"SAS\\\nL: SCRAM-SERVER-FINAL-MESSAGE: server signature must be base64\")}else throw new Error(\n\"SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature is missing\");return{serverSignature:t}}\na(nu,\"parseServerFinalMessage\");function Oi(r,e){if(!y.isBuffer(r))throw new TypeError(\n\"first argument must be a Buffer\");if(!y.isBuffer(e))throw new TypeError(\"second\\\n argument must be a Buffer\");if(r.length!==e.length)throw new Error(\"Buffer leng\\\nths must match\");if(r.length===0)throw new Error(\"Buffers cannot be empty\");return y.\nfrom(r.map((t,n)=>r[n]^e[n]))}a(Oi,\"xorBuffers\");function iu(r){return er.createHash(\n\"sha256\").update(r).digest()}a(iu,\"sha256\");function De(r,e){return er.createHmac(\n\"sha256\",r).update(e).digest()}a(De,\"hmacSha256\");function su(r,e,t){for(var n=De(\nr,y.concat([e,y.from([0,0,0,1])])),i=n,s=0;s<t-1;s++)n=De(r,n),i=Oi(i,n);return i}\na(su,\"Hi\");Ni.exports={startSession:Ja,continueSession:Xa,finalizeSession:eu}});var tr={};ie(tr,{join:()=>ou});function ou(...r){return r.join(\"/\")}var rr=z(()=>{\n\"use strict\";p();a(ou,\"join\")});var nr={};ie(nr,{stat:()=>au});function au(r,e){e(new Error(\"No filesystem\"))}var ir=z(\n()=>{\"use strict\";p();a(au,\"stat\")});var sr={};ie(sr,{default:()=>uu});var uu,or=z(()=>{\"use strict\";p();uu={}});var Qi={};ie(Qi,{StringDecoder:()=>ar});var ur,ar,Wi=z(()=>{\"use strict\";p();ur=\nclass ur{constructor(e){_(this,\"td\");this.td=new TextDecoder(e)}write(e){return this.\ntd.decode(e,{stream:!0})}end(e){return this.td.decode(e)}};a(ur,\"StringDecoder\");\nar=ur});var $i=I((ol,Gi)=>{\"use strict\";p();var{Transform:cu}=(or(),N(sr)),{StringDecoder:hu}=(Wi(),N(Qi)),\nbe=Symbol(\"last\"),ft=Symbol(\"decoder\");function lu(r,e,t){let n;if(this.overflow){\nif(n=this[ft].write(r).split(this.matcher),n.length===1)return t();n.shift(),this.\noverflow=!1}else this[be]+=this[ft].write(r),n=this[be].split(this.matcher);this[be]=\nn.pop();for(let i=0;i<n.length;i++)try{Hi(this,this.mapper(n[i]))}catch(s){return t(\ns)}if(this.overflow=this[be].length>this.maxLength,this.overflow&&!this.skipOverflow){\nt(new Error(\"maximum buffer reached\"));return}t()}a(lu,\"transform\");function fu(r){\nif(this[be]+=this[ft].end(),this[be])try{Hi(this,this.mapper(this[be]))}catch(e){\nreturn r(e)}r()}a(fu,\"flush\");function Hi(r,e){e!==void 0&&r.push(e)}a(Hi,\"push\");\nfunction ji(r){return r}a(ji,\"noop\");function pu(r,e,t){switch(r=r||/\\r?\\n/,e=e||\nji,t=t||{},arguments.length){case 1:typeof r==\"function\"?(e=r,r=/\\r?\\n/):typeof r==\n\"object\"&&!(r instanceof RegExp)&&!r[Symbol.split]&&(t=r,r=/\\r?\\n/);break;case 2:\ntypeof r==\"function\"?(t=e,e=r,r=/\\r?\\n/):typeof e==\"object\"&&(t=e,e=ji)}t=Object.\nassign({},t),t.autoDestroy=!0,t.transform=lu,t.flush=fu,t.readableObjectMode=!0;\nlet n=new cu(t);return n[be]=\"\",n[ft]=new hu(\"utf8\"),n.matcher=r,n.mapper=e,n.maxLength=\nt.maxLength,n.skipOverflow=t.skipOverflow||!1,n.overflow=!1,n._destroy=function(i,s){\nthis._writableState.errorEmitted=!1,s(i)},n}a(pu,\"split\");Gi.exports=pu});var zi=I((cl,pe)=>{\"use strict\";p();var Vi=(rr(),N(tr)),du=(or(),N(sr)).Stream,yu=$i(),\nKi=(He(),N(je)),mu=5432,pt=m.platform===\"win32\",tt=m.stderr,gu=56,wu=7,bu=61440,\nSu=32768;function xu(r){return(r&bu)==Su}a(xu,\"isRegFile\");var ke=[\"host\",\"port\",\n\"database\",\"user\",\"password\"],cr=ke.length,vu=ke[cr-1];function hr(){var r=tt instanceof\ndu&&tt.writable===!0;if(r){var e=Array.prototype.slice.call(arguments).concat(`\n`);tt.write(Ki.format.apply(Ki,e))}}a(hr,\"warn\");Object.defineProperty(pe.exports,\n\"isWin\",{get:a(function(){return pt},\"get\"),set:a(function(r){pt=r},\"set\")});pe.\nexports.warnTo=function(r){var e=tt;return tt=r,e};pe.exports.getFileName=function(r){\nvar e=r||m.env,t=e.PGPASSFILE||(pt?Vi.join(e.APPDATA||\"./\",\"postgresql\",\"pgpass.\\\nconf\"):Vi.join(e.HOME||\"./\",\".pgpass\"));return t};pe.exports.usePgPass=function(r,e){\nreturn Object.prototype.hasOwnProperty.call(m.env,\"PGPASSWORD\")?!1:pt?!0:(e=e||\"\\\n<unkn>\",xu(r.mode)?r.mode&(gu|wu)?(hr('WARNING: password file \"%s\" has group or \\\nworld access; permissions should be u=rw (0600) or less',e),!1):!0:(hr('WARNING:\\\n password file \"%s\" is not a plain file',e),!1))};var Eu=pe.exports.match=function(r,e){\nreturn ke.slice(0,-1).reduce(function(t,n,i){return i==1&&Number(r[n]||mu)===Number(\ne[n])?t&&!0:t&&(e[n]===\"*\"||e[n]===r[n])},!0)};pe.exports.getPassword=function(r,e,t){\nvar n,i=e.pipe(yu());function s(c){var h=_u(c);h&&Au(h)&&Eu(r,h)&&(n=h[vu],i.end())}\na(s,\"onLine\");var o=a(function(){e.destroy(),t(n)},\"onEnd\"),u=a(function(c){e.destroy(),\nhr(\"WARNING: error on reading file: %s\",c),t(void 0)},\"onErr\");e.on(\"error\",u),i.\non(\"data\",s).on(\"end\",o).on(\"error\",u)};var _u=pe.exports.parseLine=function(r){\nif(r.length<11||r.match(/^\\s+#/))return null;for(var e=\"\",t=\"\",n=0,i=0,s=0,o={},\nu=!1,c=a(function(l,d,b){var C=r.substring(d,b);Object.hasOwnProperty.call(m.env,\n\"PGPASS_NO_DEESCAPE\")||(C=C.replace(/\\\\([:\\\\])/g,\"$1\")),o[ke[l]]=C},\"addToObj\"),\nh=0;h<r.length-1;h+=1){if(e=r.charAt(h+1),t=r.charAt(h),u=n==cr-1,u){c(n,i);break}\nh>=0&&e==\":\"&&t!==\"\\\\\"&&(c(n,i,h+1),i=h+2,n+=1)}return o=Object.keys(o).length===\ncr?o:null,o},Au=pe.exports.isValidEntry=function(r){for(var e={0:function(o){return o.\nlength>0},1:function(o){return o===\"*\"?!0:(o=Number(o),isFinite(o)&&o>0&&o<9007199254740992&&\nMath.floor(o)===o)},2:function(o){return o.length>0},3:function(o){return o.length>\n0},4:function(o){return o.length>0}},t=0;t<ke.length;t+=1){var n=e[t],i=r[ke[t]]||\n\"\",s=n(i);if(!s)return!1}return!0}});var Zi=I((pl,lr)=>{\"use strict\";p();var fl=(rr(),N(tr)),Yi=(ir(),N(nr)),dt=zi();\nlr.exports=function(r,e){var t=dt.getFileName();Yi.stat(t,function(n,i){if(n||!dt.\nusePgPass(i,t))return e(void 0);var s=Yi.createReadStream(t);dt.getPassword(r,s,\ne)})};lr.exports.warnTo=dt.warnTo});var mt=I((yl,Ji)=>{\"use strict\";p();var Cu=Je();function yt(r){this._types=r||Cu,\nthis.text={},this.binary={}}a(yt,\"TypeOverrides\");yt.prototype.getOverrides=function(r){\nswitch(r){case\"text\":return this.text;case\"binary\":return this.binary;default:return{}}};\nyt.prototype.setTypeParser=function(r,e,t){typeof e==\"function\"&&(t=e,e=\"text\"),\nthis.getOverrides(e)[r]=t};yt.prototype.getTypeParser=function(r,e){return e=e||\n\"text\",this.getOverrides(e)[r]||this._types.getTypeParser(r,e)};Ji.exports=yt});var Xi={};ie(Xi,{default:()=>Tu});var Tu,es=z(()=>{\"use strict\";p();Tu={}});var ts={};ie(ts,{parse:()=>fr});function fr(r,e=!1){let{protocol:t}=new URL(r),n=\"\\\nhttp:\"+r.substring(t.length),{username:i,password:s,host:o,hostname:u,port:c,pathname:h,\nsearch:l,searchParams:d,hash:b}=new URL(n);s=decodeURIComponent(s),i=decodeURIComponent(\ni),h=decodeURIComponent(h);let C=i+\":\"+s,B=e?Object.fromEntries(d.entries()):l;return{\nhref:r,protocol:t,auth:C,username:i,password:s,host:o,hostname:u,port:c,pathname:h,\nsearch:l,query:B,hash:b}}var pr=z(()=>{\"use strict\";p();a(fr,\"parse\")});var ns=I((xl,rs)=>{\"use strict\";p();var Iu=(pr(),N(ts)),dr=(ir(),N(nr));function yr(r){\nif(r.charAt(0)===\"/\"){var t=r.split(\" \");return{host:t[0],database:t[1]}}var e=Iu.\nparse(/ |%[^a-f0-9]|%[a-f0-9][^a-f0-9]/i.test(r)?encodeURI(r).replace(/\\%25(\\d\\d)/g,\n\"%$1\"):r,!0),t=e.query;for(var n in t)Array.isArray(t[n])&&(t[n]=t[n][t[n].length-\n1]);var i=(e.auth||\":\").split(\":\");if(t.user=i[0],t.password=i.splice(1).join(\":\"),\nt.port=e.port,e.protocol==\"socket:\")return t.host=decodeURI(e.pathname),t.database=\ne.query.db,t.client_encoding=e.query.encoding,t;t.host||(t.host=e.hostname);var s=e.\npathname;if(!t.host&&s&&/^%2f/i.test(s)){var o=s.split(\"/\");t.host=decodeURIComponent(\no[0]),s=o.splice(1).join(\"/\")}switch(s&&s.charAt(0)===\"/\"&&(s=s.slice(1)||null),\nt.database=s&&decodeURI(s),(t.ssl===\"true\"||t.ssl===\"1\")&&(t.ssl=!0),t.ssl===\"0\"&&\n(t.ssl=!1),(t.sslcert||t.sslkey||t.sslrootcert||t.sslmode)&&(t.ssl={}),t.sslcert&&\n(t.ssl.cert=dr.readFileSync(t.sslcert).toString()),t.sslkey&&(t.ssl.key=dr.readFileSync(\nt.sslkey).toString()),t.sslrootcert&&(t.ssl.ca=dr.readFileSync(t.sslrootcert).toString()),\nt.sslmode){case\"disable\":{t.ssl=!1;break}case\"prefer\":case\"require\":case\"verify-\\\nca\":case\"verify-full\":break;case\"no-verify\":{t.ssl.rejectUnauthorized=!1;break}}\nreturn t}a(yr,\"parse\");rs.exports=yr;yr.parse=yr});var gt=I((_l,os)=>{\"use strict\";p();var Pu=(es(),N(Xi)),ss=Xe(),is=ns().parse,$=a(\nfunction(r,e,t){return t===void 0?t=m.env[\"PG\"+r.toUpperCase()]:t===!1||(t=m.env[t]),\ne[r]||t||ss[r]},\"val\"),Bu=a(function(){switch(m.env.PGSSLMODE){case\"disable\":return!1;case\"\\\nprefer\":case\"require\":case\"verify-ca\":case\"verify-full\":return!0;case\"no-verify\":\nreturn{rejectUnauthorized:!1}}return ss.ssl},\"readSSLConfigFromEnvironment\"),Ue=a(\nfunction(r){return\"'\"+(\"\"+r).replace(/\\\\/g,\"\\\\\\\\\").replace(/'/g,\"\\\\'\")+\"'\"},\"quo\\\nteParamValue\"),ne=a(function(r,e,t){var n=e[t];n!=null&&r.push(t+\"=\"+Ue(n))},\"ad\\\nd\"),gr=class gr{constructor(e){e=typeof e==\"string\"?is(e):e||{},e.connectionString&&\n(e=Object.assign({},e,is(e.connectionString))),this.user=$(\"user\",e),this.database=\n$(\"database\",e),this.database===void 0&&(this.database=this.user),this.port=parseInt(\n$(\"port\",e),10),this.host=$(\"host\",e),Object.defineProperty(this,\"password\",{configurable:!0,\nenumerable:!1,writable:!0,value:$(\"password\",e)}),this.binary=$(\"binary\",e),this.\noptions=$(\"options\",e),this.ssl=typeof e.ssl>\"u\"?Bu():e.ssl,typeof this.ssl==\"st\\\nring\"&&this.ssl===\"true\"&&(this.ssl=!0),this.ssl===\"no-verify\"&&(this.ssl={rejectUnauthorized:!1}),\nthis.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,\"key\",{enumerable:!1}),this.\nclient_encoding=$(\"client_encoding\",e),this.replication=$(\"replication\",e),this.\nisDomainSocket=!(this.host||\"\").indexOf(\"/\"),this.application_name=$(\"applicatio\\\nn_name\",e,\"PGAPPNAME\"),this.fallback_application_name=$(\"fallback_application_na\\\nme\",e,!1),this.statement_timeout=$(\"statement_timeout\",e,!1),this.lock_timeout=$(\n\"lock_timeout\",e,!1),this.idle_in_transaction_session_timeout=$(\"idle_in_transac\\\ntion_session_timeout\",e,!1),this.query_timeout=$(\"query_timeout\",e,!1),e.connectionTimeoutMillis===\nvoid 0?this.connect_timeout=m.env.PGCONNECT_TIMEOUT||0:this.connect_timeout=Math.\nfloor(e.connectionTimeoutMillis/1e3),e.keepAlive===!1?this.keepalives=0:e.keepAlive===\n!0&&(this.keepalives=1),typeof e.keepAliveInitialDelayMillis==\"number\"&&(this.keepalives_idle=\nMath.floor(e.keepAliveInitialDelayMillis/1e3))}getLibpqConnectionString(e){var t=[];\nne(t,this,\"user\"),ne(t,this,\"password\"),ne(t,this,\"port\"),ne(t,this,\"application\\\n_name\"),ne(t,this,\"fallback_application_name\"),ne(t,this,\"connect_timeout\"),ne(t,\nthis,\"options\");var n=typeof this.ssl==\"object\"?this.ssl:this.ssl?{sslmode:this.\nssl}:{};if(ne(t,n,\"sslmode\"),ne(t,n,\"sslca\"),ne(t,n,\"sslkey\"),ne(t,n,\"sslcert\"),\nne(t,n,\"sslrootcert\"),this.database&&t.push(\"dbname=\"+Ue(this.database)),this.replication&&\nt.push(\"replication=\"+Ue(this.replication)),this.host&&t.push(\"host=\"+Ue(this.host)),\nthis.isDomainSocket)return e(null,t.join(\" \"));this.client_encoding&&t.push(\"cli\\\nent_encoding=\"+Ue(this.client_encoding)),Pu.lookup(this.host,function(i,s){return i?\ne(i,null):(t.push(\"hostaddr=\"+Ue(s)),e(null,t.join(\" \")))})}};a(gr,\"ConnectionPa\\\nrameters\");var mr=gr;os.exports=mr});var cs=I((Tl,us)=>{\"use strict\";p();var Lu=Je(),as=/^([A-Za-z]+)(?: (\\d+))?(?: (\\d+))?/,\nbr=class br{constructor(e,t){this.command=null,this.rowCount=null,this.oid=null,\nthis.rows=[],this.fields=[],this._parsers=void 0,this._types=t,this.RowCtor=null,\nthis.rowAsArray=e===\"array\",this.rowAsArray&&(this.parseRow=this._parseRowAsArray)}addCommandComplete(e){\nvar t;e.text?t=as.exec(e.text):t=as.exec(e.command),t&&(this.command=t[1],t[3]?(this.\noid=parseInt(t[2],10),this.rowCount=parseInt(t[3],10)):t[2]&&(this.rowCount=parseInt(\nt[2],10)))}_parseRowAsArray(e){for(var t=new Array(e.length),n=0,i=e.length;n<i;n++){\nvar s=e[n];s!==null?t[n]=this._parsers[n](s):t[n]=null}return t}parseRow(e){for(var t={},\nn=0,i=e.length;n<i;n++){var s=e[n],o=this.fields[n].name;s!==null?t[o]=this._parsers[n](\ns):t[o]=null}return t}addRow(e){this.rows.push(e)}addFields(e){this.fields=e,this.\nfields.length&&(this._parsers=new Array(e.length));for(var t=0;t<e.length;t++){var n=e[t];\nthis._types?this._parsers[t]=this._types.getTypeParser(n.dataTypeID,n.format||\"t\\\next\"):this._parsers[t]=Lu.getTypeParser(n.dataTypeID,n.format||\"text\")}}};a(br,\"\\\nResult\");var wr=br;us.exports=wr});var ps=I((Bl,fs)=>{\"use strict\";p();var{EventEmitter:Ru}=we(),hs=cs(),ls=et(),xr=class xr extends Ru{constructor(e,t,n){\nsuper(),e=ls.normalizeQueryConfig(e,t,n),this.text=e.text,this.values=e.values,this.\nrows=e.rows,this.types=e.types,this.name=e.name,this.binary=e.binary,this.portal=\ne.portal||\"\",this.callback=e.callback,this._rowMode=e.rowMode,m.domain&&e.callback&&\n(this.callback=m.domain.bind(e.callback)),this._result=new hs(this._rowMode,this.\ntypes),this._results=this._result,this.isPreparedStatement=!1,this._canceledDueToError=\n!1,this._promise=null}requiresPreparation(){return this.name||this.rows?!0:!this.\ntext||!this.values?!1:this.values.length>0}_checkForMultirow(){this._result.command&&\n(Array.isArray(this._results)||(this._results=[this._result]),this._result=new hs(\nthis._rowMode,this.types),this._results.push(this._result))}handleRowDescription(e){\nthis._checkForMultirow(),this._result.addFields(e.fields),this._accumulateRows=this.\ncallback||!this.listeners(\"row\").length}handleDataRow(e){let t;if(!this._canceledDueToError){\ntry{t=this._result.parseRow(e.fields)}catch(n){this._canceledDueToError=n;return}\nthis.emit(\"row\",t,this._result),this._accumulateRows&&this._result.addRow(t)}}handleCommandComplete(e,t){\nthis._checkForMultirow(),this._result.addCommandComplete(e),this.rows&&t.sync()}handleEmptyQuery(e){\nthis.rows&&e.sync()}handleError(e,t){if(this._canceledDueToError&&(e=this._canceledDueToError,\nthis._canceledDueToError=!1),this.callback)return this.callback(e);this.emit(\"er\\\nror\",e)}handleReadyForQuery(e){if(this._canceledDueToError)return this.handleError(\nthis._canceledDueToError,e);if(this.callback)try{this.callback(null,this._results)}catch(t){\nm.nextTick(()=>{throw t})}this.emit(\"end\",this._results)}submit(e){if(typeof this.\ntext!=\"string\"&&typeof this.name!=\"string\")return new Error(\"A query must have e\\\nither text or a name. Supplying neither is unsupported.\");let t=e.parsedStatements[this.\nname];return this.text&&t&&this.text!==t?new Error(`Prepared statements must be \\\nunique - '${this.name}' was used for a different statement`):this.values&&!Array.\nisArray(this.values)?new Error(\"Query values must be an array\"):(this.requiresPreparation()?\nthis.prepare(e):e.query(this.text),null)}hasBeenParsed(e){return this.name&&e.parsedStatements[this.\nname]}handlePortalSuspended(e){this._getRows(e,this.rows)}_getRows(e,t){e.execute(\n{portal:this.portal,rows:t}),t?e.flush():e.sync()}prepare(e){this.isPreparedStatement=\n!0,this.hasBeenParsed(e)||e.parse({text:this.text,name:this.name,types:this.types});\ntry{e.bind({portal:this.portal,statement:this.name,values:this.values,binary:this.\nbinary,valueMapper:ls.prepareValue})}catch(t){this.handleError(t,e);return}e.describe(\n{type:\"P\",name:this.portal||\"\"}),this._getRows(e,this.rows)}handleCopyInResponse(e){\ne.sendCopyFail(\"No source stream defined\")}handleCopyData(e,t){}};a(xr,\"Query\");\nvar Sr=xr;fs.exports=Sr});var ys={};ie(ys,{Socket:()=>_e,isIP:()=>Fu});function Fu(r){return 0}var ds,Mu,E,\n_e,wt=z(()=>{\"use strict\";p();ds=Te(we(),1);a(Fu,\"isIP\");Mu=a(r=>r.replace(/^[^.]+\\./,\n\"api.\"),\"transformHost\"),E=class E extends ds.EventEmitter{constructor(){super(...arguments);\n_(this,\"opts\",{});_(this,\"connecting\",!1);_(this,\"pending\",!0);_(this,\"writable\",\n!0);_(this,\"encrypted\",!1);_(this,\"authorized\",!1);_(this,\"destroyed\",!1);_(this,\n\"ws\",null);_(this,\"writeBuffer\");_(this,\"tlsState\",0);_(this,\"tlsRead\");_(this,\"\\\ntlsWrite\")}static get poolQueryViaFetch(){return E.opts.poolQueryViaFetch??E.defaults.\npoolQueryViaFetch}static set poolQueryViaFetch(t){E.opts.poolQueryViaFetch=t}static get fetchEndpoint(){\nreturn E.opts.fetchEndpoint??E.defaults.fetchEndpoint}static set fetchEndpoint(t){\nE.opts.fetchEndpoint=t}static get fetchConnectionCache(){return!0}static set fetchConnectionCache(t){\nconsole.warn(\"The `fetchConnectionCache` option is deprecated (now always `true`\\\n)\")}static get fetchFunction(){return E.opts.fetchFunction??E.defaults.fetchFunction}static set fetchFunction(t){\nE.opts.fetchFunction=t}static get webSocketConstructor(){return E.opts.webSocketConstructor??\nE.defaults.webSocketConstructor}static set webSocketConstructor(t){E.opts.webSocketConstructor=\nt}get webSocketConstructor(){return this.opts.webSocketConstructor??E.webSocketConstructor}set webSocketConstructor(t){\nthis.opts.webSocketConstructor=t}static get wsProxy(){return E.opts.wsProxy??E.defaults.\nwsProxy}static set wsProxy(t){E.opts.wsProxy=t}get wsProxy(){return this.opts.wsProxy??\nE.wsProxy}set wsProxy(t){this.opts.wsProxy=t}static get coalesceWrites(){return E.\nopts.coalesceWrites??E.defaults.coalesceWrites}static set coalesceWrites(t){E.opts.\ncoalesceWrites=t}get coalesceWrites(){return this.opts.coalesceWrites??E.coalesceWrites}set coalesceWrites(t){\nthis.opts.coalesceWrites=t}static get useSecureWebSocket(){return E.opts.useSecureWebSocket??\nE.defaults.useSecureWebSocket}static set useSecureWebSocket(t){E.opts.useSecureWebSocket=\nt}get useSecureWebSocket(){return this.opts.useSecureWebSocket??E.useSecureWebSocket}set useSecureWebSocket(t){\nthis.opts.useSecureWebSocket=t}static get forceDisablePgSSL(){return E.opts.forceDisablePgSSL??\nE.defaults.forceDisablePgSSL}static set forceDisablePgSSL(t){E.opts.forceDisablePgSSL=\nt}get forceDisablePgSSL(){return this.opts.forceDisablePgSSL??E.forceDisablePgSSL}set forceDisablePgSSL(t){\nthis.opts.forceDisablePgSSL=t}static get disableSNI(){return E.opts.disableSNI??\nE.defaults.disableSNI}static set disableSNI(t){E.opts.disableSNI=t}get disableSNI(){\nreturn this.opts.disableSNI??E.disableSNI}set disableSNI(t){this.opts.disableSNI=\nt}static get pipelineConnect(){return E.opts.pipelineConnect??E.defaults.pipelineConnect}static set pipelineConnect(t){\nE.opts.pipelineConnect=t}get pipelineConnect(){return this.opts.pipelineConnect??\nE.pipelineConnect}set pipelineConnect(t){this.opts.pipelineConnect=t}static get subtls(){\nreturn E.opts.subtls??E.defaults.subtls}static set subtls(t){E.opts.subtls=t}get subtls(){\nreturn this.opts.subtls??E.subtls}set subtls(t){this.opts.subtls=t}static get pipelineTLS(){\nreturn E.opts.pipelineTLS??E.defaults.pipelineTLS}static set pipelineTLS(t){E.opts.\npipelineTLS=t}get pipelineTLS(){return this.opts.pipelineTLS??E.pipelineTLS}set pipelineTLS(t){\nthis.opts.pipelineTLS=t}static get rootCerts(){return E.opts.rootCerts??E.defaults.\nrootCerts}static set rootCerts(t){E.opts.rootCerts=t}get rootCerts(){return this.\nopts.rootCerts??E.rootCerts}set rootCerts(t){this.opts.rootCerts=t}wsProxyAddrForHost(t,n){\nlet i=this.wsProxy;if(i===void 0)throw new Error(\"No WebSocket proxy is configur\\\ned. Please see https://github.com/neondatabase/serverless/blob/main/CONFIG.md#ws\\\nproxy-string--host-string-port-number--string--string\");return typeof i==\"functi\\\non\"?i(t,n):`${i}?address=${t}:${n}`}setNoDelay(){return this}setKeepAlive(){return this}ref(){\nreturn this}unref(){return this}connect(t,n,i){this.connecting=!0,i&&this.once(\"\\\nconnect\",i);let s=a(()=>{this.connecting=!1,this.pending=!1,this.emit(\"connect\"),\nthis.emit(\"ready\")},\"handleWebSocketOpen\"),o=a((c,h=!1)=>{c.binaryType=\"arraybuf\\\nfer\",c.addEventListener(\"error\",l=>{this.emit(\"error\",l),this.emit(\"close\")}),c.\naddEventListener(\"message\",l=>{if(this.tlsState===0){let d=y.from(l.data);this.emit(\n\"data\",d)}}),c.addEventListener(\"close\",()=>{this.emit(\"close\")}),h?s():c.addEventListener(\n\"open\",s)},\"configureWebSocket\"),u;try{u=this.wsProxyAddrForHost(n,typeof t==\"st\\\nring\"?parseInt(t,10):t)}catch(c){this.emit(\"error\",c),this.emit(\"close\");return}\ntry{let h=(this.useSecureWebSocket?\"wss:\":\"ws:\")+\"//\"+u;if(this.webSocketConstructor!==\nvoid 0)this.ws=new this.webSocketConstructor(h),o(this.ws);else try{this.ws=new WebSocket(\nh),o(this.ws)}catch{this.ws=new __unstable_WebSocket(h),o(this.ws)}}catch(c){let l=(this.\nuseSecureWebSocket?\"https:\":\"http:\")+\"//\"+u;fetch(l,{headers:{Upgrade:\"websocket\"}}).\nthen(d=>{if(this.ws=d.webSocket,this.ws==null)throw c;this.ws.accept(),o(this.ws,\n!0)}).catch(d=>{this.emit(\"error\",new Error(`All attempts to open a WebSocket to\\\n connect to the database failed. Please refer to https://github.com/neondatabase\\\n/serverless/blob/main/CONFIG.md#websocketconstructor-typeof-websocket--undefined\\\n. Details: ${d.message}`)),this.emit(\"close\")})}}async startTls(t){if(this.subtls===\nvoid 0)throw new Error(\"For Postgres SSL connections, you must set `neonConfig.s\\\nubtls` to the subtls library. See https://github.com/neondatabase/serverless/blo\\\nb/main/CONFIG.md for more information.\");this.tlsState=1;let n=this.subtls.TrustedCert.\nfromPEM(this.rootCerts),i=new this.subtls.WebSocketReadQueue(this.ws),s=i.read.bind(\ni),o=this.rawWrite.bind(this),[u,c]=await this.subtls.startTls(t,n,s,o,{useSNI:!this.\ndisableSNI,expectPreData:this.pipelineTLS?new Uint8Array([83]):void 0});this.tlsRead=\nu,this.tlsWrite=c,this.tlsState=2,this.encrypted=!0,this.authorized=!0,this.emit(\n\"secureConnection\",this),this.tlsReadLoop()}async tlsReadLoop(){for(;;){let t=await this.\ntlsRead();if(t===void 0)break;{let n=y.from(t);this.emit(\"data\",n)}}}rawWrite(t){\nif(!this.coalesceWrites){this.ws.send(t);return}if(this.writeBuffer===void 0)this.\nwriteBuffer=t,setTimeout(()=>{this.ws.send(this.writeBuffer),this.writeBuffer=void 0},\n0);else{let n=new Uint8Array(this.writeBuffer.length+t.length);n.set(this.writeBuffer),\nn.set(t,this.writeBuffer.length),this.writeBuffer=n}}write(t,n=\"utf8\",i=s=>{}){return t.\nlength===0?(i(),!0):(typeof t==\"string\"&&(t=y.from(t,n)),this.tlsState===0?(this.\nrawWrite(t),i()):this.tlsState===1?this.once(\"secureConnection\",()=>{this.write(\nt,n,i)}):(this.tlsWrite(t),i()),!0)}end(t=y.alloc(0),n=\"utf8\",i=()=>{}){return this.\nwrite(t,n,()=>{this.ws.close(),i()}),this}destroy(){return this.destroyed=!0,this.\nend()}};a(E,\"Socket\"),_(E,\"defaults\",{poolQueryViaFetch:!1,fetchEndpoint:a(t=>\"h\\\nttps://\"+Mu(t)+\"/sql\",\"fetchEndpoint\"),fetchConnectionCache:!0,fetchFunction:void 0,\nwebSocketConstructor:void 0,wsProxy:a(t=>t+\"/v2\",\"wsProxy\"),useSecureWebSocket:!0,\nforceDisablePgSSL:!0,coalesceWrites:!0,pipelineConnect:\"password\",subtls:void 0,\nrootCerts:\"\",pipelineTLS:!1,disableSNI:!1}),_(E,\"opts\",{});_e=E});var Yr=I(T=>{\"use strict\";p();Object.defineProperty(T,\"__esModule\",{value:!0});T.\nNoticeMessage=T.DataRowMessage=T.CommandCompleteMessage=T.ReadyForQueryMessage=T.\nNotificationResponseMessage=T.BackendKeyDataMessage=T.AuthenticationMD5Password=\nT.ParameterStatusMessage=T.ParameterDescriptionMessage=T.RowDescriptionMessage=T.\nField=T.CopyResponse=T.CopyDataMessage=T.DatabaseError=T.copyDone=T.emptyQuery=T.\nreplicationStart=T.portalSuspended=T.noData=T.closeComplete=T.bindComplete=T.parseComplete=\nvoid 0;T.parseComplete={name:\"parseComplete\",length:5};T.bindComplete={name:\"bin\\\ndComplete\",length:5};T.closeComplete={name:\"closeComplete\",length:5};T.noData={name:\"\\\nnoData\",length:5};T.portalSuspended={name:\"portalSuspended\",length:5};T.replicationStart=\n{name:\"replicationStart\",length:4};T.emptyQuery={name:\"emptyQuery\",length:4};T.copyDone=\n{name:\"copyDone\",length:4};var kr=class kr extends Error{constructor(e,t,n){super(\ne),this.length=t,this.name=n}};a(kr,\"DatabaseError\");var vr=kr;T.DatabaseError=vr;\nvar Ur=class Ur{constructor(e,t){this.length=e,this.chunk=t,this.name=\"copyData\"}};\na(Ur,\"CopyDataMessage\");var Er=Ur;T.CopyDataMessage=Er;var Or=class Or{constructor(e,t,n,i){\nthis.length=e,this.name=t,this.binary=n,this.columnTypes=new Array(i)}};a(Or,\"Co\\\npyResponse\");var _r=Or;T.CopyResponse=_r;var Nr=class Nr{constructor(e,t,n,i,s,o,u){\nthis.name=e,this.tableID=t,this.columnID=n,this.dataTypeID=i,this.dataTypeSize=s,\nthis.dataTypeModifier=o,this.format=u}};a(Nr,\"Field\");var Ar=Nr;T.Field=Ar;var qr=class qr{constructor(e,t){\nthis.length=e,this.fieldCount=t,this.name=\"rowDescription\",this.fields=new Array(\nthis.fieldCount)}};a(qr,\"RowDescriptionMessage\");var Cr=qr;T.RowDescriptionMessage=\nCr;var Qr=class Qr{constructor(e,t){this.length=e,this.parameterCount=t,this.name=\n\"parameterDescription\",this.dataTypeIDs=new Array(this.parameterCount)}};a(Qr,\"P\\\narameterDescriptionMessage\");var Tr=Qr;T.ParameterDescriptionMessage=Tr;var Wr=class Wr{constructor(e,t,n){\nthis.length=e,this.parameterName=t,this.parameterValue=n,this.name=\"parameterSta\\\ntus\"}};a(Wr,\"ParameterStatusMessage\");var Ir=Wr;T.ParameterStatusMessage=Ir;var jr=class jr{constructor(e,t){\nthis.length=e,this.salt=t,this.name=\"authenticationMD5Password\"}};a(jr,\"Authenti\\\ncationMD5Password\");var Pr=jr;T.AuthenticationMD5Password=Pr;var Hr=class Hr{constructor(e,t,n){\nthis.length=e,this.processID=t,this.secretKey=n,this.name=\"backendKeyData\"}};a(Hr,\n\"BackendKeyDataMessage\");var Br=Hr;T.BackendKeyDataMessage=Br;var Gr=class Gr{constructor(e,t,n,i){\nthis.length=e,this.processId=t,this.channel=n,this.payload=i,this.name=\"notifica\\\ntion\"}};a(Gr,\"NotificationResponseMessage\");var Lr=Gr;T.NotificationResponseMessage=\nLr;var $r=class $r{constructor(e,t){this.length=e,this.status=t,this.name=\"ready\\\nForQuery\"}};a($r,\"ReadyForQueryMessage\");var Rr=$r;T.ReadyForQueryMessage=Rr;var Vr=class Vr{constructor(e,t){\nthis.length=e,this.text=t,this.name=\"commandComplete\"}};a(Vr,\"CommandCompleteMes\\\nsage\");var Fr=Vr;T.CommandCompleteMessage=Fr;var Kr=class Kr{constructor(e,t){this.\nlength=e,this.fields=t,this.name=\"dataRow\",this.fieldCount=t.length}};a(Kr,\"Data\\\nRowMessage\");var Mr=Kr;T.DataRowMessage=Mr;var zr=class zr{constructor(e,t){this.\nlength=e,this.message=t,this.name=\"notice\"}};a(zr,\"NoticeMessage\");var Dr=zr;T.NoticeMessage=\nDr});var ms=I(bt=>{\"use strict\";p();Object.defineProperty(bt,\"__esModule\",{value:!0});\nbt.Writer=void 0;var Jr=class Jr{constructor(e=256){this.size=e,this.offset=5,this.\nheaderPosition=0,this.buffer=y.allocUnsafe(e)}ensure(e){var t=this.buffer.length-\nthis.offset;if(t<e){var n=this.buffer,i=n.length+(n.length>>1)+e;this.buffer=y.allocUnsafe(\ni),n.copy(this.buffer)}}addInt32(e){return this.ensure(4),this.buffer[this.offset++]=\ne>>>24&255,this.buffer[this.offset++]=e>>>16&255,this.buffer[this.offset++]=e>>>\n8&255,this.buffer[this.offset++]=e>>>0&255,this}addInt16(e){return this.ensure(2),\nthis.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addCString(e){\nif(!e)this.ensure(1);else{var t=y.byteLength(e);this.ensure(t+1),this.buffer.write(\ne,this.offset,\"utf-8\"),this.offset+=t}return this.buffer[this.offset++]=0,this}addString(e=\"\"){\nvar t=y.byteLength(e);return this.ensure(t),this.buffer.write(e,this.offset),this.\noffset+=t,this}add(e){return this.ensure(e.length),e.copy(this.buffer,this.offset),\nthis.offset+=e.length,this}join(e){if(e){this.buffer[this.headerPosition]=e;let t=this.\noffset-(this.headerPosition+1);this.buffer.writeInt32BE(t,this.headerPosition+1)}\nreturn this.buffer.slice(e?0:5,this.offset)}flush(e){var t=this.join(e);return this.\noffset=5,this.headerPosition=0,this.buffer=y.allocUnsafe(this.size),t}};a(Jr,\"Wr\\\niter\");var Zr=Jr;bt.Writer=Zr});var ws=I(xt=>{\"use strict\";p();Object.defineProperty(xt,\"__esModule\",{value:!0});\nxt.serialize=void 0;var Xr=ms(),M=new Xr.Writer,Du=a(r=>{M.addInt16(3).addInt16(\n0);for(let n of Object.keys(r))M.addCString(n).addCString(r[n]);M.addCString(\"cl\\\nient_encoding\").addCString(\"UTF8\");var e=M.addCString(\"\").flush(),t=e.length+4;return new Xr.\nWriter().addInt32(t).add(e).flush()},\"startup\"),ku=a(()=>{let r=y.allocUnsafe(8);\nreturn r.writeInt32BE(8,0),r.writeInt32BE(80877103,4),r},\"requestSsl\"),Uu=a(r=>M.\naddCString(r).flush(112),\"password\"),Ou=a(function(r,e){return M.addCString(r).addInt32(\ny.byteLength(e)).addString(e),M.flush(112)},\"sendSASLInitialResponseMessage\"),Nu=a(\nfunction(r){return M.addString(r).flush(112)},\"sendSCRAMClientFinalMessage\"),qu=a(\nr=>M.addCString(r).flush(81),\"query\"),gs=[],Qu=a(r=>{let e=r.name||\"\";e.length>63&&\n(console.error(\"Warning! Postgres only supports 63 characters for query names.\"),\nconsole.error(\"You supplied %s (%s)\",e,e.length),console.error(\"This can cause c\\\nonflicts and silent errors executing queries\"));let t=r.types||gs;for(var n=t.length,\ni=M.addCString(e).addCString(r.text).addInt16(n),s=0;s<n;s++)i.addInt32(t[s]);return M.\nflush(80)},\"parse\"),Oe=new Xr.Writer,Wu=a(function(r,e){for(let t=0;t<r.length;t++){\nlet n=e?e(r[t],t):r[t];n==null?(M.addInt16(0),Oe.addInt32(-1)):n instanceof y?(M.\naddInt16(1),Oe.addInt32(n.length),Oe.add(n)):(M.addInt16(0),Oe.addInt32(y.byteLength(\nn)),Oe.addString(n))}},\"writeValues\"),ju=a((r={})=>{let e=r.portal||\"\",t=r.statement||\n\"\",n=r.binary||!1,i=r.values||gs,s=i.length;return M.addCString(e).addCString(t),\nM.addInt16(s),Wu(i,r.valueMapper),M.addInt16(s),M.add(Oe.flush()),M.addInt16(n?1:\n0),M.flush(66)},\"bind\"),Hu=y.from([69,0,0,0,9,0,0,0,0,0]),Gu=a(r=>{if(!r||!r.portal&&\n!r.rows)return Hu;let e=r.portal||\"\",t=r.rows||0,n=y.byteLength(e),i=4+n+1+4,s=y.\nallocUnsafe(1+i);return s[0]=69,s.writeInt32BE(i,1),s.write(e,5,\"utf-8\"),s[n+5]=\n0,s.writeUInt32BE(t,s.length-4),s},\"execute\"),$u=a((r,e)=>{let t=y.allocUnsafe(16);\nreturn t.writeInt32BE(16,0),t.writeInt16BE(1234,4),t.writeInt16BE(5678,6),t.writeInt32BE(\nr,8),t.writeInt32BE(e,12),t},\"cancel\"),en=a((r,e)=>{let n=4+y.byteLength(e)+1,i=y.\nallocUnsafe(1+n);return i[0]=r,i.writeInt32BE(n,1),i.write(e,5,\"utf-8\"),i[n]=0,i},\n\"cstringMessage\"),Vu=M.addCString(\"P\").flush(68),Ku=M.addCString(\"S\").flush(68),\nzu=a(r=>r.name?en(68,`${r.type}${r.name||\"\"}`):r.type===\"P\"?Vu:Ku,\"describe\"),Yu=a(\nr=>{let e=`${r.type}${r.name||\"\"}`;return en(67,e)},\"close\"),Zu=a(r=>M.add(r).flush(\n100),\"copyData\"),Ju=a(r=>en(102,r),\"copyFail\"),St=a(r=>y.from([r,0,0,0,4]),\"code\\\nOnlyBuffer\"),Xu=St(72),ec=St(83),tc=St(88),rc=St(99),nc={startup:Du,password:Uu,\nrequestSsl:ku,sendSASLInitialResponseMessage:Ou,sendSCRAMClientFinalMessage:Nu,query:qu,\nparse:Qu,bind:ju,execute:Gu,describe:zu,close:Yu,flush:a(()=>Xu,\"flush\"),sync:a(\n()=>ec,\"sync\"),end:a(()=>tc,\"end\"),copyData:Zu,copyDone:a(()=>rc,\"copyDone\"),copyFail:Ju,\ncancel:$u};xt.serialize=nc});var bs=I(vt=>{\"use strict\";p();Object.defineProperty(vt,\"__esModule\",{value:!0});\nvt.BufferReader=void 0;var ic=y.allocUnsafe(0),rn=class rn{constructor(e=0){this.\noffset=e,this.buffer=ic,this.encoding=\"utf-8\"}setBuffer(e,t){this.offset=e,this.\nbuffer=t}int16(){let e=this.buffer.readInt16BE(this.offset);return this.offset+=\n2,e}byte(){let e=this.buffer[this.offset];return this.offset++,e}int32(){let e=this.\nbuffer.readInt32BE(this.offset);return this.offset+=4,e}string(e){let t=this.buffer.\ntoString(this.encoding,this.offset,this.offset+e);return this.offset+=e,t}cstring(){\nlet e=this.offset,t=e;for(;this.buffer[t++]!==0;);return this.offset=t,this.buffer.\ntoString(this.encoding,e,t-1)}bytes(e){let t=this.buffer.slice(this.offset,this.\noffset+e);return this.offset+=e,t}};a(rn,\"BufferReader\");var tn=rn;vt.BufferReader=\ntn});var vs=I(Et=>{\"use strict\";p();Object.defineProperty(Et,\"__esModule\",{value:!0});\nEt.Parser=void 0;var D=Yr(),sc=bs(),nn=1,oc=4,Ss=nn+oc,xs=y.allocUnsafe(0),on=class on{constructor(e){\nif(this.buffer=xs,this.bufferLength=0,this.bufferOffset=0,this.reader=new sc.BufferReader,\ne?.mode===\"binary\")throw new Error(\"Binary mode not supported yet\");this.mode=e?.\nmode||\"text\"}parse(e,t){this.mergeBuffer(e);let n=this.bufferOffset+this.bufferLength,\ni=this.bufferOffset;for(;i+Ss<=n;){let s=this.buffer[i],o=this.buffer.readUInt32BE(\ni+nn),u=nn+o;if(u+i<=n){let c=this.handlePacket(i+Ss,s,o,this.buffer);t(c),i+=u}else\nbreak}i===n?(this.buffer=xs,this.bufferLength=0,this.bufferOffset=0):(this.bufferLength=\nn-i,this.bufferOffset=i)}mergeBuffer(e){if(this.bufferLength>0){let t=this.bufferLength+\ne.byteLength;if(t+this.bufferOffset>this.buffer.byteLength){let i;if(t<=this.buffer.\nbyteLength&&this.bufferOffset>=this.bufferLength)i=this.buffer;else{let s=this.buffer.\nbyteLength*2;for(;t>=s;)s*=2;i=y.allocUnsafe(s)}this.buffer.copy(i,0,this.bufferOffset,\nthis.bufferOffset+this.bufferLength),this.buffer=i,this.bufferOffset=0}e.copy(this.\nbuffer,this.bufferOffset+this.bufferLength),this.bufferLength=t}else this.buffer=\ne,this.bufferOffset=0,this.bufferLength=e.byteLength}handlePacket(e,t,n,i){switch(t){case 50:\nreturn D.bindComplete;case 49:return D.parseComplete;case 51:return D.closeComplete;case 110:\nreturn D.noData;case 115:return D.portalSuspended;case 99:return D.copyDone;case 87:\nreturn D.replicationStart;case 73:return D.emptyQuery;case 68:return this.parseDataRowMessage(\ne,n,i);case 67:return this.parseCommandCompleteMessage(e,n,i);case 90:return this.\nparseReadyForQueryMessage(e,n,i);case 65:return this.parseNotificationMessage(e,\nn,i);case 82:return this.parseAuthenticationResponse(e,n,i);case 83:return this.\nparseParameterStatusMessage(e,n,i);case 75:return this.parseBackendKeyData(e,n,i);case 69:\nreturn this.parseErrorMessage(e,n,i,\"error\");case 78:return this.parseErrorMessage(\ne,n,i,\"notice\");case 84:return this.parseRowDescriptionMessage(e,n,i);case 116:return this.\nparseParameterDescriptionMessage(e,n,i);case 71:return this.parseCopyInMessage(e,\nn,i);case 72:return this.parseCopyOutMessage(e,n,i);case 100:return this.parseCopyData(\ne,n,i);default:return new D.DatabaseError(\"received invalid response: \"+t.toString(\n16),n,\"error\")}}parseReadyForQueryMessage(e,t,n){this.reader.setBuffer(e,n);let i=this.\nreader.string(1);return new D.ReadyForQueryMessage(t,i)}parseCommandCompleteMessage(e,t,n){\nthis.reader.setBuffer(e,n);let i=this.reader.cstring();return new D.CommandCompleteMessage(\nt,i)}parseCopyData(e,t,n){let i=n.slice(e,e+(t-4));return new D.CopyDataMessage(\nt,i)}parseCopyInMessage(e,t,n){return this.parseCopyMessage(e,t,n,\"copyInRespons\\\ne\")}parseCopyOutMessage(e,t,n){return this.parseCopyMessage(e,t,n,\"copyOutRespon\\\nse\")}parseCopyMessage(e,t,n,i){this.reader.setBuffer(e,n);let s=this.reader.byte()!==\n0,o=this.reader.int16(),u=new D.CopyResponse(t,i,s,o);for(let c=0;c<o;c++)u.columnTypes[c]=\nthis.reader.int16();return u}parseNotificationMessage(e,t,n){this.reader.setBuffer(\ne,n);let i=this.reader.int32(),s=this.reader.cstring(),o=this.reader.cstring();return new D.\nNotificationResponseMessage(t,i,s,o)}parseRowDescriptionMessage(e,t,n){this.reader.\nsetBuffer(e,n);let i=this.reader.int16(),s=new D.RowDescriptionMessage(t,i);for(let o=0;o<\ni;o++)s.fields[o]=this.parseField();return s}parseField(){let e=this.reader.cstring(),\nt=this.reader.int32(),n=this.reader.int16(),i=this.reader.int32(),s=this.reader.\nint16(),o=this.reader.int32(),u=this.reader.int16()===0?\"text\":\"binary\";return new D.\nField(e,t,n,i,s,o,u)}parseParameterDescriptionMessage(e,t,n){this.reader.setBuffer(\ne,n);let i=this.reader.int16(),s=new D.ParameterDescriptionMessage(t,i);for(let o=0;o<\ni;o++)s.dataTypeIDs[o]=this.reader.int32();return s}parseDataRowMessage(e,t,n){this.\nreader.setBuffer(e,n);let i=this.reader.int16(),s=new Array(i);for(let o=0;o<i;o++){\nlet u=this.reader.int32();s[o]=u===-1?null:this.reader.string(u)}return new D.DataRowMessage(\nt,s)}parseParameterStatusMessage(e,t,n){this.reader.setBuffer(e,n);let i=this.reader.\ncstring(),s=this.reader.cstring();return new D.ParameterStatusMessage(t,i,s)}parseBackendKeyData(e,t,n){\nthis.reader.setBuffer(e,n);let i=this.reader.int32(),s=this.reader.int32();return new D.\nBackendKeyDataMessage(t,i,s)}parseAuthenticationResponse(e,t,n){this.reader.setBuffer(\ne,n);let i=this.reader.int32(),s={name:\"authenticationOk\",length:t};switch(i){case 0:\nbreak;case 3:s.length===8&&(s.name=\"authenticationCleartextPassword\");break;case 5:\nif(s.length===12){s.name=\"authenticationMD5Password\";let u=this.reader.bytes(4);\nreturn new D.AuthenticationMD5Password(t,u)}break;case 10:s.name=\"authentication\\\nSASL\",s.mechanisms=[];let o;do o=this.reader.cstring(),o&&s.mechanisms.push(o);while(o);\nbreak;case 11:s.name=\"authenticationSASLContinue\",s.data=this.reader.string(t-8);\nbreak;case 12:s.name=\"authenticationSASLFinal\",s.data=this.reader.string(t-8);break;default:\nthrow new Error(\"Unknown authenticationOk message type \"+i)}return s}parseErrorMessage(e,t,n,i){\nthis.reader.setBuffer(e,n);let s={},o=this.reader.string(1);for(;o!==\"\\0\";)s[o]=\nthis.reader.cstring(),o=this.reader.string(1);let u=s.M,c=i===\"notice\"?new D.NoticeMessage(\nt,u):new D.DatabaseError(u,t,i);return c.severity=s.S,c.code=s.C,c.detail=s.D,c.\nhint=s.H,c.position=s.P,c.internalPosition=s.p,c.internalQuery=s.q,c.where=s.W,c.\nschema=s.s,c.table=s.t,c.column=s.c,c.dataType=s.d,c.constraint=s.n,c.file=s.F,c.\nline=s.L,c.routine=s.R,c}};a(on,\"Parser\");var sn=on;Et.Parser=sn});var an=I(Se=>{\"use strict\";p();Object.defineProperty(Se,\"__esModule\",{value:!0});\nSe.DatabaseError=Se.serialize=Se.parse=void 0;var ac=Yr();Object.defineProperty(\nSe,\"DatabaseError\",{enumerable:!0,get:a(function(){return ac.DatabaseError},\"get\")});\nvar uc=ws();Object.defineProperty(Se,\"serialize\",{enumerable:!0,get:a(function(){\nreturn uc.serialize},\"get\")});var cc=vs();function hc(r,e){let t=new cc.Parser;return r.\non(\"data\",n=>t.parse(n,e)),new Promise(n=>r.on(\"end\",()=>n()))}a(hc,\"parse\");Se.\nparse=hc});var Es={};ie(Es,{connect:()=>lc});function lc({socket:r,servername:e}){return r.\nstartTls(e),r}var _s=z(()=>{\"use strict\";p();a(lc,\"connect\")});var hn=I((tf,Ts)=>{\"use strict\";p();var As=(wt(),N(ys)),fc=we().EventEmitter,{parse:pc,\nserialize:Q}=an(),Cs=Q.flush(),dc=Q.sync(),yc=Q.end(),cn=class cn extends fc{constructor(e){\nsuper(),e=e||{},this.stream=e.stream||new As.Socket,this._keepAlive=e.keepAlive,\nthis._keepAliveInitialDelayMillis=e.keepAliveInitialDelayMillis,this.lastBuffer=\n!1,this.parsedStatements={},this.ssl=e.ssl||!1,this._ending=!1,this._emitMessage=\n!1;var t=this;this.on(\"newListener\",function(n){n===\"message\"&&(t._emitMessage=!0)})}connect(e,t){\nvar n=this;this._connecting=!0,this.stream.setNoDelay(!0),this.stream.connect(e,\nt),this.stream.once(\"connect\",function(){n._keepAlive&&n.stream.setKeepAlive(!0,\nn._keepAliveInitialDelayMillis),n.emit(\"connect\")});let i=a(function(s){n._ending&&\n(s.code===\"ECONNRESET\"||s.code===\"EPIPE\")||n.emit(\"error\",s)},\"reportStreamError\");\nif(this.stream.on(\"error\",i),this.stream.on(\"close\",function(){n.emit(\"end\")}),!this.\nssl)return this.attachListeners(this.stream);this.stream.once(\"data\",function(s){\nvar o=s.toString(\"utf8\");switch(o){case\"S\":break;case\"N\":return n.stream.end(),n.\nemit(\"error\",new Error(\"The server does not support SSL connections\"));default:return n.\nstream.end(),n.emit(\"error\",new Error(\"There was an error establishing an SSL co\\\nnnection\"))}var u=(_s(),N(Es));let c={socket:n.stream};n.ssl!==!0&&(Object.assign(\nc,n.ssl),\"key\"in n.ssl&&(c.key=n.ssl.key)),As.isIP(t)===0&&(c.servername=t);try{\nn.stream=u.connect(c)}catch(h){return n.emit(\"error\",h)}n.attachListeners(n.stream),\nn.stream.on(\"error\",i),n.emit(\"sslconnect\")})}attachListeners(e){e.on(\"end\",()=>{\nthis.emit(\"end\")}),pc(e,t=>{var n=t.name===\"error\"?\"errorMessage\":t.name;this._emitMessage&&\nthis.emit(\"message\",t),this.emit(n,t)})}requestSsl(){this.stream.write(Q.requestSsl())}startup(e){\nthis.stream.write(Q.startup(e))}cancel(e,t){this._send(Q.cancel(e,t))}password(e){\nthis._send(Q.password(e))}sendSASLInitialResponseMessage(e,t){this._send(Q.sendSASLInitialResponseMessage(\ne,t))}sendSCRAMClientFinalMessage(e){this._send(Q.sendSCRAMClientFinalMessage(e))}_send(e){\nreturn this.stream.writable?this.stream.write(e):!1}query(e){this._send(Q.query(\ne))}parse(e){this._send(Q.parse(e))}bind(e){this._send(Q.bind(e))}execute(e){this.\n_send(Q.execute(e))}flush(){this.stream.writable&&this.stream.write(Cs)}sync(){this.\n_ending=!0,this._send(Cs),this._send(dc)}ref(){this.stream.ref()}unref(){this.stream.\nunref()}end(){if(this._ending=!0,!this._connecting||!this.stream.writable){this.\nstream.end();return}return this.stream.write(yc,()=>{this.stream.end()})}close(e){\nthis._send(Q.close(e))}describe(e){this._send(Q.describe(e))}sendCopyFromChunk(e){\nthis._send(Q.copyData(e))}endCopyFrom(){this._send(Q.copyDone())}sendCopyFail(e){\nthis._send(Q.copyFail(e))}};a(cn,\"Connection\");var un=cn;Ts.exports=un});var Bs=I((of,Ps)=>{\"use strict\";p();var mc=we().EventEmitter,sf=(He(),N(je)),gc=et(),\nln=qi(),wc=Zi(),bc=mt(),Sc=gt(),Is=ps(),xc=Xe(),vc=hn(),fn=class fn extends mc{constructor(e){\nsuper(),this.connectionParameters=new Sc(e),this.user=this.connectionParameters.\nuser,this.database=this.connectionParameters.database,this.port=this.connectionParameters.\nport,this.host=this.connectionParameters.host,Object.defineProperty(this,\"passwo\\\nrd\",{configurable:!0,enumerable:!1,writable:!0,value:this.connectionParameters.password}),\nthis.replication=this.connectionParameters.replication;var t=e||{};this._Promise=\nt.Promise||S.Promise,this._types=new bc(t.types),this._ending=!1,this._connecting=\n!1,this._connected=!1,this._connectionError=!1,this._queryable=!0,this.connection=\nt.connection||new vc({stream:t.stream,ssl:this.connectionParameters.ssl,keepAlive:t.\nkeepAlive||!1,keepAliveInitialDelayMillis:t.keepAliveInitialDelayMillis||0,encoding:this.\nconnectionParameters.client_encoding||\"utf8\"}),this.queryQueue=[],this.binary=t.\nbinary||xc.binary,this.processID=null,this.secretKey=null,this.ssl=this.connectionParameters.\nssl||!1,this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,\"key\",{enumerable:!1}),\nthis._connectionTimeoutMillis=t.connectionTimeoutMillis||0}_errorAllQueries(e){let t=a(\nn=>{m.nextTick(()=>{n.handleError(e,this.connection)})},\"enqueueError\");this.activeQuery&&\n(t(this.activeQuery),this.activeQuery=null),this.queryQueue.forEach(t),this.queryQueue.\nlength=0}_connect(e){var t=this,n=this.connection;if(this._connectionCallback=e,\nthis._connecting||this._connected){let i=new Error(\"Client has already been conn\\\nected. You cannot reuse a client.\");m.nextTick(()=>{e(i)});return}this._connecting=\n!0,this.connectionTimeoutHandle,this._connectionTimeoutMillis>0&&(this.connectionTimeoutHandle=\nsetTimeout(()=>{n._ending=!0,n.stream.destroy(new Error(\"timeout expired\"))},this.\n_connectionTimeoutMillis)),this.host&&this.host.indexOf(\"/\")===0?n.connect(this.\nhost+\"/.s.PGSQL.\"+this.port):n.connect(this.port,this.host),n.on(\"connect\",function(){\nt.ssl?n.requestSsl():n.startup(t.getStartupConf())}),n.on(\"sslconnect\",function(){\nn.startup(t.getStartupConf())}),this._attachListeners(n),n.once(\"end\",()=>{let i=this.\n_ending?new Error(\"Connection terminated\"):new Error(\"Connection terminated unex\\\npectedly\");clearTimeout(this.connectionTimeoutHandle),this._errorAllQueries(i),this.\n_ending||(this._connecting&&!this._connectionError?this._connectionCallback?this.\n_connectionCallback(i):this._handleErrorEvent(i):this._connectionError||this._handleErrorEvent(\ni)),m.nextTick(()=>{this.emit(\"end\")})})}connect(e){if(e){this._connect(e);return}\nreturn new this._Promise((t,n)=>{this._connect(i=>{i?n(i):t()})})}_attachListeners(e){\ne.on(\"authenticationCleartextPassword\",this._handleAuthCleartextPassword.bind(this)),\ne.on(\"authenticationMD5Password\",this._handleAuthMD5Password.bind(this)),e.on(\"a\\\nuthenticationSASL\",this._handleAuthSASL.bind(this)),e.on(\"authenticationSASLCont\\\ninue\",this._handleAuthSASLContinue.bind(this)),e.on(\"authenticationSASLFinal\",this.\n_handleAuthSASLFinal.bind(this)),e.on(\"backendKeyData\",this._handleBackendKeyData.\nbind(this)),e.on(\"error\",this._handleErrorEvent.bind(this)),e.on(\"errorMessage\",\nthis._handleErrorMessage.bind(this)),e.on(\"readyForQuery\",this._handleReadyForQuery.\nbind(this)),e.on(\"notice\",this._handleNotice.bind(this)),e.on(\"rowDescription\",this.\n_handleRowDescription.bind(this)),e.on(\"dataRow\",this._handleDataRow.bind(this)),\ne.on(\"portalSuspended\",this._handlePortalSuspended.bind(this)),e.on(\"emptyQuery\",\nthis._handleEmptyQuery.bind(this)),e.on(\"commandComplete\",this._handleCommandComplete.\nbind(this)),e.on(\"parseComplete\",this._handleParseComplete.bind(this)),e.on(\"cop\\\nyInResponse\",this._handleCopyInResponse.bind(this)),e.on(\"copyData\",this._handleCopyData.\nbind(this)),e.on(\"notification\",this._handleNotification.bind(this))}_checkPgPass(e){\nlet t=this.connection;typeof this.password==\"function\"?this._Promise.resolve().then(\n()=>this.password()).then(n=>{if(n!==void 0){if(typeof n!=\"string\"){t.emit(\"erro\\\nr\",new TypeError(\"Password must be a string\"));return}this.connectionParameters.\npassword=this.password=n}else this.connectionParameters.password=this.password=null;\ne()}).catch(n=>{t.emit(\"error\",n)}):this.password!==null?e():wc(this.connectionParameters,\nn=>{n!==void 0&&(this.connectionParameters.password=this.password=n),e()})}_handleAuthCleartextPassword(e){\nthis._checkPgPass(()=>{this.connection.password(this.password)})}_handleAuthMD5Password(e){\nthis._checkPgPass(()=>{let t=gc.postgresMd5PasswordHash(this.user,this.password,\ne.salt);this.connection.password(t)})}_handleAuthSASL(e){this._checkPgPass(()=>{\nthis.saslSession=ln.startSession(e.mechanisms),this.connection.sendSASLInitialResponseMessage(\nthis.saslSession.mechanism,this.saslSession.response)})}_handleAuthSASLContinue(e){\nln.continueSession(this.saslSession,this.password,e.data),this.connection.sendSCRAMClientFinalMessage(\nthis.saslSession.response)}_handleAuthSASLFinal(e){ln.finalizeSession(this.saslSession,\ne.data),this.saslSession=null}_handleBackendKeyData(e){this.processID=e.processID,\nthis.secretKey=e.secretKey}_handleReadyForQuery(e){this._connecting&&(this._connecting=\n!1,this._connected=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback&&\n(this._connectionCallback(null,this),this._connectionCallback=null),this.emit(\"c\\\nonnect\"));let{activeQuery:t}=this;this.activeQuery=null,this.readyForQuery=!0,t&&\nt.handleReadyForQuery(this.connection),this._pulseQueryQueue()}_handleErrorWhileConnecting(e){\nif(!this._connectionError){if(this._connectionError=!0,clearTimeout(this.connectionTimeoutHandle),\nthis._connectionCallback)return this._connectionCallback(e);this.emit(\"error\",e)}}_handleErrorEvent(e){\nif(this._connecting)return this._handleErrorWhileConnecting(e);this._queryable=!1,\nthis._errorAllQueries(e),this.emit(\"error\",e)}_handleErrorMessage(e){if(this._connecting)\nreturn this._handleErrorWhileConnecting(e);let t=this.activeQuery;if(!t){this._handleErrorEvent(\ne);return}this.activeQuery=null,t.handleError(e,this.connection)}_handleRowDescription(e){\nthis.activeQuery.handleRowDescription(e)}_handleDataRow(e){this.activeQuery.handleDataRow(\ne)}_handlePortalSuspended(e){this.activeQuery.handlePortalSuspended(this.connection)}_handleEmptyQuery(e){\nthis.activeQuery.handleEmptyQuery(this.connection)}_handleCommandComplete(e){this.\nactiveQuery.handleCommandComplete(e,this.connection)}_handleParseComplete(e){this.\nactiveQuery.name&&(this.connection.parsedStatements[this.activeQuery.name]=this.\nactiveQuery.text)}_handleCopyInResponse(e){this.activeQuery.handleCopyInResponse(\nthis.connection)}_handleCopyData(e){this.activeQuery.handleCopyData(e,this.connection)}_handleNotification(e){\nthis.emit(\"notification\",e)}_handleNotice(e){this.emit(\"notice\",e)}getStartupConf(){\nvar e=this.connectionParameters,t={user:e.user,database:e.database},n=e.application_name||\ne.fallback_application_name;return n&&(t.application_name=n),e.replication&&(t.replication=\n\"\"+e.replication),e.statement_timeout&&(t.statement_timeout=String(parseInt(e.statement_timeout,\n10))),e.lock_timeout&&(t.lock_timeout=String(parseInt(e.lock_timeout,10))),e.idle_in_transaction_session_timeout&&\n(t.idle_in_transaction_session_timeout=String(parseInt(e.idle_in_transaction_session_timeout,\n10))),e.options&&(t.options=e.options),t}cancel(e,t){if(e.activeQuery===t){var n=this.\nconnection;this.host&&this.host.indexOf(\"/\")===0?n.connect(this.host+\"/.s.PGSQL.\"+\nthis.port):n.connect(this.port,this.host),n.on(\"connect\",function(){n.cancel(e.processID,\ne.secretKey)})}else e.queryQueue.indexOf(t)!==-1&&e.queryQueue.splice(e.queryQueue.\nindexOf(t),1)}setTypeParser(e,t,n){return this._types.setTypeParser(e,t,n)}getTypeParser(e,t){\nreturn this._types.getTypeParser(e,t)}escapeIdentifier(e){return'\"'+e.replace(/\"/g,\n'\"\"')+'\"'}escapeLiteral(e){for(var t=!1,n=\"'\",i=0;i<e.length;i++){var s=e[i];s===\n\"'\"?n+=s+s:s===\"\\\\\"?(n+=s+s,t=!0):n+=s}return n+=\"'\",t===!0&&(n=\" E\"+n),n}_pulseQueryQueue(){\nif(this.readyForQuery===!0)if(this.activeQuery=this.queryQueue.shift(),this.activeQuery){\nthis.readyForQuery=!1,this.hasExecuted=!0;let e=this.activeQuery.submit(this.connection);\ne&&m.nextTick(()=>{this.activeQuery.handleError(e,this.connection),this.readyForQuery=\n!0,this._pulseQueryQueue()})}else this.hasExecuted&&(this.activeQuery=null,this.\nemit(\"drain\"))}query(e,t,n){var i,s,o,u,c;if(e==null)throw new TypeError(\"Client\\\n was passed a null or undefined query\");return typeof e.submit==\"function\"?(o=e.\nquery_timeout||this.connectionParameters.query_timeout,s=i=e,typeof t==\"function\"&&\n(i.callback=i.callback||t)):(o=this.connectionParameters.query_timeout,i=new Is(\ne,t,n),i.callback||(s=new this._Promise((h,l)=>{i.callback=(d,b)=>d?l(d):h(b)}))),\no&&(c=i.callback,u=setTimeout(()=>{var h=new Error(\"Query read timeout\");m.nextTick(\n()=>{i.handleError(h,this.connection)}),c(h),i.callback=()=>{};var l=this.queryQueue.\nindexOf(i);l>-1&&this.queryQueue.splice(l,1),this._pulseQueryQueue()},o),i.callback=\n(h,l)=>{clearTimeout(u),c(h,l)}),this.binary&&!i.binary&&(i.binary=!0),i._result&&\n!i._result._types&&(i._result._types=this._types),this._queryable?this._ending?(m.\nnextTick(()=>{i.handleError(new Error(\"Client was closed and is not queryable\"),\nthis.connection)}),s):(this.queryQueue.push(i),this._pulseQueryQueue(),s):(m.nextTick(\n()=>{i.handleError(new Error(\"Client has encountered a connection error and is n\\\not queryable\"),this.connection)}),s)}ref(){this.connection.ref()}unref(){this.connection.\nunref()}end(e){if(this._ending=!0,!this.connection._connecting)if(e)e();else return this.\n_Promise.resolve();if(this.activeQuery||!this._queryable?this.connection.stream.\ndestroy():this.connection.end(),e)this.connection.once(\"end\",e);else return new this.\n_Promise(t=>{this.connection.once(\"end\",t)})}};a(fn,\"Client\");var _t=fn;_t.Query=\nIs;Ps.exports=_t});var Ms=I((cf,Fs)=>{\"use strict\";p();var Ec=we().EventEmitter,Ls=a(function(){},\"\\\nNOOP\"),Rs=a((r,e)=>{let t=r.findIndex(e);return t===-1?void 0:r.splice(t,1)[0]},\n\"removeWhere\"),yn=class yn{constructor(e,t,n){this.client=e,this.idleListener=t,\nthis.timeoutId=n}};a(yn,\"IdleItem\");var pn=yn,mn=class mn{constructor(e){this.callback=\ne}};a(mn,\"PendingItem\");var Ne=mn;function _c(){throw new Error(\"Release called \\\non client which has already been released to the pool.\")}a(_c,\"throwOnDoubleRele\\\nase\");function At(r,e){if(e)return{callback:e,result:void 0};let t,n,i=a(function(o,u){\no?t(o):n(u)},\"cb\"),s=new r(function(o,u){n=o,t=u}).catch(o=>{throw Error.captureStackTrace(\no),o});return{callback:i,result:s}}a(At,\"promisify\");function Ac(r,e){return a(function t(n){\nn.client=e,e.removeListener(\"error\",t),e.on(\"error\",()=>{r.log(\"additional clien\\\nt error after disconnection due to error\",n)}),r._remove(e),r.emit(\"error\",n,e)},\n\"idleListener\")}a(Ac,\"makeIdleListener\");var gn=class gn extends Ec{constructor(e,t){\nsuper(),this.options=Object.assign({},e),e!=null&&\"password\"in e&&Object.defineProperty(\nthis.options,\"password\",{configurable:!0,enumerable:!1,writable:!0,value:e.password}),\ne!=null&&e.ssl&&e.ssl.key&&Object.defineProperty(this.options.ssl,\"key\",{enumerable:!1}),\nthis.options.max=this.options.max||this.options.poolSize||10,this.options.maxUses=\nthis.options.maxUses||1/0,this.options.allowExitOnIdle=this.options.allowExitOnIdle||\n!1,this.options.maxLifetimeSeconds=this.options.maxLifetimeSeconds||0,this.log=this.\noptions.log||function(){},this.Client=this.options.Client||t||Ct().Client,this.Promise=\nthis.options.Promise||S.Promise,typeof this.options.idleTimeoutMillis>\"u\"&&(this.\noptions.idleTimeoutMillis=1e4),this._clients=[],this._idle=[],this._expired=new WeakSet,\nthis._pendingQueue=[],this._endCallback=void 0,this.ending=!1,this.ended=!1}_isFull(){\nreturn this._clients.length>=this.options.max}_pulseQueue(){if(this.log(\"pulse q\\\nueue\"),this.ended){this.log(\"pulse queue ended\");return}if(this.ending){this.log(\n\"pulse queue on ending\"),this._idle.length&&this._idle.slice().map(t=>{this._remove(\nt.client)}),this._clients.length||(this.ended=!0,this._endCallback());return}if(!this.\n_pendingQueue.length){this.log(\"no queued requests\");return}if(!this._idle.length&&\nthis._isFull())return;let e=this._pendingQueue.shift();if(this._idle.length){let t=this.\n_idle.pop();clearTimeout(t.timeoutId);let n=t.client;n.ref&&n.ref();let i=t.idleListener;\nreturn this._acquireClient(n,e,i,!1)}if(!this._isFull())return this.newClient(e);\nthrow new Error(\"unexpected condition\")}_remove(e){let t=Rs(this._idle,n=>n.client===\ne);t!==void 0&&clearTimeout(t.timeoutId),this._clients=this._clients.filter(n=>n!==\ne),e.end(),this.emit(\"remove\",e)}connect(e){if(this.ending){let i=new Error(\"Can\\\nnot use a pool after calling end on the pool\");return e?e(i):this.Promise.reject(\ni)}let t=At(this.Promise,e),n=t.result;if(this._isFull()||this._idle.length){if(this.\n_idle.length&&m.nextTick(()=>this._pulseQueue()),!this.options.connectionTimeoutMillis)\nreturn this._pendingQueue.push(new Ne(t.callback)),n;let i=a((u,c,h)=>{clearTimeout(\no),t.callback(u,c,h)},\"queueCallback\"),s=new Ne(i),o=setTimeout(()=>{Rs(this._pendingQueue,\nu=>u.callback===i),s.timedOut=!0,t.callback(new Error(\"timeout exceeded when try\\\ning to connect\"))},this.options.connectionTimeoutMillis);return this._pendingQueue.\npush(s),n}return this.newClient(new Ne(t.callback)),n}newClient(e){let t=new this.\nClient(this.options);this._clients.push(t);let n=Ac(this,t);this.log(\"checking c\\\nlient timeout\");let i,s=!1;this.options.connectionTimeoutMillis&&(i=setTimeout(()=>{\nthis.log(\"ending client due to timeout\"),s=!0,t.connection?t.connection.stream.destroy():\nt.end()},this.options.connectionTimeoutMillis)),this.log(\"connecting new client\"),\nt.connect(o=>{if(i&&clearTimeout(i),t.on(\"error\",n),o)this.log(\"client failed to\\\n connect\",o),this._clients=this._clients.filter(u=>u!==t),s&&(o.message=\"Connect\\\nion terminated due to connection timeout\"),this._pulseQueue(),e.timedOut||e.callback(\no,void 0,Ls);else{if(this.log(\"new client connected\"),this.options.maxLifetimeSeconds!==\n0){let u=setTimeout(()=>{this.log(\"ending client due to expired lifetime\"),this.\n_expired.add(t),this._idle.findIndex(h=>h.client===t)!==-1&&this._acquireClient(\nt,new Ne((h,l,d)=>d()),n,!1)},this.options.maxLifetimeSeconds*1e3);u.unref(),t.once(\n\"end\",()=>clearTimeout(u))}return this._acquireClient(t,e,n,!0)}})}_acquireClient(e,t,n,i){\ni&&this.emit(\"connect\",e),this.emit(\"acquire\",e),e.release=this._releaseOnce(e,n),\ne.removeListener(\"error\",n),t.timedOut?i&&this.options.verify?this.options.verify(\ne,e.release):e.release():i&&this.options.verify?this.options.verify(e,s=>{if(s)return e.\nrelease(s),t.callback(s,void 0,Ls);t.callback(void 0,e,e.release)}):t.callback(void 0,\ne,e.release)}_releaseOnce(e,t){let n=!1;return i=>{n&&_c(),n=!0,this._release(e,\nt,i)}}_release(e,t,n){if(e.on(\"error\",t),e._poolUseCount=(e._poolUseCount||0)+1,\nthis.emit(\"release\",n,e),n||this.ending||!e._queryable||e._ending||e._poolUseCount>=\nthis.options.maxUses){e._poolUseCount>=this.options.maxUses&&this.log(\"remove ex\\\npended client\"),this._remove(e),this._pulseQueue();return}if(this._expired.has(e)){\nthis.log(\"remove expired client\"),this._expired.delete(e),this._remove(e),this._pulseQueue();\nreturn}let s;this.options.idleTimeoutMillis&&(s=setTimeout(()=>{this.log(\"remove\\\n idle client\"),this._remove(e)},this.options.idleTimeoutMillis),this.options.allowExitOnIdle&&\ns.unref()),this.options.allowExitOnIdle&&e.unref(),this._idle.push(new pn(e,t,s)),\nthis._pulseQueue()}query(e,t,n){if(typeof e==\"function\"){let s=At(this.Promise,e);\nreturn x(function(){return s.callback(new Error(\"Passing a function as the first\\\n parameter to pool.query is not supported\"))}),s.result}typeof t==\"function\"&&(n=\nt,t=void 0);let i=At(this.Promise,n);return n=i.callback,this.connect((s,o)=>{if(s)\nreturn n(s);let u=!1,c=a(h=>{u||(u=!0,o.release(h),n(h))},\"onError\");o.once(\"err\\\nor\",c),this.log(\"dispatching query\");try{o.query(e,t,(h,l)=>{if(this.log(\"query \\\ndispatched\"),o.removeListener(\"error\",c),!u)return u=!0,o.release(h),h?n(h):n(void 0,\nl)})}catch(h){return o.release(h),n(h)}}),i.result}end(e){if(this.log(\"ending\"),\nthis.ending){let n=new Error(\"Called end on pool more than once\");return e?e(n):\nthis.Promise.reject(n)}this.ending=!0;let t=At(this.Promise,e);return this._endCallback=\nt.callback,this._pulseQueue(),t.result}get waitingCount(){return this._pendingQueue.\nlength}get idleCount(){return this._idle.length}get expiredCount(){return this._clients.\nreduce((e,t)=>e+(this._expired.has(t)?1:0),0)}get totalCount(){return this._clients.\nlength}};a(gn,\"Pool\");var dn=gn;Fs.exports=dn});var Ds={};ie(Ds,{default:()=>Cc});var Cc,ks=z(()=>{\"use strict\";p();Cc={}});var Us=I((pf,Tc)=>{Tc.exports={name:\"pg\",version:\"8.8.0\",description:\"PostgreSQL\\\n client - pure javascript & libpq with the same API\",keywords:[\"database\",\"libpq\",\n\"pg\",\"postgre\",\"postgres\",\"postgresql\",\"rdbms\"],homepage:\"https://github.com/bri\\\nanc/node-postgres\",repository:{type:\"git\",url:\"git://github.com/brianc/node-post\\\ngres.git\",directory:\"packages/pg\"},author:\"Brian Carlson <brian.m.carlson@gmail.\\\ncom>\",main:\"./lib\",dependencies:{\"buffer-writer\":\"2.0.0\",\"packet-reader\":\"1.0.0\",\n\"pg-connection-string\":\"^2.5.0\",\"pg-pool\":\"^3.5.2\",\"pg-protocol\":\"^1.5.0\",\"pg-ty\\\npes\":\"^2.1.0\",pgpass:\"1.x\"},devDependencies:{async:\"2.6.4\",bluebird:\"3.5.2\",co:\"\\\n4.6.0\",\"pg-copy-streams\":\"0.3.0\"},peerDependencies:{\"pg-native\":\">=3.0.1\"},peerDependenciesMeta:{\n\"pg-native\":{optional:!0}},scripts:{test:\"make test-all\"},files:[\"lib\",\"SPONSORS\\\n.md\"],license:\"MIT\",engines:{node:\">= 8.0.0\"},gitHead:\"c99fb2c127ddf8d712500db2c\\\n7b9a5491a178655\"}});var qs=I((df,Ns)=>{\"use strict\";p();var Os=we().EventEmitter,Ic=(He(),N(je)),wn=et(),\nqe=Ns.exports=function(r,e,t){Os.call(this),r=wn.normalizeQueryConfig(r,e,t),this.\ntext=r.text,this.values=r.values,this.name=r.name,this.callback=r.callback,this.\nstate=\"new\",this._arrayMode=r.rowMode===\"array\",this._emitRowEvents=!1,this.on(\"\\\nnewListener\",function(n){n===\"row\"&&(this._emitRowEvents=!0)}.bind(this))};Ic.inherits(\nqe,Os);var Pc={sqlState:\"code\",statementPosition:\"position\",messagePrimary:\"mess\\\nage\",context:\"where\",schemaName:\"schema\",tableName:\"table\",columnName:\"column\",dataTypeName:\"\\\ndataType\",constraintName:\"constraint\",sourceFile:\"file\",sourceLine:\"line\",sourceFunction:\"\\\nroutine\"};qe.prototype.handleError=function(r){var e=this.native.pq.resultErrorFields();\nif(e)for(var t in e){var n=Pc[t]||t;r[n]=e[t]}this.callback?this.callback(r):this.\nemit(\"error\",r),this.state=\"error\"};qe.prototype.then=function(r,e){return this.\n_getPromise().then(r,e)};qe.prototype.catch=function(r){return this._getPromise().\ncatch(r)};qe.prototype._getPromise=function(){return this._promise?this._promise:\n(this._promise=new Promise(function(r,e){this._once(\"end\",r),this._once(\"error\",\ne)}.bind(this)),this._promise)};qe.prototype.submit=function(r){this.state=\"runn\\\ning\";var e=this;this.native=r.native,r.native.arrayMode=this._arrayMode;var t=a(\nfunction(s,o,u){if(r.native.arrayMode=!1,x(function(){e.emit(\"_done\")}),s)return e.\nhandleError(s);e._emitRowEvents&&(u.length>1?o.forEach((c,h)=>{c.forEach(l=>{e.emit(\n\"row\",l,u[h])})}):o.forEach(function(c){e.emit(\"row\",c,u)})),e.state=\"end\",e.emit(\n\"end\",u),e.callback&&e.callback(null,u)},\"after\");if(m.domain&&(t=m.domain.bind(\nt)),this.name){this.name.length>63&&(console.error(\"Warning! Postgres only suppo\\\nrts 63 characters for query names.\"),console.error(\"You supplied %s (%s)\",this.name,\nthis.name.length),console.error(\"This can cause conflicts and silent errors exec\\\nuting queries\"));var n=(this.values||[]).map(wn.prepareValue);if(r.namedQueries[this.\nname]){if(this.text&&r.namedQueries[this.name]!==this.text){let s=new Error(`Pre\\\npared statements must be unique - '${this.name}' was used for a different statem\\\nent`);return t(s)}return r.native.execute(this.name,n,t)}return r.native.prepare(\nthis.name,this.text,n.length,function(s){return s?t(s):(r.namedQueries[e.name]=e.\ntext,e.native.execute(e.name,n,t))})}else if(this.values){if(!Array.isArray(this.\nvalues)){let s=new Error(\"Query values must be an array\");return t(s)}var i=this.\nvalues.map(wn.prepareValue);r.native.query(this.text,i,t)}else r.native.query(this.\ntext,t)}});var Hs=I((wf,js)=>{\"use strict\";p();var Bc=(ks(),N(Ds)),Lc=mt(),gf=Us(),Qs=we().\nEventEmitter,Rc=(He(),N(je)),Fc=gt(),Ws=qs(),J=js.exports=function(r){Qs.call(this),\nr=r||{},this._Promise=r.Promise||S.Promise,this._types=new Lc(r.types),this.native=\nnew Bc({types:this._types}),this._queryQueue=[],this._ending=!1,this._connecting=\n!1,this._connected=!1,this._queryable=!0;var e=this.connectionParameters=new Fc(\nr);this.user=e.user,Object.defineProperty(this,\"password\",{configurable:!0,enumerable:!1,\nwritable:!0,value:e.password}),this.database=e.database,this.host=e.host,this.port=\ne.port,this.namedQueries={}};J.Query=Ws;Rc.inherits(J,Qs);J.prototype._errorAllQueries=\nfunction(r){let e=a(t=>{m.nextTick(()=>{t.native=this.native,t.handleError(r)})},\n\"enqueueError\");this._hasActiveQuery()&&(e(this._activeQuery),this._activeQuery=\nnull),this._queryQueue.forEach(e),this._queryQueue.length=0};J.prototype._connect=\nfunction(r){var e=this;if(this._connecting){m.nextTick(()=>r(new Error(\"Client h\\\nas already been connected. You cannot reuse a client.\")));return}this._connecting=\n!0,this.connectionParameters.getLibpqConnectionString(function(t,n){if(t)return r(\nt);e.native.connect(n,function(i){if(i)return e.native.end(),r(i);e._connected=!0,\ne.native.on(\"error\",function(s){e._queryable=!1,e._errorAllQueries(s),e.emit(\"er\\\nror\",s)}),e.native.on(\"notification\",function(s){e.emit(\"notification\",{channel:s.\nrelname,payload:s.extra})}),e.emit(\"connect\"),e._pulseQueryQueue(!0),r()})})};J.\nprototype.connect=function(r){if(r){this._connect(r);return}return new this._Promise(\n(e,t)=>{this._connect(n=>{n?t(n):e()})})};J.prototype.query=function(r,e,t){var n,\ni,s,o,u;if(r==null)throw new TypeError(\"Client was passed a null or undefined qu\\\nery\");if(typeof r.submit==\"function\")s=r.query_timeout||this.connectionParameters.\nquery_timeout,i=n=r,typeof e==\"function\"&&(r.callback=e);else if(s=this.connectionParameters.\nquery_timeout,n=new Ws(r,e,t),!n.callback){let c,h;i=new this._Promise((l,d)=>{c=\nl,h=d}),n.callback=(l,d)=>l?h(l):c(d)}return s&&(u=n.callback,o=setTimeout(()=>{\nvar c=new Error(\"Query read timeout\");m.nextTick(()=>{n.handleError(c,this.connection)}),\nu(c),n.callback=()=>{};var h=this._queryQueue.indexOf(n);h>-1&&this._queryQueue.\nsplice(h,1),this._pulseQueryQueue()},s),n.callback=(c,h)=>{clearTimeout(o),u(c,h)}),\nthis._queryable?this._ending?(n.native=this.native,m.nextTick(()=>{n.handleError(\nnew Error(\"Client was closed and is not queryable\"))}),i):(this._queryQueue.push(\nn),this._pulseQueryQueue(),i):(n.native=this.native,m.nextTick(()=>{n.handleError(\nnew Error(\"Client has encountered a connection error and is not queryable\"))}),i)};\nJ.prototype.end=function(r){var e=this;this._ending=!0,this._connected||this.once(\n\"connect\",this.end.bind(this,r));var t;return r||(t=new this._Promise(function(n,i){\nr=a(s=>s?i(s):n(),\"cb\")})),this.native.end(function(){e._errorAllQueries(new Error(\n\"Connection terminated\")),m.nextTick(()=>{e.emit(\"end\"),r&&r()})}),t};J.prototype.\n_hasActiveQuery=function(){return this._activeQuery&&this._activeQuery.state!==\"\\\nerror\"&&this._activeQuery.state!==\"end\"};J.prototype._pulseQueryQueue=function(r){\nif(this._connected&&!this._hasActiveQuery()){var e=this._queryQueue.shift();if(!e){\nr||this.emit(\"drain\");return}this._activeQuery=e,e.submit(this);var t=this;e.once(\n\"_done\",function(){t._pulseQueryQueue()})}};J.prototype.cancel=function(r){this.\n_activeQuery===r?this.native.cancel(function(){}):this._queryQueue.indexOf(r)!==\n-1&&this._queryQueue.splice(this._queryQueue.indexOf(r),1)};J.prototype.ref=function(){};\nJ.prototype.unref=function(){};J.prototype.setTypeParser=function(r,e,t){return this.\n_types.setTypeParser(r,e,t)};J.prototype.getTypeParser=function(r,e){return this.\n_types.getTypeParser(r,e)}});var bn=I((xf,Gs)=>{\"use strict\";p();Gs.exports=Hs()});var Ct=I((Ef,rt)=>{\"use strict\";p();var Mc=Bs(),Dc=Xe(),kc=hn(),Uc=Ms(),{DatabaseError:Oc}=an(),\nNc=a(r=>{var e;return e=class extends Uc{constructor(n){super(n,r)}},a(e,\"BoundP\\\nool\"),e},\"poolFactory\"),Sn=a(function(r){this.defaults=Dc,this.Client=r,this.Query=\nthis.Client.Query,this.Pool=Nc(this.Client),this._pools=[],this.Connection=kc,this.\ntypes=Je(),this.DatabaseError=Oc},\"PG\");typeof m.env.NODE_PG_FORCE_NATIVE<\"u\"?rt.\nexports=new Sn(bn()):(rt.exports=new Sn(Mc),Object.defineProperty(rt.exports,\"na\\\ntive\",{configurable:!0,enumerable:!1,get(){var r=null;try{r=new Sn(bn())}catch(e){\nif(e.code!==\"MODULE_NOT_FOUND\")throw e}return Object.defineProperty(rt.exports,\"\\\nnative\",{value:r}),r}}))});p();var Tt=Te(Ct());wt();p();pr();wt();var Ks=Te(et()),zs=Te(mt());var xn=class xn extends Error{constructor(){super(...arguments);_(this,\"name\",\"N\\\neonDbError\");_(this,\"severity\");_(this,\"code\");_(this,\"detail\");_(this,\"hint\");_(\nthis,\"position\");_(this,\"internalPosition\");_(this,\"internalQuery\");_(this,\"wher\\\ne\");_(this,\"schema\");_(this,\"table\");_(this,\"column\");_(this,\"dataType\");_(this,\n\"constraint\");_(this,\"file\");_(this,\"line\");_(this,\"routine\");_(this,\"sourceErro\\\nr\")}};a(xn,\"NeonDbError\");var Ae=xn,$s=\"transaction() expects an array of querie\\\ns, or a function returning an array of queries\",qc=[\"severity\",\"code\",\"detail\",\"\\\nhint\",\"position\",\"internalPosition\",\"internalQuery\",\"where\",\"schema\",\"table\",\"co\\\nlumn\",\"dataType\",\"constraint\",\"file\",\"line\",\"routine\"];function Ys(r,{arrayMode:e,\nfullResults:t,fetchOptions:n,isolationLevel:i,readOnly:s,deferrable:o,queryCallback:u,\nresultCallback:c}={}){if(!r)throw new Error(\"No database connection string was p\\\nrovided to `neon()`. Perhaps an environment variable has not been set?\");let h;try{\nh=fr(r)}catch{throw new Error(\"Database connection string provided to `neon()` i\\\ns not a valid URL. Connection string: \"+String(r))}let{protocol:l,username:d,password:b,\nhostname:C,port:B,pathname:W}=h;if(l!==\"postgres:\"&&l!==\"postgresql:\"||!d||!b||!C||\n!W)throw new Error(\"Database connection string format for `neon()` should be: po\\\nstgresql://user:<EMAIL>/dbname?option=value\");function X(A,...w){let P,\nV;if(typeof A==\"string\")P=A,V=w[1],w=w[0]??[];else{P=\"\";for(let j=0;j<A.length;j++)\nP+=A[j],j<w.length&&(P+=\"$\"+(j+1))}w=w.map(j=>(0,Ks.prepareValue)(j));let k={query:P,\nparams:w};return u&&u(k),Qc(de,k,V)}a(X,\"resolve\"),X.transaction=async(A,w)=>{if(typeof A==\n\"function\"&&(A=A(X)),!Array.isArray(A))throw new Error($s);A.forEach(k=>{if(k[Symbol.\ntoStringTag]!==\"NeonQueryPromise\")throw new Error($s)});let P=A.map(k=>k.parameterizedQuery),\nV=A.map(k=>k.opts??{});return de(P,V,w)};async function de(A,w,P){let{fetchEndpoint:V,\nfetchFunction:k}=_e,j=typeof V==\"function\"?V(C,B):V,ce=Array.isArray(A)?{queries:A}:\nA,ee=n??{},R=e??!1,G=t??!1,he=i,ye=s,xe=o;P!==void 0&&(P.fetchOptions!==void 0&&\n(ee={...ee,...P.fetchOptions}),P.arrayMode!==void 0&&(R=P.arrayMode),P.fullResults!==\nvoid 0&&(G=P.fullResults),P.isolationLevel!==void 0&&(he=P.isolationLevel),P.readOnly!==\nvoid 0&&(ye=P.readOnly),P.deferrable!==void 0&&(xe=P.deferrable)),w!==void 0&&!Array.\nisArray(w)&&w.fetchOptions!==void 0&&(ee={...ee,...w.fetchOptions});let me={\"Neo\\\nn-Connection-String\":r,\"Neon-Raw-Text-Output\":\"true\",\"Neon-Array-Mode\":\"true\"};Array.\nisArray(A)&&(he!==void 0&&(me[\"Neon-Batch-Isolation-Level\"]=he),ye!==void 0&&(me[\"\\\nNeon-Batch-Read-Only\"]=String(ye)),xe!==void 0&&(me[\"Neon-Batch-Deferrable\"]=String(\nxe)));let se;try{se=await(k??fetch)(j,{method:\"POST\",body:JSON.stringify(ce),headers:me,\n...ee})}catch(oe){let U=new Ae(`Error connecting to database: ${oe.message}`);throw U.\nsourceError=oe,U}if(se.ok){let oe=await se.json();if(Array.isArray(A)){let U=oe.\nresults;if(!Array.isArray(U))throw new Ae(\"Neon internal error: unexpected resul\\\nt format\");return U.map((K,le)=>{let It=w[le]??{},Xs=It.arrayMode??R,eo=It.fullResults??\nG;return Vs(K,{arrayMode:Xs,fullResults:eo,parameterizedQuery:A[le],resultCallback:c,\ntypes:It.types})})}else{let U=w??{},K=U.arrayMode??R,le=U.fullResults??G;return Vs(\noe,{arrayMode:K,fullResults:le,parameterizedQuery:A,resultCallback:c,types:U.types})}}else{\nlet{status:oe}=se;if(oe===400){let U=await se.json(),K=new Ae(U.message);for(let le of qc)\nK[le]=U[le]??void 0;throw K}else{let U=await se.text();throw new Ae(`Server erro\\\nr (HTTP status ${oe}): ${U}`)}}}return a(de,\"execute\"),X}a(Ys,\"neon\");function Qc(r,e,t){\nreturn{[Symbol.toStringTag]:\"NeonQueryPromise\",parameterizedQuery:e,opts:t,then:a(\n(n,i)=>r(e,t).then(n,i),\"then\"),catch:a(n=>r(e,t).catch(n),\"catch\"),finally:a(n=>r(\ne,t).finally(n),\"finally\")}}a(Qc,\"createNeonQueryPromise\");function Vs(r,{arrayMode:e,\nfullResults:t,parameterizedQuery:n,resultCallback:i,types:s}){let o=new zs.default(\ns),u=r.fields.map(l=>l.name),c=r.fields.map(l=>o.getTypeParser(l.dataTypeID)),h=e===\n!0?r.rows.map(l=>l.map((d,b)=>d===null?null:c[b](d))):r.rows.map(l=>Object.fromEntries(\nl.map((d,b)=>[u[b],d===null?null:c[b](d)])));return i&&i(n,r,h,{arrayMode:e,fullResults:t}),\nt?(r.viaNeonFetch=!0,r.rowAsArray=e,r.rows=h,r._parsers=c,r._types=o,r):h}a(Vs,\"\\\nprocessQueryResult\");var Js=Te(gt()),Qe=Te(Ct());var En=class En extends Tt.Client{constructor(t){super(t);this.config=t}get neonConfig(){\nreturn this.connection.stream}connect(t){let{neonConfig:n}=this;n.forceDisablePgSSL&&\n(this.ssl=this.connection.ssl=!1),this.ssl&&n.useSecureWebSocket&&console.warn(\"\\\nSSL is enabled for both Postgres (e.g. ?sslmode=require in the connection string\\\n + forceDisablePgSSL = false) and the WebSocket tunnel (useSecureWebSocket = tru\\\ne). Double encryption will increase latency and CPU usage. It may be appropriate\\\n to disable SSL in the Postgres connection parameters or set forceDisablePgSSL =\\\n true.\");let i=this.config?.host!==void 0||this.config?.connectionString!==void 0||\nm.env.PGHOST!==void 0,s=m.env.USER??m.env.USERNAME;if(!i&&this.host===\"localhost\"&&\nthis.user===s&&this.database===s&&this.password===null)throw new Error(`No datab\\\nase host or connection string was set, and key parameters have default values (h\\\nost: localhost, user: ${s}, db: ${s}, password: null). Is an environment variabl\\\ne missing? Alternatively, if you intended to connect with these parameters, plea\\\nse set the host to 'localhost' explicitly.`);let o=super.connect(t),u=n.pipelineTLS&&\nthis.ssl,c=n.pipelineConnect===\"password\";if(!u&&!n.pipelineConnect)return o;let h=this.\nconnection;if(u&&h.on(\"connect\",()=>h.stream.emit(\"data\",\"S\")),c){h.removeAllListeners(\n\"authenticationCleartextPassword\"),h.removeAllListeners(\"readyForQuery\"),h.once(\n\"readyForQuery\",()=>h.on(\"readyForQuery\",this._handleReadyForQuery.bind(this)));\nlet l=this.ssl?\"sslconnect\":\"connect\";h.on(l,()=>{this._handleAuthCleartextPassword(),\nthis._handleReadyForQuery()})}return o}async _handleAuthSASLContinue(t){let n=this.\nsaslSession,i=this.password,s=t.data;if(n.message!==\"SASLInitialResponse\"||typeof i!=\n\"string\"||typeof s!=\"string\")throw new Error(\"SASL: protocol error\");let o=Object.\nfromEntries(s.split(\",\").map(U=>{if(!/^.=/.test(U))throw new Error(\"SASL: Invali\\\nd attribute pair entry\");let K=U[0],le=U.substring(2);return[K,le]})),u=o.r,c=o.\ns,h=o.i;if(!u||!/^[!-+--~]+$/.test(u))throw new Error(\"SASL: SCRAM-SERVER-FIRST-\\\nMESSAGE: nonce missing/unprintable\");if(!c||!/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.\ntest(c))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing/not base\\\n64\");if(!h||!/^[1-9][0-9]*$/.test(h))throw new Error(\"SASL: SCRAM-SERVER-FIRST-M\\\nESSAGE: missing/invalid iteration count\");if(!u.startsWith(n.clientNonce))throw new Error(\n\"SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce\");\nif(u.length===n.clientNonce.length)throw new Error(\"SASL: SCRAM-SERVER-FIRST-MES\\\nSAGE: server nonce is too short\");let l=parseInt(h,10),d=y.from(c,\"base64\"),b=new TextEncoder,\nC=b.encode(i),B=await g.subtle.importKey(\"raw\",C,{name:\"HMAC\",hash:{name:\"SHA-25\\\n6\"}},!1,[\"sign\"]),W=new Uint8Array(await g.subtle.sign(\"HMAC\",B,y.concat([d,y.from(\n[0,0,0,1])]))),X=W;for(var de=0;de<l-1;de++)W=new Uint8Array(await g.subtle.sign(\n\"HMAC\",B,W)),X=y.from(X.map((U,K)=>X[K]^W[K]));let A=X,w=await g.subtle.importKey(\n\"raw\",A,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]),P=new Uint8Array(await g.\nsubtle.sign(\"HMAC\",w,b.encode(\"Client Key\"))),V=await g.subtle.digest(\"SHA-256\",\nP),k=\"n=*,r=\"+n.clientNonce,j=\"r=\"+u+\",s=\"+c+\",i=\"+l,ce=\"c=biws,r=\"+u,ee=k+\",\"+j+\n\",\"+ce,R=await g.subtle.importKey(\"raw\",V,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,\n[\"sign\"]);var G=new Uint8Array(await g.subtle.sign(\"HMAC\",R,b.encode(ee))),he=y.\nfrom(P.map((U,K)=>P[K]^G[K])),ye=he.toString(\"base64\");let xe=await g.subtle.importKey(\n\"raw\",A,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]),me=await g.subtle.sign(\n\"HMAC\",xe,b.encode(\"Server Key\")),se=await g.subtle.importKey(\"raw\",me,{name:\"HM\\\nAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]);var oe=y.from(await g.subtle.sign(\"HMAC\",\nse,b.encode(ee)));n.message=\"SASLResponse\",n.serverSignature=oe.toString(\"base64\"),\nn.response=ce+\",p=\"+ye,this.connection.sendSCRAMClientFinalMessage(this.saslSession.\nresponse)}};a(En,\"NeonClient\");var vn=En;function Wc(r,e){if(e)return{callback:e,\nresult:void 0};let t,n,i=a(function(o,u){o?t(o):n(u)},\"cb\"),s=new r(function(o,u){\nn=o,t=u});return{callback:i,result:s}}a(Wc,\"promisify\");var _n=class _n extends Tt.Pool{constructor(){\nsuper(...arguments);_(this,\"Client\",vn);_(this,\"hasFetchUnsupportedListeners\",!1)}on(t,n){\nreturn t!==\"error\"&&(this.hasFetchUnsupportedListeners=!0),super.on(t,n)}query(t,n,i){\nif(!_e.poolQueryViaFetch||this.hasFetchUnsupportedListeners||typeof t==\"function\")\nreturn super.query(t,n,i);typeof n==\"function\"&&(i=n,n=void 0);let s=Wc(this.Promise,\ni);i=s.callback;try{let o=new Js.default(this.options),u=encodeURIComponent,c=encodeURI,\nh=`postgresql://${u(o.user)}:${u(o.password)}@${u(o.host)}/${c(o.database)}`,l=typeof t==\n\"string\"?t:t.text,d=n??t.values??[];Ys(h,{fullResults:!0,arrayMode:t.rowMode===\"\\\narray\"})(l,d,{types:t.types??this.options?.types}).then(C=>i(void 0,C)).catch(C=>i(\nC))}catch(o){i(o)}return s.result}};a(_n,\"NeonPool\");var Zs=_n;var export_ClientBase=Qe.ClientBase;var export_Connection=Qe.Connection;var export_DatabaseError=Qe.DatabaseError;\nvar export_Query=Qe.Query;var export_defaults=Qe.defaults;var export_types=Qe.types;\n\n/*! Bundled license information:\n\nieee754/index.js:\n  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)\n\nbuffer/index.js:\n  (*!\n   * The buffer module from node.js, for the browser.\n   *\n   * <AUTHOR> Aboukhadijeh <https://feross.org>\n   * @license  MIT\n   *)\n*/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@neondatabase/serverless/index.mjs\n");

/***/ })

};
;