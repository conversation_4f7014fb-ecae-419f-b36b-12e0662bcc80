(()=>{var t={};t.id=3494,t.ids=[3494],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(t,e,r)=>{"use strict";r.d(e,{Qq:()=>w,Tq:()=>p,bS:()=>c,fF:()=>d,mU:()=>l});var n=r(85663),a=r(43205),i=r.n(a);let s=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(t,e){try{return await n.Ay.compare(t,e)}catch(t){return console.error("Error verifying password:",t),!1}}function l(t){return o.delete(t)}async function c(t){try{let e=function(){let t=process.env.ADMIN_PASSWORD||"admin123";return t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$"),t}(),r=!1;if(!(e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$")?await u(t,e):t===e))return{success:!1,message:"Invalid credentials"};{let t=function(t="admin"){try{let e={id:t,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(e,s,{expiresIn:"24h"})}catch(t){throw console.error("Error generating token:",t),Error("Failed to generate authentication token")}}(),e=function(t="admin"){let e=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(e,{id:t,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let t=Date.now();for(let[e,r]of o.entries())t>r.expiresAt&&o.delete(e)}(),e}();return{success:!0,token:t,sessionId:e,message:"Authentication successful"}}}catch(t){return console.error("Authentication error:",t),{success:!1,message:"Authentication failed"}}}function d(t,e){if(t&&t.startsWith("Bearer ")){let e=function(t){try{let e=i().verify(t,s);if(e.isAdmin)return{id:e.id,isAdmin:e.isAdmin};return null}catch(t){return null}}(t.substring(7));if(e)return{isValid:!0,adminId:e.id,message:"Token authentication successful"}}if(e){let t=function(t){let e=o.get(t);if(!e)return null;let r=Date.now();return r>e.expiresAt?(o.delete(t),null):(e.lastActivity=r,o.set(t,e),e)}(e);if(t)return{isValid:!0,adminId:t.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let f=new Map;function w(t){let e=Date.now(),r=f.get(t);return!r||e-r.lastAttempt>9e5?(f.set(t,{count:1,lastAttempt:e}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(e-r.lastAttempt)}:(r.count++,r.lastAttempt=e,f.set(t,r),{allowed:!0,remainingAttempts:5-r.count})}function p(t){f.delete(t)}},23870:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(55511);let a={randomUUID:n.randomUUID},i=new Uint8Array(256),s=i.length,o=[];for(let t=0;t<256;++t)o.push((t+256).toString(16).slice(1));let u=function(t,e,r){if(a.randomUUID&&!e&&!t)return a.randomUUID();let u=(t=t||{}).random??t.rng?.()??(s>i.length-16&&((0,n.randomFillSync)(i),s=0),i.slice(s,s+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,e){if((r=r||0)<0||r+16>e.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let t=0;t<16;++t)e[r+t]=u[t];return e}return function(t,e=0){return(o[t[e+0]]+o[t[e+1]]+o[t[e+2]]+o[t[e+3]]+"-"+o[t[e+4]]+o[t[e+5]]+"-"+o[t[e+6]]+o[t[e+7]]+"-"+o[t[e+8]]+o[t[e+9]]+"-"+o[t[e+10]]+o[t[e+11]]+o[t[e+12]]+o[t[e+13]]+o[t[e+14]]+o[t[e+15]]).toLowerCase()}(u)}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:t=>{"use strict";t.exports=require("path")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46092:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>g,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>p});var n={};r.r(n),r.d(n,{GET:()=>c});var a=r(96559),i=r(48088),s=r(37719),o=r(32190),u=r(77268),l=r(53190);async function c(t){try{if(!(0,u.iY)(t).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let e=await d();return o.NextResponse.json(e)}catch(t){return console.error("Error fetching CRM stats:",t),o.NextResponse.json({success:!1,message:"Failed to fetch CRM statistics"},{status:500})}}async function d(){try{let[t,e,r]=await Promise.all([(0,l.Rf)(),(0,l.tS)(),(0,l.fE)()]),n=t.length,a=t.filter(t=>"active"===t.status).length,i=t.filter(t=>{let e=new Date(t.createdAt),r=new Date;return r.setDate(r.getDate()-30),e>r}).length,s=t.filter(t=>"Gold"===t.membershipTier||"Platinum"===t.membershipTier||"VIP"===t.membershipTier).length,o=e.length,u=e.filter(t=>"new"===t.status).length,c=e.filter(t=>"qualified"===t.status).length,d=e.filter(t=>"won"===t.status||"converted"===t.status).length,f=t.reduce((t,e)=>t+(e.totalSpent||0),0),w=t.reduce((t,e)=>{let r=e.lastOrderDate?new Date(e.lastOrderDate):null,n=new Date;return(n.setDate(1),r&&r>=n)?t+(e.totalSpent||0):t},0),p=t.filter(t=>t.totalOrders>0),m=p.length>0?p.reduce((t,e)=>t+e.averageOrderValue,0)/p.length:0,g=r.length,h=r.filter(t=>{let e=new Date(t.createdAt),r=new Date;return r.setDate(r.getDate()-7),e>r}).length,y=r.filter(t=>"outbound"===t.direction).length,A=e.reduce((t,e)=>(t[e.source]=(t[e.source]||0)+1,t),{}),D=t.reduce((t,e)=>(t[e.segment]=(t[e.segment]||0)+1,t),{}),S=function(t,e,r){let n=[],a=new Date;for(let i=5;i>=0;i--){let s=new Date(a.getFullYear(),a.getMonth()-i,1),o=new Date(a.getFullYear(),a.getMonth()-i+1,1),u=s.toISOString().slice(0,7),l=t.filter(t=>{let e=new Date(t.createdAt);return e>=s&&e<o}).length,c=e.filter(t=>{let e=new Date(t.createdAt);return e>=s&&e<o}).length,d=r.filter(t=>{let e=new Date(t.createdAt);return e>=s&&e<o}).length,f=t.reduce((t,e)=>{if(e.lastOrderDate){let r=new Date(e.lastOrderDate);if(r>=s&&r<o)return t+(e.totalSpent||0)}return t},0);n.push({month:u,customers:l,leads:c,interactions:d,revenue:Math.round(f)})}return n}(t,e,r),I=t.sort((t,e)=>(e.totalSpent||0)-(t.totalSpent||0)).slice(0,5).map(t=>({id:t.id,name:t.personalInfo.name,email:t.personalInfo.email,totalSpent:t.totalSpent||0,totalOrders:t.totalOrders||0,membershipTier:t.membershipTier}));return{customers:{total:n,active:a,new:i,vip:s,retentionRate:Math.round(10*(n>0?a/n*100:0))/10},leads:{total:o,new:u,qualified:c,converted:d,conversionRate:Math.round(10*(o>0?d/o*100:0))/10},revenue:{total:Math.round(f),thisMonth:Math.round(w),averageOrderValue:Math.round(m)},interactions:{total:g,thisWeek:h,responseRate:Math.round(10*(g>0?y/g*100:0))/10},analytics:{leadSources:A,customerSegments:D,monthlyTrends:S,topCustomers:I}}}catch(t){return console.error("Error calculating CRM stats:",t),{customers:{total:0,active:0,new:0,vip:0,retentionRate:0},leads:{total:0,new:0,qualified:0,converted:0,conversionRate:0},revenue:{total:0,thisMonth:0,averageOrderValue:0},interactions:{total:0,thisWeek:0,responseRate:0},analytics:{leadSources:{},customerSegments:{},monthlyTrends:[],topCustomers:[]}}}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/crm/stats/route",pathname:"/api/admin/crm/stats",filename:"route",bundlePath:"app/api/admin/crm/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\crm\\stats\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:w,workUnitAsyncStorage:p,serverHooks:m}=f;function g(){return(0,s.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:p})}},53190:(t,e,r)=>{"use strict";r.d(e,{Gk:()=>N,Gq:()=>W,HR:()=>R,Kt:()=>M,Q6:()=>x,Rf:()=>T,XL:()=>Y,Y2:()=>j,_Y:()=>z,aN:()=>Z,createCustomerActivity:()=>X,createInteraction:()=>C,dD:()=>B,fE:()=>P,getAllFollowUps:()=>L,getCustomerByEmail:()=>q,getCustomerById:()=>k,getLeadById:()=>O,getPendingFollowUps:()=>G,oP:()=>tt,qz:()=>V,sr:()=>F,tR:()=>I,tS:()=>v,updateFollowUp:()=>K});var n=r(29021),a=r(33873),i=r.n(a),s=r(23870);let o=i().join(process.cwd(),"data"),u=i().join(o,"leads.json"),l=i().join(o,"customers.json"),c=i().join(o,"interactions.json"),d=i().join(o,"followups.json"),f=i().join(o,"activities.json"),w=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,p=[],m=[],g=[],h=[],y=[];async function A(){if(!w)try{await n.promises.access(o)}catch{await n.promises.mkdir(o,{recursive:!0})}}async function D(){if(w)return p;try{await A();let t=await n.promises.readFile(u,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function S(t){if(w){p=t;return}await A(),await n.promises.writeFile(u,JSON.stringify(t,null,2))}async function I(t){let e=await D(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await S(e),r}async function v(){return await D()}async function O(t){return(await D()).find(e=>e.id===t)||null}async function x(t,e){let r=await D(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await S(r),!0)}async function F(t){let e=await D(),r=e.filter(e=>e.id!==t);return r.length!==e.length&&(await S(r),!0)}async function R(t){return(await D()).filter(e=>e.status===t)}async function M(t){return(await D()).filter(e=>e.source===t)}async function U(){if(w)return m;try{await A();let t=await n.promises.readFile(l,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function b(t){if(w){m=t;return}await A(),await n.promises.writeFile(l,JSON.stringify(t,null,2))}async function j(t){let e=await U(),r=e.findIndex(e=>e.personalInfo.email===t.personalInfo.email);if(-1!==r)return e[r]={...e[r],...t,updatedAt:new Date().toISOString()},await b(e),e[r];{let r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await b(e),r}}async function T(){return await U()}async function k(t){return(await U()).find(e=>e.id===t)||null}async function q(t){return(await U()).find(e=>e.personalInfo.email===t)||null}async function N(t,e){let r=await U(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await b(r),!0)}async function E(){if(w)return g;try{await A();let t=await n.promises.readFile(c,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function $(t){if(w){g=t;return}await A(),await n.promises.writeFile(c,JSON.stringify(t,null,2))}async function C(t){let e=await E(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString()};return e.push(r),await $(e),r}async function P(){return await E()}async function V(t){return(await E()).filter(e=>e.customerId===t)}async function W(t){return(await E()).filter(e=>e.leadId===t)}async function J(){if(w)return h;try{await A();let t=await n.promises.readFile(d,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function _(t){if(w){h=t;return}await A(),await n.promises.writeFile(d,JSON.stringify(t,null,2))}async function Y(t){let e=await J(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await _(e),r}async function L(){return await J()}async function B(t){return(await J()).filter(e=>e.status===t)}async function G(){let t=await J(),e=new Date().toISOString();return t.filter(t=>"pending"===t.status&&t.scheduledDate<=e)}async function K(t,e){let r=await J(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await _(r),!0)}async function z(t){return(await J()).filter(e=>e.customerId===t)}async function H(){if(w)return y;try{await A();let t=await n.promises.readFile(f,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function Q(t){if(w){y=t;return}await A(),await n.promises.writeFile(f,JSON.stringify(t,null,2))}async function X(t){let e=await H(),r={...t,id:(0,s.A)(),timestamp:new Date().toISOString()};return e.push(r),await Q(e),r}async function Z(t){return(await H()).filter(e=>e.customerId===t)}async function tt(t){let e=await k(t);if(!e)return null;let r=await V(t),n=await z(t),a=await Z(t);return{customer:e,stats:{totalInteractions:r.length,pendingFollowUps:n.filter(t=>"pending"===t.status).length,recentActivities:a.filter(t=>new Date(t.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:r.sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime())[0]?.createdAt},recentInteractions:r.sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()).slice(0,5),upcomingFollowUps:n.filter(t=>"pending"===t.status).sort((t,e)=>new Date(t.scheduledDate).getTime()-new Date(e.scheduledDate).getTime()).slice(0,3)}}},55511:t=>{"use strict";t.exports=require("crypto")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77268:(t,e,r)=>{"use strict";r.d(e,{iY:()=>a}),r(32190);var n=r(12909);function a(t,e){let r=t.headers.get("authorization"),a=t.cookies.get("admin_session")?.value,i=(0,n.fF)(r,a);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let s=e?.adminKey||t.nextUrl.searchParams.get("adminKey");return s&&s===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:t=>{"use strict";t.exports=require("buffer")},96487:()=>{}};var e=require("../../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[4447,580,7696],()=>r(46092));module.exports=n})();