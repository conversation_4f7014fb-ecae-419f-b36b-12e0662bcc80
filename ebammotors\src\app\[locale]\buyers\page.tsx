import Link from 'next/link';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';

export default async function BuyersPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  const benefits = [
    {
      title: locale === 'en' ? 'Quality Japanese Goods' : '高品質な日本製品',
      description: locale === 'en'
        ? 'All items are carefully selected and inspected to ensure they meet our high standards before export.'
        : 'すべての商品は輸出前に慎重に選別・検査され、当社の高い基準を満たしています。',
      icon: ''
    },
    {
      title: locale === 'en' ? 'Affordable Prices' : '手頃な価格',
      description: locale === 'en'
        ? 'Get premium Japanese products at competitive prices, significantly lower than buying new.'
        : '新品購入よりもかなり安い競争力のある価格で、プレミアムな日本製品を手に入れることができます。',
      icon: ''
    },
    {
      title: locale === 'en' ? 'Reliable Shipping' : '信頼できる配送',
      description: locale === 'en'
        ? 'We use established shipping routes and provide tracking information for all shipments.'
        : '確立された配送ルートを使用し、すべての出荷に追跡情報を提供いたします。',
      icon: ''
    },
    {
      title: locale === 'en' ? 'Local Support' : '現地サポート',
      description: locale === 'en'
        ? 'Our team in Ghana provides local customer support and assistance with customs clearance.'
        : 'ガーナの当社チームが現地カスタマーサポートと通関手続きのサポートを提供いたします。',
      icon: '🤝'
    }
  ];

  const productCategories = [
    {
      name: messages.services.usedCars,
      description: messages.services.usedCarsDesc,
      icon: '',
      popular: ['Toyota Voxy', 'Honda Freed', 'Nissan Serena', 'Toyota Hiace']
    },
    {
      name: messages.services.electronics,
      description: messages.services.electronicsDesc,
      icon: '',
      popular: ['Refrigerators', 'Air Conditioners', 'Washing Machines', 'TVs']
    },
    {
      name: messages.services.furniture,
      description: messages.services.furnitureDesc,
      icon: '',
      popular: ['Dining Sets', 'Sofas', 'Office Chairs', 'Wardrobes']
    },
    {
      name: messages.services.carParts,
      description: messages.services.carPartsDesc,
      icon: '',
      popular: ['Engines', 'Tires', 'Body Parts', 'Electronics']
    }
  ];

  const faqs = [
    {
      question: locale === 'en' ? 'How long does shipping take from Japan to Ghana?' : '日本からガーナまでの配送にはどのくらい時間がかかりますか？',
      answer: locale === 'en'
        ? 'Shipping typically takes 4-6 weeks depending on the items and shipping method. We provide tracking information so you can monitor your shipment\'s progress.'
        : '商品や配送方法によって通常4〜6週間かかります。追跡情報を提供しているため、出荷の進捗を監視できます。'
    },
    {
      question: locale === 'en' ? 'What payment methods do you accept?' : 'どのような支払い方法を受け付けていますか？',
      answer: locale === 'en'
        ? 'We accept bank transfers, mobile money (MTN Mobile Money, Vodafone Cash), and cash payments through our local agents in Ghana.'
        : '銀行振込、モバイルマネー（MTN Mobile Money、Vodafone Cash）、およびガーナの現地代理店を通じた現金支払いを受け付けています。'
    },
    {
      question: locale === 'en' ? 'Do you handle customs and import duties?' : '税関や輸入関税は対応していますか？',
      answer: locale === 'en'
        ? 'We assist with customs documentation, but import duties and taxes are the responsibility of the buyer. Our local team can help guide you through the process.'
        : '税関書類の作成をサポートしますが、輸入関税と税金は買い手の責任となります。現地チームがプロセスをガイドいたします。'
    },
    {
      question: locale === 'en' ? 'Can I inspect items before purchasing?' : '購入前に商品を検査できますか？',
      answer: locale === 'en'
        ? 'We provide detailed photos and condition reports for all items. For high-value purchases, we can arrange video calls to show the items in detail.'
        : 'すべての商品について詳細な写真と状態レポートを提供いたします。高額購入の場合、商品を詳細に見せるビデオ通話を手配できます。'
    },
    {
      question: locale === 'en' ? 'What if an item is damaged during shipping?' : '配送中に商品が損傷した場合はどうなりますか？',
      answer: locale === 'en'
        ? 'All shipments are insured. In the rare case of damage during transit, we work with you and the insurance company to resolve the issue promptly.'
        : 'すべての出荷は保険に加入しています。輸送中の損傷という稀なケースでは、お客様と保険会社と協力して迅速に問題を解決いたします。'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-heading font-bold mb-6">
            {messages.buyers.title}
          </h1>
          <p className="text-xl lg:text-2xl text-primary-100 max-w-4xl mx-auto">
            {locale === 'en'
              ? 'Access premium Japanese used goods at affordable prices with reliable shipping to Ghana and across Africa'
              : 'ガーナ・アフリカ全域への信頼できる配送で、手頃な価格でプレミアムな日本の中古品にアクセス'
            }
          </p>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {locale === 'en' ? 'Why Choose EBAM Motors?' : 'なぜEBAM Motorsを選ぶのか？'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {messages.buyers.benefits}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-3xl">{benefit.icon}</span>
                </div>
                <h3 className="text-xl font-heading font-bold text-neutral-800 mb-4">
                  {benefit.title}
                </h3>
                <p className="text-neutral-600 leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Product Categories */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {locale === 'en' ? 'What We Export' : '輸出商品'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'Browse our wide range of quality Japanese products available for export'
                : '輸出可能な幅広い高品質日本製品をご覧ください'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {productCategories.map((category, index) => (
              <div key={index} className="bg-neutral-50 rounded-xl p-8">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-3xl">{category.icon}</span>
                  </div>
                  <h3 className="text-2xl font-heading font-bold text-neutral-800">
                    {category.name}
                  </h3>
                </div>
                
                <p className="text-neutral-600 mb-6 leading-relaxed">
                  {category.description}
                </p>
                
                <div>
                  <h4 className="font-semibold text-neutral-800 mb-3">
                    {locale === 'en' ? 'Popular Items:' : '人気商品：'}
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {category.popular.map((item, itemIndex) => (
                      <span key={itemIndex} className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                        {item}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href={`/${locale}/inventory`}
              className="inline-block bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {messages.homepage.viewInventory}
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 mb-6">
              {locale === 'en' ? 'Frequently Asked Questions' : 'よくある質問'}
            </h2>
            <p className="text-xl text-neutral-600">
              {messages.buyers.faq}
            </p>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-8">
                <h3 className="text-xl font-heading font-bold text-neutral-800 mb-4">
                  {faq.question}
                </h3>
                <p className="text-neutral-600 leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Local Agents Section */}
      <section className="py-20 bg-primary-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold mb-6">
              {locale === 'en' ? 'Our Local Presence' : '現地での存在'}
            </h2>
            <p className="text-xl text-primary-100 max-w-3xl mx-auto">
              {locale === 'en'
                ? 'We have established partnerships and local agents across Ghana and Africa to serve you better'
                : 'より良いサービスを提供するため、ガーナ・アフリカ全域でパートナーシップと現地代理店を確立しています'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-4xl">🇬🇭</span>
              </div>
              <h3 className="text-2xl font-heading font-bold mb-4">Ghana</h3>
              <p className="text-primary-100">
                {locale === 'en'
                  ? 'Main hub in Accra with pickup locations in major cities'
                  : 'アクラのメインハブと主要都市の受取場所'
                }
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-4xl">🌍</span>
              </div>
              <h3 className="text-2xl font-heading font-bold mb-4">
                {locale === 'en' ? 'West Africa' : '西アフリカ'}
              </h3>
              <p className="text-primary-100">
                {locale === 'en'
                  ? 'Expanding network across Nigeria, Ivory Coast, and Senegal'
                  : 'ナイジェリア、コートジボワール、セネガル全域でネットワークを拡大'
                }
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-4xl">🤝</span>
              </div>
              <h3 className="text-2xl font-heading font-bold mb-4">
                {locale === 'en' ? 'Local Support' : '現地サポート'}
              </h3>
              <p className="text-primary-100">
                {locale === 'en'
                  ? 'Dedicated customer service team speaking local languages'
                  : '現地言語を話す専任カスタマーサービスチーム'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-secondary-500 text-neutral-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-heading font-bold mb-6">
            {locale === 'en' ? 'Ready to Start Shopping?' : 'ショッピングを始める準備はできましたか？'}
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            {locale === 'en'
              ? 'Browse our current inventory or contact our team to discuss your specific needs'
              : '現在の在庫をご覧いただくか、特定のニーズについて当社チームにお問い合わせください'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href={`/${locale}/stock`}
              className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {messages.homepage.viewInventory}
            </Link>
            <Link
              href={`/${locale}/contact`}
              className="border-2 border-neutral-900 text-neutral-900 hover:bg-neutral-900 hover:text-secondary-500 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {messages.navigation.contact}
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">EM</span>
                </div>
                <span className="font-heading font-bold text-xl">EBAM MOTORS</span>
              </div>
              <p className="text-neutral-300">
                {messages.about?.mission || 'Promote sustainable trade by giving used goods a second life where they are needed most.'}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">Contact Info</h3>
              <div className="space-y-2 text-neutral-300">
                <div>
                  <p className="font-medium text-neutral-200">Japan: {messages.about?.phone || '080-6985-2864'}</p>
                  <p className="font-medium text-neutral-200">Ghana: {messages.about?.ghanaPhone || '+233245375692'}</p>
                </div>
                <p>{messages.about?.email || '<EMAIL>'}</p>
                <p>{messages.about?.location || 'Japan, Saitama, Hanno City, Nagata'}</p>
                <p>{messages.about?.ghanaLocation || 'Kumasi, Ghana'}</p>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">{messages.contact?.quickLinks || 'Quick Links'}</h3>
              <div className="space-y-2">
                <Link href={`/${locale}/services`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.services}
                </Link>
                <Link href={`/${locale}/how-it-works`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.howItWorks}
                </Link>
                <Link href={`/${locale}/suppliers`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.suppliers}
                </Link>
              </div>
            </div>
          </div>
          <div className="border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 EBAM MOTORS. {messages.ui?.allRightsReserved || 'All rights reserved.'}</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
