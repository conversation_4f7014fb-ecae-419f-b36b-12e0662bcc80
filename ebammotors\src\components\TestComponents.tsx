'use client';

// Test components for quickly testing all advanced features
// Add these to any page for easy testing

import { useState } from 'react';
import AdvancedSearch from './AdvancedSearch';
import SocialShare from './SocialShare';
import NotificationSettings from './NotificationSettings';
import { PWAStatus } from './PWAInstaller';
import { trackCarView, trackSearch, trackContactForm } from '@/lib/analytics';

// Test all analytics events
export function AnalyticsTestPanel() {
  const [message, setMessage] = useState('');

  const testAnalytics = async (eventType: string) => {
    try {
      switch (eventType) {
        case 'car_view':
          trackCarView('test-car-123', 'Toyota Voxy 2020', 2500000);
          setMessage('Car view event tracked');
          break;
        case 'search':
          trackSearch('Toyota Voxy', 5);
          setMessage('Search event tracked');
          break;
        case 'contact':
          trackContactForm('test_form');
          setMessage('Contact form event tracked');
          break;
      }
    } catch (error) {
      setMessage(`Error: ${error}`);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">🔍 Analytics Testing</h3>
      <div className="space-y-2">
        <button
          onClick={() => testAnalytics('car_view')}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
        >
          Test Car View Event
        </button>
        <button
          onClick={() => testAnalytics('search')}
          className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
        >
          Test Search Event
        </button>
        <button
          onClick={() => testAnalytics('contact')}
          className="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700"
        >
          Test Contact Form Event
        </button>
      </div>
      {message && (
        <div className="mt-4 p-3 bg-gray-100 rounded text-sm">
          {message}
        </div>
      )}
      <div className="mt-4 text-xs text-gray-600">
        Open browser DevTools → Network tab to see analytics requests
      </div>
    </div>
  );
}

// Test social sharing
export function SocialShareTestPanel() {
  const testShareData = {
    url: 'http://localhost:3000/test',
    title: 'Test Car - Toyota Voxy 2020',
    description: 'Testing social sharing functionality for EBAM Motors',
    hashtags: ['UsedCars', 'Toyota', 'Test']
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">📱 Social Share Testing</h3>
      
      <div className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Button Style:</h4>
          <SocialShare 
            shareData={testShareData}
            variant="buttons"
            showLabels={true}
            size="md"
          />
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Dropdown Style:</h4>
          <SocialShare 
            shareData={testShareData}
            variant="dropdown"
            showLabels={true}
          />
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Modal Style:</h4>
          <SocialShare 
            shareData={testShareData}
            variant="modal"
            showLabels={true}
          />
        </div>
      </div>
    </div>
  );
}

// Test advanced search
export function SearchTestPanel() {
  const [searchResults, setSearchResults] = useState<any>(null);

  const handleSearch = (query: string, filters: any, sortBy: any) => {
    console.log('Search performed:', { query, filters, sortBy });
    setSearchResults({ query, filters, sortBy, timestamp: new Date().toISOString() });
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">🔍 Advanced Search Testing</h3>
      
      <AdvancedSearch 
        onSearch={handleSearch}
        initialQuery="Toyota"
      />
      
      {searchResults && (
        <div className="mt-4 p-4 bg-gray-100 rounded">
          <h4 className="font-medium mb-2">Last Search Results:</h4>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(searchResults, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

// Test API endpoints
export function APITestPanel() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState<string | null>(null);

  const testAPI = async (endpoint: string, method: string = 'GET', data?: any) => {
    setLoading(endpoint);
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };
      
      if (data) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(endpoint, options);
      const result = await response.json();
      
      setResults(prev => ({
        ...prev,
        [endpoint]: {
          status: response.status,
          data: result,
          timestamp: new Date().toISOString()
        }
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [endpoint]: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setLoading(null);
    }
  };

  const apiTests = [
    { name: 'Health Check', endpoint: '/api/admin/health', method: 'GET' },
    { name: 'Analytics', endpoint: '/api/admin/analytics', method: 'GET' },
    { name: 'Notification Stats', endpoint: '/api/notifications/send', method: 'GET' },
    { name: 'Forum Posts', endpoint: '/api/community/forum/posts', method: 'GET' },
    { name: 'Social Feed', endpoint: '/api/social/feed', method: 'GET' },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">🔌 API Testing</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
        {apiTests.map((test) => (
          <button
            key={test.endpoint}
            onClick={() => testAPI(test.endpoint, test.method)}
            disabled={loading === test.endpoint}
            className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm"
          >
            {loading === test.endpoint ? 'Testing...' : test.name}
          </button>
        ))}
      </div>

      <div className="space-y-2 max-h-64 overflow-auto">
        {Object.entries(results).map(([endpoint, result]: [string, any]) => (
          <div key={endpoint} className="p-3 bg-gray-100 rounded">
            <div className="font-medium text-sm">{endpoint}</div>
            <div className="text-xs text-gray-600 mt-1">
              {result.error ? (
                <span className="text-red-600">Error: {result.error}</span>
              ) : (
                <span className="text-green-600">Status: {result.status}</span>
              )}
            </div>
            <pre className="text-xs mt-2 overflow-auto max-h-32">
              {JSON.stringify(result.data || result.error, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </div>
  );
}

// Test push notifications
export function NotificationTestPanel() {
  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">🔔 Push Notification Testing</h3>
      <NotificationSettings />
    </div>
  );
}

// PWA status display
export function PWATestPanel() {
  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">📱 PWA Status</h3>
      <PWAStatus />
      
      <div className="mt-4 space-y-2 text-sm">
        <div>
          <strong>Test Steps:</strong>
        </div>
        <ol className="list-decimal list-inside space-y-1 text-gray-600">
          <li>Check if service worker is registered</li>
          <li>Test offline functionality (DevTools → Network → Offline)</li>
          <li>Look for installation prompt (wait 5 seconds)</li>
          <li>Check manifest.json at /manifest.json</li>
        </ol>
      </div>
    </div>
  );
}

// All-in-one testing dashboard
export default function TestingDashboard() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 EBAM Motors - Feature Testing Dashboard
          </h1>
          <p className="text-gray-600">
            Test all advanced features from this dashboard. Open browser DevTools to monitor network requests and console logs.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AnalyticsTestPanel />
          <SocialShareTestPanel />
          <NotificationTestPanel />
          <PWATestPanel />
          <APITestPanel />
          <SearchTestPanel />
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">⚠️ Testing Notes:</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Make sure your .env.local file has the required environment variables</li>
            <li>• Some features require HTTPS (use ngrok for local testing if needed)</li>
            <li>• Push notifications need VAPID keys to be configured</li>
            <li>• Check browser console for any error messages</li>
            <li>• Test on different browsers and devices</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
