'use client';

// Test components for quickly testing all advanced features
// Add these to any page for easy testing

import { useState } from 'react';
// Import analytics functions
import { trackCarView, trackSearch, trackContactForm } from '@/lib/analytics';

// Test all analytics events
export function AnalyticsTestPanel() {
  const [message, setMessage] = useState('');

  const testAnalytics = async (eventType: string) => {
    try {
      switch (eventType) {
        case 'car_view':
          trackCarView('test-car-123', 'Toyota Voxy 2020', 2500000);
          setMessage('Car view event tracked');
          break;
        case 'search':
          trackSearch('Toyota Voxy', 5);
          setMessage('Search event tracked');
          break;
        case 'contact':
          trackContactForm('test_form');
          setMessage('Contact form event tracked');
          break;
      }
    } catch (error) {
      setMessage(`Error: ${error}`);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">🔍 Analytics Testing</h3>
      <div className="space-y-2">
        <button
          onClick={() => testAnalytics('car_view')}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
        >
          Test Car View Event
        </button>
        <button
          onClick={() => testAnalytics('search')}
          className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
        >
          Test Search Event
        </button>
        <button
          onClick={() => testAnalytics('contact')}
          className="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700"
        >
          Test Contact Form Event
        </button>
      </div>
      {message && (
        <div className="mt-4 p-3 bg-gray-100 rounded text-sm">
          {message}
        </div>
      )}
      <div className="mt-4 text-xs text-gray-600">
        Open browser DevTools → Network tab to see analytics requests
      </div>
    </div>
  );
}

// Test social sharing
export function SocialShareTestPanel() {
  const testShare = (platform: string) => {
    console.log(`Testing share on ${platform}`);
    alert(`Would share on ${platform} - check console for details`);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">📱 Social Share Testing</h3>

      <div className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Test Social Sharing:</h4>
          <div className="flex space-x-2">
            <button
              onClick={() => testShare('Facebook')}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Facebook
            </button>
            <button
              onClick={() => testShare('Twitter')}
              className="bg-blue-400 text-white px-4 py-2 rounded hover:bg-blue-500"
            >
              Twitter
            </button>
            <button
              onClick={() => testShare('WhatsApp')}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              WhatsApp
            </button>
          </div>
        </div>

        <div className="text-sm text-gray-600">
          <p>To test full social sharing functionality:</p>
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li>Import SocialShare component</li>
            <li>Add social media platform configurations</li>
            <li>Test on different devices and browsers</li>
          </ol>
        </div>
      </div>
    </div>
  );
}

// Test advanced search
export function SearchTestPanel() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any>(null);

  const handleSearch = () => {
    const mockResults = {
      query: searchQuery,
      results: [
        { id: 1, name: 'Toyota Voxy 2020', price: 2500000 },
        { id: 2, name: 'Honda Freed 2019', price: 2200000 },
      ],
      timestamp: new Date().toISOString()
    };
    setSearchResults(mockResults);
    console.log('Search performed:', mockResults);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">🔍 Advanced Search Testing</h3>

      <div className="space-y-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search for cars..."
            className="flex-1 border border-gray-300 rounded px-3 py-2"
          />
          <button
            onClick={handleSearch}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Search
          </button>
        </div>

        {searchResults && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h4 className="font-medium mb-2">Mock Search Results:</h4>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(searchResults, null, 2)}
            </pre>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <p>To test full advanced search functionality:</p>
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li>Import AdvancedSearch component</li>
            <li>Configure search filters and sorting</li>
            <li>Test autocomplete and suggestions</li>
            <li>Test saved searches functionality</li>
          </ol>
        </div>
      </div>
    </div>
  );
}

// Test API endpoints
export function APITestPanel() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState<string | null>(null);

  const testAPI = async (endpoint: string, method: string = 'GET', data?: any) => {
    setLoading(endpoint);
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };
      
      if (data) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(endpoint, options);
      const result = await response.json();
      
      setResults(prev => ({
        ...prev,
        [endpoint]: {
          status: response.status,
          data: result,
          timestamp: new Date().toISOString()
        }
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [endpoint]: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setLoading(null);
    }
  };

  const apiTests = [
    { name: 'Health Check', endpoint: '/api/admin/health', method: 'GET' },
    { name: 'Analytics', endpoint: '/api/admin/analytics', method: 'GET' },
    { name: 'Notification Stats', endpoint: '/api/notifications/send', method: 'GET' },
    { name: 'Forum Posts', endpoint: '/api/community/forum/posts', method: 'GET' },
    { name: 'Social Feed', endpoint: '/api/social/feed', method: 'GET' },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">🔌 API Testing</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
        {apiTests.map((test) => (
          <button
            key={test.endpoint}
            onClick={() => testAPI(test.endpoint, test.method)}
            disabled={loading === test.endpoint}
            className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm"
          >
            {loading === test.endpoint ? 'Testing...' : test.name}
          </button>
        ))}
      </div>

      <div className="space-y-2 max-h-64 overflow-auto">
        {Object.entries(results).map(([endpoint, result]: [string, any]) => (
          <div key={endpoint} className="p-3 bg-gray-100 rounded">
            <div className="font-medium text-sm">{endpoint}</div>
            <div className="text-xs text-gray-600 mt-1">
              {result.error ? (
                <span className="text-red-600">Error: {result.error}</span>
              ) : (
                <span className="text-green-600">Status: {result.status}</span>
              )}
            </div>
            <pre className="text-xs mt-2 overflow-auto max-h-32">
              {JSON.stringify(result.data || result.error, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </div>
  );
}

// Test push notifications
export function NotificationTestPanel() {
  const [notificationStatus, setNotificationStatus] = useState('Not supported');

  const testNotifications = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      setNotificationStatus(`Permission: ${permission}`);

      if (permission === 'granted') {
        new Notification('Test Notification', {
          body: 'This is a test notification from EBAM Motors',
          icon: '/favicon.ico'
        });
      }
    } else {
      setNotificationStatus('Notifications not supported');
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">🔔 Push Notification Testing</h3>

      <div className="space-y-4">
        <div>
          <button
            onClick={testNotifications}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Test Browser Notification
          </button>
        </div>

        <div className="text-sm">
          <strong>Status:</strong> {notificationStatus}
        </div>

        <div className="text-sm text-gray-600">
          <p>To test full push notification functionality:</p>
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li>Configure VAPID keys in environment variables</li>
            <li>Import NotificationSettings component</li>
            <li>Test subscription management</li>
            <li>Test admin notification sender</li>
            <li>Test on mobile devices with HTTPS</li>
          </ol>
        </div>
      </div>
    </div>
  );
}

// PWA status display
export function PWATestPanel() {
  const [pwaStatus, setPwaStatus] = useState<any>({});

  const checkPWAStatus = () => {
    const status = {
      serviceWorker: 'serviceWorker' in navigator,
      manifest: document.querySelector('link[rel="manifest"]') !== null,
      isStandalone: window.matchMedia('(display-mode: standalone)').matches,
      isInstallable: 'BeforeInstallPromptEvent' in window,
    };
    setPwaStatus(status);
  };

  const testManifest = () => {
    window.open('/manifest.json', '_blank');
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <h3 className="text-lg font-semibold mb-4">📱 PWA Status</h3>

      <div className="space-y-4">
        <button
          onClick={checkPWAStatus}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Check PWA Status
        </button>

        <button
          onClick={testManifest}
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-2"
        >
          View Manifest
        </button>

        {Object.keys(pwaStatus).length > 0 && (
          <div className="p-3 bg-gray-100 rounded">
            <h4 className="font-medium mb-2">PWA Status:</h4>
            <pre className="text-xs">
              {JSON.stringify(pwaStatus, null, 2)}
            </pre>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <p><strong>Test Steps:</strong></p>
          <ol className="list-decimal list-inside space-y-1 mt-2">
            <li>Check if service worker is registered</li>
            <li>Test offline functionality (DevTools → Network → Offline)</li>
            <li>Look for installation prompt (wait 5 seconds)</li>
            <li>Check manifest.json accessibility</li>
          </ol>
        </div>
      </div>
    </div>
  );
}

// All-in-one testing dashboard
export default function TestingDashboard() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 EBAM Motors - Feature Testing Dashboard
          </h1>
          <p className="text-gray-600">
            Test all advanced features from this dashboard. Open browser DevTools to monitor network requests and console logs.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AnalyticsTestPanel />
          <SocialShareTestPanel />
          <NotificationTestPanel />
          <PWATestPanel />
          <APITestPanel />
          <SearchTestPanel />
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">⚠️ Testing Notes:</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Make sure your .env.local file has the required environment variables</li>
            <li>• Some features require HTTPS (use ngrok for local testing if needed)</li>
            <li>• Push notifications need VAPID keys to be configured</li>
            <li>• Check browser console for any error messages</li>
            <li>• Test on different browsers and devices</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
