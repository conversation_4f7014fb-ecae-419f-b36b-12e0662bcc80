exports.id=1542,exports.ids=[1542],exports.modules={6234:(e,t,r)=>{"use strict";r.d(t,{kA:()=>m,yV:()=>y,GJ:()=>g,_S:()=>h,pG:()=>v});var a=r(53190);let o={abandoned_cart:{id:"abandoned_cart",name:"Abandoned Cart Reminder",subject:"Complete Your Purchase - {{customerName}}",htmlBody:`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Don't Miss Out on Your Selected Vehicle!</h2>
        <p>Hi {{customerName}},</p>
        <p>We noticed you were interested in purchasing a vehicle from EBAM Motors but didn't complete your order.</p>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Your Selected Vehicle:</h3>
          <p><strong>{{vehicleTitle}}</strong></p>
          <p>Price: <strong>\xa5{{vehiclePrice}}</strong></p>
        </div>
        <p>Complete your purchase now to secure this vehicle before it's sold to someone else!</p>
        <a href="{{checkoutUrl}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Complete Purchase</a>
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
          If you have any questions, feel free to contact <NAME_EMAIL> or +233245375692
        </p>
      </div>
    `,textBody:`Hi {{customerName}},

We noticed you were interested in purchasing {{vehicleTitle}} for \xa5{{vehiclePrice}} but didn't complete your order.

Complete your purchase now: {{checkoutUrl}}

Contact us: <EMAIL> or +233245375692`,variables:["customerName","vehicleTitle","vehiclePrice","checkoutUrl"]},order_delivered:{id:"order_delivered",name:"Order Delivered Follow-up",subject:"How was your recent purchase? - EBAM Motors",htmlBody:`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Thank You for Your Purchase!</h2>
        <p>Hi {{customerName}},</p>
        <p>We hope you're enjoying your recent vehicle purchase from EBAM Motors!</p>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Your Order:</h3>
          <p><strong>{{vehicleTitle}}</strong></p>
          <p>Order #: {{orderNumber}}</p>
          <p>Delivered: {{deliveryDate}}</p>
        </div>
        <p>We'd love to hear about your experience. Would you mind leaving us a review?</p>
        <a href="{{reviewUrl}}" style="background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Leave a Review</a>
        <p style="margin-top: 20px;">Also, don't forget to refer friends and family - you'll earn loyalty points for each successful referral!</p>
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
          Need support? Contact <NAME_EMAIL> or +233245375692
        </p>
      </div>
    `,textBody:`Hi {{customerName}},

Thank you for purchasing {{vehicleTitle}} (Order #{{orderNumber}}).

We'd love to hear about your experience. Leave a review: {{reviewUrl}}

Contact us: <EMAIL> or +233245375692`,variables:["customerName","vehicleTitle","orderNumber","deliveryDate","reviewUrl"]},customer_reengagement:{id:"customer_reengagement",name:"Customer Re-engagement",subject:"We Miss You! Check Out Our Latest Vehicles",htmlBody:`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">We Miss You at EBAM Motors!</h2>
        <p>Hi {{customerName}},</p>
        <p>It's been a while since your last visit, and we wanted to reach out with some exciting updates!</p>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>What's New:</h3>
          <ul>
            <li>Fresh vehicle arrivals from Japan</li>
            <li>Special pricing on popular models</li>
            <li>Enhanced shipping options to Ghana</li>
            <li>New loyalty rewards program</li>
          </ul>
        </div>
        <p>Browse our latest stock and find your perfect vehicle today!</p>
        <a href="{{stockUrl}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">View Latest Stock</a>
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
          Questions? Contact <NAME_EMAIL> or +233245375692
        </p>
      </div>
    `,textBody:`Hi {{customerName}},

We miss you at EBAM Motors! Check out our latest vehicle arrivals and special offers.

Browse our stock: {{stockUrl}}

Contact us: <EMAIL> or +233245375692`,variables:["customerName","stockUrl"]},lead_followup:{id:"lead_followup",name:"Lead Follow-up",subject:"Following up on your inquiry - EBAM Motors",htmlBody:`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Thank You for Your Inquiry!</h2>
        <p>Hi {{customerName}},</p>
        <p>Thank you for reaching out to EBAM Motors regarding {{inquirySubject}}.</p>
        <p>We wanted to follow up and see if you have any additional questions or if there's anything specific we can help you with.</p>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Your Inquiry:</h3>
          <p>{{inquiryMessage}}</p>
          <p><em>Submitted: {{inquiryDate}}</em></p>
        </div>
        <p>Our team is ready to assist you with:</p>
        <ul>
          <li>Vehicle recommendations based on your needs</li>
          <li>Pricing and financing options</li>
          <li>Shipping arrangements to Ghana</li>
          <li>Documentation and import procedures</li>
        </ul>
        <a href="{{contactUrl}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Contact Us</a>
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
          Direct contact: <EMAIL> or +233245375692
        </p>
      </div>
    `,textBody:`Hi {{customerName}},

Thank you for your inquiry about {{inquirySubject}}.

Your message: {{inquiryMessage}}

We're here to help with vehicle recommendations, pricing, shipping, and documentation.

Contact us: {{contactUrl}} or <EMAIL> or +233245375692`,variables:["customerName","inquirySubject","inquiryMessage","inquiryDate","contactUrl"]}};function i(e,t){let r=e;return Object.entries(t).forEach(([e,t])=>{let a=RegExp(`{{${e}}}`,"g");r=r.replace(a,t||"")}),r}function n(e,t,r){let a=o[e]||null;return a?{to:r,subject:i(a.subject,t),htmlBody:i(a.htmlBody,t),textBody:i(a.textBody,t),from:"EBAM Motors <<EMAIL>>",replyTo:"<EMAIL>"}:null}async function s(e){try{return await new Promise(e=>setTimeout(e,1e3)),{success:!0,messageId:`mock_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}}catch(e){return console.error("Email sending failed:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function c(e,t,r,a,o){let i=n("abandoned_cart",{customerName:t,vehicleTitle:r,vehiclePrice:a.toLocaleString(),checkoutUrl:o},e);return!!i&&(await s(i)).success}async function l(e,t,r,a,o,i){let c=n("order_delivered",{customerName:t,vehicleTitle:r,orderNumber:a,deliveryDate:o,reviewUrl:i},e);return!!c&&(await s(c)).success}async function u(e,t,r){let a=n("customer_reengagement",{customerName:t,stockUrl:r},e);return!!a&&(await s(a)).success}async function d(e,t,r,a,o,i){let c=n("lead_followup",{customerName:t,inquirySubject:r,inquiryMessage:a,inquiryDate:o,contactUrl:i},e);return!!c&&(await s(c)).success}var p=r(16967);async function m(){try{for(let e of(await (0,a.getPendingFollowUps)()))await f(e)}catch(e){console.error("❌ Error processing follow-ups:",e)}}async function f(e){try{let t=!1,r="";switch(e.type){case"email":r=(t=await w(e))?"Email sent successfully":"Failed to send email";break;case"task":t=!0,r="Task reminder processed";break;case"phone":t=!0,r="Phone follow-up reminder created";break;case"whatsapp":t=!0,r="WhatsApp follow-up reminder created";break;default:return}await (0,a.updateFollowUp)(e.id,{status:t?"completed":"failed",completedDate:t?new Date().toISOString():void 0,result:{success:t,message:r,responseReceived:!1,nextAction:t?void 0:"retry_later"}})}catch(t){console.error(`❌ Error processing follow-up ${e.id}:`,t),await (0,a.updateFollowUp)(e.id,{status:"failed",result:{success:!1,message:t instanceof Error?t.message:"Unknown error",responseReceived:!1,nextAction:"manual_review"}})}}async function w(e){if(!e.customerId&&!e.leadId)return console.error("Follow-up missing customer or lead ID"),!1;let t="",r="";if(e.customerId){let o=await (0,a.getCustomerById)(e.customerId);if(!o)return console.error(`Customer not found: ${e.customerId}`),!1;t=o.personalInfo.email,r=o.personalInfo.name}if(!t)return console.error("Customer email not found"),!1;if(e.automationRule)switch(e.automationRule.trigger){case"abandoned_cart":return await c(t,r,e.automationRule.conditions?.vehicleTitle||"Selected Vehicle",e.automationRule.conditions?.cartValue||0,e.automationRule.conditions?.checkoutUrl||"https://ebammotors.com/checkout");case"order_delivered":return await l(t,r,e.automationRule.conditions?.vehicleTitle||"Your Vehicle",e.automationRule.conditions?.orderNumber||"N/A",new Date().toLocaleDateString(),"https://ebammotors.com/leave-review");case"no_activity":return await u(t,r,"https://ebammotors.com/stock")}return await d(t,r,e.title,e.description,new Date(e.createdAt).toLocaleDateString(),"https://ebammotors.com/contact")}async function y(e,t){try{if(!await (0,a.getCustomerById)(e))return;await (0,a.XL)({type:"email",status:"pending",priority:"medium",customerId:e,title:"Abandoned Cart Follow-up",description:`Customer abandoned cart with ${t.items.length} items worth \xa5${t.totalValue.toLocaleString()}`,scheduledDate:new Date(Date.now()+864e5).toISOString(),automationRule:{trigger:"abandoned_cart",delay:24,conditions:{vehicleTitle:t.items[0]?.title,cartValue:t.totalValue,checkoutUrl:t.checkoutUrl}},createdBy:"system"})}catch(e){console.error("Error scheduling abandoned cart follow-up:",e)}}async function h(e,t,r){try{await (0,a.XL)({type:"email",status:"pending",priority:"low",customerId:e,orderId:t,title:"Order Delivery Follow-up",description:`Follow up on delivered order ${r.orderNumber}`,scheduledDate:new Date(Date.now()+6048e5).toISOString(),automationRule:{trigger:"order_delivered",delay:168,conditions:{vehicleTitle:r.vehicleTitle,orderNumber:r.orderNumber}},createdBy:"system"})}catch(e){console.error("Error scheduling order delivery follow-up:",e)}}async function g(){try{let e=await (0,a.Rf)(),t=new Date(Date.now()-2592e6);for(let r of e)!(await (0,a.aN)(r.id)).find(e=>new Date(e.timestamp)>t)&&"active"===r.status&&((await (0,a.getPendingFollowUps)()).some(e=>e.customerId===r.id&&e.automationRule?.trigger==="no_activity"&&new Date(e.createdAt)>new Date(Date.now()-6048e5))||await (0,a.XL)({type:"email",status:"pending",priority:"low",customerId:r.id,title:"Customer Re-engagement",description:"Re-engage inactive customer (30+ days since last activity)",scheduledDate:new Date().toISOString(),automationRule:{trigger:"no_activity",delay:0,conditions:{daysSinceLastActivity:30}},createdBy:"system"}))}catch(e){console.error("Error scheduling inactive customer follow-ups:",e)}}async function v(e){let t={customerName:e.customerName,customerEmail:e.customerEmail,type:"delivery_update",data:{orderId:e.orderId,status:e.status,location:e.location,estimatedArrival:e.estimatedArrival}};try{return await p.gm.sendFollowUpEmail(t),{success:!0}}catch(e){return console.error("Failed to send delivery update follow-up via Resend:",e),{success:!1,error:e}}}},53190:(e,t,r)=>{"use strict";r.d(t,{Gk:()=>T,Gq:()=>Y,HR:()=>N,Kt:()=>O,Q6:()=>A,Rf:()=>U,XL:()=>V,Y2:()=>E,_Y:()=>z,aN:()=>Z,createCustomerActivity:()=>K,createInteraction:()=>j,dD:()=>H,fE:()=>L,getAllFollowUps:()=>J,getCustomerByEmail:()=>C,getCustomerById:()=>_,getLeadById:()=>I,getPendingFollowUps:()=>$,oP:()=>ee,qz:()=>q,sr:()=>k,tR:()=>S,tS:()=>D,updateFollowUp:()=>G});var a=r(29021),o=r(33873),i=r.n(o),n=r(23870);let s=i().join(process.cwd(),"data"),c=i().join(s,"leads.json"),l=i().join(s,"customers.json"),u=i().join(s,"interactions.json"),d=i().join(s,"followups.json"),p=i().join(s,"activities.json"),m=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,f=[],w=[],y=[],h=[],g=[];async function v(){if(!m)try{await a.promises.access(s)}catch{await a.promises.mkdir(s,{recursive:!0})}}async function b(){if(m)return f;try{await v();let e=await a.promises.readFile(c,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function x(e){if(m){f=e;return}await v(),await a.promises.writeFile(c,JSON.stringify(e,null,2))}async function S(e){let t=await b(),r={...e,id:(0,n.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await x(t),r}async function D(){return await b()}async function I(e){return(await b()).find(t=>t.id===e)||null}async function A(e,t){let r=await b(),a=r.findIndex(t=>t.id===e);return -1!==a&&(r[a]={...r[a],...t,updatedAt:new Date().toISOString()},await x(r),!0)}async function k(e){let t=await b(),r=t.filter(t=>t.id!==e);return r.length!==t.length&&(await x(r),!0)}async function N(e){return(await b()).filter(t=>t.status===e)}async function O(e){return(await b()).filter(t=>t.source===e)}async function F(){if(m)return w;try{await v();let e=await a.promises.readFile(l,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function B(e){if(m){w=e;return}await v(),await a.promises.writeFile(l,JSON.stringify(e,null,2))}async function E(e){let t=await F(),r=t.findIndex(t=>t.personalInfo.email===e.personalInfo.email);if(-1!==r)return t[r]={...t[r],...e,updatedAt:new Date().toISOString()},await B(t),t[r];{let r={...e,id:(0,n.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await B(t),r}}async function U(){return await F()}async function _(e){return(await F()).find(t=>t.id===e)||null}async function C(e){return(await F()).find(t=>t.personalInfo.email===e)||null}async function T(e,t){let r=await F(),a=r.findIndex(t=>t.id===e);return -1!==a&&(r[a]={...r[a],...t,updatedAt:new Date().toISOString()},await B(r),!0)}async function M(){if(m)return y;try{await v();let e=await a.promises.readFile(u,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function R(e){if(m){y=e;return}await v(),await a.promises.writeFile(u,JSON.stringify(e,null,2))}async function j(e){let t=await M(),r={...e,id:(0,n.A)(),createdAt:new Date().toISOString()};return t.push(r),await R(t),r}async function L(){return await M()}async function q(e){return(await M()).filter(t=>t.customerId===e)}async function Y(e){return(await M()).filter(t=>t.leadId===e)}async function P(){if(m)return h;try{await v();let e=await a.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function W(e){if(m){h=e;return}await v(),await a.promises.writeFile(d,JSON.stringify(e,null,2))}async function V(e){let t=await P(),r={...e,id:(0,n.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await W(t),r}async function J(){return await P()}async function H(e){return(await P()).filter(t=>t.status===e)}async function $(){let e=await P(),t=new Date().toISOString();return e.filter(e=>"pending"===e.status&&e.scheduledDate<=t)}async function G(e,t){let r=await P(),a=r.findIndex(t=>t.id===e);return -1!==a&&(r[a]={...r[a],...t,updatedAt:new Date().toISOString()},await W(r),!0)}async function z(e){return(await P()).filter(t=>t.customerId===e)}async function X(){if(m)return g;try{await v();let e=await a.promises.readFile(p,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function Q(e){if(m){g=e;return}await v(),await a.promises.writeFile(p,JSON.stringify(e,null,2))}async function K(e){let t=await X(),r={...e,id:(0,n.A)(),timestamp:new Date().toISOString()};return t.push(r),await Q(t),r}async function Z(e){return(await X()).filter(t=>t.customerId===e)}async function ee(e){let t=await _(e);if(!t)return null;let r=await q(e),a=await z(e),o=await Z(e);return{customer:t,stats:{totalInteractions:r.length,pendingFollowUps:a.filter(e=>"pending"===e.status).length,recentActivities:o.filter(e=>new Date(e.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:r.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())[0]?.createdAt},recentInteractions:r.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()).slice(0,5),upcomingFollowUps:a.filter(e=>"pending"===e.status).sort((e,t)=>new Date(e.scheduledDate).getTime()-new Date(t.scheduledDate).getTime()).slice(0,3)}}},78335:()=>{},96487:()=>{}};