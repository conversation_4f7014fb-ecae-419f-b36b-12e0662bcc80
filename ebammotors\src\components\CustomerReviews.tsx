'use client';

import { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import TestimonialCard from './TestimonialCard';
import { getMessagesSync } from '@/lib/messages';

interface Review {
  id: string;
  name: string;
  location: string;
  rating: number;
  review: string;
  title?: string;
  locale?: string;
  // Translation fields
  titleEn?: string;
  titleJa?: string;
  reviewEn?: string;
  reviewJa?: string;
}

interface CustomerReviewsProps {
  locale: string;
}

export default function CustomerReviews({ locale }: CustomerReviewsProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const messages = getMessagesSync(locale);

  // Static fallback testimonials with localized content
  const staticTestimonials = [
    {
      name: "Kwame Asante",
      location: "Accra, Ghana",
      rating: 5,
      testimonial: locale === 'ja'
        ? "EBAM Motorsは私の家族に最適なトヨタ ヴォクシーを手に入れるのを手伝ってくれました。プロセスはスムーズで、車は優れた状態で到着しました。強くお勧めします！"
        : "EBAM Motors helped me get the perfect Toyota Voxy for my family. The process was smooth, and the car arrived in excellent condition. Highly recommended!"
    },
    {
      name: "Sarah Johnson",
      location: "Lagos, Nigeria",
      rating: 5,
      testimonial: locale === 'ja'
        ? "優れたサービス！私が探していたものを正確に見つけ、すべての配送詳細を処理してくれました。最初から最後までプロフェッショナルで信頼できます。"
        : "Outstanding service! They found exactly what I was looking for and handled all the shipping details. Professional and reliable from start to finish."
    },
    {
      name: "Osei Gyima",
      location: "Kumasi, Ghana",
      rating: 5,
      testimonial: locale === 'ja'
        ? "EBAM Motorsでの素晴らしい体験。優れたカスタマーサービスを提供し、約束したことを正確に実行してくれました。必ずまた利用します！"
        : "Excellent experience with EBAM Motors. They provided great customer service and delivered exactly what they promised. Will definitely use them again!"
    }
  ];

  useEffect(() => {
    fetchApprovedReviews();
  }, []);

  const fetchApprovedReviews = async () => {
    try {
      const response = await fetch('/api/reviews?status=approved');
      const data = await response.json();
      setReviews(data.reviews || []);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get the appropriate review text based on locale
  const getLocalizedReview = (review: Review) => {
    if (locale === 'ja') {
      return review.reviewJa || review.review;
    }
    return review.reviewEn || review.review;
  };

  // Combine dynamic reviews with static testimonials
  const allTestimonials = [
    // Convert dynamic reviews to testimonial format with localized content
    ...reviews.map(review => ({
      name: review.name,
      location: review.location,
      rating: review.rating,
      testimonial: getLocalizedReview(review)
    })),
    // Add static testimonials if we have fewer than 3 dynamic reviews
    ...(reviews.length < 3 ? staticTestimonials.slice(0, 3 - reviews.length) : [])
  ];

  // Show only first 6 testimonials
  const displayTestimonials = allTestimonials.slice(0, 6);

  if (loading) {
    return (
      <section className="py-24 bg-gradient-to-br from-neutral-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6 mb-16">
            <div className="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-semibold">
              <Star className="w-4 h-4" />
              <span>{messages.reviews.customerReviews}</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-heading font-bold text-neutral-800 leading-tight">
              {messages.reviews.whatCustomersSay}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              {messages.reviews.customerTestimonial}
            </p>
          </div>
          
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-neutral-600">{messages.reviews.loadingReviews}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 bg-gradient-to-br from-neutral-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-6 mb-16">
          <div className="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-semibold">
            <Star className="w-4 h-4" />
            <span>{messages.reviews.customerReviews}</span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-heading font-bold text-neutral-800 leading-tight">
            {messages.reviews.whatCustomersSay}
          </h2>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
            {messages.reviews.customerTestimonial}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayTestimonials.map((testimonial, index) => (
            <TestimonialCard
              key={`testimonial-${index}`}
              name={testimonial.name}
              location={testimonial.location}
              rating={testimonial.rating}
              testimonial={testimonial.testimonial}
              image={testimonial.image}
            />
          ))}
        </div>


      </div>
    </section>
  );
}
