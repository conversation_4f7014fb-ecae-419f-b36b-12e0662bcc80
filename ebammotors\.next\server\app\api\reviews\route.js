"use strict";(()=>{var e={};e.id=9217,e.ids=[2833,9217],e.modules={1741:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>f,serverHooks:()=>R,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>w,PATCH:()=>h,POST:()=>g});var n=r(96559),i=r(48088),o=r(37719),a=r(32190),u=r(67462),l=r(16967),c=r(77268),p=r(92833),d=r(48137),m=r(4161);async function g(e){try{let t=await e.formData(),r=t.get("name"),s=t.get("location"),n=t.get("email"),i=t.get("rating"),o=t.get("review");if(!r?.trim())return a.NextResponse.json({success:!1,message:"Name is required"},{status:400});if(!s?.trim())return a.NextResponse.json({success:!1,message:"Location is required"},{status:400});if(!n?.trim())return a.NextResponse.json({success:!1,message:"Email is required"},{status:400});if(!i||isNaN(parseInt(i))||1>parseInt(i)||parseInt(i)>5)return a.NextResponse.json({success:!1,message:"Valid rating (1-5) is required"},{status:400});if(!o?.trim())return a.NextResponse.json({success:!1,message:"Review text is required"},{status:400});let c=o.trim().split(/\s+/).filter(e=>e.length>0);if(c.length<10)return a.NextResponse.json({success:!1,message:"Review must be at least 10 words long"},{status:400});if(c.length>150)return a.NextResponse.json({success:!1,message:"Review must not exceed 150 words"},{status:400});let p={name:r.trim(),location:s.trim(),email:n.trim(),rating:parseInt(i),review:o.trim(),locale:t.get("locale")||"en",submittedAt:t.get("submittedAt")||new Date().toISOString(),status:"pending"};try{let e=(0,m.od)(p.review),t=await (0,m.Uw)({name:p.name,location:p.location,review:p.review,title:void 0},"en"===e?"ja":"en");"en"===e?(p.reviewEn=p.review,p.reviewJa=t.review):(p.reviewJa=p.review,p.reviewEn=t.review)}catch(e){console.error("Translation failed, continuing without translation:",e)}let d=await (0,u.kD)(p);try{let e={customerName:p.name,vehicleTitle:p.vehicleTitle,rating:p.rating,review:p.review,reviewDate:new Date(p.submittedAt).toLocaleDateString()};await l.gm.sendReviewNotificationToAdmin(e)}catch(e){console.error("Failed to send review notification email:",e)}return a.NextResponse.json({success:!0,message:"Review submitted successfully! It will be reviewed by our team before being published.",reviewId:d.id})}catch(t){console.error("Error processing review:",t);let e="Failed to submit review";return t instanceof Error&&(e=t.message),a.NextResponse.json({success:!1,message:e},{status:500})}}async function w(e){let t=(0,d.HL)(e);try{if((0,c.iY)(e).isValid){let e=await (0,u.zu)();t.finish(200);let r=a.NextResponse.json({reviews:e}),s=(0,p.getSecurityHeaders)();return Object.entries(s).forEach(([e,t])=>{r.headers.set(e,t)}),r}let r=await (0,u.Ve)();t.finish(200);let s=a.NextResponse.json({reviews:r}),n=(0,p.getSecurityHeaders)();return Object.entries(n).forEach(([e,t])=>{s.headers.set(e,t)}),s}catch(s){(0,d.vV)(s instanceof Error?s:"Unknown error",{endpoint:"/api/reviews",method:"GET",request:e,additionalContext:{action:"fetch_reviews"}}),t.finish(500,s instanceof Error?s.message:"Unknown error");let r=a.NextResponse.json({success:!1,message:"Failed to fetch reviews"},{status:500});return Object.entries((0,p.getSecurityHeaders)()).forEach(([e,t])=>{r.headers.set(e,t)}),r}}async function h(e){try{let{reviewId:t,status:r,adminKey:s}=await e.json(),n=process.env.ADMIN_PASSWORD||"admin123";if(s!==n)return a.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let i=await (0,u.RV)(t);if(!i)return a.NextResponse.json({success:!1,message:"Review not found"},{status:404});if(!await (0,u.WD)(t,r))return a.NextResponse.json({success:!1,message:"Failed to update review status"},{status:500});if("approved"===r&&i.email)try{let e={customerName:i.name,vehicleTitle:i.vehicleTitle,rating:i.rating,review:i.review,reviewDate:new Date(i.submittedAt).toLocaleDateString(),isApproval:!0};await l.gm.sendReviewApprovalNotification(i.email,e)}catch(e){console.error("Failed to send review approval email:",e)}return a.NextResponse.json({success:!0,message:`Review ${r} successfully`})}catch(e){return console.error("Error moderating review:",e),a.NextResponse.json({success:!1,message:"Failed to moderate review"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/reviews/route",pathname:"/api/reviews",filename:"route",bundlePath:"app/api/reviews/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\reviews\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:x,serverHooks:R}=f;function q(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:x})}},2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48137:(e,t,r)=>{r.d(t,{AD:()=>c,HL:()=>i,PG:()=>l,Sz:()=>a,ZX:()=>u,pX:()=>p,vV:()=>o});let s=[],n=[];function i(e){let t=Date.now(),r=new URL(e.url).pathname,n=e.method;return{endpoint:r,method:n,startTime:t,finish:(i,o)=>{let a=Date.now()-t,u=e.headers.get("user-agent")||void 0,l=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||void 0,c={timestamp:Date.now(),endpoint:r,method:n,duration:a,status:i,userAgent:u,ip:l,error:o};s.push(c),s.length>1e3&&s.splice(0,s.length-1e3),a>5e3&&console.warn(`[PERFORMANCE] Slow request detected: ${n} ${r} took ${a}ms`),i>=400&&console.error(`[ERROR] ${n} ${r} returned ${i}${o?`: ${o}`:""}`)}}}function o(e,t){let r=e instanceof Error?e.message:e,s=e instanceof Error?e.stack:void 0,i={timestamp:Date.now(),error:r,stack:s,endpoint:t.endpoint||"unknown",method:t.method||"unknown",userAgent:t.request?.headers.get("user-agent")||void 0,ip:t.request?.headers.get("x-forwarded-for")||t.request?.headers.get("x-real-ip")||void 0,context:t.additionalContext};n.push(i),n.length>500&&n.splice(0,n.length-500),console.error(`[ERROR] ${r}`,{endpoint:i.endpoint,method:i.method,ip:i.ip,context:i.context})}function a(e=36e5){let t=Date.now()-e,r=s.filter(e=>e.timestamp>t);if(0===r.length)return{totalRequests:0,averageResponseTime:0,errorRate:0,slowRequests:0,endpointStats:{}};let n=r.length,i=r.reduce((e,t)=>e+t.duration,0)/n,o=r.filter(e=>e.status>=400).length,u=r.filter(e=>e.duration>5e3).length,l={};return r.forEach(e=>{l[e.endpoint]||(l[e.endpoint]={requests:0,averageTime:0,errors:0,slowRequests:0});let t=l[e.endpoint];t.requests++,t.averageTime=(t.averageTime*(t.requests-1)+e.duration)/t.requests,e.status>=400&&t.errors++,e.duration>5e3&&t.slowRequests++}),{totalRequests:n,averageResponseTime:Math.round(i),errorRate:Math.round(o/n*1e4)/100,slowRequests:u,endpointStats:l}}function u(e=50){return n.slice(-e).reverse().map(e=>({timestamp:new Date(e.timestamp).toISOString(),error:e.error,endpoint:e.endpoint,method:e.method,ip:e.ip,context:e.context}))}function l(){let e=a(3e5),t=u(10),r="healthy",s=[];return e.errorRate>10?(r="unhealthy",s.push(`High error rate: ${e.errorRate}%`)):e.errorRate>5&&(r="degraded",s.push(`Elevated error rate: ${e.errorRate}%`)),e.averageResponseTime>1e4?(r="unhealthy",s.push(`Very slow response time: ${e.averageResponseTime}ms`)):e.averageResponseTime>5e3&&("healthy"===r&&(r="degraded"),s.push(`Slow response time: ${e.averageResponseTime}ms`)),e.slowRequests>.1*e.totalRequests&&("healthy"===r&&(r="degraded"),s.push(`High number of slow requests: ${e.slowRequests}`)),{status:r,timestamp:new Date().toISOString(),uptime:process.uptime(),issues:s,stats:{requests:e.totalRequests,averageResponseTime:e.averageResponseTime,errorRate:e.errorRate,slowRequests:e.slowRequests},recentErrors:t.slice(0,3)}}async function c(){try{let e=Date.now(),{sql:t}=await Promise.all([r.e(3376),r.e(7990)]).then(r.bind(r,83376));await t`SELECT 1 as health_check`;let s=Date.now()-e;return{healthy:!0,latency:s}}catch(e){return{healthy:!1,error:e instanceof Error?e.message:"Unknown database error"}}}function p(){let e=process.memoryUsage();return{rss:Math.round(e.rss/1024/1024),heapTotal:Math.round(e.heapTotal/1024/1024),heapUsed:Math.round(e.heapUsed/1024/1024),external:Math.round(e.external/1024/1024),arrayBuffers:Math.round(e.arrayBuffers/1024/1024)}}setInterval(function(){let e=Date.now()-36e5,t=s.filter(t=>t.timestamp>e);s.splice(0,s.length,...t);let r=n.filter(t=>t.timestamp>e);n.splice(0,n.length,...r)},6e5)},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")},91645:e=>{e.exports=require("net")},92833:(e,t,r)=>{r.d(t,{B9:()=>u,BH:()=>l,Eb:()=>i,Tf:()=>o,Ww:()=>c,getSecurityHeaders:()=>p,h4:()=>d,o2:()=>a});let s={auth:{maxAttempts:5,windowMs:9e5},api:{maxRequests:100,windowMs:6e4},contact:{maxSubmissions:3,windowMs:36e5},review:{maxSubmissions:2,windowMs:36e5}},n=new Map;function i(e,t){let r=s[t],i=Date.now(),o=`${t}:${e}`,a=n.get(o);if(!a||i>a.resetTime){let e={count:1,resetTime:i+r.windowMs};return n.set(o,e),{allowed:!0,remaining:r.maxAttempts-1,resetTime:e.resetTime}}return a.count>=r.maxAttempts?{allowed:!1,remaining:0,resetTime:a.resetTime}:(a.count++,n.set(o,a),{allowed:!0,remaining:r.maxAttempts-a.count,resetTime:a.resetTime})}function o(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip"),s=e.headers.get("remote-addr");return t?t.split(",")[0].trim():r||s||"unknown"}function a(e){return"string"!=typeof e?"":e.trim().replace(/[<>]/g,"").replace(/javascript:/gi,"").replace(/on\w+=/gi,"").substring(0,1e3)}function u(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)&&e.length<=254}function l(e){return/^[\+]?[1-9][\d]{0,15}$/.test(e.replace(/[\s\-\(\)]/g,""))}function c(e){return[/script/gi,/javascript/gi,/vbscript/gi,/onload/gi,/onerror/gi,/onclick/gi,/<iframe/gi,/<object/gi,/<embed/gi,/eval\(/gi,/document\.cookie/gi,/window\.location/gi].some(t=>t.test(e))}function p(){return{"X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY","X-XSS-Protection":"1; mode=block","Referrer-Policy":"strict-origin-when-cross-origin","Content-Security-Policy":"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"}}function d(e,t,r){let s=new Date().toISOString();console.warn(`[SECURITY] ${s} - ${e}`,{ip:o(r),userAgent:r.headers.get("user-agent")||"unknown",details:t,url:r.url})}setInterval(function(){let e=Date.now();for(let[t,r]of n.entries())e>r.resetTime&&n.delete(t)},3e5)},94735:e=>{e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7696,6967,5887],()=>r(1741));module.exports=s})();