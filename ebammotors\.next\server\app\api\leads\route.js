(()=>{var e={};e.id=299,e.ids=[299],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23870:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var n=s(55511);let r={randomUUID:n.randomUUID},a=new Uint8Array(256),i=a.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let u=function(e,t,s){if(r.randomUUID&&!t&&!e)return r.randomUUID();let u=(e=e||{}).random??e.rng?.()??(i>a.length-16&&((0,n.randomFillSync)(a),i=0),a.slice(i,i+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((s=s||0)<0||s+16>t.length)throw RangeError(`UUID byte range ${s}:${s+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[s+e]=u[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(u)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53190:(e,t,s)=>{"use strict";s.d(t,{Gk:()=>P,Gq:()=>$,HR:()=>O,Kt:()=>v,Q6:()=>A,Rf:()=>L,XL:()=>K,Y2:()=>q,_Y:()=>Y,aN:()=>Z,createCustomerActivity:()=>V,createInteraction:()=>_,dD:()=>G,fE:()=>C,getAllFollowUps:()=>z,getCustomerByEmail:()=>E,getCustomerById:()=>b,getLeadById:()=>D,getPendingFollowUps:()=>H,oP:()=>ee,qz:()=>J,sr:()=>R,tR:()=>N,tS:()=>j,updateFollowUp:()=>W});var n=s(29021),r=s(33873),a=s.n(r),i=s(23870);let o=a().join(process.cwd(),"data"),u=a().join(o,"leads.json"),c=a().join(o,"customers.json"),d=a().join(o,"interactions.json"),l=a().join(o,"followups.json"),p=a().join(o,"activities.json"),w=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,f=[],m=[],g=[],y=[],h=[];async function S(){if(!w)try{await n.promises.access(o)}catch{await n.promises.mkdir(o,{recursive:!0})}}async function I(){if(w)return f;try{await S();let e=await n.promises.readFile(u,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function x(e){if(w){f=e;return}await S(),await n.promises.writeFile(u,JSON.stringify(e,null,2))}async function N(e){let t=await I(),s={...e,id:(0,i.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(s),await x(t),s}async function j(){return await I()}async function D(e){return(await I()).find(t=>t.id===e)||null}async function A(e,t){let s=await I(),n=s.findIndex(t=>t.id===e);return -1!==n&&(s[n]={...s[n],...t,updatedAt:new Date().toISOString()},await x(s),!0)}async function R(e){let t=await I(),s=t.filter(t=>t.id!==e);return s.length!==t.length&&(await x(s),!0)}async function O(e){return(await I()).filter(t=>t.status===e)}async function v(e){return(await I()).filter(t=>t.source===e)}async function U(){if(w)return m;try{await S();let e=await n.promises.readFile(c,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function F(e){if(w){m=e;return}await S(),await n.promises.writeFile(c,JSON.stringify(e,null,2))}async function q(e){let t=await U(),s=t.findIndex(t=>t.personalInfo.email===e.personalInfo.email);if(-1!==s)return t[s]={...t[s],...e,updatedAt:new Date().toISOString()},await F(t),t[s];{let s={...e,id:(0,i.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(s),await F(t),s}}async function L(){return await U()}async function b(e){return(await U()).find(t=>t.id===e)||null}async function E(e){return(await U()).find(t=>t.personalInfo.email===e)||null}async function P(e,t){let s=await U(),n=s.findIndex(t=>t.id===e);return -1!==n&&(s[n]={...s[n],...t,updatedAt:new Date().toISOString()},await F(s),!0)}async function k(){if(w)return g;try{await S();let e=await n.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function T(e){if(w){g=e;return}await S(),await n.promises.writeFile(d,JSON.stringify(e,null,2))}async function _(e){let t=await k(),s={...e,id:(0,i.A)(),createdAt:new Date().toISOString()};return t.push(s),await T(t),s}async function C(){return await k()}async function J(e){return(await k()).filter(t=>t.customerId===e)}async function $(e){return(await k()).filter(t=>t.leadId===e)}async function B(){if(w)return y;try{await S();let e=await n.promises.readFile(l,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function M(e){if(w){y=e;return}await S(),await n.promises.writeFile(l,JSON.stringify(e,null,2))}async function K(e){let t=await B(),s={...e,id:(0,i.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(s),await M(t),s}async function z(){return await B()}async function G(e){return(await B()).filter(t=>t.status===e)}async function H(){let e=await B(),t=new Date().toISOString();return e.filter(e=>"pending"===e.status&&e.scheduledDate<=t)}async function W(e,t){let s=await B(),n=s.findIndex(t=>t.id===e);return -1!==n&&(s[n]={...s[n],...t,updatedAt:new Date().toISOString()},await M(s),!0)}async function Y(e){return(await B()).filter(t=>t.customerId===e)}async function Q(){if(w)return h;try{await S();let e=await n.promises.readFile(p,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function X(e){if(w){h=e;return}await S(),await n.promises.writeFile(p,JSON.stringify(e,null,2))}async function V(e){let t=await Q(),s={...e,id:(0,i.A)(),timestamp:new Date().toISOString()};return t.push(s),await X(t),s}async function Z(e){return(await Q()).filter(t=>t.customerId===e)}async function ee(e){let t=await b(e);if(!t)return null;let s=await J(e),n=await Y(e),r=await Z(e);return{customer:t,stats:{totalInteractions:s.length,pendingFollowUps:n.filter(e=>"pending"===e.status).length,recentActivities:r.filter(e=>new Date(e.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:s.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())[0]?.createdAt},recentInteractions:s.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()).slice(0,5),upcomingFollowUps:n.filter(e=>"pending"===e.status).sort((e,t)=>new Date(e.scheduledDate).getTime()-new Date(t.scheduledDate).getTime()).slice(0,3)}}},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},58510:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>S,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var n={};s.r(n),s.d(n,{DELETE:()=>f,GET:()=>l,PATCH:()=>w,POST:()=>p});var r=s(96559),a=s(48088),i=s(37719),o=s(32190),u=s(53190),c=s(16967);async function d(e){let t={type:"new_review",title:"New Lead Generated",message:`New lead generated from ${e.source}. Customer: ${e.customerName} (${e.customerEmail}). Priority: ${e.priority}. Lead ID: ${e.leadId}`,data:e,timestamp:new Date().toISOString()};try{await c.gm.sendAdminNotification(t)}catch(e){console.error("Failed to send admin new lead notification:",e)}}async function l(e){try{let{searchParams:t}=new URL(e.url),s=t.get("adminKey"),n=t.get("id"),r=t.get("status"),a=t.get("source"),i=process.env.ADMIN_PASSWORD||"admin123";if(s!==i)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(n){let e=await (0,u.getLeadById)(n);if(!e)return o.NextResponse.json({success:!1,message:"Lead not found"},{status:404});return o.NextResponse.json({success:!0,lead:e})}if(r){let e=await (0,u.HR)(r);return o.NextResponse.json({success:!0,leads:e})}if(a){let e=await (0,u.Kt)(a);return o.NextResponse.json({success:!0,leads:e})}let c=await (0,u.tS)();return o.NextResponse.json({success:!0,leads:c})}catch(e){return console.error("Error fetching leads:",e),o.NextResponse.json({success:!1,message:"Failed to fetch leads"},{status:500})}}async function p(e){try{let t=await e.json();if(!t.customerInfo?.name||!t.inquiry?.message)return o.NextResponse.json({success:!1,message:"Name and message are required"},{status:400});let n={source:"manual",status:"new",priority:"medium",tags:[],...t},r=await (0,u.tR)(n),{createInteraction:a}=await Promise.resolve().then(s.bind(s,53190));await a({leadId:r.id,type:"chat",direction:"inbound",channel:"chatbot"===t.source?"website":"manual",content:t.inquiry.message,subject:t.inquiry.subject||"New Lead Inquiry",tags:["lead_creation"],createdBy:"system"});let{scheduleAutoFollowupForLead:i}=await s.e(4654).then(s.bind(s,62273));await i(r.id,n);try{await d({leadId:r.id,customerName:r.customerInfo.name,customerEmail:r.customerInfo.email||"No email provided",source:r.source,priority:r.priority,inquiry:r.inquiry.message})}catch(e){console.error("Failed to send admin lead notification:",e)}return o.NextResponse.json({success:!0,message:"Lead created successfully",lead:r})}catch(e){return console.error("Error creating lead:",e),o.NextResponse.json({success:!1,message:"Failed to create lead"},{status:500})}}async function w(e){try{let{leadId:t,adminKey:n,...r}=await e.json(),a=process.env.ADMIN_PASSWORD||"admin123";if(n!==a)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!t)return o.NextResponse.json({success:!1,message:"Lead ID is required"},{status:400});if(!await (0,u.Q6)(t,r))return o.NextResponse.json({success:!1,message:"Lead not found"},{status:404});let{createInteraction:i}=await Promise.resolve().then(s.bind(s,53190));return await i({leadId:t,type:"support",direction:"outbound",channel:"website",content:`Lead updated: ${Object.keys(r).join(", ")}`,subject:"Lead Status Update",tags:["lead_update","admin_action"],createdBy:"admin"}),o.NextResponse.json({success:!0,message:"Lead updated successfully"})}catch(e){return console.error("Error updating lead:",e),o.NextResponse.json({success:!1,message:"Failed to update lead"},{status:500})}}async function f(e){try{let{searchParams:t}=new URL(e.url),s=t.get("id"),n=t.get("adminKey"),r=process.env.ADMIN_PASSWORD||"admin123";if(n!==r)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!s)return o.NextResponse.json({success:!1,message:"Lead ID is required"},{status:400});if(!await (0,u.sr)(s))return o.NextResponse.json({success:!1,message:"Lead not found"},{status:404});return o.NextResponse.json({success:!0,message:"Lead deleted successfully"})}catch(e){return console.error("Error deleting lead:",e),o.NextResponse.json({success:!1,message:"Failed to delete lead"},{status:500})}}let m=new r.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/leads/route",pathname:"/api/leads",filename:"route",bundlePath:"app/api/leads/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\leads\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:h}=m;function S(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[4447,580,6967],()=>s(58510));module.exports=n})();