(()=>{var t={};t.id=1319,t.ids=[1319,7990],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(t,e,r)=>{"use strict";r.d(e,{Qq:()=>E,Tq:()=>m,bS:()=>l,fF:()=>p,mU:()=>c});var s=r(85663),a=r(43205),i=r.n(a);let n=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(t,e){try{return await s.Ay.compare(t,e)}catch(t){return console.error("Error verifying password:",t),!1}}function c(t){return o.delete(t)}async function l(t){try{let e=function(){let t=process.env.ADMIN_PASSWORD||"admin123";return t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$"),t}(),r=!1;if(!(e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$")?await u(t,e):t===e))return{success:!1,message:"Invalid credentials"};{let t=function(t="admin"){try{let e={id:t,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(e,n,{expiresIn:"24h"})}catch(t){throw console.error("Error generating token:",t),Error("Failed to generate authentication token")}}(),e=function(t="admin"){let e=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(e,{id:t,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let t=Date.now();for(let[e,r]of o.entries())t>r.expiresAt&&o.delete(e)}(),e}();return{success:!0,token:t,sessionId:e,message:"Authentication successful"}}}catch(t){return console.error("Authentication error:",t),{success:!1,message:"Authentication failed"}}}function p(t,e){if(t&&t.startsWith("Bearer ")){let e=function(t){try{let e=i().verify(t,n);if(e.isAdmin)return{id:e.id,isAdmin:e.isAdmin};return null}catch(t){return null}}(t.substring(7));if(e)return{isValid:!0,adminId:e.id,message:"Token authentication successful"}}if(e){let t=function(t){let e=o.get(t);if(!e)return null;let r=Date.now();return r>e.expiresAt?(o.delete(t),null):(e.lastActivity=r,o.set(t,e),e)}(e);if(t)return{isValid:!0,adminId:t.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let d=new Map;function E(t){let e=Date.now(),r=d.get(t);return!r||e-r.lastAttempt>9e5?(d.set(t,{count:1,lastAttempt:e}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(e-r.lastAttempt)}:(r.count++,r.lastAttempt=e,d.set(t,r),{allowed:!0,remainingAttempts:5-r.count})}function m(t){d.delete(t)}},21820:t=>{"use strict";t.exports=require("os")},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32696:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>l});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(77268),c=r(83376);async function l(t){try{if(!(0,u.iY)(t).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let e=await p();return o.NextResponse.json(e)}catch(t){return console.error("Error fetching car stats:",t),o.NextResponse.json({success:!1,message:"Failed to fetch car statistics"},{status:500})}}async function p(){try{let t=await (0,c.sql)`SELECT COUNT(*) as count FROM cars`,e=parseInt(t.rows[0]?.count||"0"),r=await (0,c.sql)`SELECT COUNT(*) as count FROM cars WHERE LOWER(status) = 'available'`,s=parseInt(r.rows[0]?.count||"0"),a=await (0,c.sql)`SELECT COUNT(*) as count FROM cars WHERE LOWER(status) = 'sold'`,i=parseInt(a.rows[0]?.count||"0"),n=await (0,c.sql)`SELECT COUNT(*) as count FROM cars WHERE is_featured = true`,o=parseInt(n.rows[0]?.count||"0"),u=await (0,c.sql)`SELECT COUNT(*) as count FROM cars WHERE stock_quantity <= 1 AND LOWER(status) = 'available'`,l=parseInt(u.rows[0]?.count||"0"),p=await (0,c.sql)`
      SELECT make, COUNT(*) as count 
      FROM cars 
      GROUP BY make 
      ORDER BY count DESC 
      LIMIT 5
    `,d=await (0,c.sql)`
      SELECT status, COUNT(*) as count 
      FROM cars 
      GROUP BY status 
      ORDER BY count DESC
    `,E=await (0,c.sql)`
      SELECT COUNT(*) as count 
      FROM cars 
      WHERE created_at >= NOW() - INTERVAL '30 days'
    `,m=parseInt(E.rows[0]?.count||"0"),f=await (0,c.sql)`
      SELECT AVG(price) as avg_price 
      FROM cars 
      WHERE LOWER(status) = 'available'
    `,w=parseFloat(f.rows[0]?.avg_price||"0"),g=await (0,c.sql)`
      SELECT MIN(price) as min_price, MAX(price) as max_price 
      FROM cars 
      WHERE LOWER(status) = 'available'
    `,R=parseFloat(g.rows[0]?.min_price||"0"),h=parseFloat(g.rows[0]?.max_price||"0"),y=await (0,c.sql)`
      SELECT 
        CASE 
          WHEN year >= 2020 THEN '2020+'
          WHEN year >= 2015 THEN '2015-2019'
          WHEN year >= 2010 THEN '2010-2014'
          WHEN year >= 2005 THEN '2005-2009'
          ELSE 'Before 2005'
        END as year_range,
        COUNT(*) as count
      FROM cars
      GROUP BY year_range
      ORDER BY 
        CASE 
          WHEN year >= 2020 THEN 1
          WHEN year >= 2015 THEN 2
          WHEN year >= 2010 THEN 3
          WHEN year >= 2005 THEN 4
          ELSE 5
        END
    `;return{total:e,available:s,sold:i,featured:o,lowStock:l,recentAdditions:m,averagePrice:Math.round(w),priceRange:{min:R,max:h},makeStats:p.rows.map(t=>({make:t.make,count:parseInt(t.count)})),statusStats:d.rows.map(t=>({status:t.status,count:parseInt(t.count)})),yearStats:y.rows.map(t=>({yearRange:t.year_range,count:parseInt(t.count)}))}}catch(t){return console.error("Error calculating car stats:",t),{total:0,available:0,sold:0,featured:0,lowStock:0,recentAdditions:0,averagePrice:0,priceRange:{min:0,max:0},makeStats:[],statusStats:[],yearStats:[]}}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/cars/stats/route",pathname:"/api/admin/cars/stats",filename:"route",bundlePath:"app/api/admin/cars/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\cars\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:m,serverHooks:f}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:m})}},33873:t=>{"use strict";t.exports=require("path")},34631:t=>{"use strict";t.exports=require("tls")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:t=>{"use strict";t.exports=require("zlib")},77268:(t,e,r)=>{"use strict";r.d(e,{iY:()=>a}),r(32190);var s=r(12909);function a(t,e){let r=t.headers.get("authorization"),a=t.cookies.get("admin_session")?.value,i=(0,s.fF)(r,a);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let n=e?.adminKey||t.nextUrl.searchParams.get("adminKey");return n&&n===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:t=>{"use strict";t.exports=require("buffer")},79551:t=>{"use strict";t.exports=require("url")},81630:t=>{"use strict";t.exports=require("http")},91645:t=>{"use strict";t.exports=require("net")},94735:t=>{"use strict";t.exports=require("events")},96487:()=>{}};var e=require("../../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),s=e.X(0,[4447,580,7696,3376],()=>r(32696));module.exports=s})();