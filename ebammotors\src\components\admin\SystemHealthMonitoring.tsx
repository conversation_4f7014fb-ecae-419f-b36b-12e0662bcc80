'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Database, 
  Zap,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Server,
  Wifi,
  HardDrive
} from 'lucide-react';

interface HealthData {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  issues: string[];
  stats: {
    requests: number;
    averageResponseTime: number;
    errorRate: number;
    slowRequests: number;
  };
  database?: {
    healthy: boolean;
    latency?: number;
    error?: string;
  };
  memory?: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers?: number;
  };
  recentErrors?: any[];
  performance?: {
    endpointStats: Record<string, {
      requests: number;
      averageTime: number;
      errors: number;
      slowRequests: number;
    }>;
  };
}

export default function SystemHealthMonitoring() {
  const [healthData, setHealthData] = useState<HealthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchHealthData();
    
    if (autoRefresh) {
      intervalRef.current = setInterval(fetchHealthData, refreshInterval * 1000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, refreshInterval]);

  const fetchHealthData = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/admin/health', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setHealthData(data);
      }
    } catch (error) {
      console.error('Failed to fetch health data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshToggle = () => {
    setAutoRefresh(!autoRefresh);
    if (!autoRefresh) {
      fetchHealthData();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'unhealthy': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5" />;
      case 'degraded': return <Clock className="w-5 h-5" />;
      case 'unhealthy': return <AlertTriangle className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!healthData) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Health Data Unavailable</h3>
          <p className="text-gray-600 mb-4">Unable to fetch system health information.</p>
          <button
            onClick={fetchHealthData}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${getStatusColor(healthData.status)}`}>
              {getStatusIcon(healthData.status)}
            </div>
            <div className="ml-4">
              <h2 className="text-xl font-semibold text-gray-900 capitalize">
                System {healthData.status}
              </h2>
              <p className="text-sm text-gray-600">
                Last updated: {new Date(healthData.timestamp).toLocaleTimeString()}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <label className="text-sm text-gray-600 mr-2">Auto-refresh:</label>
              <button
                onClick={handleRefreshToggle}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoRefresh ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    autoRefresh ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            
            <button
              onClick={fetchHealthData}
              className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Refresh
            </button>
          </div>
        </div>

        {/* System Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <Server className="w-5 h-5 text-gray-400 mr-2" />
            <div>
              <p className="text-sm text-gray-600">Uptime</p>
              <p className="font-semibold">{formatUptime(healthData.uptime)}</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <Wifi className="w-5 h-5 text-gray-400 mr-2" />
            <div>
              <p className="text-sm text-gray-600">Requests (5m)</p>
              <p className="font-semibold">{healthData.stats.requests}</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <Zap className="w-5 h-5 text-gray-400 mr-2" />
            <div>
              <p className="text-sm text-gray-600">Avg Response</p>
              <p className="font-semibold">{healthData.stats.averageResponseTime}ms</p>
            </div>
          </div>
        </div>

        {/* Issues */}
        {healthData.issues.length > 0 && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">Active Issues</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              {healthData.issues.map((issue, index) => (
                <li key={index} className="flex items-center">
                  <AlertTriangle className="w-4 h-4 mr-2 flex-shrink-0" />
                  {issue}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Error Rate</span>
              <div className="flex items-center">
                {healthData.stats.errorRate > 5 ? (
                  <TrendingUp className="w-4 h-4 text-red-500 mr-1" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-green-500 mr-1" />
                )}
                <span className={`font-semibold ${
                  healthData.stats.errorRate > 5 ? 'text-red-600' : 'text-green-600'
                }`}>
                  {healthData.stats.errorRate}%
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Slow Requests</span>
              <span className="font-semibold text-gray-900">
                {healthData.stats.slowRequests}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Requests</span>
              <span className="font-semibold text-gray-900">
                {healthData.stats.requests}
              </span>
            </div>
          </div>
        </div>

        {/* Database Health */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Database Health</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Status</span>
              <div className="flex items-center">
                {healthData.database?.healthy ? (
                  <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
                ) : (
                  <AlertTriangle className="w-4 h-4 text-red-500 mr-1" />
                )}
                <span className={`font-semibold ${
                  healthData.database?.healthy ? 'text-green-600' : 'text-red-600'
                }`}>
                  {healthData.database?.healthy ? 'Healthy' : 'Error'}
                </span>
              </div>
            </div>
            
            {healthData.database?.latency && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Latency</span>
                <span className="font-semibold text-gray-900">
                  {healthData.database.latency}ms
                </span>
              </div>
            )}
            
            {healthData.database?.error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <p className="text-sm text-red-700">{healthData.database.error}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Memory Usage */}
      {healthData.memory && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Memory Usage</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <Server className="w-6 h-6 text-blue-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600">RSS</p>
              <p className="font-semibold text-gray-900">{healthData.memory.rss} MB</p>
            </div>
            
            <div className="text-center">
              <HardDrive className="w-6 h-6 text-green-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Heap Total</p>
              <p className="font-semibold text-gray-900">{healthData.memory.heapTotal} MB</p>
            </div>
            
            <div className="text-center">
              <Database className="w-6 h-6 text-purple-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Heap Used</p>
              <p className="font-semibold text-gray-900">{healthData.memory.heapUsed} MB</p>
            </div>
            
            <div className="text-center">
              <Activity className="w-6 h-6 text-orange-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600">External</p>
              <p className="font-semibold text-gray-900">{healthData.memory.external} MB</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
