'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, Filter, X, ChevronDown, Bookmark, Star } from 'lucide-react';
import { 
  SearchFilters, 
  SortOption, 
  SearchSuggestion,
  SORT_OPTIONS, 
  FILTER_OPTIONS, 
  DEFAULT_FILTERS,
  getSearchSuggestions,
  saveSearch,
  getSavedSearches,
  trackSearch
} from '@/lib/advancedSearch';

interface AdvancedSearchProps {
  onSearch: (query: string, filters: Partial<SearchFilters>, sortBy: SortOption) => void;
  initialQuery?: string;
  initialFilters?: Partial<SearchFilters>;
  initialSort?: SortOption;
}

export default function AdvancedSearch({ 
  onSearch, 
  initialQuery = '', 
  initialFilters = {}, 
  initialSort = SORT_OPTIONS[0] 
}: AdvancedSearchProps) {
  const [query, setQuery] = useState(initialQuery);
  const [filters, setFilters] = useState<Partial<SearchFilters>>(initialFilters);
  const [sortBy, setSortBy] = useState<SortOption>(initialSort);
  const [showFilters, setShowFilters] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [savedSearches, setSavedSearches] = useState<any[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveSearchName, setSaveSearchName] = useState('');
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadSavedSearches();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(async () => {
      if (query.length > 2) {
        try {
          const searchSuggestions = await getSearchSuggestions(query);
          setSuggestions(searchSuggestions);
          setShowSuggestions(true);
        } catch (error) {
          // Error getting suggestions
        }
      } else {
        setShowSuggestions(false);
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [query]);

  const loadSavedSearches = async () => {
    try {
      const searches = await getSavedSearches();
      setSavedSearches(searches);
    } catch (error) {
      // Error loading saved searches
    }
  };

  const handleSearch = () => {
    onSearch(query, filters, sortBy);
    setShowSuggestions(false);
    
    // Track search for analytics
    trackSearch(query, filters, 0);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    setShowSuggestions(false);
    setTimeout(() => handleSearch(), 100);
  };

  const handleSaveSearch = async () => {
    if (!saveSearchName.trim()) return;
    
    try {
      await saveSearch(saveSearchName, query, filters, sortBy);
      setSaveSearchName('');
      setShowSaveDialog(false);
      loadSavedSearches();
    } catch (error) {
      // Error saving search
    }
  };

  const clearFilters = () => {
    setFilters({});
  };

  const getActiveFilterCount = () => {
    return Object.values(filters).filter(value => {
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === 'object' && value !== null) {
        const obj = value as any;
        return obj.min !== DEFAULT_FILTERS[Object.keys(filters).find(k => filters[k as keyof SearchFilters] === value) as keyof SearchFilters]?.min ||
               obj.max !== DEFAULT_FILTERS[Object.keys(filters).find(k => filters[k as keyof SearchFilters] === value) as keyof SearchFilters]?.max;
      }
      return value !== undefined && value !== null;
    }).length;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Search Input */}
      <div className="relative mb-4">
        <div className="flex space-x-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              ref={searchInputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="Search for cars, brands, models..."
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            
            {/* Search Suggestions */}
            {showSuggestions && suggestions.length > 0 && (
              <div
                ref={suggestionsRef}
                className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-60 overflow-y-auto"
              >
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center justify-between"
                  >
                    <span>{suggestion.text}</span>
                    <span className="text-xs text-gray-500">{suggestion.count}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`px-4 py-3 border rounded-lg flex items-center space-x-2 ${
              showFilters ? 'bg-blue-50 border-blue-300 text-blue-700' : 'border-gray-300 text-gray-700'
            }`}
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            {getActiveFilterCount() > 0 && (
              <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {getActiveFilterCount()}
              </span>
            )}
          </button>
          
          <button
            onClick={handleSearch}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
          >
            Search
          </button>
        </div>
      </div>

      {/* Sort Options */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Sort by:</label>
          <select
            value={`${sortBy.field}_${sortBy.direction}`}
            onChange={(e) => {
              const [field, direction] = e.target.value.split('_');
              const option = SORT_OPTIONS.find(opt => opt.field === field && opt.direction === direction);
              if (option) setSortBy(option);
            }}
            className="border border-gray-300 rounded px-3 py-1 text-sm"
          >
            {SORT_OPTIONS.map((option) => (
              <option key={`${option.field}_${option.direction}`} value={`${option.field}_${option.direction}`}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowSaveDialog(true)}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1"
          >
            <Bookmark className="w-4 h-4" />
            <span>Save Search</span>
          </button>
        </div>
      </div>

      {/* Saved Searches */}
      {savedSearches.length > 0 && (
        <div className="mb-4">
          <label className="text-sm font-medium text-gray-700 mb-2 block">Saved Searches:</label>
          <div className="flex flex-wrap gap-2">
            {savedSearches.slice(0, 5).map((search) => (
              <button
                key={search.id}
                onClick={() => {
                  setQuery(search.query);
                  setFilters(search.filters);
                  setSortBy(search.sortBy);
                }}
                className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded flex items-center space-x-1"
              >
                <Star className="w-3 h-3" />
                <span>{search.name}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Advanced Filters */}
      {showFilters && (
        <div className="border-t pt-4 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-900">Advanced Filters</h3>
            <button
              onClick={clearFilters}
              className="text-sm text-red-600 hover:text-red-800"
            >
              Clear All
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price Range (¥)</label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={filters.priceRange?.min || ''}
                  onChange={(e) => handleFilterChange('priceRange', {
                    ...filters.priceRange,
                    min: parseInt(e.target.value) || 0
                  })}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={filters.priceRange?.max || ''}
                  onChange={(e) => handleFilterChange('priceRange', {
                    ...filters.priceRange,
                    max: parseInt(e.target.value) || 10000000
                  })}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                />
              </div>
            </div>

            {/* Year Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Year Range</label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  placeholder="From"
                  value={filters.yearRange?.min || ''}
                  onChange={(e) => handleFilterChange('yearRange', {
                    ...filters.yearRange,
                    min: parseInt(e.target.value) || 1990
                  })}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                />
                <input
                  type="number"
                  placeholder="To"
                  value={filters.yearRange?.max || ''}
                  onChange={(e) => handleFilterChange('yearRange', {
                    ...filters.yearRange,
                    max: parseInt(e.target.value) || new Date().getFullYear()
                  })}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                />
              </div>
            </div>

            {/* Brands */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Brands</label>
              <select
                multiple
                value={filters.brands || []}
                onChange={(e) => handleFilterChange('brands', Array.from(e.target.selectedOptions, option => option.value))}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm h-20"
              >
                {FILTER_OPTIONS.brands.map((brand) => (
                  <option key={brand} value={brand}>{brand}</option>
                ))}
              </select>
            </div>

            {/* Fuel Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Fuel Type</label>
              <select
                value={filters.fuelTypes?.[0] || ''}
                onChange={(e) => handleFilterChange('fuelTypes', e.target.value ? [e.target.value] : [])}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              >
                <option value="">Any</option>
                {FILTER_OPTIONS.fuelTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Transmission */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Transmission</label>
              <select
                value={filters.transmissions?.[0] || ''}
                onChange={(e) => handleFilterChange('transmissions', e.target.value ? [e.target.value] : [])}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              >
                <option value="">Any</option>
                {FILTER_OPTIONS.transmissions.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Condition */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Condition</label>
              <select
                value={filters.conditions?.[0] || ''}
                onChange={(e) => handleFilterChange('conditions', e.target.value ? [e.target.value] : [])}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              >
                <option value="">Any</option>
                {FILTER_OPTIONS.conditions.map((condition) => (
                  <option key={condition} value={condition}>{condition}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Save Search Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Save Search</h3>
            <input
              type="text"
              value={saveSearchName}
              onChange={(e) => setSaveSearchName(e.target.value)}
              placeholder="Enter search name..."
              className="w-full border border-gray-300 rounded px-3 py-2 mb-4"
            />
            <div className="flex space-x-3">
              <button
                onClick={handleSaveSearch}
                className="flex-1 bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
              >
                Save
              </button>
              <button
                onClick={() => setShowSaveDialog(false)}
                className="flex-1 border border-gray-300 py-2 rounded hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
