#!/usr/bin/env node

// Setup script for EBAM Motors Advanced Features
// This script helps you configure the advanced features quickly

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 EBAM Motors Advanced Features Setup');
console.log('=====================================\n');

// Check if .env.local exists
const envPath = path.join(__dirname, '.env.local');
const envExists = fs.existsSync(envPath);

if (!envExists) {
  console.log('📝 Creating .env.local file...');
  
  const envTemplate = `# EBAM Motors Advanced Features Configuration
# Copy this file to .env.local and update the values

# Google Analytics (Optional - for analytics tracking)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Push Notifications (Required for push notifications)
# Generate these using: npx web-push generate-vapid-keys
VAPID_PUBLIC_KEY=your-vapid-public-key-here
VAPID_PRIVATE_KEY=your-vapid-private-key-here
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your-vapid-public-key-here

# Admin Authentication (Required for admin features)
ADMIN_PASSWORD=admin123

# Social Media (Optional - for social login)
NEXT_PUBLIC_SOCIAL_CLIENT_IDS={"facebook":"your-fb-client-id","linkedin":"your-linkedin-client-id"}

# Database (Optional - if using database features)
POSTGRES_URL=your-postgres-connection-string

# Base URL (Required for production)
NEXT_PUBLIC_BASE_URL=https://your-domain.com
`;

  fs.writeFileSync(envPath, envTemplate);
  console.log('✅ Created .env.local template');
} else {
  console.log('✅ .env.local file already exists');
}

// Check if web-push is installed
try {
  require.resolve('web-push');
  console.log('✅ web-push package is installed');
} catch (error) {
  console.log('📦 Installing web-push package...');
  try {
    execSync('npm install web-push @types/web-push', { stdio: 'inherit' });
    console.log('✅ web-push package installed');
  } catch (installError) {
    console.log('❌ Failed to install web-push package');
    console.log('   Please run: npm install web-push @types/web-push');
  }
}

// Generate VAPID keys
console.log('\n🔑 Generating VAPID keys for push notifications...');
try {
  const webpush = require('web-push');
  const vapidKeys = webpush.generateVAPIDKeys();
  
  console.log('\n📋 VAPID Keys Generated:');
  console.log('========================');
  console.log('Public Key:', vapidKeys.publicKey);
  console.log('Private Key:', vapidKeys.privateKey);
  console.log('\n💡 Add these to your .env.local file:');
  console.log(`VAPID_PUBLIC_KEY=${vapidKeys.publicKey}`);
  console.log(`VAPID_PRIVATE_KEY=${vapidKeys.privateKey}`);
  console.log(`NEXT_PUBLIC_VAPID_PUBLIC_KEY=${vapidKeys.publicKey}`);
  
  // Update .env.local with generated keys
  let envContent = fs.readFileSync(envPath, 'utf8');
  envContent = envContent.replace('VAPID_PUBLIC_KEY=your-vapid-public-key-here', `VAPID_PUBLIC_KEY=${vapidKeys.publicKey}`);
  envContent = envContent.replace('VAPID_PRIVATE_KEY=your-vapid-private-key-here', `VAPID_PRIVATE_KEY=${vapidKeys.privateKey}`);
  envContent = envContent.replace('NEXT_PUBLIC_VAPID_PUBLIC_KEY=your-vapid-public-key-here', `NEXT_PUBLIC_VAPID_PUBLIC_KEY=${vapidKeys.publicKey}`);
  fs.writeFileSync(envPath, envContent);
  
  console.log('✅ Updated .env.local with VAPID keys');
} catch (error) {
  console.log('❌ Failed to generate VAPID keys');
  console.log('   Please run manually: npx web-push generate-vapid-keys');
}

// Check for PWA icons directory
const iconsDir = path.join(__dirname, 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  console.log('\n📁 Creating PWA icons directory...');
  fs.mkdirSync(iconsDir, { recursive: true });
  console.log('✅ Created public/icons directory');
  console.log('💡 Add PWA icons (72x72, 96x96, 128x128, 144x144, 152x152, 192x192, 384x384, 512x512) to this directory');
} else {
  console.log('✅ PWA icons directory exists');
}

// Check for data directory
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  console.log('\n📁 Creating data directory...');
  fs.mkdirSync(dataDir, { recursive: true });
  console.log('✅ Created data directory for storing app data');
} else {
  console.log('✅ Data directory exists');
}

console.log('\n🎉 Setup Complete!');
console.log('==================');
console.log('\n📋 Next Steps:');
console.log('1. Update .env.local with your actual values');
console.log('2. Add PWA icons to public/icons/ directory');
console.log('3. Configure Google Analytics (optional)');
console.log('4. Set up social media apps (optional)');
console.log('5. Start development server: npm run dev');
console.log('6. Visit http://localhost:3000/test-features to test all features');

console.log('\n🧪 Testing:');
console.log('- Visit http://localhost:3000/test-features for comprehensive testing');
console.log('- Check http://localhost:3000/manifest.json for PWA manifest');
console.log('- Open DevTools to monitor service worker and analytics');

console.log('\n📚 Documentation:');
console.log('- Read TESTING_GUIDE.md for detailed testing instructions');
console.log('- Read ADVANCED_FEATURES_IMPLEMENTATION_GUIDE.md for full documentation');

console.log('\n🔧 Troubleshooting:');
console.log('- Ensure all environment variables are set correctly');
console.log('- Use HTTPS for testing push notifications (try ngrok)');
console.log('- Check browser console for any error messages');

console.log('\n✨ Features Available:');
console.log('- ✅ Google Analytics 4 tracking');
console.log('- ✅ Progressive Web App (PWA)');
console.log('- ✅ Push Notifications');
console.log('- ✅ Advanced Search');
console.log('- ✅ Social Media Integration');
console.log('- ✅ Real-time Tracking');
console.log('- ✅ Community Features');
console.log('- ✅ Advanced Analytics');

console.log('\n🚀 Happy coding!');
