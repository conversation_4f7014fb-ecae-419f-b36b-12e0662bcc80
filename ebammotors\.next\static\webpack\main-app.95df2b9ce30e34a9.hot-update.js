"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-index.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/client/app-index.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// imports polyfill from `@next/polyfill-module` after build.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hydrate\", ({\n    enumerable: true,\n    get: function() {\n        return hydrate;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js\");\n__webpack_require__(/*! ./components/globals/patch-console */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/patch-console.js\");\n__webpack_require__(/*! ./components/globals/handle-global-errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/handle-global-errors.js\");\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/client.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _client1 = __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _errorboundarycallbacks = __webpack_require__(/*! ./react-client-callbacks/error-boundary-callbacks */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\");\nconst _appcallserver = __webpack_require__(/*! ./app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ./app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ./components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./components/app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\"));\nconst _createinitialrouterstate = __webpack_require__(/*! ./components/router-reducer/create-initial-router-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _appbuildid = __webpack_require__(/*! ./app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\n/// <reference types=\"react-dom/experimental\" />\nconst appElement = document;\nconst encoder = new TextEncoder();\nlet initialServerDataBuffer = undefined;\nlet initialServerDataWriter = undefined;\nlet initialServerDataLoaded = false;\nlet initialServerDataFlushed = false;\nlet initialFormStateData = null;\nfunction nextServerDataCallback(seg) {\n    if (seg[0] === 0) {\n        initialServerDataBuffer = [];\n    } else if (seg[0] === 1) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(encoder.encode(seg[1]));\n        } else {\n            initialServerDataBuffer.push(seg[1]);\n        }\n    } else if (seg[0] === 2) {\n        initialFormStateData = seg[1];\n    } else if (seg[0] === 3) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        // Decode the base64 string back to binary data.\n        const binaryString = atob(seg[1]);\n        const decodedChunk = new Uint8Array(binaryString.length);\n        for(var i = 0; i < binaryString.length; i++){\n            decodedChunk[i] = binaryString.charCodeAt(i);\n        }\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(decodedChunk);\n        } else {\n            initialServerDataBuffer.push(decodedChunk);\n        }\n    }\n}\nfunction isStreamErrorOrUnfinished(ctr) {\n    // If `desiredSize` is null, it means the stream is closed or errored. If it is lower than 0, the stream is still unfinished.\n    return ctr.desiredSize === null || ctr.desiredSize < 0;\n}\n// There might be race conditions between `nextServerDataRegisterWriter` and\n// `DOMContentLoaded`. The former will be called when React starts to hydrate\n// the root, the latter will be called when the DOM is fully loaded.\n// For streaming, the former is called first due to partial hydration.\n// For non-streaming, the latter can be called first.\n// Hence, we use two variables `initialServerDataLoaded` and\n// `initialServerDataFlushed` to make sure the writer will be closed and\n// `initialServerDataBuffer` will be cleared in the right time.\nfunction nextServerDataRegisterWriter(ctr) {\n    if (initialServerDataBuffer) {\n        initialServerDataBuffer.forEach((val)=>{\n            ctr.enqueue(typeof val === 'string' ? encoder.encode(val) : val);\n        });\n        if (initialServerDataLoaded && !initialServerDataFlushed) {\n            if (isStreamErrorOrUnfinished(ctr)) {\n                ctr.error(Object.defineProperty(new Error('The connection to the page was unexpectedly closed, possibly due to the stop button being clicked, loss of Wi-Fi, or an unstable internet connection.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E117\",\n                    enumerable: false,\n                    configurable: true\n                }));\n            } else {\n                ctr.close();\n            }\n            initialServerDataFlushed = true;\n            initialServerDataBuffer = undefined;\n        }\n    }\n    initialServerDataWriter = ctr;\n}\n// When `DOMContentLoaded`, we can close all pending writers to finish hydration.\nconst DOMContentLoaded = function() {\n    if (initialServerDataWriter && !initialServerDataFlushed) {\n        initialServerDataWriter.close();\n        initialServerDataFlushed = true;\n        initialServerDataBuffer = undefined;\n    }\n    initialServerDataLoaded = true;\n};\n_c = DOMContentLoaded;\n// It's possible that the DOM is already loaded.\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', DOMContentLoaded, false);\n} else {\n    // Delayed in marco task to ensure it's executed later than hydration\n    setTimeout(DOMContentLoaded);\n}\nconst nextServerDataLoadingGlobal = self.__next_f = self.__next_f || [];\nnextServerDataLoadingGlobal.forEach(nextServerDataCallback);\nnextServerDataLoadingGlobal.push = nextServerDataCallback;\nconst readable = new ReadableStream({\n    start (controller) {\n        nextServerDataRegisterWriter(controller);\n    }\n});\nconst initialServerResponse = (0, _client1.createFromReadableStream)(readable, {\n    callServer: _appcallserver.callServer,\n    findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n});\nfunction ServerRoot(param) {\n    let { pendingActionQueue } = param;\n    const initialRSCPayload = (0, _react.use)(initialServerResponse);\n    const actionQueue = (0, _react.use)(pendingActionQueue);\n    const router = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuter.default, {\n        actionQueue: actionQueue,\n        globalErrorComponentAndStyles: initialRSCPayload.G,\n        assetPrefix: initialRSCPayload.p\n    });\n    if ( true && initialRSCPayload.m) {\n        // We provide missing slot information in a context provider only during development\n        // as we log some additional information about the missing slots in the console.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.MissingSlotContext, {\n            value: initialRSCPayload.m,\n            children: router\n        });\n    }\n    return router;\n}\n_c1 = ServerRoot;\nconst StrictModeIfEnabled =  false ? 0 : _react.default.Fragment;\nfunction Root(param) {\n    let { children } = param;\n    if (false) {}\n    return children;\n}\n_c2 = Root;\nconst reactRootOptions = {\n    onRecoverableError: _onrecoverableerror.onRecoverableError,\n    onCaughtError: _errorboundarycallbacks.onCaughtError,\n    onUncaughtError: _errorboundarycallbacks.onUncaughtError\n};\nfunction hydrate(instrumentationHooks) {\n    // React overrides `.then` and doesn't return a new promise chain,\n    // so we wrap the action queue in a promise to ensure that its value\n    // is defined when the promise resolves.\n    // https://github.com/facebook/react/blob/163365a07872337e04826c4f501565d43dbd2fd4/packages/react-client/src/ReactFlightClient.js#L189-L190\n    const pendingActionQueue = new Promise((resolve, reject)=>{\n        initialServerResponse.then((initialRSCPayload)=>{\n            // setAppBuildId should be called only once, during JS initialization\n            // and before any components have hydrated.\n            (0, _appbuildid.setAppBuildId)(initialRSCPayload.b);\n            const initialTimestamp = Date.now();\n            resolve((0, _approuterinstance.createMutableActionQueue)((0, _createinitialrouterstate.createInitialRouterState)({\n                navigatedAt: initialTimestamp,\n                initialFlightData: initialRSCPayload.f,\n                initialCanonicalUrlParts: initialRSCPayload.c,\n                initialParallelRoutes: new Map(),\n                location: window.location,\n                couldBeIntercepted: initialRSCPayload.i,\n                postponed: initialRSCPayload.s,\n                prerendered: initialRSCPayload.S\n            }), instrumentationHooks));\n        }, (err)=>reject(err));\n    });\n    const reactEl = /*#__PURE__*/ (0, _jsxruntime.jsx)(StrictModeIfEnabled, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n            value: {\n                appDir: true\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ServerRoot, {\n                    pendingActionQueue: pendingActionQueue\n                })\n            })\n        })\n    });\n    if (document.documentElement.id === '__next_error__') {\n        let element = reactEl;\n        // Server rendering failed, fall back to client-side rendering\n        if (true) {\n            const { createRootLevelDevOverlayElement } = __webpack_require__(/*! ./components/react-dev-overlay/app/client-entry */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js\");\n            // Note this won't cause hydration mismatch because we are doing CSR w/o hydration\n            element = createRootLevelDevOverlayElement(element);\n        }\n        _client.default.createRoot(appElement, reactRootOptions).render(element);\n    } else {\n        _react.default.startTransition(()=>{\n            _client.default.hydrateRoot(appElement, reactEl, {\n                ...reactRootOptions,\n                formState: initialFormStateData\n            });\n        });\n    }\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const { linkGc } = __webpack_require__(/*! ./app-link-gc */ \"(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\");\n        linkGc();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DOMContentLoaded\");\n$RefreshReg$(_c1, \"ServerRoot\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/app-link-gc.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"linkGc\", ({\n    enumerable: true,\n    get: function() {\n        return linkGc;\n    }\n}));\nfunction linkGc() {\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const callback = (mutationList)=>{\n            for (const mutation of mutationList){\n                if (mutation.type === 'childList') {\n                    for (const node of mutation.addedNodes){\n                        if ('tagName' in node && node.tagName === 'LINK') {\n                            var _link_dataset_precedence;\n                            const link = node;\n                            if ((_link_dataset_precedence = link.dataset.precedence) == null ? void 0 : _link_dataset_precedence.startsWith('next')) {\n                                const href = link.getAttribute('href');\n                                if (href) {\n                                    const [resource, version] = href.split('?v=', 2);\n                                    if (version) {\n                                        const currentOrigin = window.location.origin;\n                                        const allLinks = [\n                                            ...document.querySelectorAll('link[href^=\"' + resource + '\"]'),\n                                            // It's possible that the resource is a full URL or only pathname,\n                                            // so we need to remove the alternative href as well.\n                                            ...document.querySelectorAll('link[href^=\"' + (resource.startsWith(currentOrigin) ? resource.slice(currentOrigin.length) : currentOrigin + resource) + '\"]')\n                                        ];\n                                        for (const otherLink of allLinks){\n                                            var _otherLink_dataset_precedence;\n                                            if ((_otherLink_dataset_precedence = otherLink.dataset.precedence) == null ? void 0 : _otherLink_dataset_precedence.startsWith('next')) {\n                                                const otherHref = otherLink.getAttribute('href');\n                                                if (otherHref) {\n                                                    const [, otherVersion] = otherHref.split('?v=', 2);\n                                                    if (!otherVersion || +otherVersion < +version) {\n                                                        // Delay the removal of the stylesheet to avoid FOUC\n                                                        // caused by `@font-face` rules, as they seem to be\n                                                        // a couple of ticks delayed between the old and new\n                                                        // styles being swapped even if the font is cached.\n                                                        setTimeout(()=>{\n                                                            otherLink.remove();\n                                                        }, 5);\n                                                        const preloadLink = document.querySelector('link[rel=\"preload\"][as=\"style\"][href=\"' + otherHref + '\"]');\n                                                        if (preloadLink) {\n                                                            preloadLink.remove();\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        // Create an observer instance linked to the callback function\n        const observer = new MutationObserver(callback);\n        observer.observe(document.head, {\n            childList: true\n        });\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-link-gc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-instance.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createMutableActionQueue: function() {\n        return createMutableActionQueue;\n    },\n    dispatchNavigateAction: function() {\n        return dispatchNavigateAction;\n    },\n    dispatchTraverseAction: function() {\n        return dispatchTraverseAction;\n    },\n    getCurrentAppRouterState: function() {\n        return getCurrentAppRouterState;\n    },\n    publicAppRouterInstance: function() {\n        return publicAppRouterInstance;\n    }\n});\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _routerreducer = __webpack_require__(/*! ./router-reducer/router-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nfunction runRemainingActions(actionQueue, setState) {\n    if (actionQueue.pending !== null) {\n        actionQueue.pending = actionQueue.pending.next;\n        if (actionQueue.pending !== null) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            runAction({\n                actionQueue,\n                action: actionQueue.pending,\n                setState\n            });\n        } else {\n            // No more actions are pending, check if a refresh is needed\n            if (actionQueue.needsRefresh) {\n                actionQueue.needsRefresh = false;\n                actionQueue.dispatch({\n                    type: _routerreducertypes.ACTION_REFRESH,\n                    origin: window.location.origin\n                }, setState);\n            }\n        }\n    }\n}\nasync function runAction(param) {\n    let { actionQueue, action, setState } = param;\n    const prevState = actionQueue.state;\n    actionQueue.pending = action;\n    const payload = action.payload;\n    const actionResult = actionQueue.action(prevState, payload);\n    function handleResult(nextState) {\n        // if we discarded this action, the state should also be discarded\n        if (action.discarded) {\n            return;\n        }\n        actionQueue.state = nextState;\n        runRemainingActions(actionQueue, setState);\n        action.resolve(nextState);\n    }\n    // if the action is a promise, set up a callback to resolve it\n    if ((0, _isthenable.isThenable)(actionResult)) {\n        actionResult.then(handleResult, (err)=>{\n            runRemainingActions(actionQueue, setState);\n            action.reject(err);\n        });\n    } else {\n        handleResult(actionResult);\n    }\n}\nfunction dispatchAction(actionQueue, payload, setState) {\n    let resolvers = {\n        resolve: setState,\n        reject: ()=>{}\n    };\n    // most of the action types are async with the exception of restore\n    // it's important that restore is handled quickly since it's fired on the popstate event\n    // and we don't want to add any delay on a back/forward nav\n    // this only creates a promise for the async actions\n    if (payload.type !== _routerreducertypes.ACTION_RESTORE) {\n        // Create the promise and assign the resolvers to the object.\n        const deferredPromise = new Promise((resolve, reject)=>{\n            resolvers = {\n                resolve,\n                reject\n            };\n        });\n        (0, _react.startTransition)(()=>{\n            // we immediately notify React of the pending promise -- the resolver is attached to the action node\n            // and will be called when the associated action promise resolves\n            setState(deferredPromise);\n        });\n    }\n    const newAction = {\n        payload,\n        next: null,\n        resolve: resolvers.resolve,\n        reject: resolvers.reject\n    };\n    // Check if the queue is empty\n    if (actionQueue.pending === null) {\n        // The queue is empty, so add the action and start it immediately\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else if (payload.type === _routerreducertypes.ACTION_NAVIGATE || payload.type === _routerreducertypes.ACTION_RESTORE) {\n        // Navigations (including back/forward) take priority over any pending actions.\n        // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n        actionQueue.pending.discarded = true;\n        // The rest of the current queue should still execute after this navigation.\n        // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n        newAction.next = actionQueue.pending.next;\n        // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n        if (actionQueue.pending.payload.type === _routerreducertypes.ACTION_SERVER_ACTION) {\n            actionQueue.needsRefresh = true;\n        }\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else {\n        // The queue is not empty, so add the action to the end of the queue\n        // It will be started by runRemainingActions after the previous action finishes\n        if (actionQueue.last !== null) {\n            actionQueue.last.next = newAction;\n        }\n        actionQueue.last = newAction;\n    }\n}\nlet globalActionQueue = null;\nfunction createMutableActionQueue(initialState, instrumentationHooks) {\n    const actionQueue = {\n        state: initialState,\n        dispatch: (payload, setState)=>dispatchAction(actionQueue, payload, setState),\n        action: async (state, action)=>{\n            const result = (0, _routerreducer.reducer)(state, action);\n            return result;\n        },\n        pending: null,\n        last: null,\n        onRouterTransitionStart: instrumentationHooks !== null && typeof instrumentationHooks.onRouterTransitionStart === 'function' ? instrumentationHooks.onRouterTransitionStart : null\n    };\n    if (true) {\n        // The action queue is lazily created on hydration, but after that point\n        // it doesn't change. So we can store it in a global rather than pass\n        // it around everywhere via props/context.\n        if (globalActionQueue !== null) {\n            throw Object.defineProperty(new Error('Internal Next.js Error: createMutableActionQueue was called more ' + 'than once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E624\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        globalActionQueue = actionQueue;\n    }\n    return actionQueue;\n}\nfunction getCurrentAppRouterState() {\n    return globalActionQueue !== null ? globalActionQueue.state : null;\n}\nfunction getAppRouterActionQueue() {\n    if (globalActionQueue === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return globalActionQueue;\n}\nfunction getProfilingHookForOnNavigationStart() {\n    if (globalActionQueue !== null) {\n        return globalActionQueue.onRouterTransitionStart;\n    }\n    return null;\n}\nfunction dispatchNavigateAction(href, navigateType, shouldScroll, linkInstanceRef) {\n    // TODO: This stuff could just go into the reducer. Leaving as-is for now\n    // since we're about to rewrite all the router reducer stuff anyway.\n    const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n    if (false) {}\n    (0, _links.setLinkForCurrentNavigation)(linkInstanceRef);\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, navigateType);\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_NAVIGATE,\n        url,\n        isExternalUrl: (0, _approuter.isExternalURL)(url),\n        locationSearch: location.search,\n        shouldScroll,\n        navigateType,\n        allowAliasing: true\n    });\n}\nfunction dispatchTraverseAction(href, tree) {\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, 'traverse');\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_RESTORE,\n        url: new URL(href),\n        tree\n    });\n}\nconst publicAppRouterInstance = {\n    back: ()=>window.history.back(),\n    forward: ()=>window.history.forward(),\n    prefetch:  false ? // cache. So we don't need to dispatch an action.\n    0 : (href, options)=>{\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue();\n        const url = (0, _approuter.createPrefetchURL)(href);\n        if (url !== null) {\n            var _options_kind;\n            // The prefetch reducer doesn't actually update any state or\n            // trigger a rerender. It just writes to a mutable cache. So we\n            // shouldn't bother calling setState/dispatch; we can just re-run\n            // the reducer directly using the current state.\n            // TODO: Refactor this away from a \"reducer\" so it's\n            // less confusing.\n            (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                type: _routerreducertypes.ACTION_PREFETCH,\n                url,\n                kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n            });\n        }\n    },\n    replace: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'replace', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    push: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'push', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    refresh: ()=>{\n        (0, _react.startTransition)(()=>{\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_REFRESH,\n                origin: window.location.origin\n            });\n        });\n    },\n    hmrRefresh: ()=>{\n        if (false) {} else {\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_HMR_REFRESH,\n                    origin: window.location.origin\n                });\n            });\n        }\n    }\n};\n// Exists for debugging purposes. Don't use in application code.\nif ( true && window.next) {\n    window.next.router = publicAppRouterInstance;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-instance.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    },\n    isExternalURL: function() {\n        return isExternalURL;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1\n    };\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    let { actionQueue, assetPrefix, globalError } = param;\n    const state = (0, _useactionqueue.useActionQueue)(actionQueue);\n    const { canonicalUrl } = state;\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = state;\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: _approuterinstance.publicAppRouterInstance,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, []);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                // TODO: This should access the router methods directly, rather than\n                // go through the public interface.\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    _approuterinstance.publicAppRouterInstance.push(url, {});\n                } else {\n                    _approuterinstance.publicAppRouterInstance.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, []);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = state;\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location = window.location;\n            if (pushRef.pendingPush) {\n                location.assign(canonicalUrl);\n            } else {\n                location.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                (0, _approuterinstance.dispatchTraverseAction)(window.location.href, event.state.__PRIVATE_NEXTJS_INTERNALS_TREE);\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, []);\n    const { cache, tree, nextUrl, focusAndScrollRef } = state;\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: state\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: _approuterinstance.publicAppRouterInstance,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleConsoleError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/links.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/components/links.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    IDLE_LINK_STATUS: function() {\n        return IDLE_LINK_STATUS;\n    },\n    PENDING_LINK_STATUS: function() {\n        return PENDING_LINK_STATUS;\n    },\n    mountFormInstance: function() {\n        return mountFormInstance;\n    },\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    setLinkForCurrentNavigation: function() {\n        return setLinkForCurrentNavigation;\n    },\n    unmountLinkForCurrentNavigation: function() {\n        return unmountLinkForCurrentNavigation;\n    },\n    unmountPrefetchableInstance: function() {\n        return unmountPrefetchableInstance;\n    }\n});\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation = null;\nconst PENDING_LINK_STATUS = {\n    pending: true\n};\nconst IDLE_LINK_STATUS = {\n    pending: false\n};\nfunction setLinkForCurrentNavigation(link) {\n    (0, _react.startTransition)(()=>{\n        linkForMostRecentNavigation == null ? void 0 : linkForMostRecentNavigation.setOptimisticLinkStatus(IDLE_LINK_STATUS);\n        link == null ? void 0 : link.setOptimisticLinkStatus(PENDING_LINK_STATUS);\n        linkForMostRecentNavigation = link;\n    });\n}\nfunction unmountLinkForCurrentNavigation(link) {\n    if (linkForMostRecentNavigation === link) {\n        linkForMostRecentNavigation = null;\n    }\n}\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction observeVisibility(element, instance) {\n    const existingInstance = prefetchable.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountPrefetchableInstance(element);\n    }\n    // Only track prefetchable links that have a valid prefetch URL\n    prefetchable.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction coercePrefetchableUrl(href) {\n    try {\n        return (0, _approuter.createPrefetchURL)(href);\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return null;\n    }\n}\nfunction mountLinkInstance(element, href, router, kind, prefetchEnabled, setOptimisticLinkStatus) {\n    if (prefetchEnabled) {\n        const prefetchURL = coercePrefetchableUrl(href);\n        if (prefetchURL !== null) {\n            const instance = {\n                router,\n                kind,\n                isVisible: false,\n                wasHoveredOrTouched: false,\n                prefetchTask: null,\n                cacheVersion: -1,\n                prefetchHref: prefetchURL.href,\n                setOptimisticLinkStatus\n            };\n            // We only observe the link's visibility if it's prefetchable. For\n            // example, this excludes links to external URLs.\n            observeVisibility(element, instance);\n            return instance;\n        }\n    }\n    // If the link is not prefetchable, we still create an instance so we can\n    // track its optimistic state (i.e. useLinkStatus).\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: null,\n        setOptimisticLinkStatus\n    };\n    return instance;\n}\nfunction mountFormInstance(element, href, router, kind) {\n    const prefetchURL = coercePrefetchableUrl(href);\n    if (prefetchURL === null) {\n        // This href is not prefetchable, so we don't track it.\n        // TODO: We currently observe/unobserve a form every time its href changes.\n        // For Links, this isn't a big deal because the href doesn't usually change,\n        // but for forms it's extremely common. We should optimize this.\n        return;\n    }\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus: null\n    };\n    observeVisibility(element, instance);\n}\nfunction unmountPrefetchableInstance(element) {\n    const instance = prefetchable.get(element);\n    if (instance !== undefined) {\n        prefetchable.delete(element);\n        prefetchableAndVisible.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        prefetchableAndVisible.add(instance);\n    } else {\n        prefetchableAndVisible.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element, unstable_upgradeToDynamicPrefetch) {\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        if (false) {}\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with reschedulePrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    const appRouterState = (0, _approuterinstance.getCurrentAppRouterState)();\n    if (appRouterState !== null) {\n        const treeAtTimeOfPrefetch = appRouterState.tree;\n        if (existingPrefetchTask === null) {\n            // Initiate a prefetch task.\n            const nextUrl = appRouterState.nextUrl;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        } else {\n            // We already have an old task object that we can reschedule. This is\n            // effectively the same as canceling the old task and creating a new one.\n            (0, _segmentcache.reschedulePrefetchTask)(existingPrefetchTask, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        }\n        // Keep track of the cache version at the time the prefetch was requested.\n        // This is used to check if the prefetch is stale.\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of prefetchableAndVisible){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _appdevoverlayerrorboundary = __webpack_require__(/*! ./app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _constants = __webpack_require__(/*! ../../../../shared/lib/errors/constants */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/errors/constants.js\");\nfunction readSsrError() {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    const ssrErrorTemplateTag = document.querySelector('template[data-next-error-message]');\n    if (ssrErrorTemplateTag) {\n        const message = ssrErrorTemplateTag.getAttribute('data-next-error-message');\n        const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack');\n        const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest');\n        const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n        if (digest) {\n            ;\n            error.digest = digest;\n        }\n        // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            return null;\n        }\n        error.stack = stack || '';\n        return error;\n    }\n    return null;\n}\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors(param) {\n    let { onBlockingError } = param;\n    if (true) {\n        // Need to read during render. The attributes will be gone after commit.\n        const ssrError = readSsrError();\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            if (ssrError !== null) {\n                // TODO(veil): Produces wrong Owner Stack\n                // TODO(veil): Mark as recoverable error\n                // TODO(veil): console.error\n                (0, _useerrorhandler.handleClientError)(ssrError);\n                // If it's missing root tags, we can't recover, make it blocking.\n                if (ssrError.digest === _constants.MISSING_ROOT_TAGS_ERROR) {\n                    onBlockingError();\n                }\n            }\n        }, [\n            ssrError,\n            onBlockingError\n        ]);\n    }\n    return null;\n}\n_c = ReplaySsrOnlyErrors;\nfunction AppDevOverlay(param) {\n    let { state, globalError, children } = param;\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(false);\n    const openOverlay = (0, _react.useCallback)(()=>{\n        setIsErrorOverlayOpen(true);\n    }, []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_appdevoverlayerrorboundary.AppDevOverlayErrorBoundary, {\n                globalError: globalError,\n                onError: setIsErrorOverlayOpen,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(ReplaySsrOnlyErrors, {\n                        onBlockingError: openOverlay\n                    }),\n                    children\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                        state: state,\n                        isErrorOverlayOpen: isErrorOverlayOpen,\n                        setIsErrorOverlayOpen: setIsErrorOverlayOpen\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = AppDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReplaySsrOnlyErrors\");\n$RefreshReg$(_c1, \"AppDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { navigatedAt, initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading,\n        navigatedAt\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(navigatedAt, cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createFetch: function() {\n        return createFetch;\n    },\n    createFromNextReadableStream: function() {\n        return createFromNextReadableStream;\n    },\n    fetchServerResponse: function() {\n        return fetchServerResponse;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _appcallserver = __webpack_require__(/*! ../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _appbuildid = __webpack_require__(/*! ../../app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\nconst _setcachebustingsearchparam = __webpack_require__(/*! ./set-cache-busting-search-param */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js\");\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(url).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nlet abortController = new AbortController();\nif (true) {\n    // Abort any in-flight requests when the page is unloaded, e.g. due to\n    // reloading the page or performing hard navigations. This allows us to ignore\n    // what would otherwise be a thrown TypeError when the browser cancels the\n    // requests.\n    window.addEventListener('pagehide', ()=>{\n        abortController.abort();\n    });\n    // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n    // and the JavaScript execution context is restored by the browser.\n    window.addEventListener('pageshow', ()=>{\n        abortController = new AbortController();\n    });\n}\nasync function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [_approuterheaders.RSC_HEADER]: '1',\n        // Provide the current router state\n        [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(JSON.stringify(flightRouterState))\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === _routerreducertypes.PrefetchKind.AUTO) {\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if ( true && options.isHmrRefresh) {\n        headers[_approuterheaders.NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[_approuterheaders.NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === _routerreducertypes.PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        if (false) {}\n        const res = await createFetch(url, headers, fetchPriority, abortController.signal);\n        const responseUrl = urlToUrlWithoutFlightMarker(res.url);\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(_approuterheaders.NEXT_URL));\n        const postponed = !!res.headers.get(_approuterheaders.NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeaderSeconds = res.headers.get(_approuterheaders.NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeaderSeconds !== null ? parseInt(staleTimeHeaderSeconds, 10) * 1000 : -1;\n        let isFlightResponse = contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER);\n        if (false) {}\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (true) {\n            await (__webpack_require__(/*! ../react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\").waitForWebpackRuntimeHotUpdate)();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if ((0, _appbuildid.getAppBuildId)() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        if (!abortController.signal.aborted) {\n            console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        }\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nfunction createFetch(url, headers, fetchPriority, signal) {\n    const fetchUrl = new URL(url);\n    // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n    // cache busting search param) from the request so they're\n    // maximally cacheable.\n    (0, _setcachebustingsearchparam.setCacheBustingSearchParam)(fetchUrl, headers);\n    if (false) {}\n    if (false) {}\n    return fetch(fetchUrl, {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined,\n        signal\n    });\n}\nfunction createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer: _appcallserver.callServer,\n        findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fetch-server-response.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_STALETIME_MS: function() {\n        return DYNAMIC_STALETIME_MS;\n    },\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcHJlZmV0Y2gtY2FjaGUtdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBNFlhQSxvQkFBb0I7ZUFBcEJBOztJQUdBQyxtQkFBbUI7ZUFBbkJBOztJQW5JR0MsOEJBQThCO2VBQTlCQTs7SUE5R0FDLDZCQUE2QjtlQUE3QkE7O0lBK05BQyxrQkFBa0I7ZUFBbEJBOzs7aURBMVhUO2dEQU1BOzZDQUN1QjtBQUU5QixNQUFNQyxnQ0FBZ0M7QUFVdEM7Ozs7OztDQU1DLEdBQ0QsU0FBU0MsMkJBQ1BDLEdBQVEsRUFDUkMsbUJBQTRCLEVBQzVCQyxNQUFzQjtJQUV0QixnRkFBZ0Y7SUFDaEYsZ0ZBQWdGO0lBQ2hGLGtCQUFrQjtJQUNsQixJQUFJQyxrQkFBa0JILElBQUlJLFFBQVE7SUFFbEMsNEZBQTRGO0lBQzVGLDhEQUE4RDtJQUM5RCxxRkFBcUY7SUFDckYsZ0ZBQWdGO0lBQ2hGLHFEQUFxRDtJQUNyRCxJQUFJSCxxQkFBcUI7UUFDdkIsMEVBQTBFO1FBQzFFLGtGQUFrRjtRQUNsRiw0Q0FBNEM7UUFDNUNFLG1CQUFtQkgsSUFBSUssTUFBTTtJQUMvQjtJQUVBLElBQUlILFFBQVE7UUFDVixPQUFRLEtBQUVBLFNBQVNKLGdDQUFnQ0s7SUFDckQ7SUFFQSxPQUFPQTtBQUNUO0FBRUEsU0FBU0csdUJBQ1BOLEdBQVEsRUFDUk8sSUFBOEIsRUFDOUJDLE9BQXVCO0lBRXZCLE9BQU9ULDJCQUEyQkMsS0FBS08sU0FBU0Usb0JBQUFBLFlBQVksQ0FBQ0MsSUFBSSxFQUFFRjtBQUNyRTtBQUVBLFNBQVNHLHNCQUNQWCxHQUFRLEVBQ1JPLElBQTJDLEVBQzNDQyxPQUFzQixFQUN0QkksYUFBOEMsRUFDOUNDLGFBQXNCO0lBSHRCTixJQUFBQSxTQUFBQSxLQUFBQSxHQUFBQSxPQUFxQkUsb0JBQUFBLFlBQVksQ0FBQ0ssU0FBUztJQUszQyw4RUFBOEU7SUFDOUUsa0pBQWtKO0lBQ2xKLGlJQUFpSTtJQUNqSSxLQUFLLE1BQU1DLGdCQUFnQjtRQUFDUDtRQUFTO0tBQUssQ0FBRTtRQUMxQyxNQUFNUSxxQkFBcUJqQiwyQkFDekJDLEtBQ0EsTUFDQWU7UUFFRixNQUFNRSx3QkFBd0JsQiwyQkFDNUJDLEtBQ0EsT0FDQWU7UUFHRix3RUFBd0U7UUFDeEUsTUFBTUcsZ0JBQWdCbEIsSUFBSUssTUFBTSxHQUM1QlcscUJBQ0FDO1FBRUosTUFBTUUsZ0JBQWdCUCxjQUFjUSxHQUFHLENBQUNGO1FBQ3hDLElBQUlDLGlCQUFpQk4sZUFBZTtZQUNsQyxrR0FBa0c7WUFDbEcsTUFBTVEsWUFDSkYsY0FBY25CLEdBQUcsQ0FBQ0ksUUFBUSxLQUFLSixJQUFJSSxRQUFRLElBQzNDZSxjQUFjbkIsR0FBRyxDQUFDSyxNQUFNLEtBQUtMLElBQUlLLE1BQU07WUFFekMsSUFBSWdCLFdBQVc7Z0JBQ2IsT0FBTztvQkFDTCxHQUFHRixhQUFhO29CQUNoQkcsU0FBUztnQkFDWDtZQUNGO1lBRUEsT0FBT0g7UUFDVDtRQUVBLGdHQUFnRztRQUNoRyxpQ0FBaUM7UUFDakMsOEdBQThHO1FBQzlHLDJEQUEyRDtRQUMzRCxNQUFNSSxxQkFBcUJYLGNBQWNRLEdBQUcsQ0FBQ0g7UUFDN0MsSUFDRU8sS0FPK0QxQixFQUMvRCxFQUVEO0lBQ0g7SUFFQSxvRkFBb0Y7SUFDcEYsbUJBQW1CO0lBQ25CLDBGQUEwRjtJQUMxRiwyR0FBMkc7SUFDM0cscUdBQXFHO0lBQ3JHLElBQ0UwQixLQUVhWCxFQUNiLEVBV0Q7SUFFRCxPQUFPa0I7QUFDVDtBQU1PLFNBQVNuQyw4QkFBOEIsS0FXN0M7SUFYNkMsTUFDNUNJLEdBQUcsRUFDSFEsT0FBTyxFQUNQd0IsSUFBSSxFQUNKcEIsYUFBYSxFQUNiTCxJQUFJLEVBQ0pNLGdCQUFnQixJQUFJLEVBS3JCLEdBWDZDO0lBWTVDLE1BQU1vQixxQkFBcUJ0QixzQkFDekJYLEtBQ0FPLE1BQ0FDLFNBQ0FJLGVBQ0FDO0lBR0YsSUFBSW9CLG9CQUFvQjtRQUN0QiwwREFBMEQ7UUFDMURBLG1CQUFtQkMsTUFBTSxHQUFHQyw0QkFBNEJGO1FBRXhELCtEQUErRDtRQUMvRCxxSEFBcUg7UUFDckgsTUFBTUcseUJBQ0pILG1CQUFtQjFCLElBQUksS0FBS0Usb0JBQUFBLFlBQVksQ0FBQ0MsSUFBSSxJQUM3Q0gsU0FBU0Usb0JBQUFBLFlBQVksQ0FBQ0MsSUFBSTtRQUU1QixJQUFJMEIsd0JBQXdCO1lBQzFCLG9HQUFvRztZQUNwRyxxSEFBcUg7WUFDckgsNENBQTRDO1lBQzVDSCxtQkFBbUJJLElBQUksQ0FBQ0MsSUFBSSxDQUFDLENBQUNDO2dCQUM1QixNQUFNQyxpQkFDSkMsTUFBTUMsT0FBTyxDQUFDSCxpQkFBaUJJLFVBQVUsS0FDekNKLGlCQUFpQkksVUFBVSxDQUFDQyxJQUFJLENBQUMsQ0FBQ0Q7b0JBQ2hDLDZHQUE2RztvQkFDN0csT0FBT0EsV0FBV0UsWUFBWSxJQUFJRixXQUFXRyxRQUFRLEtBQUs7Z0JBQzVEO2dCQUVGLElBQUksQ0FBQ04sZ0JBQWdCO29CQUNuQixPQUFPTyx3QkFBd0I7d0JBQzdCZjt3QkFDQWhDO3dCQUNBUTt3QkFDQUk7d0JBQ0EsOEVBQThFO3dCQUM5RSwyRkFBMkY7d0JBQzNGLGtFQUFrRTt3QkFDbEVMLE1BQU1BLFFBQUFBLE9BQUFBLE9BQVFFLG9CQUFBQSxZQUFZLENBQUNLLFNBQVM7b0JBQ3RDO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLHVIQUF1SDtRQUN2SCw0SUFBNEk7UUFDNUksSUFBSVAsUUFBUTBCLG1CQUFtQjFCLElBQUksS0FBS0Usb0JBQUFBLFlBQVksQ0FBQ0ssU0FBUyxFQUFFO1lBQzlEbUIsbUJBQW1CMUIsSUFBSSxHQUFHQTtRQUM1QjtRQUVBLHFGQUFxRjtRQUNyRixPQUFPMEI7SUFDVDtJQUVBLGtEQUFrRDtJQUNsRCxPQUFPYyx3QkFBd0I7UUFDN0JmO1FBQ0FoQztRQUNBUTtRQUNBSTtRQUNBTCxNQUFNQSxRQUFRRSxvQkFBQUEsWUFBWSxDQUFDSyxTQUFTO0lBQ3RDO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDRCxTQUFTa0MsaUNBQWlDLEtBUXpDO0lBUnlDLE1BQ3hDaEQsR0FBRyxFQUNIUSxPQUFPLEVBQ1BJLGFBQWEsRUFDYnFDLGdCQUFnQixFQUlqQixHQVJ5QztJQVN4QyxNQUFNaEIscUJBQXFCckIsY0FBY1EsR0FBRyxDQUFDNkI7SUFDN0MsSUFBSSxDQUFDaEIsb0JBQW9CO1FBQ3ZCLHlDQUF5QztRQUN6QztJQUNGO0lBRUEsTUFBTWlCLGNBQWM1Qyx1QkFDbEJOLEtBQ0FpQyxtQkFBbUIxQixJQUFJLEVBQ3ZCQztJQUVGSSxjQUFjdUMsR0FBRyxDQUFDRCxhQUFhO1FBQUUsR0FBR2pCLGtCQUFrQjtRQUFFTixLQUFLdUI7SUFBWTtJQUN6RXRDLGNBQWN3QyxNQUFNLENBQUNIO0lBRXJCLE9BQU9DO0FBQ1Q7QUFLTyxTQUFTdkQsK0JBQStCLEtBVzlDO0lBWDhDLE1BQzdDYSxPQUFPLEVBQ1B3QixJQUFJLEVBQ0pwQixhQUFhLEVBQ2JaLEdBQUcsRUFDSHFDLElBQUksRUFDSjlCLElBQUksRUFLTCxHQVg4QztJQVk3QyxpSEFBaUg7SUFDakgsc0dBQXNHO0lBQ3RHLHFHQUFxRztJQUNyRyxNQUFNOEMsbUJBQW1CaEIsS0FBS2lCLGtCQUFrQixHQUM1Q2hELHVCQUF1Qk4sS0FBS08sTUFBTUMsV0FDbENGLHVCQUF1Qk4sS0FBS087SUFFaEMsTUFBTWdELGdCQUFnQjtRQUNwQkMsc0JBQXNCeEI7UUFDdEJLLE1BQU1vQixRQUFRQyxPQUFPLENBQUNyQjtRQUN0QjlCO1FBQ0FvRCxjQUFjQyxLQUFLQyxHQUFHO1FBQ3RCQyxjQUFjRixLQUFLQyxHQUFHO1FBQ3RCRSxXQUFXLENBQUM7UUFDWnBDLEtBQUswQjtRQUNMbkIsUUFBUThCLG9CQUFBQSx3QkFBd0IsQ0FBQ0MsS0FBSztRQUN0Q2pFO0lBQ0Y7SUFFQVksY0FBY3VDLEdBQUcsQ0FBQ0Usa0JBQWtCRTtJQUVwQyxPQUFPQTtBQUNUO0FBRUE7O0NBRUMsR0FDRCxTQUFTUix3QkFBd0IsS0FTaEM7SUFUZ0MsTUFDL0IvQyxHQUFHLEVBQ0hPLElBQUksRUFDSnlCLElBQUksRUFDSnhCLE9BQU8sRUFDUEksYUFBYSxFQUlkLEdBVGdDO0lBVS9CLE1BQU15QyxtQkFBbUIvQyx1QkFBdUJOLEtBQUtPO0lBRXJELHVFQUF1RTtJQUN2RSw2RkFBNkY7SUFDN0YsTUFBTThCLE9BQU82QixpQkFBQUEsYUFBYSxDQUFDQyxPQUFPLENBQUMsSUFDakNDLENBQUFBLEdBQUFBLHFCQUFBQSxtQkFBQUEsRUFBb0JwRSxLQUFLO1lBQ3ZCcUUsbUJBQW1CckM7WUFDbkJ4QjtZQUNBOEQsY0FBYy9EO1FBQ2hCLEdBQUcrQixJQUFJLENBQUMsQ0FBQ0M7WUFDUCwrRkFBK0Y7WUFDL0Ysd0RBQXdEO1lBQ3hELGtFQUFrRTtZQUNsRSxJQUFJVztZQUVKLElBQUlYLGlCQUFpQmUsa0JBQWtCLEVBQUU7Z0JBQ3ZDLGdFQUFnRTtnQkFDaEVKLGNBQWNGLGlDQUFpQztvQkFDN0NoRDtvQkFDQWlELGtCQUFrQkk7b0JBQ2xCN0M7b0JBQ0FJO2dCQUNGO1lBQ0Y7WUFFQSxzSEFBc0g7WUFDdEgsK0hBQStIO1lBQy9ILGFBQWE7WUFDYixJQUFJMkIsaUJBQWlCZ0MsV0FBVyxFQUFFO2dCQUNoQyxNQUFNdEMscUJBQXFCckIsY0FBY1EsR0FBRyxDQUMxQyxlQUNBOEIsT0FBQUEsY0FBZUc7Z0JBRWpCLElBQUlwQixvQkFBb0I7b0JBQ3RCQSxtQkFBbUIxQixJQUFJLEdBQUdFLG9CQUFBQSxZQUFZLENBQUNDLElBQUk7b0JBQzNDLElBQUk2QixpQkFBaUJ3QixTQUFTLEtBQUssQ0FBQyxHQUFHO3dCQUNyQyxpRUFBaUU7d0JBQ2pFLGtFQUFrRTt3QkFDbEU5QixtQkFBbUI4QixTQUFTLEdBQUd4QixpQkFBaUJ3QixTQUFTO29CQUMzRDtnQkFDRjtZQUNGO1lBRUEsT0FBT3hCO1FBQ1Q7SUFHRixNQUFNZ0IsZ0JBQWdCO1FBQ3BCQyxzQkFBc0J4QjtRQUN0Qks7UUFDQTlCO1FBQ0FvRCxjQUFjQyxLQUFLQyxHQUFHO1FBQ3RCQyxjQUFjO1FBQ2RDLFdBQVcsQ0FBQztRQUNacEMsS0FBSzBCO1FBQ0xuQixRQUFROEIsb0JBQUFBLHdCQUF3QixDQUFDQyxLQUFLO1FBQ3RDakU7SUFDRjtJQUVBWSxjQUFjdUMsR0FBRyxDQUFDRSxrQkFBa0JFO0lBRXBDLE9BQU9BO0FBQ1Q7QUFFTyxTQUFTMUQsbUJBQ2RlLGFBQW9EO0lBRXBELEtBQUssTUFBTSxDQUFDNEQsTUFBTUMsbUJBQW1CLElBQUk3RCxjQUFlO1FBQ3RELElBQ0V1Qiw0QkFBNEJzQyx3QkFDNUJULG9CQUFBQSx3QkFBd0IsQ0FBQ1UsT0FBTyxFQUNoQztZQUNBOUQsY0FBY3dDLE1BQU0sQ0FBQ29CO1FBQ3ZCO0lBQ0Y7QUFDRjtBQUlPLE1BQU0vRSx1QkFDWGtGLE9BQU9uRCxHQUFrRCxJQUFJO0FBRXhELE1BQU05QixzQkFDWGlGLE9BQU9uRCxLQUFpRCxJQUFJO0FBRTlELFNBQVNXLDRCQUE0QixLQUtoQjtJQUxnQixNQUNuQzVCLElBQUksRUFDSm9ELFlBQVksRUFDWkcsWUFBWSxFQUNaQyxTQUFTLEVBQ1UsR0FMZ0I7SUFNbkMsSUFBSUEsY0FBYyxDQUFDLEdBQUc7UUFDcEIsd0VBQXdFO1FBQ3hFLHlFQUF5RTtRQUN6RSxlQUFlO1FBQ2YsRUFBRTtRQUNGLDBFQUEwRTtRQUMxRSx5RUFBeUU7UUFDekUsMkVBQTJFO1FBQzNFLHdFQUF3RTtRQUN4RSxPQUFPSCxLQUFLQyxHQUFHLEtBQUtGLGVBQWVJLFlBQy9CQyxvQkFBQUEsd0JBQXdCLENBQUNDLEtBQUssR0FDOUJELG9CQUFBQSx3QkFBd0IsQ0FBQ2MsS0FBSztJQUNwQztJQUVBLGdGQUFnRjtJQUNoRixJQUFJbEIsS0FBS0MsR0FBRyxLQUFNQyxDQUFBQSxnQkFBQUEsT0FBQUEsZUFBZ0JILFlBQUFBLENBQVcsR0FBS2xFLHNCQUFzQjtRQUN0RSxPQUFPcUUsZUFDSEUsb0JBQUFBLHdCQUF3QixDQUFDZSxRQUFRLEdBQ2pDZixvQkFBQUEsd0JBQXdCLENBQUNDLEtBQUs7SUFDcEM7SUFFQSxzR0FBc0c7SUFDdEcsNEVBQTRFO0lBQzVFLHNEQUFzRDtJQUN0RCxJQUFJMUQsU0FBU0Usb0JBQUFBLFlBQVksQ0FBQ3VFLElBQUksRUFBRTtRQUM5QixJQUFJcEIsS0FBS0MsR0FBRyxLQUFLRixlQUFlakUscUJBQXFCO1lBQ25ELE9BQU9zRSxvQkFBQUEsd0JBQXdCLENBQUNjLEtBQUs7UUFDdkM7SUFDRjtJQUVBLGlHQUFpRztJQUNqRyxJQUFJdkUsU0FBU0Usb0JBQUFBLFlBQVksQ0FBQ0MsSUFBSSxFQUFFO1FBQzlCLElBQUlrRCxLQUFLQyxHQUFHLEtBQUtGLGVBQWVqRSxxQkFBcUI7WUFDbkQsT0FBT3NFLG9CQUFBQSx3QkFBd0IsQ0FBQ2UsUUFBUTtRQUMxQztJQUNGO0lBRUEsT0FBT2Ysb0JBQUFBLHdCQUF3QixDQUFDVSxPQUFPO0FBQ3pDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyb3V0ZXItcmVkdWNlclxccHJlZmV0Y2gtY2FjaGUtdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgZmV0Y2hTZXJ2ZXJSZXNwb25zZSxcbiAgdHlwZSBGZXRjaFNlcnZlclJlc3BvbnNlUmVzdWx0LFxufSBmcm9tICcuL2ZldGNoLXNlcnZlci1yZXNwb25zZSdcbmltcG9ydCB7XG4gIFByZWZldGNoQ2FjaGVFbnRyeVN0YXR1cyxcbiAgdHlwZSBQcmVmZXRjaENhY2hlRW50cnksXG4gIFByZWZldGNoS2luZCxcbiAgdHlwZSBSZWFkb25seVJlZHVjZXJTdGF0ZSxcbn0gZnJvbSAnLi9yb3V0ZXItcmVkdWNlci10eXBlcydcbmltcG9ydCB7IHByZWZldGNoUXVldWUgfSBmcm9tICcuL3JlZHVjZXJzL3ByZWZldGNoLXJlZHVjZXInXG5cbmNvbnN0IElOVEVSQ0VQVElPTl9DQUNIRV9LRVlfTUFSS0VSID0gJyUnXG5cbmV4cG9ydCB0eXBlIEFsaWFzZWRQcmVmZXRjaENhY2hlRW50cnkgPSBQcmVmZXRjaENhY2hlRW50cnkgJiB7XG4gIC8qKiBUaGlzIGlzIGEgc3BlY2lhbCBwcm9wZXJ0eSB0aGF0IGluZGljYXRlcyBhIHByZWZldGNoIGVudHJ5IGFzc29jaWF0ZWQgd2l0aCBhIGRpZmZlcmVudCBVUkxcbiAgICogd2FzIHJldHVybmVkIHJhdGhlciB0aGFuIHRoZSByZXF1ZXN0ZWQgVVJMLiBUaGlzIHNpZ25hbHMgdG8gdGhlIHJvdXRlciB0aGF0IGl0IHNob3VsZCBvbmx5XG4gICAqIGFwcGx5IHRoZSBwYXJ0IHRoYXQgZG9lc24ndCBkZXBlbmQgb24gc2VhcmNoUGFyYW1zIChzcGVjaWZpY2FsbHkgdGhlIGxvYWRpbmcgc3RhdGUpLlxuICAgKi9cbiAgYWxpYXNlZD86IGJvb2xlYW5cbn1cblxuLyoqXG4gKiBDcmVhdGVzIGEgY2FjaGUga2V5IGZvciB0aGUgcm91dGVyIHByZWZldGNoIGNhY2hlXG4gKlxuICogQHBhcmFtIHVybCAtIFRoZSBVUkwgYmVpbmcgbmF2aWdhdGVkIHRvXG4gKiBAcGFyYW0gbmV4dFVybCAtIGFuIGludGVybmFsIFVSTCwgcHJpbWFyaWx5IHVzZWQgZm9yIGhhbmRsaW5nIHJld3JpdGVzLiBEZWZhdWx0cyB0byAnLycuXG4gKiBAcmV0dXJuIFRoZSBnZW5lcmF0ZWQgcHJlZmV0Y2ggY2FjaGUga2V5LlxuICovXG5mdW5jdGlvbiBjcmVhdGVQcmVmZXRjaENhY2hlS2V5SW1wbChcbiAgdXJsOiBVUkwsXG4gIGluY2x1ZGVTZWFyY2hQYXJhbXM6IGJvb2xlYW4sXG4gIHByZWZpeD86IHN0cmluZyB8IG51bGxcbikge1xuICAvLyBJbml0aWFsbHkgd2Ugb25seSB1c2UgdGhlIHBhdGhuYW1lIGFzIHRoZSBjYWNoZSBrZXkuIFdlIGRvbid0IHdhbnQgdG8gaW5jbHVkZVxuICAvLyBzZWFyY2ggcGFyYW1zIHNvIHRoYXQgbXVsdGlwbGUgVVJMcyB3aXRoIHRoZSBzYW1lIHNlYXJjaCBwYXJhbWV0ZXIgY2FuIHJlLXVzZVxuICAvLyBsb2FkaW5nIHN0YXRlcy5cbiAgbGV0IHBhdGhuYW1lRnJvbVVybCA9IHVybC5wYXRobmFtZVxuXG4gIC8vIFJTQyByZXNwb25zZXMgY2FuIGRpZmZlciBiYXNlZCBvbiBzZWFyY2ggcGFyYW1zLCBzcGVjaWZpY2FsbHkgaW4gdGhlIGNhc2Ugd2hlcmUgd2UgYXJlbid0XG4gIC8vIHJldHVybmluZyBhIHBhcnRpYWwgcmVzcG9uc2UgKGllIHdpdGggYFByZWZldGNoS2luZC5BVVRPYCkuXG4gIC8vIEluIHRoZSBhdXRvIGNhc2UsIHNpbmNlIGxvYWRpbmcuanMgJiBsYXlvdXQuanMgd29uJ3QgaGF2ZSBhY2Nlc3MgdG8gc2VhcmNoIHBhcmFtcyxcbiAgLy8gd2UgY2FuIHNhZmVseSByZS11c2UgdGhhdCBjYWNoZSBlbnRyeS4gQnV0IGZvciBmdWxsIHByZWZldGNoZXMsIHdlIHNob3VsZCBub3RcbiAgLy8gcmUtdXNlIHRoZSBjYWNoZSBlbnRyeSBhcyB0aGUgcmVzcG9uc2UgbWF5IGRpZmZlci5cbiAgaWYgKGluY2x1ZGVTZWFyY2hQYXJhbXMpIHtcbiAgICAvLyBpZiB3ZSBoYXZlIGEgZnVsbCBwcmVmZXRjaCwgd2UgY2FuIGluY2x1ZGUgdGhlIHNlYXJjaCBwYXJhbSBpbiB0aGUga2V5LFxuICAgIC8vIGFzIHdlJ2xsIGJlIGdldHRpbmcgYmFjayBhIGZ1bGwgcmVzcG9uc2UuIFRoZSBzZXJ2ZXIgbWlnaHQgaGF2ZSByZWFkIHRoZSBzZWFyY2hcbiAgICAvLyBwYXJhbXMgd2hlbiBnZW5lcmF0aW5nIHRoZSBmdWxsIHJlc3BvbnNlLlxuICAgIHBhdGhuYW1lRnJvbVVybCArPSB1cmwuc2VhcmNoXG4gIH1cblxuICBpZiAocHJlZml4KSB7XG4gICAgcmV0dXJuIGAke3ByZWZpeH0ke0lOVEVSQ0VQVElPTl9DQUNIRV9LRVlfTUFSS0VSfSR7cGF0aG5hbWVGcm9tVXJsfWBcbiAgfVxuXG4gIHJldHVybiBwYXRobmFtZUZyb21Vcmxcbn1cblxuZnVuY3Rpb24gY3JlYXRlUHJlZmV0Y2hDYWNoZUtleShcbiAgdXJsOiBVUkwsXG4gIGtpbmQ6IFByZWZldGNoS2luZCB8IHVuZGVmaW5lZCxcbiAgbmV4dFVybD86IHN0cmluZyB8IG51bGxcbikge1xuICByZXR1cm4gY3JlYXRlUHJlZmV0Y2hDYWNoZUtleUltcGwodXJsLCBraW5kID09PSBQcmVmZXRjaEtpbmQuRlVMTCwgbmV4dFVybClcbn1cblxuZnVuY3Rpb24gZ2V0RXhpc3RpbmdDYWNoZUVudHJ5KFxuICB1cmw6IFVSTCxcbiAga2luZDogUHJlZmV0Y2hLaW5kID0gUHJlZmV0Y2hLaW5kLlRFTVBPUkFSWSxcbiAgbmV4dFVybDogc3RyaW5nIHwgbnVsbCxcbiAgcHJlZmV0Y2hDYWNoZTogTWFwPHN0cmluZywgUHJlZmV0Y2hDYWNoZUVudHJ5PixcbiAgYWxsb3dBbGlhc2luZzogYm9vbGVhblxuKTogQWxpYXNlZFByZWZldGNoQ2FjaGVFbnRyeSB8IHVuZGVmaW5lZCB7XG4gIC8vIFdlIGZpcnN0IGNoZWNrIGlmIHRoZXJlJ3MgYSBtb3JlIHNwZWNpZmljIGludGVyY2VwdGlvbiByb3V0ZSBwcmVmZXRjaCBlbnRyeVxuICAvLyBUaGlzIGlzIGJlY2F1c2Ugd2hlbiB3ZSBkZXRlY3QgYSBwcmVmZXRjaCB0aGF0IGNvcnJlc3BvbmRzIHdpdGggYW4gaW50ZXJjZXB0aW9uIHJvdXRlLCB3ZSBwcmVmaXggaXQgd2l0aCBuZXh0VXJsIChzZWUgYGNyZWF0ZVByZWZldGNoQ2FjaGVLZXlgKVxuICAvLyB0byBhdm9pZCBjb25mbGljdHMgd2l0aCBvdGhlciBwYWdlcyB0aGF0IG1heSBoYXZlIHRoZSBzYW1lIFVSTCBidXQgcmVuZGVyIGRpZmZlcmVudCB0aGluZ3MgZGVwZW5kaW5nIG9uIHRoZSBgTmV4dC1VUkxgIGhlYWRlci5cbiAgZm9yIChjb25zdCBtYXliZU5leHRVcmwgb2YgW25leHRVcmwsIG51bGxdKSB7XG4gICAgY29uc3QgY2FjaGVLZXlXaXRoUGFyYW1zID0gY3JlYXRlUHJlZmV0Y2hDYWNoZUtleUltcGwoXG4gICAgICB1cmwsXG4gICAgICB0cnVlLFxuICAgICAgbWF5YmVOZXh0VXJsXG4gICAgKVxuICAgIGNvbnN0IGNhY2hlS2V5V2l0aG91dFBhcmFtcyA9IGNyZWF0ZVByZWZldGNoQ2FjaGVLZXlJbXBsKFxuICAgICAgdXJsLFxuICAgICAgZmFsc2UsXG4gICAgICBtYXliZU5leHRVcmxcbiAgICApXG5cbiAgICAvLyBGaXJzdCwgd2UgY2hlY2sgaWYgd2UgaGF2ZSBhIGNhY2hlIGVudHJ5IHRoYXQgZXhhY3RseSBtYXRjaGVzIHRoZSBVUkxcbiAgICBjb25zdCBjYWNoZUtleVRvVXNlID0gdXJsLnNlYXJjaFxuICAgICAgPyBjYWNoZUtleVdpdGhQYXJhbXNcbiAgICAgIDogY2FjaGVLZXlXaXRob3V0UGFyYW1zXG5cbiAgICBjb25zdCBleGlzdGluZ0VudHJ5ID0gcHJlZmV0Y2hDYWNoZS5nZXQoY2FjaGVLZXlUb1VzZSlcbiAgICBpZiAoZXhpc3RpbmdFbnRyeSAmJiBhbGxvd0FsaWFzaW5nKSB7XG4gICAgICAvLyBXZSBrbm93IHdlJ3JlIHJldHVybmluZyBhbiBhbGlhc2VkIGVudHJ5IHdoZW4gdGhlIHBhdGhuYW1lIG1hdGNoZXMgYnV0IHRoZSBzZWFyY2ggcGFyYW1zIGRvbid0LFxuICAgICAgY29uc3QgaXNBbGlhc2VkID1cbiAgICAgICAgZXhpc3RpbmdFbnRyeS51cmwucGF0aG5hbWUgPT09IHVybC5wYXRobmFtZSAmJlxuICAgICAgICBleGlzdGluZ0VudHJ5LnVybC5zZWFyY2ggIT09IHVybC5zZWFyY2hcblxuICAgICAgaWYgKGlzQWxpYXNlZCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIC4uLmV4aXN0aW5nRW50cnksXG4gICAgICAgICAgYWxpYXNlZDogdHJ1ZSxcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZXhpc3RpbmdFbnRyeVxuICAgIH1cblxuICAgIC8vIElmIHRoZSByZXF1ZXN0IGNvbnRhaW5zIHNlYXJjaCBwYXJhbXMsIGFuZCB3ZSdyZSBub3QgZG9pbmcgYSBmdWxsIHByZWZldGNoLCB3ZSBjYW4gcmV0dXJuIHRoZVxuICAgIC8vIHBhcmFtLWxlc3MgZW50cnkgaWYgaXQgZXhpc3RzLlxuICAgIC8vIFRoaXMgaXMgdGVjaG5pY2FsbHkgY292ZXJlZCBieSB0aGUgY2hlY2sgYXQgdGhlIGJvdHRvbSBvZiB0aGlzIGZ1bmN0aW9uLCB3aGljaCBpdGVyYXRlcyBvdmVyIGNhY2hlIGVudHJpZXMsXG4gICAgLy8gYnV0IGxldHMgdXMgYXJyaXZlIHRoZXJlIHF1aWNrZXIgaW4gdGhlIHBhcmFtLWZ1bGwgY2FzZS5cbiAgICBjb25zdCBlbnRyeVdpdGhvdXRQYXJhbXMgPSBwcmVmZXRjaENhY2hlLmdldChjYWNoZUtleVdpdGhvdXRQYXJhbXMpXG4gICAgaWYgKFxuICAgICAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcgJiZcbiAgICAgIGFsbG93QWxpYXNpbmcgJiZcbiAgICAgIHVybC5zZWFyY2ggJiZcbiAgICAgIGtpbmQgIT09IFByZWZldGNoS2luZC5GVUxMICYmXG4gICAgICBlbnRyeVdpdGhvdXRQYXJhbXMgJiZcbiAgICAgIC8vIFdlIHNob3VsZG4ndCByZXR1cm4gdGhlIGFsaWFzZWQgZW50cnkgaWYgaXQgd2FzIHJlbG9jYXRlZCB0byBhIG5ldyBjYWNoZSBrZXkuXG4gICAgICAvLyBTaW5jZSBpdCdzIHJld3JpdHRlbiwgaXQgY291bGQgcmVzcG9uZCB3aXRoIGEgY29tcGxldGVseSBkaWZmZXJlbnQgbG9hZGluZyBzdGF0ZS5cbiAgICAgICFlbnRyeVdpdGhvdXRQYXJhbXMua2V5LmluY2x1ZGVzKElOVEVSQ0VQVElPTl9DQUNIRV9LRVlfTUFSS0VSKVxuICAgICkge1xuICAgICAgcmV0dXJuIHsgLi4uZW50cnlXaXRob3V0UGFyYW1zLCBhbGlhc2VkOiB0cnVlIH1cbiAgICB9XG4gIH1cblxuICAvLyBJZiB3ZSd2ZSBnb3R0ZW4gdG8gdGhpcyBwb2ludCwgd2UgZGlkbid0IGZpbmQgYSBzcGVjaWZpYyBjYWNoZSBlbnRyeSB0aGF0IG1hdGNoZWRcbiAgLy8gdGhlIHJlcXVlc3QgVVJMLlxuICAvLyBXZSBhdHRlbXB0IGEgcGFydGlhbCBtYXRjaCBieSBjaGVja2luZyBpZiB0aGVyZSdzIGEgY2FjaGUgZW50cnkgd2l0aCB0aGUgc2FtZSBwYXRobmFtZS5cbiAgLy8gUmVnYXJkbGVzcyBvZiB3aGF0IHdlIGZpbmQsIHNpbmNlIGl0IGRvZXNuJ3QgY29ycmVzcG9uZCB3aXRoIHRoZSByZXF1ZXN0ZWQgVVJMLCB3ZSdsbCBtYXJrIGl0IFwiYWxpYXNlZFwiLlxuICAvLyBUaGlzIHdpbGwgc2lnbmFsIHRvIHRoZSByb3V0ZXIgdGhhdCBpdCBzaG91bGQgb25seSBhcHBseSB0aGUgbG9hZGluZyBzdGF0ZSBvbiB0aGUgcHJlZmV0Y2hlZCBkYXRhLlxuICBpZiAoXG4gICAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcgJiZcbiAgICBraW5kICE9PSBQcmVmZXRjaEtpbmQuRlVMTCAmJlxuICAgIGFsbG93QWxpYXNpbmdcbiAgKSB7XG4gICAgZm9yIChjb25zdCBjYWNoZUVudHJ5IG9mIHByZWZldGNoQ2FjaGUudmFsdWVzKCkpIHtcbiAgICAgIGlmIChcbiAgICAgICAgY2FjaGVFbnRyeS51cmwucGF0aG5hbWUgPT09IHVybC5wYXRobmFtZSAmJlxuICAgICAgICAvLyBXZSBzaG91bGRuJ3QgcmV0dXJuIHRoZSBhbGlhc2VkIGVudHJ5IGlmIGl0IHdhcyByZWxvY2F0ZWQgdG8gYSBuZXcgY2FjaGUga2V5LlxuICAgICAgICAvLyBTaW5jZSBpdCdzIHJld3JpdHRlbiwgaXQgY291bGQgcmVzcG9uZCB3aXRoIGEgY29tcGxldGVseSBkaWZmZXJlbnQgbG9hZGluZyBzdGF0ZS5cbiAgICAgICAgIWNhY2hlRW50cnkua2V5LmluY2x1ZGVzKElOVEVSQ0VQVElPTl9DQUNIRV9LRVlfTUFSS0VSKVxuICAgICAgKSB7XG4gICAgICAgIHJldHVybiB7IC4uLmNhY2hlRW50cnksIGFsaWFzZWQ6IHRydWUgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB1bmRlZmluZWRcbn1cblxuLyoqXG4gKiBSZXR1cm5zIGEgcHJlZmV0Y2ggY2FjaGUgZW50cnkgaWYgb25lIGV4aXN0cy4gT3RoZXJ3aXNlIGNyZWF0ZXMgYSBuZXcgb25lIGFuZCBlbnF1ZXVlcyBhIGZldGNoIHJlcXVlc3RcbiAqIHRvIHJldHJpZXZlIHRoZSBwcmVmZXRjaCBkYXRhIGZyb20gdGhlIHNlcnZlci5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldE9yQ3JlYXRlUHJlZmV0Y2hDYWNoZUVudHJ5KHtcbiAgdXJsLFxuICBuZXh0VXJsLFxuICB0cmVlLFxuICBwcmVmZXRjaENhY2hlLFxuICBraW5kLFxuICBhbGxvd0FsaWFzaW5nID0gdHJ1ZSxcbn06IFBpY2s8UmVhZG9ubHlSZWR1Y2VyU3RhdGUsICduZXh0VXJsJyB8ICdwcmVmZXRjaENhY2hlJyB8ICd0cmVlJz4gJiB7XG4gIHVybDogVVJMXG4gIGtpbmQ/OiBQcmVmZXRjaEtpbmRcbiAgYWxsb3dBbGlhc2luZzogYm9vbGVhblxufSk6IEFsaWFzZWRQcmVmZXRjaENhY2hlRW50cnkge1xuICBjb25zdCBleGlzdGluZ0NhY2hlRW50cnkgPSBnZXRFeGlzdGluZ0NhY2hlRW50cnkoXG4gICAgdXJsLFxuICAgIGtpbmQsXG4gICAgbmV4dFVybCxcbiAgICBwcmVmZXRjaENhY2hlLFxuICAgIGFsbG93QWxpYXNpbmdcbiAgKVxuXG4gIGlmIChleGlzdGluZ0NhY2hlRW50cnkpIHtcbiAgICAvLyBHcmFiIHRoZSBsYXRlc3Qgc3RhdHVzIG9mIHRoZSBjYWNoZSBlbnRyeSBhbmQgdXBkYXRlIGl0XG4gICAgZXhpc3RpbmdDYWNoZUVudHJ5LnN0YXR1cyA9IGdldFByZWZldGNoRW50cnlDYWNoZVN0YXR1cyhleGlzdGluZ0NhY2hlRW50cnkpXG5cbiAgICAvLyB3aGVuIGBraW5kYCBpcyBwcm92aWRlZCwgYW4gZXhwbGljaXQgcHJlZmV0Y2ggd2FzIHJlcXVlc3RlZC5cbiAgICAvLyBpZiB0aGUgcmVxdWVzdGVkIHByZWZldGNoIGlzIFwiZnVsbFwiIGFuZCB0aGUgY3VycmVudCBjYWNoZSBlbnRyeSB3YXNuJ3QsIHdlIHdhbnQgdG8gcmUtcHJlZmV0Y2ggd2l0aCB0aGUgbmV3IGludGVudFxuICAgIGNvbnN0IHN3aXRjaGVkVG9GdWxsUHJlZmV0Y2ggPVxuICAgICAgZXhpc3RpbmdDYWNoZUVudHJ5LmtpbmQgIT09IFByZWZldGNoS2luZC5GVUxMICYmXG4gICAgICBraW5kID09PSBQcmVmZXRjaEtpbmQuRlVMTFxuXG4gICAgaWYgKHN3aXRjaGVkVG9GdWxsUHJlZmV0Y2gpIHtcbiAgICAgIC8vIElmIHdlIHN3aXRjaGVkIHRvIGEgZnVsbCBwcmVmZXRjaCwgdmFsaWRhdGUgdGhhdCB0aGUgZXhpc3RpbmcgY2FjaGUgZW50cnkgY29udGFpbmVkIHBhcnRpYWwgZGF0YS5cbiAgICAgIC8vIEl0J3MgcG9zc2libGUgdGhhdCB0aGUgY2FjaGUgZW50cnkgd2FzIHNlZWRlZCB3aXRoIGZ1bGwgZGF0YSBidXQgaGFzIGEgY2FjaGUgdHlwZSBvZiBcImF1dG9cIiAoaWUgd2hlbiBjYWNoZSBlbnRyaWVzXG4gICAgICAvLyBhcmUgc2VlZGVkIGJ1dCB3aXRob3V0IGEgcHJlZmV0Y2ggaW50ZW50KVxuICAgICAgZXhpc3RpbmdDYWNoZUVudHJ5LmRhdGEudGhlbigocHJlZmV0Y2hSZXNwb25zZSkgPT4ge1xuICAgICAgICBjb25zdCBpc0Z1bGxQcmVmZXRjaCA9XG4gICAgICAgICAgQXJyYXkuaXNBcnJheShwcmVmZXRjaFJlc3BvbnNlLmZsaWdodERhdGEpICYmXG4gICAgICAgICAgcHJlZmV0Y2hSZXNwb25zZS5mbGlnaHREYXRhLnNvbWUoKGZsaWdodERhdGEpID0+IHtcbiAgICAgICAgICAgIC8vIElmIHdlIHN0YXJ0ZWQgcmVuZGVyaW5nIGZyb20gdGhlIHJvb3QgYW5kIHdlIHJldHVybmVkIFJTQyBkYXRhIChzZWVkRGF0YSksIHdlIGFscmVhZHkgaGFkIGEgZnVsbCBwcmVmZXRjaC5cbiAgICAgICAgICAgIHJldHVybiBmbGlnaHREYXRhLmlzUm9vdFJlbmRlciAmJiBmbGlnaHREYXRhLnNlZWREYXRhICE9PSBudWxsXG4gICAgICAgICAgfSlcblxuICAgICAgICBpZiAoIWlzRnVsbFByZWZldGNoKSB7XG4gICAgICAgICAgcmV0dXJuIGNyZWF0ZUxhenlQcmVmZXRjaEVudHJ5KHtcbiAgICAgICAgICAgIHRyZWUsXG4gICAgICAgICAgICB1cmwsXG4gICAgICAgICAgICBuZXh0VXJsLFxuICAgICAgICAgICAgcHJlZmV0Y2hDYWNoZSxcbiAgICAgICAgICAgIC8vIElmIHdlIGRpZG4ndCBnZXQgYW4gZXhwbGljaXQgcHJlZmV0Y2gga2luZCwgd2Ugd2FudCB0byBzZXQgYSB0ZW1wb3Jhcnkga2luZFxuICAgICAgICAgICAgLy8gcmF0aGVyIHRoYW4gYXNzdW1pbmcgdGhlIHNhbWUgaW50ZW50IGFzIHRoZSBwcmV2aW91cyBlbnRyeSwgdG8gYmUgY29uc2lzdGVudCB3aXRoIGhvdyB3ZVxuICAgICAgICAgICAgLy8gbGF6aWx5IGNyZWF0ZSBwcmVmZXRjaCBlbnRyaWVzIHdoZW4gaW50ZW50IGlzIGxlZnQgdW5zcGVjaWZpZWQuXG4gICAgICAgICAgICBraW5kOiBraW5kID8/IFByZWZldGNoS2luZC5URU1QT1JBUlksXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyBJZiB0aGUgZXhpc3RpbmcgY2FjaGUgZW50cnkgd2FzIG1hcmtlZCBhcyB0ZW1wb3JhcnksIGl0IG1lYW5zIGl0IHdhcyBsYXppbHkgY3JlYXRlZCB3aGVuIGF0dGVtcHRpbmcgdG8gZ2V0IGFuIGVudHJ5LFxuICAgIC8vIHdoZXJlIHdlIGRpZG4ndCBoYXZlIHRoZSBwcmVmZXRjaCBpbnRlbnQuIE5vdyB0aGF0IHdlIGhhdmUgdGhlIGludGVudCAoaW4gYGtpbmRgKSwgd2Ugd2FudCB0byB1cGRhdGUgdGhlIGVudHJ5IHRvIHRoZSBtb3JlIGFjY3VyYXRlIGtpbmQuXG4gICAgaWYgKGtpbmQgJiYgZXhpc3RpbmdDYWNoZUVudHJ5LmtpbmQgPT09IFByZWZldGNoS2luZC5URU1QT1JBUlkpIHtcbiAgICAgIGV4aXN0aW5nQ2FjaGVFbnRyeS5raW5kID0ga2luZFxuICAgIH1cblxuICAgIC8vIFdlJ3ZlIGRldGVybWluZWQgdGhhdCB0aGUgZXhpc3RpbmcgZW50cnkgd2UgZm91bmQgaXMgc3RpbGwgdmFsaWQsIHNvIHdlIHJldHVybiBpdC5cbiAgICByZXR1cm4gZXhpc3RpbmdDYWNoZUVudHJ5XG4gIH1cblxuICAvLyBJZiB3ZSBkaWRuJ3QgcmV0dXJuIGFuIGVudHJ5LCBjcmVhdGUgYSBuZXcgb25lLlxuICByZXR1cm4gY3JlYXRlTGF6eVByZWZldGNoRW50cnkoe1xuICAgIHRyZWUsXG4gICAgdXJsLFxuICAgIG5leHRVcmwsXG4gICAgcHJlZmV0Y2hDYWNoZSxcbiAgICBraW5kOiBraW5kIHx8IFByZWZldGNoS2luZC5URU1QT1JBUlksXG4gIH0pXG59XG5cbi8qXG4gKiBVc2VkIHRvIHRha2UgYW4gZXhpc3RpbmcgY2FjaGUgZW50cnkgYW5kIHByZWZpeCBpdCB3aXRoIHRoZSBuZXh0VXJsLCBpZiBpdCBleGlzdHMuXG4gKiBUaGlzIGVuc3VyZXMgdGhhdCB3ZSBkb24ndCBoYXZlIGNvbmZsaWN0aW5nIGNhY2hlIGVudHJpZXMgZm9yIHRoZSBzYW1lIFVSTCAoYXMgaXMgdGhlIGNhc2Ugd2l0aCByb3V0ZSBpbnRlcmNlcHRpb24pLlxuICovXG5mdW5jdGlvbiBwcmVmaXhFeGlzdGluZ1ByZWZldGNoQ2FjaGVFbnRyeSh7XG4gIHVybCxcbiAgbmV4dFVybCxcbiAgcHJlZmV0Y2hDYWNoZSxcbiAgZXhpc3RpbmdDYWNoZUtleSxcbn06IFBpY2s8UmVhZG9ubHlSZWR1Y2VyU3RhdGUsICduZXh0VXJsJyB8ICdwcmVmZXRjaENhY2hlJz4gJiB7XG4gIHVybDogVVJMXG4gIGV4aXN0aW5nQ2FjaGVLZXk6IHN0cmluZ1xufSkge1xuICBjb25zdCBleGlzdGluZ0NhY2hlRW50cnkgPSBwcmVmZXRjaENhY2hlLmdldChleGlzdGluZ0NhY2hlS2V5KVxuICBpZiAoIWV4aXN0aW5nQ2FjaGVFbnRyeSkge1xuICAgIC8vIG5vLW9wIC0tIHRoZXJlIHdhc24ndCBhbiBlbnRyeSB0byBtb3ZlXG4gICAgcmV0dXJuXG4gIH1cblxuICBjb25zdCBuZXdDYWNoZUtleSA9IGNyZWF0ZVByZWZldGNoQ2FjaGVLZXkoXG4gICAgdXJsLFxuICAgIGV4aXN0aW5nQ2FjaGVFbnRyeS5raW5kLFxuICAgIG5leHRVcmxcbiAgKVxuICBwcmVmZXRjaENhY2hlLnNldChuZXdDYWNoZUtleSwgeyAuLi5leGlzdGluZ0NhY2hlRW50cnksIGtleTogbmV3Q2FjaGVLZXkgfSlcbiAgcHJlZmV0Y2hDYWNoZS5kZWxldGUoZXhpc3RpbmdDYWNoZUtleSlcblxuICByZXR1cm4gbmV3Q2FjaGVLZXlcbn1cblxuLyoqXG4gKiBVc2UgdG8gc2VlZCB0aGUgcHJlZmV0Y2ggY2FjaGUgd2l0aCBkYXRhIHRoYXQgaGFzIGFscmVhZHkgYmVlbiBmZXRjaGVkLlxuICovXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlU2VlZGVkUHJlZmV0Y2hDYWNoZUVudHJ5KHtcbiAgbmV4dFVybCxcbiAgdHJlZSxcbiAgcHJlZmV0Y2hDYWNoZSxcbiAgdXJsLFxuICBkYXRhLFxuICBraW5kLFxufTogUGljazxSZWFkb25seVJlZHVjZXJTdGF0ZSwgJ25leHRVcmwnIHwgJ3RyZWUnIHwgJ3ByZWZldGNoQ2FjaGUnPiAmIHtcbiAgdXJsOiBVUkxcbiAgZGF0YTogRmV0Y2hTZXJ2ZXJSZXNwb25zZVJlc3VsdFxuICBraW5kOiBQcmVmZXRjaEtpbmRcbn0pIHtcbiAgLy8gVGhlIGluaXRpYWwgY2FjaGUgZW50cnkgdGVjaG5pY2FsbHkgaW5jbHVkZXMgZnVsbCBkYXRhLCBidXQgaXQgaXNuJ3QgZXhwbGljaXRseSBwcmVmZXRjaGVkIC0tIHdlIGp1c3Qgc2VlZCB0aGVcbiAgLy8gcHJlZmV0Y2ggY2FjaGUgc28gdGhhdCB3ZSBjYW4gc2tpcCBhbiBleHRyYSBwcmVmZXRjaCByZXF1ZXN0IGxhdGVyLCBzaW5jZSB3ZSBhbHJlYWR5IGhhdmUgdGhlIGRhdGEuXG4gIC8vIGlmIHRoZSBwcmVmZXRjaCBjb3JyZXNwb25kcyB3aXRoIGFuIGludGVyY2VwdGlvbiByb3V0ZSwgd2UgdXNlIHRoZSBuZXh0VXJsIHRvIHByZWZpeCB0aGUgY2FjaGUga2V5XG4gIGNvbnN0IHByZWZldGNoQ2FjaGVLZXkgPSBkYXRhLmNvdWxkQmVJbnRlcmNlcHRlZFxuICAgID8gY3JlYXRlUHJlZmV0Y2hDYWNoZUtleSh1cmwsIGtpbmQsIG5leHRVcmwpXG4gICAgOiBjcmVhdGVQcmVmZXRjaENhY2hlS2V5KHVybCwga2luZClcblxuICBjb25zdCBwcmVmZXRjaEVudHJ5ID0ge1xuICAgIHRyZWVBdFRpbWVPZlByZWZldGNoOiB0cmVlLFxuICAgIGRhdGE6IFByb21pc2UucmVzb2x2ZShkYXRhKSxcbiAgICBraW5kLFxuICAgIHByZWZldGNoVGltZTogRGF0ZS5ub3coKSxcbiAgICBsYXN0VXNlZFRpbWU6IERhdGUubm93KCksXG4gICAgc3RhbGVUaW1lOiAtMSxcbiAgICBrZXk6IHByZWZldGNoQ2FjaGVLZXksXG4gICAgc3RhdHVzOiBQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMuZnJlc2gsXG4gICAgdXJsLFxuICB9IHNhdGlzZmllcyBQcmVmZXRjaENhY2hlRW50cnlcblxuICBwcmVmZXRjaENhY2hlLnNldChwcmVmZXRjaENhY2hlS2V5LCBwcmVmZXRjaEVudHJ5KVxuXG4gIHJldHVybiBwcmVmZXRjaEVudHJ5XG59XG5cbi8qKlxuICogQ3JlYXRlcyBhIHByZWZldGNoIGVudHJ5IGVudHJ5IGFuZCBlbnF1ZXVlcyBhIGZldGNoIHJlcXVlc3QgdG8gcmV0cmlldmUgdGhlIGRhdGEuXG4gKi9cbmZ1bmN0aW9uIGNyZWF0ZUxhenlQcmVmZXRjaEVudHJ5KHtcbiAgdXJsLFxuICBraW5kLFxuICB0cmVlLFxuICBuZXh0VXJsLFxuICBwcmVmZXRjaENhY2hlLFxufTogUGljazxSZWFkb25seVJlZHVjZXJTdGF0ZSwgJ25leHRVcmwnIHwgJ3RyZWUnIHwgJ3ByZWZldGNoQ2FjaGUnPiAmIHtcbiAgdXJsOiBVUkxcbiAga2luZDogUHJlZmV0Y2hLaW5kXG59KTogUHJlZmV0Y2hDYWNoZUVudHJ5IHtcbiAgY29uc3QgcHJlZmV0Y2hDYWNoZUtleSA9IGNyZWF0ZVByZWZldGNoQ2FjaGVLZXkodXJsLCBraW5kKVxuXG4gIC8vIGluaXRpYXRlcyB0aGUgZmV0Y2ggcmVxdWVzdCBmb3IgdGhlIHByZWZldGNoIGFuZCBhdHRhY2hlcyBhIGxpc3RlbmVyXG4gIC8vIHRvIHRoZSBwcm9taXNlIHRvIHVwZGF0ZSB0aGUgcHJlZmV0Y2ggY2FjaGUgZW50cnkgd2hlbiB0aGUgcHJvbWlzZSByZXNvbHZlcyAoaWYgbmVjZXNzYXJ5KVxuICBjb25zdCBkYXRhID0gcHJlZmV0Y2hRdWV1ZS5lbnF1ZXVlKCgpID0+XG4gICAgZmV0Y2hTZXJ2ZXJSZXNwb25zZSh1cmwsIHtcbiAgICAgIGZsaWdodFJvdXRlclN0YXRlOiB0cmVlLFxuICAgICAgbmV4dFVybCxcbiAgICAgIHByZWZldGNoS2luZDoga2luZCxcbiAgICB9KS50aGVuKChwcmVmZXRjaFJlc3BvbnNlKSA9PiB7XG4gICAgICAvLyBUT0RPOiBgZmV0Y2hTZXJ2ZXJSZXNwb25zZWAgc2hvdWxkIGJlIG1vcmUgdGlnaGx5IGNvdXBsZWQgdG8gdGhlc2UgcHJlZmV0Y2ggY2FjaGUgb3BlcmF0aW9uc1xuICAgICAgLy8gdG8gYXZvaWQgZHJpZnQgYmV0d2VlbiB0aGlzIGNhY2hlIGtleSBwcmVmaXhpbmcgbG9naWNcbiAgICAgIC8vICh3aGljaCBpcyBjdXJyZW50bHkgZGlyZWN0bHkgaW5mbHVlbmNlZCBieSB0aGUgc2VydmVyIHJlc3BvbnNlKVxuICAgICAgbGV0IG5ld0NhY2hlS2V5XG5cbiAgICAgIGlmIChwcmVmZXRjaFJlc3BvbnNlLmNvdWxkQmVJbnRlcmNlcHRlZCkge1xuICAgICAgICAvLyBEZXRlcm1pbmUgaWYgd2UgbmVlZCB0byBwcmVmaXggdGhlIGNhY2hlIGtleSB3aXRoIHRoZSBuZXh0VXJsXG4gICAgICAgIG5ld0NhY2hlS2V5ID0gcHJlZml4RXhpc3RpbmdQcmVmZXRjaENhY2hlRW50cnkoe1xuICAgICAgICAgIHVybCxcbiAgICAgICAgICBleGlzdGluZ0NhY2hlS2V5OiBwcmVmZXRjaENhY2hlS2V5LFxuICAgICAgICAgIG5leHRVcmwsXG4gICAgICAgICAgcHJlZmV0Y2hDYWNoZSxcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgLy8gSWYgdGhlIHByZWZldGNoIHdhcyBhIGNhY2hlIGhpdCwgd2Ugd2FudCB0byB1cGRhdGUgdGhlIGV4aXN0aW5nIGNhY2hlIGVudHJ5IHRvIHJlZmxlY3QgdGhhdCBpdCB3YXMgYSBmdWxsIHByZWZldGNoLlxuICAgICAgLy8gVGhpcyBpcyBiZWNhdXNlIHdlIGtub3cgdGhhdCBhIHN0YXRpYyByZXNwb25zZSB3aWxsIGNvbnRhaW4gdGhlIGZ1bGwgUlNDIHBheWxvYWQsIGFuZCBjYW4gYmUgdXBkYXRlZCB0byByZXNwZWN0IHRoZSBgc3RhdGljYFxuICAgICAgLy8gc3RhbGVUaW1lLlxuICAgICAgaWYgKHByZWZldGNoUmVzcG9uc2UucHJlcmVuZGVyZWQpIHtcbiAgICAgICAgY29uc3QgZXhpc3RpbmdDYWNoZUVudHJ5ID0gcHJlZmV0Y2hDYWNoZS5nZXQoXG4gICAgICAgICAgLy8gaWYgd2UgcHJlZml4ZWQgdGhlIGNhY2hlIGtleSBkdWUgdG8gcm91dGUgaW50ZXJjZXB0aW9uLCB3ZSB3YW50IHRvIHVzZSB0aGUgbmV3IGtleS4gT3RoZXJ3aXNlIHdlIHVzZSB0aGUgb3JpZ2luYWwga2V5XG4gICAgICAgICAgbmV3Q2FjaGVLZXkgPz8gcHJlZmV0Y2hDYWNoZUtleVxuICAgICAgICApXG4gICAgICAgIGlmIChleGlzdGluZ0NhY2hlRW50cnkpIHtcbiAgICAgICAgICBleGlzdGluZ0NhY2hlRW50cnkua2luZCA9IFByZWZldGNoS2luZC5GVUxMXG4gICAgICAgICAgaWYgKHByZWZldGNoUmVzcG9uc2Uuc3RhbGVUaW1lICE9PSAtMSkge1xuICAgICAgICAgICAgLy8gVGhpcyBpcyB0aGUgc3RhbGUgdGltZSB0aGF0IHdhcyBjb2xsZWN0ZWQgYnkgdGhlIHNlcnZlciBkdXJpbmdcbiAgICAgICAgICAgIC8vIHN0YXRpYyBnZW5lcmF0aW9uLiBVc2UgdGhpcyBpbiBwbGFjZSBvZiB0aGUgZGVmYXVsdCBzdGFsZSB0aW1lLlxuICAgICAgICAgICAgZXhpc3RpbmdDYWNoZUVudHJ5LnN0YWxlVGltZSA9IHByZWZldGNoUmVzcG9uc2Uuc3RhbGVUaW1lXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBwcmVmZXRjaFJlc3BvbnNlXG4gICAgfSlcbiAgKVxuXG4gIGNvbnN0IHByZWZldGNoRW50cnkgPSB7XG4gICAgdHJlZUF0VGltZU9mUHJlZmV0Y2g6IHRyZWUsXG4gICAgZGF0YSxcbiAgICBraW5kLFxuICAgIHByZWZldGNoVGltZTogRGF0ZS5ub3coKSxcbiAgICBsYXN0VXNlZFRpbWU6IG51bGwsXG4gICAgc3RhbGVUaW1lOiAtMSxcbiAgICBrZXk6IHByZWZldGNoQ2FjaGVLZXksXG4gICAgc3RhdHVzOiBQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMuZnJlc2gsXG4gICAgdXJsLFxuICB9XG5cbiAgcHJlZmV0Y2hDYWNoZS5zZXQocHJlZmV0Y2hDYWNoZUtleSwgcHJlZmV0Y2hFbnRyeSlcblxuICByZXR1cm4gcHJlZmV0Y2hFbnRyeVxufVxuXG5leHBvcnQgZnVuY3Rpb24gcHJ1bmVQcmVmZXRjaENhY2hlKFxuICBwcmVmZXRjaENhY2hlOiBSZWFkb25seVJlZHVjZXJTdGF0ZVsncHJlZmV0Y2hDYWNoZSddXG4pIHtcbiAgZm9yIChjb25zdCBbaHJlZiwgcHJlZmV0Y2hDYWNoZUVudHJ5XSBvZiBwcmVmZXRjaENhY2hlKSB7XG4gICAgaWYgKFxuICAgICAgZ2V0UHJlZmV0Y2hFbnRyeUNhY2hlU3RhdHVzKHByZWZldGNoQ2FjaGVFbnRyeSkgPT09XG4gICAgICBQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMuZXhwaXJlZFxuICAgICkge1xuICAgICAgcHJlZmV0Y2hDYWNoZS5kZWxldGUoaHJlZilcbiAgICB9XG4gIH1cbn1cblxuLy8gVGhlc2UgdmFsdWVzIGFyZSBzZXQgYnkgYGRlZmluZS1lbnYtcGx1Z2luYCAoYmFzZWQgb24gYG5leHRDb25maWcuZXhwZXJpbWVudGFsLnN0YWxlVGltZXNgKVxuLy8gYW5kIGRlZmF1bHQgdG8gNSBtaW51dGVzIChzdGF0aWMpIC8gMCBzZWNvbmRzIChkeW5hbWljKVxuZXhwb3J0IGNvbnN0IERZTkFNSUNfU1RBTEVUSU1FX01TID1cbiAgTnVtYmVyKHByb2Nlc3MuZW52Ll9fTkVYVF9DTElFTlRfUk9VVEVSX0RZTkFNSUNfU1RBTEVUSU1FKSAqIDEwMDBcblxuZXhwb3J0IGNvbnN0IFNUQVRJQ19TVEFMRVRJTUVfTVMgPVxuICBOdW1iZXIocHJvY2Vzcy5lbnYuX19ORVhUX0NMSUVOVF9ST1VURVJfU1RBVElDX1NUQUxFVElNRSkgKiAxMDAwXG5cbmZ1bmN0aW9uIGdldFByZWZldGNoRW50cnlDYWNoZVN0YXR1cyh7XG4gIGtpbmQsXG4gIHByZWZldGNoVGltZSxcbiAgbGFzdFVzZWRUaW1lLFxuICBzdGFsZVRpbWUsXG59OiBQcmVmZXRjaENhY2hlRW50cnkpOiBQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMge1xuICBpZiAoc3RhbGVUaW1lICE9PSAtMSkge1xuICAgIC8vIGBzdGFsZVRpbWVgIGlzIHRoZSB2YWx1ZSBzZW50IGJ5IHRoZSBzZXJ2ZXIgZHVyaW5nIHN0YXRpYyBnZW5lcmF0aW9uLlxuICAgIC8vIFdoZW4gdGhpcyBpcyBhdmFpbGFibGUsIGl0IHRha2VzIHByZWNlZGVuY2Ugb3ZlciBhbnkgb2YgdGhlIGhldXJpc3RpY3NcbiAgICAvLyB0aGF0IGZvbGxvdy5cbiAgICAvL1xuICAgIC8vIFRPRE86IFdoZW4gUFBSIGlzIGVuYWJsZWQsIHRoZSBzZXJ2ZXIgd2lsbCAqYWx3YXlzKiByZXR1cm4gYSBzdGFsZSB0aW1lXG4gICAgLy8gd2hlbiBwcmVmZXRjaGluZy4gV2Ugc2hvdWxkIG5ldmVyIHVzZSBhIHByZWZldGNoIGVudHJ5IHRoYXQgaGFzbid0IHlldFxuICAgIC8vIHJlY2VpdmVkIGRhdGEgZnJvbSB0aGUgc2VydmVyLiBTbyB0aGUgb25seSB0d28gY2FzZXMgc2hvdWxkIGJlIDEpIHdlIHVzZVxuICAgIC8vIHRoZSBzZXJ2ZXItZ2VuZXJhdGVkIHN0YWxlIHRpbWUgMikgdGhlIHVucmVzb2x2ZWQgZW50cnkgaXMgZGlzY2FyZGVkLlxuICAgIHJldHVybiBEYXRlLm5vdygpIDwgcHJlZmV0Y2hUaW1lICsgc3RhbGVUaW1lXG4gICAgICA/IFByZWZldGNoQ2FjaGVFbnRyeVN0YXR1cy5mcmVzaFxuICAgICAgOiBQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMuc3RhbGVcbiAgfVxuXG4gIC8vIFdlIHdpbGwgcmUtdXNlIHRoZSBjYWNoZSBlbnRyeSBkYXRhIGZvciB1cCB0byB0aGUgYGR5bmFtaWNgIHN0YWxldGltZSB3aW5kb3cuXG4gIGlmIChEYXRlLm5vdygpIDwgKGxhc3RVc2VkVGltZSA/PyBwcmVmZXRjaFRpbWUpICsgRFlOQU1JQ19TVEFMRVRJTUVfTVMpIHtcbiAgICByZXR1cm4gbGFzdFVzZWRUaW1lXG4gICAgICA/IFByZWZldGNoQ2FjaGVFbnRyeVN0YXR1cy5yZXVzYWJsZVxuICAgICAgOiBQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMuZnJlc2hcbiAgfVxuXG4gIC8vIEZvciBcImF1dG9cIiBwcmVmZXRjaGluZywgd2UnbGwgcmUtdXNlIG9ubHkgdGhlIGxvYWRpbmcgYm91bmRhcnkgZm9yIHVwIHRvIGBzdGF0aWNgIHN0YWxldGltZSB3aW5kb3cuXG4gIC8vIEEgc3RhbGUgZW50cnkgd2lsbCBvbmx5IHJlLXVzZSB0aGUgYGxvYWRpbmdgIGJvdW5kYXJ5LCBub3QgdGhlIGZ1bGwgZGF0YS5cbiAgLy8gVGhpcyB3aWxsIHRyaWdnZXIgYSBcImxhenkgZmV0Y2hcIiBmb3IgdGhlIGZ1bGwgZGF0YS5cbiAgaWYgKGtpbmQgPT09IFByZWZldGNoS2luZC5BVVRPKSB7XG4gICAgaWYgKERhdGUubm93KCkgPCBwcmVmZXRjaFRpbWUgKyBTVEFUSUNfU1RBTEVUSU1FX01TKSB7XG4gICAgICByZXR1cm4gUHJlZmV0Y2hDYWNoZUVudHJ5U3RhdHVzLnN0YWxlXG4gICAgfVxuICB9XG5cbiAgLy8gZm9yIFwiZnVsbFwiIHByZWZldGNoaW5nLCB3ZSdsbCByZS11c2UgdGhlIGNhY2hlIGVudHJ5IGRhdGEgZm9yIHVwIHRvIGBzdGF0aWNgIHN0YWxldGltZSB3aW5kb3cuXG4gIGlmIChraW5kID09PSBQcmVmZXRjaEtpbmQuRlVMTCkge1xuICAgIGlmIChEYXRlLm5vdygpIDwgcHJlZmV0Y2hUaW1lICsgU1RBVElDX1NUQUxFVElNRV9NUykge1xuICAgICAgcmV0dXJuIFByZWZldGNoQ2FjaGVFbnRyeVN0YXR1cy5yZXVzYWJsZVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMuZXhwaXJlZFxufVxuIl0sIm5hbWVzIjpbIkRZTkFNSUNfU1RBTEVUSU1FX01TIiwiU1RBVElDX1NUQUxFVElNRV9NUyIsImNyZWF0ZVNlZWRlZFByZWZldGNoQ2FjaGVFbnRyeSIsImdldE9yQ3JlYXRlUHJlZmV0Y2hDYWNoZUVudHJ5IiwicHJ1bmVQcmVmZXRjaENhY2hlIiwiSU5URVJDRVBUSU9OX0NBQ0hFX0tFWV9NQVJLRVIiLCJjcmVhdGVQcmVmZXRjaENhY2hlS2V5SW1wbCIsInVybCIsImluY2x1ZGVTZWFyY2hQYXJhbXMiLCJwcmVmaXgiLCJwYXRobmFtZUZyb21VcmwiLCJwYXRobmFtZSIsInNlYXJjaCIsImNyZWF0ZVByZWZldGNoQ2FjaGVLZXkiLCJraW5kIiwibmV4dFVybCIsIlByZWZldGNoS2luZCIsIkZVTEwiLCJnZXRFeGlzdGluZ0NhY2hlRW50cnkiLCJwcmVmZXRjaENhY2hlIiwiYWxsb3dBbGlhc2luZyIsIlRFTVBPUkFSWSIsIm1heWJlTmV4dFVybCIsImNhY2hlS2V5V2l0aFBhcmFtcyIsImNhY2hlS2V5V2l0aG91dFBhcmFtcyIsImNhY2hlS2V5VG9Vc2UiLCJleGlzdGluZ0VudHJ5IiwiZ2V0IiwiaXNBbGlhc2VkIiwiYWxpYXNlZCIsImVudHJ5V2l0aG91dFBhcmFtcyIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImtleSIsImluY2x1ZGVzIiwiY2FjaGVFbnRyeSIsInZhbHVlcyIsInVuZGVmaW5lZCIsInRyZWUiLCJleGlzdGluZ0NhY2hlRW50cnkiLCJzdGF0dXMiLCJnZXRQcmVmZXRjaEVudHJ5Q2FjaGVTdGF0dXMiLCJzd2l0Y2hlZFRvRnVsbFByZWZldGNoIiwiZGF0YSIsInRoZW4iLCJwcmVmZXRjaFJlc3BvbnNlIiwiaXNGdWxsUHJlZmV0Y2giLCJBcnJheSIsImlzQXJyYXkiLCJmbGlnaHREYXRhIiwic29tZSIsImlzUm9vdFJlbmRlciIsInNlZWREYXRhIiwiY3JlYXRlTGF6eVByZWZldGNoRW50cnkiLCJwcmVmaXhFeGlzdGluZ1ByZWZldGNoQ2FjaGVFbnRyeSIsImV4aXN0aW5nQ2FjaGVLZXkiLCJuZXdDYWNoZUtleSIsInNldCIsImRlbGV0ZSIsInByZWZldGNoQ2FjaGVLZXkiLCJjb3VsZEJlSW50ZXJjZXB0ZWQiLCJwcmVmZXRjaEVudHJ5IiwidHJlZUF0VGltZU9mUHJlZmV0Y2giLCJQcm9taXNlIiwicmVzb2x2ZSIsInByZWZldGNoVGltZSIsIkRhdGUiLCJub3ciLCJsYXN0VXNlZFRpbWUiLCJzdGFsZVRpbWUiLCJQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMiLCJmcmVzaCIsInByZWZldGNoUXVldWUiLCJlbnF1ZXVlIiwiZmV0Y2hTZXJ2ZXJSZXNwb25zZSIsImZsaWdodFJvdXRlclN0YXRlIiwicHJlZmV0Y2hLaW5kIiwicHJlcmVuZGVyZWQiLCJocmVmIiwicHJlZmV0Y2hDYWNoZUVudHJ5IiwiZXhwaXJlZCIsIk51bWJlciIsIl9fTkVYVF9DTElFTlRfUk9VVEVSX0RZTkFNSUNfU1RBTEVUSU1FIiwiX19ORVhUX0NMSUVOVF9ST1VURVJfU1RBVElDX1NUQUxFVElNRSIsInN0YWxlIiwicmV1c2FibGUiLCJBVVRPIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    const navigatedAt = Date.now();\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(navigatedAt, currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This file is only used in app router due to the specific error state handling.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    onCaughtError: function() {\n        return onCaughtError;\n    },\n    onUncaughtError: function() {\n        return onUncaughtError;\n    }\n});\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../components/errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../components/is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _interceptconsoleerror = __webpack_require__(/*! ../components/globals/intercept-console-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\");\nconst _errorboundary = __webpack_require__(/*! ../components/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nfunction onCaughtError(err, errorInfo) {\n    var _errorInfo_errorBoundary;\n    const errorBoundaryComponent = (_errorInfo_errorBoundary = errorInfo.errorBoundary) == null ? void 0 : _errorInfo_errorBoundary.constructor;\n    let isImplicitErrorBoundary;\n    if (true) {\n        const { AppDevOverlayErrorBoundary } = __webpack_require__(/*! ../components/react-dev-overlay/app/app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\n        isImplicitErrorBoundary = errorBoundaryComponent === AppDevOverlayErrorBoundary;\n    }\n    isImplicitErrorBoundary = isImplicitErrorBoundary || errorBoundaryComponent === _errorboundary.ErrorBoundaryHandler && errorInfo.errorBoundary.props.errorComponent === _errorboundary.GlobalError;\n    if (isImplicitErrorBoundary) {\n        // We don't consider errors caught unless they're caught by an explicit error\n        // boundary. The built-in ones are considered implicit.\n        // This mimics how the same app would behave without Next.js.\n        return onUncaughtError(err, errorInfo);\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        var _errorInfo_componentStack;\n        const errorBoundaryName = (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.displayName) || (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.name) || 'Unknown';\n        const componentThatErroredFrame = errorInfo == null ? void 0 : (_errorInfo_componentStack = errorInfo.componentStack) == null ? void 0 : _errorInfo_componentStack.split('\\n')[1];\n        var // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n        // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n        _componentThatErroredFrame_match;\n        // Match chrome or safari stack trace\n        const matches = (_componentThatErroredFrame_match = componentThatErroredFrame == null ? void 0 : componentThatErroredFrame.match(/\\s+at (\\w+)\\s+|(\\w+)@/)) != null ? _componentThatErroredFrame_match : [];\n        const componentThatErroredName = matches[1] || matches[2] || 'Unknown';\n        // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n        const errorBoundaryMessage = \"It was handled by the <\" + errorBoundaryName + \"> error boundary.\";\n        const componentErrorMessage = componentThatErroredName ? \"The above error occurred in the <\" + componentThatErroredName + \"> component.\" : \"The above error occurred in one of your components.\";\n        const errorLocation = componentErrorMessage + \" \" + errorBoundaryMessage;\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // Log and report the error with location but without modifying the error stack\n        (0, _interceptconsoleerror.originConsoleError)('%o\\n\\n%s', err, errorLocation);\n        (0, _useerrorhandler.handleClientError)(stitchedError);\n    } else {}\n}\nfunction onUncaughtError(err, errorInfo) {\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // TODO: Add an adendum to the overlay telling people about custom error boundaries.\n        (0, _reportglobalerror.reportGlobalError)(stitchedError);\n    } else {}\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary-callbacks.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\n"));

/***/ })

});