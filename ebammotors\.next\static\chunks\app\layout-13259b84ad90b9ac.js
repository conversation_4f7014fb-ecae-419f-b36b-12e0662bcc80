(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>n,AuthProvider:()=>i});var s=r(5155),a=r(2115);let o=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)(null),[n,l]=(0,a.useState)(!0),[c,d]=(0,a.useState)([]),[m,u]=(0,a.useState)([]),[h,g]=(0,a.useState)([]);(0,a.useEffect)(()=>{try{let e=localStorage.getItem("ebam_user"),t=localStorage.getItem("ebam_favorites"),r=localStorage.getItem("ebam_saved_searches"),s=localStorage.getItem("ebam_purchase_history");e&&i(JSON.parse(e)),t&&d(JSON.parse(t)),r&&u(JSON.parse(r)),s&&g(JSON.parse(s))}catch(e){console.error("Error initializing auth:",e)}finally{l(!1)}},[]),(0,a.useEffect)(()=>{r?localStorage.setItem("ebam_user",JSON.stringify(r)):localStorage.removeItem("ebam_user")},[r]),(0,a.useEffect)(()=>{localStorage.setItem("ebam_favorites",JSON.stringify(c))},[c]),(0,a.useEffect)(()=>{localStorage.setItem("ebam_saved_searches",JSON.stringify(m))},[m]),(0,a.useEffect)(()=>{localStorage.setItem("ebam_purchase_history",JSON.stringify(h))},[h]);let f=async(e,t)=>{l(!0);try{await new Promise(e=>setTimeout(e,1e3));let t={id:"1",email:e,name:e.split("@")[0],joinDate:new Date().toISOString(),loyaltyPoints:1250,membershipTier:"Silver",preferences:{notifications:{email:!0,sms:!1,push:!0,marketing:!1},language:"en",currency:"JPY"}};return i(t),!0}catch(e){return console.error("Login error:",e),!1}finally{l(!1)}},v=async e=>{l(!0);try{await new Promise(e=>setTimeout(e,1e3));let t={id:Date.now().toString(),email:e.email,name:e.name,phone:e.phone,location:e.location,joinDate:new Date().toISOString(),loyaltyPoints:100,membershipTier:"Bronze",preferences:{notifications:{email:!0,sms:!1,push:!0,marketing:!1},language:"en",currency:"JPY"}};return i(t),!0}catch(e){return console.error("Registration error:",e),!1}finally{l(!1)}},x=async e=>{if(!r)return!1;try{let t={...r,...e};return i(t),!0}catch(e){return console.error("Profile update error:",e),!1}},p=(e,t)=>{if(!r)return;let s=r.loyaltyPoints+e,a=r.membershipTier;a=s>=1e4?"Platinum":s>=5e3?"Gold":s>=2e3?"Silver":"Bronze",i(e=>e?{...e,loyaltyPoints:s,membershipTier:a}:null)};return(0,s.jsx)(o.Provider,{value:{user:r,isLoading:n,login:f,register:v,logout:()=>{i(null),d([]),u([]),g([]),localStorage.removeItem("ebam_user"),localStorage.removeItem("ebam_favorites"),localStorage.removeItem("ebam_saved_searches"),localStorage.removeItem("ebam_purchase_history")},updateProfile:x,favorites:c,addToFavorites:(e,t)=>{if(!r)return;let s={id:Date.now().toString(),vehicleId:e,addedAt:new Date().toISOString(),notes:t};d(e=>[...e,s]),p(5,"Added to favorites")},removeFromFavorites:e=>{d(t=>t.filter(t=>t.vehicleId!==e))},isFavorite:e=>c.some(t=>t.vehicleId===e),savedSearches:m,saveSearch:(e,t)=>{if(!r)return;let s={id:Date.now().toString(),name:e,filters:t,createdAt:new Date().toISOString(),lastUsed:new Date().toISOString()};u(e=>[...e,s])},removeSavedSearch:e=>{u(t=>t.filter(t=>t.id!==e))},purchaseHistory:h,addPurchase:e=>{if(!r)return;let t={...e,id:Date.now().toString(),orderDate:new Date().toISOString()};g(e=>[...e,t]),p(Math.floor((parseInt(e.price.replace(/[¥,]/g,""))||0)/1e3),"Purchase: ".concat(e.vehicleTitle))},addLoyaltyPoints:p,redeemPoints:(e,t)=>!!r&&!(r.loyaltyPoints<e)&&(i(t=>t?{...t,loyaltyPoints:t.loyaltyPoints-e}:null),!0)},children:t})}function n(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},1340:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(5155),a=r(2115),o=r(6767),i=r(4416),n=r(1788);function l(){let[e,t]=(0,a.useState)(null),[r,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[m,u]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=async()=>{if("serviceWorker"in navigator)try{(await navigator.serviceWorker.register("/sw.js")).addEventListener("updatefound",()=>{})}catch(e){console.error("Service Worker registration failed:",e)}},r=e=>{e.preventDefault(),t(e),setTimeout(()=>{c||l(!0)},5e3)},s=()=>{d(!0),l(!1),t(null)},a=window.matchMedia("(display-mode: standalone)").matches,o=!0===window.navigator.standalone;return g(a||o),d(a||o),u(/iPad|iPhone|iPod/.test(navigator.userAgent)),e(),window.addEventListener("beforeinstallprompt",r),window.addEventListener("appinstalled",s),()=>{window.removeEventListener("beforeinstallprompt",r),window.removeEventListener("appinstalled",s)}},[c]);let f=async()=>{if(e)try{await e.prompt();let{outcome:r}=await e.userChoice;t(null),l(!1)}catch(e){console.error("Error during installation:",e)}},v=()=>{l(!1),sessionStorage.setItem("pwa-prompt-dismissed","true")};return c||sessionStorage.getItem("pwa-prompt-dismissed")?null:m&&!h?(0,s.jsx)("div",{className:"fixed bottom-4 left-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 max-w-sm mx-auto",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(o.A,{className:"w-6 h-6 text-blue-600 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Install EBAM Motors"}),(0,s.jsxs)("p",{className:"text-xs text-gray-600 mt-1",children:["Tap ",(0,s.jsx)("span",{className:"font-mono bg-gray-100 px-1 rounded",children:"⎘"}),' then "Add to Home Screen"']})]})]}),(0,s.jsx)("button",{onClick:v,className:"text-gray-400 hover:text-gray-600 flex-shrink-0",children:(0,s.jsx)(i.A,{className:"w-4 h-4"})})]})}):r&&e?(0,s.jsx)("div",{className:"fixed bottom-4 left-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 max-w-sm mx-auto",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"bg-blue-100 p-2 rounded-full",children:(0,s.jsx)(n.A,{className:"w-5 h-5 text-blue-600"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Install EBAM Motors"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Get the app for faster access and offline browsing"}),(0,s.jsxs)("div",{className:"flex space-x-2 mt-3",children:[(0,s.jsx)("button",{onClick:f,className:"bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-blue-700 transition-colors",children:"Install"}),(0,s.jsx)("button",{onClick:v,className:"text-gray-600 px-3 py-1 rounded text-xs hover:text-gray-800 transition-colors",children:"Not now"})]})]})]}),(0,s.jsx)("button",{onClick:v,className:"text-gray-400 hover:text-gray-600 flex-shrink-0",children:(0,s.jsx)(i.A,{className:"w-4 h-4"})})]})}):null}},6372:(e,t,r)=>{Promise.resolve().then(r.bind(r,6259)),Promise.resolve().then(r.bind(r,6063)),Promise.resolve().then(r.bind(r,8930)),Promise.resolve().then(r.t.bind(r,9243,23)),Promise.resolve().then(r.t.bind(r,5302,23)),Promise.resolve().then(r.t.bind(r,7495,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,1340)),Promise.resolve().then(r.bind(r,283))}},e=>{var t=t=>e(e.s=t);e.O(0,[0,7690,8357,8441,1684,7358],()=>t(6372)),_N_E=e.O()}]);