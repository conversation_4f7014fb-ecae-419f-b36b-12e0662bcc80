import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { sql } from '@vercel/postgres';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action, car_ids } = await request.json();

    if (!action || !car_ids || !Array.isArray(car_ids) || car_ids.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid request. Action and car_ids are required.' },
        { status: 400 }
      );
    }

    let result;
    let message = '';

    switch (action) {
      case 'feature':
        result = await bulkFeature(car_ids, true);
        message = `${result.count} cars marked as featured`;
        break;

      case 'unfeature':
        result = await bulkFeature(car_ids, false);
        message = `${result.count} cars unmarked as featured`;
        break;

      case 'delete':
        result = await bulkDelete(car_ids);
        message = `${result.count} cars deleted`;
        break;

      case 'update_status':
        const { status } = await request.json();
        if (!status) {
          return NextResponse.json(
            { success: false, message: 'Status is required for update_status action' },
            { status: 400 }
          );
        }
        result = await bulkUpdateStatus(car_ids, status);
        message = `${result.count} cars status updated to ${status}`;
        break;

      case 'update_location':
        const { location } = await request.json();
        if (!location) {
          return NextResponse.json(
            { success: false, message: 'Location is required for update_location action' },
            { status: 400 }
          );
        }
        result = await bulkUpdateLocation(car_ids, location);
        message = `${result.count} cars location updated to ${location}`;
        break;

      case 'export':
        const exportData = await bulkExport(car_ids);
        return NextResponse.json({
          success: true,
          message: 'Cars exported successfully',
          data: exportData
        });

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message,
      affected_count: result.count
    });

  } catch (error) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}

async function bulkFeature(carIds: string[], featured: boolean) {
  const placeholders = carIds.map((_, index) => `$${index + 1}`).join(',');
  const query = `
    UPDATE cars 
    SET is_featured = $${carIds.length + 1}, updated_at = NOW() 
    WHERE id IN (${placeholders})
  `;
  
  const result = await sql.query(query, [...carIds, featured]);
  return { count: result.rowCount || 0 };
}

async function bulkDelete(carIds: string[]) {
  const placeholders = carIds.map((_, index) => `$${index + 1}`).join(',');
  const query = `DELETE FROM cars WHERE id IN (${placeholders})`;
  
  const result = await sql.query(query, carIds);
  return { count: result.rowCount || 0 };
}

async function bulkUpdateStatus(carIds: string[], status: string) {
  const placeholders = carIds.map((_, index) => `$${index + 1}`).join(',');
  const query = `
    UPDATE cars 
    SET status = $${carIds.length + 1}, updated_at = NOW() 
    WHERE id IN (${placeholders})
  `;
  
  const result = await sql.query(query, [...carIds, status]);
  return { count: result.rowCount || 0 };
}

async function bulkUpdateLocation(carIds: string[], location: string) {
  const placeholders = carIds.map((_, index) => `$${index + 1}`).join(',');
  const query = `
    UPDATE cars 
    SET location = $${carIds.length + 1}, updated_at = NOW() 
    WHERE id IN (${placeholders})
  `;
  
  const result = await sql.query(query, [...carIds, location]);
  return { count: result.rowCount || 0 };
}

async function bulkExport(carIds: string[]) {
  const placeholders = carIds.map((_, index) => `$${index + 1}`).join(',');
  const query = `
    SELECT 
      car_id,
      make,
      model,
      year,
      title,
      price,
      original_price,
      currency,
      status,
      mileage,
      fuel_type,
      transmission,
      engine_size,
      drive_type,
      seats,
      doors,
      body_type,
      body_condition,
      interior_condition,
      exterior_color,
      interior_color,
      location,
      is_featured,
      stock_quantity,
      description,
      created_at,
      updated_at
    FROM cars 
    WHERE id IN (${placeholders})
    ORDER BY created_at DESC
  `;
  
  const result = await sql.query(query, carIds);
  return result.rows;
}

// Additional bulk operations can be added here
export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { car_ids, updates } = await request.json();

    if (!car_ids || !Array.isArray(car_ids) || car_ids.length === 0 || !updates) {
      return NextResponse.json(
        { success: false, message: 'Invalid request. car_ids and updates are required.' },
        { status: 400 }
      );
    }

    // Build dynamic update query
    const updateFields = [];
    const values = [];
    let paramIndex = 1;

    for (const [field, value] of Object.entries(updates)) {
      // Validate allowed fields
      const allowedFields = [
        'make', 'model', 'year', 'title', 'price', 'original_price', 'currency',
        'status', 'mileage', 'fuel_type', 'transmission', 'body_condition',
        'location', 'is_featured', 'stock_quantity', 'description'
      ];

      if (allowedFields.includes(field)) {
        updateFields.push(`${field} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Add updated_at
    updateFields.push(`updated_at = NOW()`);

    // Add car IDs to values
    const placeholders = car_ids.map((_, index) => `$${paramIndex + index}`).join(',');
    values.push(...car_ids);

    const query = `
      UPDATE cars 
      SET ${updateFields.join(', ')} 
      WHERE id IN (${placeholders})
    `;

    const result = await sql.query(query, values);

    return NextResponse.json({
      success: true,
      message: `${result.rowCount || 0} cars updated successfully`,
      affected_count: result.rowCount || 0
    });

  } catch (error) {
    console.error('Error performing bulk update:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to perform bulk update' },
      { status: 500 }
    );
  }
}
