(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7061],{693:(e,r,s)=>{Promise.resolve().then(s.bind(s,8411))},5695:(e,r,s)=>{"use strict";var a=s(8999);s.o(a,"usePathname")&&s.d(r,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})},8411:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var a=s(5155),t=s(2115),u=s(5695);function n(){let e=(0,u.useRouter)();return(0,t.useEffect)(()=>{e.replace("/admin/dashboard?section=security")},[e]),(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Redirecting to security management..."})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[8441,1684,7358],()=>r(693)),_N_E=e.O()}]);