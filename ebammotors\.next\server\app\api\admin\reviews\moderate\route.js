(()=>{var e={};e.id=1379,e.ids=[1379],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>m,Tq:()=>h,bS:()=>d,fF:()=>l,mU:()=>u});var s=r(85663),a=r(43205),i=r.n(a);let n=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function c(e,t){try{return await s.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function u(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),r=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await c(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(t,n,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let e=Date.now();for(let[t,r]of o.entries())e>r.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function l(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=i().verify(e,n);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let r=Date.now();return r>t.expiresAt?(o.delete(e),null):(t.lastActivity=r,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),r=p.get(e);return!r||t-r.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-r.lastAttempt)}:(r.count++,r.lastAttempt=t,p.set(e,r),{allowed:!0,remainingAttempts:5-r.count})}function h(e){p.delete(e)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45169:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{PATCH:()=>l});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),c=r(77268),u=r(67462),d=r(16967);async function l(e){try{if(!(0,c.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{reviewId:t,status:r}=await e.json();if(!t||!r)return o.NextResponse.json({success:!1,message:"Review ID and status are required"},{status:400});if(!["approved","rejected"].includes(r))return o.NextResponse.json({success:!1,message:'Invalid status. Must be "approved" or "rejected"'},{status:400});let s=await (0,u.RV)(t);if(!s)return o.NextResponse.json({success:!1,message:"Review not found"},{status:404});let a=await (0,u.WD)(t,r);if(!a)return o.NextResponse.json({success:!1,message:"Failed to update review status"},{status:500});if("approved"===r&&s.email)try{let e={customerName:s.name,vehicleTitle:s.vehiclePurchased||"Vehicle",rating:s.rating,review:s.review,reviewDate:new Date(s.submittedAt).toLocaleDateString(),isApproval:!0};await d.gm.sendReviewApprovalNotification(s.email,e)}catch(e){console.error("Failed to send review approval email:",e)}return o.NextResponse.json({success:!0,message:`Review ${r} successfully`,review:a})}catch(e){return console.error("Error moderating review:",e),o.NextResponse.json({success:!1,message:"Failed to moderate review"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/reviews/moderate/route",pathname:"/api/admin/reviews/moderate",filename:"route",bundlePath:"app/api/admin/reviews/moderate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\reviews\\moderate\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:w}=p;function v(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67462:(e,t,r)=>{"use strict";r.d(t,{CE:()=>h,RV:()=>y,Ve:()=>g,WD:()=>v,kD:()=>w,zu:()=>m});var s=r(29021),a=r(33873),i=r.n(a);let n=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,o=i().join(process.cwd(),"data","reviews.json"),c=i().join(process.cwd(),"data"),u=[],d=[{id:"sample-1",name:"Kwame Mensah",location:"Accra, Ghana",email:"<EMAIL>",rating:5,title:"Excellent Service and Quality Vehicle",review:"I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!",vehiclePurchased:"Toyota Voxy 2015",purchaseDate:"2024-01",locale:"en",submittedAt:"2024-01-15T10:30:00.000Z",status:"approved",images:[],titleEn:"Excellent Service and Quality Vehicle",titleJa:"優れたサービスと高品質な車両",reviewEn:"I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!",reviewJa:"EBAM Motorsを通じてトヨタ ヴォクシーを購入しましたが、全プロセスがスムーズでプロフェッショナルでした。車は説明通りの優れた状態で到着しました。チームは私のすべての質問に迅速に対応し、配送プロセスを手間なく進めてくれました。強くお勧めします！"},{id:"sample-2",name:"Akosua Boateng",location:"Kumasi, Ghana",email:"<EMAIL>",rating:5,title:"Professional and Trustworthy",review:"EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!",vehiclePurchased:"Honda Fit 2016",purchaseDate:"2024-02",locale:"en",submittedAt:"2024-02-10T14:20:00.000Z",status:"approved",images:[],titleEn:"Professional and Trustworthy",titleJa:"プロフェッショナルで信頼できる",reviewEn:"EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!",reviewJa:"EBAM Motorsは私の日常通勤に最適なホンダ フィットを見つけるのを手伝ってくれました。彼らのチームは車両について非常に知識が豊富で、各車について詳細な情報を提供してくれました。配送は迅速で、車は完璧な状態で到着しました。全体的に素晴らしい体験でした！"}];async function l(){try{await s.promises.access(c)}catch{await s.promises.mkdir(c,{recursive:!0})}}async function p(){try{await s.promises.access(o)}catch{await l(),await s.promises.writeFile(o,JSON.stringify(d,null,2),"utf8")}}async function m(){if(n)return 0===u.length&&(u=[...d]),u;try{await p();let e=await s.promises.readFile(o,"utf8");return JSON.parse(e)}catch(e){return console.error("Error reading reviews:",e),d}}async function h(e){if(n){u=[...e];return}try{await l(),await s.promises.writeFile(o,JSON.stringify(e,null,2),"utf8")}catch(e){throw console.error("Error saving reviews:",e),Error("Failed to save reviews")}}async function w(e){let t=await m(),r={...e,id:Date.now().toString()+"-"+Math.random().toString(36).substr(2,9)};return t.push(r),await h(t),r}async function v(e,t){let r=await m(),s=r.findIndex(t=>t.id===e);return -1!==s&&(r[s].status=t,await h(r),!0)}async function f(e){return(await m()).filter(t=>t.status===e)}async function g(){return f("approved")}async function y(e){return(await m()).find(t=>t.id===e)||null}},77268:(e,t,r)=>{"use strict";r.d(t,{iY:()=>a}),r(32190);var s=r(12909);function a(e,t){let r=e.headers.get("authorization"),a=e.cookies.get("admin_session")?.value,i=(0,s.fF)(r,a);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let n=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return n&&n===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7696,6967],()=>r(45169));module.exports=s})();