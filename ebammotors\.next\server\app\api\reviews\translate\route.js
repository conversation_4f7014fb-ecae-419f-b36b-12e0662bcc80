"use strict";(()=>{var e={};e.id=3786,e.ids=[3786],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72027:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>w,serverHooks:()=>g,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>c});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(67462),u=r(4161),p=r(77268);async function c(e){try{if(!(0,p.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await (0,l.zu)(),r=0,s=0;for(let e of t)try{if(e.reviewEn&&e.reviewJa){s++;continue}let t=(0,u.od)(e.review),a="en"===t?"ja":"en",i=await (0,u.Uw)({name:e.name,location:e.location,review:e.review,title:e.title},a);"en"===t?(e.reviewEn=e.review,e.reviewJa=i.review,e.title&&(e.titleEn=e.title,e.titleJa=i.title)):(e.reviewJa=e.review,e.reviewEn=i.review,e.title&&(e.titleJa=e.title,e.titleEn=i.title)),r++}catch(t){console.error(`❌ Failed to translate review ${e.id}:`,t),s++}return await (0,l.CE)(t),o.NextResponse.json({success:!0,message:`Translation complete: ${r} reviews translated, ${s} skipped`,stats:{total:t.length,translated:r,skipped:s}})}catch(e){return console.error("Error translating reviews:",e),o.NextResponse.json({success:!1,message:"Failed to translate reviews"},{status:500})}}async function d(e){try{if(!(0,p.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await (0,l.zu)(),r={total:t.length,withTranslations:t.filter(e=>e.reviewEn&&e.reviewJa).length,needingTranslation:t.filter(e=>!e.reviewEn||!e.reviewJa).length};return o.NextResponse.json({success:!0,stats:r})}catch(e){return console.error("Error checking translation status:",e),o.NextResponse.json({success:!1,message:"Failed to check translation status"},{status:500})}}let w=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/reviews/translate/route",pathname:"/api/reviews/translate",filename:"route",bundlePath:"app/api/reviews/translate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\reviews\\translate\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:x,serverHooks:g}=w;function h(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:x})}},79428:e=>{e.exports=require("buffer")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7696,5887],()=>r(72027));module.exports=s})();