"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/resend";
exports.ids = ["vendor-chunks/resend"];
exports.modules = {

/***/ "(rsc)/./node_modules/resend/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/resend/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Resend: () => (/* binding */ Resend)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// package.json\nvar version = \"4.6.0\";\n\n// src/api-keys/api-keys.ts\nvar ApiKeys = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/api-keys\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/api-keys\");\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/api-keys/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/audiences/audiences.ts\nvar Audiences = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/audiences\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/audiences\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-email-to-api-options.ts\nfunction parseEmailToApiOptions(email) {\n  return {\n    attachments: email.attachments,\n    bcc: email.bcc,\n    cc: email.cc,\n    from: email.from,\n    headers: email.headers,\n    html: email.html,\n    reply_to: email.replyTo,\n    scheduled_at: email.scheduledAt,\n    subject: email.subject,\n    tags: email.tags,\n    text: email.text,\n    to: email.to\n  };\n}\n\n// src/batch/batch.ts\nvar Batch = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const emails = [];\n      for (const email of payload) {\n        if (email.react) {\n          if (!this.renderAsync) {\n            try {\n              const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/./node_modules/@react-email/render/dist/node/index.mjs\"));\n              this.renderAsync = renderAsync;\n            } catch (error) {\n              throw new Error(\n                \"Failed to render React component. Make sure to install `@react-email/render`\"\n              );\n            }\n          }\n          email.html = yield this.renderAsync(email.react);\n          email.react = void 0;\n        }\n        emails.push(parseEmailToApiOptions(email));\n      }\n      const data = yield this.resend.post(\n        \"/emails/batch\",\n        emails,\n        options\n      );\n      return data;\n    });\n  }\n};\n\n// src/broadcasts/broadcasts.ts\nvar Broadcasts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/./node_modules/@react-email/render/dist/node/index.mjs\"));\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\n              \"Failed to render React component. Make sure to install `@react-email/render`\"\n            );\n          }\n        }\n        payload.html = yield this.renderAsync(\n          payload.react\n        );\n      }\n      const data = yield this.resend.post(\n        \"/broadcasts\",\n        {\n          name: payload.name,\n          audience_id: payload.audienceId,\n          preview_text: payload.previewText,\n          from: payload.from,\n          html: payload.html,\n          reply_to: payload.replyTo,\n          subject: payload.subject,\n          text: payload.text\n        },\n        options\n      );\n      return data;\n    });\n  }\n  send(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/broadcasts/${id}/send`,\n        { scheduled_at: payload == null ? void 0 : payload.scheduledAt }\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/broadcasts\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/broadcasts/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/broadcasts/${id}`\n      );\n      return data;\n    });\n  }\n  update(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/broadcasts/${id}`,\n        {\n          name: payload.name,\n          audience_id: payload.audienceId,\n          from: payload.from,\n          html: payload.html,\n          text: payload.text,\n          subject: payload.subject,\n          reply_to: payload.replyTo,\n          preview_text: payload.previewText\n        }\n      );\n      return data;\n    });\n  }\n};\n\n// src/contacts/contacts.ts\nvar Contacts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        `/audiences/${payload.audienceId}/contacts`,\n        {\n          unsubscribed: payload.unsubscribed,\n          email: payload.email,\n          first_name: payload.firstName,\n          last_name: payload.lastName\n        },\n        options\n      );\n      return data;\n    });\n  }\n  list(options) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${options.audienceId}/contacts`\n      );\n      return data;\n    });\n  }\n  get(options) {\n    return __async(this, null, function* () {\n      if (!options.id && !options.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.get(\n        `/audiences/${options.audienceId}/contacts/${(options == null ? void 0 : options.email) ? options == null ? void 0 : options.email : options == null ? void 0 : options.id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.patch(\n        `/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`,\n        {\n          unsubscribed: payload.unsubscribed,\n          first_name: payload.firstName,\n          last_name: payload.lastName\n        }\n      );\n      return data;\n    });\n  }\n  remove(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.delete(\n        `/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-domain-to-api-options.ts\nfunction parseDomainToApiOptions(domain) {\n  return {\n    name: domain.name,\n    region: domain.region,\n    custom_return_path: domain.customReturnPath\n  };\n}\n\n// src/domains/domains.ts\nvar Domains = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/domains\",\n        parseDomainToApiOptions(payload),\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/domains\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/domains/${payload.id}`,\n        {\n          click_tracking: payload.clickTracking,\n          open_tracking: payload.openTracking,\n          tls: payload.tls\n        }\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  verify(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/domains/${id}/verify`\n      );\n      return data;\n    });\n  }\n};\n\n// src/emails/emails.ts\nvar Emails = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const { renderAsync } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/entities\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/./node_modules/@react-email/render/dist/node/index.mjs\"));\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\n              \"Failed to render React component. Make sure to install `@react-email/render`\"\n            );\n          }\n        }\n        payload.html = yield this.renderAsync(\n          payload.react\n        );\n      }\n      const data = yield this.resend.post(\n        \"/emails\",\n        parseEmailToApiOptions(payload),\n        options\n      );\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/emails/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/emails/${payload.id}`,\n        {\n          scheduled_at: payload.scheduledAt\n        }\n      );\n      return data;\n    });\n  }\n  cancel(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/emails/${id}/cancel`\n      );\n      return data;\n    });\n  }\n};\n\n// src/resend.ts\nvar defaultBaseUrl = \"https://api.resend.com\";\nvar defaultUserAgent = `resend-node:${version}`;\nvar baseUrl = typeof process !== \"undefined\" && process.env ? process.env.RESEND_BASE_URL || defaultBaseUrl : defaultBaseUrl;\nvar userAgent = typeof process !== \"undefined\" && process.env ? process.env.RESEND_USER_AGENT || defaultUserAgent : defaultUserAgent;\nvar Resend = class {\n  constructor(key) {\n    this.key = key;\n    this.apiKeys = new ApiKeys(this);\n    this.audiences = new Audiences(this);\n    this.batch = new Batch(this);\n    this.broadcasts = new Broadcasts(this);\n    this.contacts = new Contacts(this);\n    this.domains = new Domains(this);\n    this.emails = new Emails(this);\n    if (!key) {\n      if (typeof process !== \"undefined\" && process.env) {\n        this.key = process.env.RESEND_API_KEY;\n      }\n      if (!this.key) {\n        throw new Error(\n          'Missing API key. Pass it to the constructor `new Resend(\"re_123\")`'\n        );\n      }\n    }\n    this.headers = new Headers({\n      Authorization: `Bearer ${this.key}`,\n      \"User-Agent\": userAgent,\n      \"Content-Type\": \"application/json\"\n    });\n  }\n  fetchRequest(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      try {\n        const response = yield fetch(`${baseUrl}${path}`, options);\n        if (!response.ok) {\n          try {\n            const rawError = yield response.text();\n            return { data: null, error: JSON.parse(rawError) };\n          } catch (err) {\n            if (err instanceof SyntaxError) {\n              return {\n                data: null,\n                error: {\n                  name: \"application_error\",\n                  message: \"Internal server error. We are unable to process your request right now, please try again later.\"\n                }\n              };\n            }\n            const error = {\n              message: response.statusText,\n              name: \"application_error\"\n            };\n            if (err instanceof Error) {\n              return { data: null, error: __spreadProps(__spreadValues({}, error), { message: err.message }) };\n            }\n            return { data: null, error };\n          }\n        }\n        const data = yield response.json();\n        return { data, error: null };\n      } catch (error) {\n        return {\n          data: null,\n          error: {\n            name: \"application_error\",\n            message: \"Unable to fetch data. The request could not be resolved.\"\n          }\n        };\n      }\n    });\n  }\n  post(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const headers = new Headers(this.headers);\n      if (options.idempotencyKey) {\n        headers.set(\"Idempotency-Key\", options.idempotencyKey);\n      }\n      const requestOptions = __spreadValues({\n        method: \"POST\",\n        headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  get(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"GET\",\n        headers: this.headers\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  put(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PUT\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  patch(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PATCH\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  delete(path, query) {\n    return __async(this, null, function* () {\n      const requestOptions = {\n        method: \"DELETE\",\n        headers: this.headers,\n        body: JSON.stringify(query)\n      };\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resend/dist/index.mjs\n");

/***/ })

};
;