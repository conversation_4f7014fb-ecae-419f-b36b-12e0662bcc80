'use client';

import { useState } from 'react';
import { Share2, Copy, Check, ExternalLink } from 'lucide-react';
import { 
  SOCIAL_PLATFORMS, 
  shareOnSocialMedia, 
  shareNative, 
  copyToClipboard,
  trackSocialInteraction,
  ShareData 
} from '@/lib/socialMedia';

interface SocialShareProps {
  shareData: ShareData;
  showLabels?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'buttons' | 'dropdown' | 'modal';
  platforms?: string[];
}

export default function SocialShare({ 
  shareData, 
  showLabels = false, 
  size = 'md',
  variant = 'buttons',
  platforms = ['facebook', 'twitter', 'whatsapp', 'line', 'linkedin']
}: SocialShareProps) {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [copied, setCopied] = useState(false);

  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
  };

  const buttonSizeClasses = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-3',
  };

  const filteredPlatforms = SOCIAL_PLATFORMS.filter(platform => 
    platforms.includes(platform.id)
  );

  const handleShare = async (platformId: string) => {
    // Try native share first on mobile
    if (platformId === 'native') {
      const success = await shareNative(shareData);
      if (success) {
        trackSocialInteraction('share', 'native', shareData.title);
        return;
      }
    }

    // Fallback to platform-specific sharing
    shareOnSocialMedia(platformId, shareData);
    trackSocialInteraction('share', platformId, shareData.title);
    
    if (variant === 'dropdown') setShowDropdown(false);
    if (variant === 'modal') setShowModal(false);
  };

  const handleCopyLink = async () => {
    const success = await copyToClipboard(shareData.url);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      trackSocialInteraction('copy_link', 'clipboard', shareData.title);
    }
  };

  const SocialButton = ({ platform }: { platform: any }) => (
    <button
      onClick={() => handleShare(platform.id)}
      className={`${buttonSizeClasses[size]} rounded-full transition-all duration-200 hover:scale-110 flex items-center space-x-2 ${
        showLabels ? 'px-4 py-2 border border-gray-200 hover:border-gray-300' : ''
      }`}
      style={{ backgroundColor: showLabels ? 'transparent' : platform.color }}
      title={`Share on ${platform.name}`}
    >
      <div 
        className={`${sizeClasses[size]} rounded-full flex items-center justify-center ${
          showLabels ? 'text-gray-700' : 'text-white'
        }`}
        style={{ backgroundColor: showLabels ? platform.color : 'transparent' }}
      >
        <span className="text-xs font-bold">
          {platform.id === 'facebook' && 'f'}
          {platform.id === 'twitter' && '𝕏'}
          {platform.id === 'instagram' && '📷'}
          {platform.id === 'linkedin' && 'in'}
          {platform.id === 'whatsapp' && '💬'}
          {platform.id === 'line' && 'L'}
          {platform.id === 'youtube' && '▶'}
        </span>
      </div>
      {showLabels && (
        <span className="text-sm font-medium text-gray-700">{platform.name}</span>
      )}
    </button>
  );

  if (variant === 'dropdown') {
    return (
      <div className="relative">
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          className={`${buttonSizeClasses[size]} bg-gray-100 hover:bg-gray-200 rounded-full transition-colors flex items-center space-x-2`}
        >
          <Share2 className={sizeClasses[size]} />
          {showLabels && <span className="text-sm font-medium">Share</span>}
        </button>

        {showDropdown && (
          <div className="absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-48">
            <div className="p-2 space-y-1">
              {/* Native share option for mobile */}
              {navigator.share && (
                <button
                  onClick={() => handleShare('native')}
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-50 rounded"
                >
                  <Share2 className="w-4 h-4 text-gray-600" />
                  <span className="text-sm">Share...</span>
                </button>
              )}
              
              {filteredPlatforms.map((platform) => (
                <button
                  key={platform.id}
                  onClick={() => handleShare(platform.id)}
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-50 rounded"
                >
                  <div 
                    className="w-4 h-4 rounded flex items-center justify-center text-white text-xs font-bold"
                    style={{ backgroundColor: platform.color }}
                  >
                    {platform.id === 'facebook' && 'f'}
                    {platform.id === 'twitter' && '𝕏'}
                    {platform.id === 'whatsapp' && '💬'}
                    {platform.id === 'line' && 'L'}
                    {platform.id === 'linkedin' && 'in'}
                  </div>
                  <span className="text-sm">{platform.name}</span>
                </button>
              ))}
              
              <hr className="my-1" />
              
              <button
                onClick={handleCopyLink}
                className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-50 rounded"
              >
                {copied ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-600" />
                )}
                <span className="text-sm">{copied ? 'Copied!' : 'Copy Link'}</span>
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  if (variant === 'modal') {
    return (
      <>
        <button
          onClick={() => setShowModal(true)}
          className={`${buttonSizeClasses[size]} bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-colors flex items-center space-x-2`}
        >
          <Share2 className={sizeClasses[size]} />
          {showLabels && <span className="text-sm font-medium">Share</span>}
        </button>

        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Share this car</h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 mb-3">Share on social media:</p>
                  <div className="grid grid-cols-4 gap-3">
                    {filteredPlatforms.map((platform) => (
                      <SocialButton key={platform.id} platform={platform} />
                    ))}
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-gray-600 mb-2">Or copy link:</p>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={shareData.url}
                      readOnly
                      className="flex-1 border border-gray-300 rounded px-3 py-2 text-sm bg-gray-50"
                    />
                    <button
                      onClick={handleCopyLink}
                      className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded text-sm flex items-center space-x-1"
                    >
                      {copied ? (
                        <>
                          <Check className="w-4 h-4 text-green-600" />
                          <span className="text-green-600">Copied</span>
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4" />
                          <span>Copy</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </>
    );
  }

  // Default buttons variant
  return (
    <div className={`flex items-center ${showLabels ? 'space-x-3' : 'space-x-2'}`}>
      {filteredPlatforms.map((platform) => (
        <SocialButton key={platform.id} platform={platform} />
      ))}
      
      <button
        onClick={handleCopyLink}
        className={`${buttonSizeClasses[size]} bg-gray-100 hover:bg-gray-200 rounded-full transition-colors flex items-center space-x-2 ${
          showLabels ? 'px-4 py-2 border border-gray-200' : ''
        }`}
        title="Copy link"
      >
        {copied ? (
          <Check className={`${sizeClasses[size]} text-green-600`} />
        ) : (
          <Copy className={`${sizeClasses[size]} text-gray-600`} />
        )}
        {showLabels && (
          <span className="text-sm font-medium text-gray-700">
            {copied ? 'Copied!' : 'Copy Link'}
          </span>
        )}
      </button>
    </div>
  );
}

// Social Login Component
export function SocialLogin({ onSuccess, onError }: {
  onSuccess?: (user: any) => void;
  onError?: (error: string) => void;
}) {
  const handleSocialLogin = (platform: string) => {
    // This would integrate with your authentication system
    console.log('Social login with:', platform);
    // Implementation would depend on your auth provider (NextAuth.js, Auth0, etc.)
  };

  return (
    <div className="space-y-3">
      <div className="text-center text-sm text-gray-600 mb-4">
        Or continue with
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <button
          onClick={() => handleSocialLogin('facebook')}
          className="flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center text-white text-xs font-bold">
            f
          </div>
          <span className="text-sm font-medium">Facebook</span>
        </button>
        
        <button
          onClick={() => handleSocialLogin('linkedin')}
          className="flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div className="w-5 h-5 bg-blue-700 rounded flex items-center justify-center text-white text-xs font-bold">
            in
          </div>
          <span className="text-sm font-medium">LinkedIn</span>
        </button>
      </div>
    </div>
  );
}

// Social Proof Component
export function SocialProof({ shares, mentions }: {
  shares?: number;
  mentions?: any[];
}) {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-medium text-gray-900">Social Activity</h4>
        <ExternalLink className="w-4 h-4 text-gray-500" />
      </div>
      
      <div className="space-y-2">
        {shares && (
          <div className="flex items-center space-x-2">
            <Share2 className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-gray-600">
              Shared {shares.toLocaleString()} times
            </span>
          </div>
        )}
        
        {mentions && mentions.length > 0 && (
          <div className="space-y-1">
            <p className="text-xs text-gray-500">Recent mentions:</p>
            {mentions.slice(0, 3).map((mention, index) => (
              <div key={index} className="text-xs text-gray-600 truncate">
                @{mention.username}: {mention.text}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
