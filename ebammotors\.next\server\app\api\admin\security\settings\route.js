(()=>{var e={};e.id=7652,e.ids=[7652],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,s)=>{"use strict";s.d(t,{Qq:()=>p,Tq:()=>g,bS:()=>d,fF:()=>l,mU:()=>c});var r=s(85663),i=s(43205),n=s.n(i);let a=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await r.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),s=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return n().sign(t,a,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,s=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:s,expiresAt:s+864e5,lastActivity:s}),function(){let e=Date.now();for(let[t,s]of o.entries())e>s.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function l(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=n().verify(e,a);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let s=Date.now();return s>t.expiresAt?(o.delete(e),null):(t.lastActivity=s,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let m=new Map;function p(e){let t=Date.now(),s=m.get(e);return!s||t-s.lastAttempt>9e5?(m.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):s.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-s.lastAttempt)}:(s.count++,s.lastAttempt=t,m.set(e,s),{allowed:!0,remainingAttempts:5-s.count})}function g(e){m.delete(e)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77268:(e,t,s)=>{"use strict";s.d(t,{iY:()=>i}),s(32190);var r=s(12909);function i(e,t){let s=e.headers.get("authorization"),i=e.cookies.get("admin_session")?.value,n=(0,r.fF)(s,i);if(n.isValid)return{isValid:!0,adminId:n.adminId,method:"token/session"};let a=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return a&&a===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},87831:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{GET:()=>c,PUT:()=>l});var i=s(96559),n=s(48088),a=s(37719),o=s(32190),u=s(77268);async function c(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await d();return o.NextResponse.json({success:!0,settings:t})}catch(e){return console.error("Error fetching security settings:",e),o.NextResponse.json({success:!1,message:"Failed to fetch security settings"},{status:500})}}async function d(){return{passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSpecialChars:!0,maxAge:90},sessionSettings:{maxDuration:480,idleTimeout:60,maxConcurrentSessions:3},loginSecurity:{maxFailedAttempts:5,lockoutDuration:30,requireTwoFactor:!1,allowedIpRanges:["***********/24","10.0.0.0/8"]},auditSettings:{logRetentionDays:365,enableRealTimeAlerts:!0,alertThresholds:{failedLogins:10,suspiciousActivity:5}}}}async function l(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await e.json(),s=function(e){if(e.passwordPolicy){let{minLength:t,maxAge:s}=e.passwordPolicy;if(t<6||t>128)return{valid:!1,error:"Password minimum length must be between 6 and 128 characters"};if(s<1||s>365)return{valid:!1,error:"Password max age must be between 1 and 365 days"}}if(e.sessionSettings){let{maxDuration:t,idleTimeout:s,maxConcurrentSessions:r}=e.sessionSettings;if(t<5||t>1440)return{valid:!1,error:"Session max duration must be between 5 and 1440 minutes"};if(s<5||s>480)return{valid:!1,error:"Session idle timeout must be between 5 and 480 minutes"};if(r<1||r>10)return{valid:!1,error:"Max concurrent sessions must be between 1 and 10"}}if(e.loginSecurity){let{maxFailedAttempts:t,lockoutDuration:s}=e.loginSecurity;if(t<3||t>20)return{valid:!1,error:"Max failed attempts must be between 3 and 20"};if(s<5||s>1440)return{valid:!1,error:"Lockout duration must be between 5 and 1440 minutes"}}if(e.auditSettings){let{logRetentionDays:t,alertThresholds:s}=e.auditSettings;if(t<30||t>2555)return{valid:!1,error:"Log retention must be between 30 and 2555 days"};if(s){if(s.failedLogins<1||s.failedLogins>100)return{valid:!1,error:"Failed logins alert threshold must be between 1 and 100"};if(s.suspiciousActivity<1||s.suspiciousActivity>50)return{valid:!1,error:"Suspicious activity alert threshold must be between 1 and 50"}}}return{valid:!0}}(t);if(!s.valid)return o.NextResponse.json({success:!1,message:s.error},{status:400});return await m("settings_changed","admin","Security settings updated"),o.NextResponse.json({success:!0,message:"Security settings updated successfully",settings:t})}catch(e){return console.error("Error updating security settings:",e),o.NextResponse.json({success:!1,message:"Failed to update security settings"},{status:500})}}async function m(e,t,s){try{new Date().toISOString()}catch(e){console.error("Failed to log security event:",e)}}let p=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/security/settings/route",pathname:"/api/admin/security/settings",filename:"route",bundlePath:"app/api/admin/security/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\security\\settings\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:y}=p;function h(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,7696],()=>s(87831));module.exports=r})();