(()=>{var e={};e.id=6120,e.ids=[6120],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13926:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\[locale]\\\\stock\\\\StockWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\[locale]\\stock\\StockWrapper.tsx","default")},17198:(e,t,s)=>{"use strict";s.d(t,{l:()=>c});var a=s(33873),r=s.n(a);let l={"toyota-voxy":{name:"Toyota Voxy",basePrice:3e5,seats:"8-seater",transmission:"Automatic",fuelType:"Gasoline"},"toyota-noah":{name:"Toyota Noah",basePrice:35e4,seats:"8-seater",transmission:"Automatic",fuelType:"Gasoline"},"toyota-sienta":{name:"Toyota Sienta",basePrice:32e4,seats:"7-seater",transmission:"CVT",fuelType:"Hybrid"},"toyota-vitz":{name:"Toyota Vitz",basePrice:325e3,seats:"5-seater",transmission:"Manual",fuelType:"Gasoline"},"toyota-yaris":{name:"Toyota Yaris",basePrice:55e4,seats:"5-seater",transmission:"CVT",fuelType:"Hybrid"}},n={yearAdjustment:{old:-5e3,new:5e3}};function i(){return[{id:1,category:"cars",title:"Toyota Voxy 2015",price:"\xa5450,000",location:"Japan",status:"Available",image:"/car-models/voxy/voxy-2015-001/1.jpg",images:["/car-models/voxy/voxy-2015-001/1.jpg","/car-models/voxy/voxy-2015-001/2.jpg"],specs:["8 seats","CVT","85k km","Gasoline","Excellent"],carId:"voxy-2015-001",year:2015,mileage:85e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Excellent"},{id:2,category:"cars",title:"Toyota Noah 2016",price:"\xa5480,000",location:"Japan",status:"Available",image:"/car-models/noah/noah-2016-001/1.jpg",images:["/car-models/noah/noah-2016-001/1.jpg","/car-models/noah/noah-2016-001/2.jpg"],specs:["8 seats","CVT","72k km","Gasoline","Very Good"],carId:"noah-2016-001",year:2016,mileage:72e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Very Good"},{id:3,category:"cars",title:"Toyota Sienta 2017",price:"\xa5520,000",location:"Japan",status:"Available",image:"/car-models/sienta/sienta-2017-001/1.jpg",images:["/car-models/sienta/sienta-2017-001/1.jpg","/car-models/sienta/sienta-2017-001/2.jpg"],specs:["7 seats","CVT","58k km","Gasoline","Excellent"],carId:"sienta-2017-001",year:2017,mileage:58e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Excellent"}]}async function c(){let e=[],t=r().join(process.cwd(),"public","car-models");try{let a=await Promise.resolve().then(s.t.bind(s,29021,23));if(!a.existsSync(t))return console.warn("Car models directory not found:",t),i();let c=a.readdirSync(t),o=1;for(let s of c){let i=r().join(t,s);if(!a.statSync(i).isDirectory())continue;let c=l[s];if(c)for(let t of a.readdirSync(i)){let d=r().join(i,t);if(!a.statSync(d).isDirectory())continue;let x=a.readdirSync(d).filter(e=>[".jpg",".jpeg",".JPG",".JPEG",".png",".PNG"].includes(r().extname(e))).sort();if(0===x.length)continue;let m=function(e){let t=e.match(/(\d{4})/);return t?parseInt(t[1]):2010}(t),p=function(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t&=t;return Math.floor(Math.abs(t)/0x7fffffff*9e4+3e4)}(t),h=function(e){let t=["Excellent","Very Good","Good","Fair","Needs Work"],s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t),s&=s;return t[Math.abs(s)%t.length]}(t),u=function(e,t){let s=l[e];if(!s)return 0;let a=s.basePrice;return t<=2010?a+=n.yearAdjustment.old:t>=2012&&(a+=n.yearAdjustment.new),Math.max(a,0)}(s,m),g=x.map(e=>`/car-models/${s}/${t}/${e}`),j=t.split("").reduce((e,t)=>e+t.charCodeAt(0),0),b=j%4==0,y=j%5==0,f=b?j%7:j%30+7,v=new Date;v.setDate(v.getDate()-f);let N={id:o++,category:"cars",title:`${c.name} ${m}`,price:`\xa5${u.toLocaleString()}`,location:"Japan",status:t.length%10==0?"Reserved":"Available",image:g[0],images:g,specs:[c.seats,c.transmission,`${Math.floor(p/1e3)}k km`,c.fuelType,h],carId:t,year:m,mileage:p,fuelType:c.fuelType,transmission:c.transmission,bodyCondition:h,addedDate:v.toISOString(),originalPrice:y?`\xa5${(u+2e4).toLocaleString()}`:void 0,stockQuantity:j%3+1,popularity:j%100+1};e.push(N)}}return e}catch(e){return console.error("Error generating stock from file system:",e),i()}}},19080:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25366:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52037:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,13926)),Promise.resolve().then(s.bind(s,88928)),Promise.resolve().then(s.bind(s,88079))},56568:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(37413),r=s(4536),l=s.n(r),n=s(40024),i=s(88928),c=s(88079),o=s(13926),d=s(17198);async function x({params:e}){let{locale:t}=await e,s=await (0,n.V)(t),r=await (0,d.l)();return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(i.default,{}),(0,a.jsx)("section",{className:"pt-20",children:(0,a.jsx)(o.default,{items:r,locale:t})}),(0,a.jsx)("section",{className:"py-16 bg-neutral-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm",children:[(0,a.jsx)("h3",{className:"text-2xl font-heading font-bold text-center mb-8",children:s.stock?.carModelPricingGuide||("en"===t?"Car Model Pricing Guide":"車種別価格ガイド")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-6 text-center",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg mb-2",children:"Toyota Voxy"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mb-2",children:"8-seater MPV"}),(0,a.jsx)("p",{className:"text-xl font-bold text-primary-600",children:"\xa5295,000-\xa5300,000"})]}),(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-6 text-center",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg mb-2",children:"Toyota Noah"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mb-2",children:"8-seater MPV"}),(0,a.jsx)("p",{className:"text-xl font-bold text-primary-600",children:"\xa5350,000"})]}),(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-6 text-center",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg mb-2",children:"Toyota Sienta"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mb-2",children:"7-seater Compact MPV"}),(0,a.jsx)("p",{className:"text-xl font-bold text-primary-600",children:"\xa5320,000"})]}),(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-6 text-center",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg mb-2",children:"Toyota Vitz"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mb-2",children:"5-seater Compact"}),(0,a.jsx)("p",{className:"text-xl font-bold text-primary-600",children:"\xa5325,000"})]}),(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-6 text-center",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg mb-2",children:"Toyota Yaris"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mb-2",children:"5-seater Compact"}),(0,a.jsx)("p",{className:"text-xl font-bold text-primary-600",children:"\xa5550,000"})]})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:s.stock?.allPricesInYen||("en"===t?"All prices in Japanese Yen (\xa5). Shipping and import duties not included.":"全ての価格は日本円（\xa5）表示です。送料・輸入関税は含まれません。")})})]}),(0,a.jsx)("div",{className:"mt-12",children:(0,a.jsx)(c.default,{locale:t,variant:"compact",showQR:!0})})]})}),(0,a.jsx)("section",{className:"py-20 bg-primary-600 text-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl lg:text-4xl font-heading font-bold mb-6",children:"en"===t?"Don't See What You're Looking For?":"お探しの商品が見つかりませんか？"}),(0,a.jsx)("p",{className:"text-xl text-primary-100 mb-8 max-w-3xl mx-auto",children:"en"===t?"Contact us with your specific requirements. We source new stock regularly and can help you find exactly what you need.":"ご希望の商品についてお問い合わせください。定期的に新しい在庫を調達しており、お客様のニーズに合った商品を見つけるお手伝いをいたします。"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(l(),{href:`/${t}/contact`,className:"bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-8 py-4 rounded-lg font-semibold transition-colors duration-200",children:s.navigation.contact}),(0,a.jsx)(l(),{href:`/${t}/buyers`,className:"border-2 border-white text-white hover:bg-white hover:text-primary-800 px-8 py-4 rounded-lg font-semibold transition-colors duration-200",children:s.navigation.buyers})]})]})}),(0,a.jsx)("footer",{className:"bg-neutral-800 text-white py-12",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"EM"})}),(0,a.jsx)("span",{className:"font-heading font-bold text-xl",children:"EBAM MOTORS"})]}),(0,a.jsx)("p",{className:"text-neutral-300",children:s.about?.mission||"Promote sustainable trade by giving used goods a second life where they are needed most."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg mb-4",children:s.contact.contactInfo}),(0,a.jsxs)("div",{className:"space-y-2 text-neutral-300",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:["Japan: ",s.about?.phone||"080-6985-2864"]}),(0,a.jsxs)("p",{children:["Ghana: ",s.about?.ghanaPhone||"+233245375692"]})]}),(0,a.jsx)("p",{children:s.about?.email||"<EMAIL>"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:["Japan: ",s.about?.location||"Japan, Saitama, Hanno City, Nagata"]}),(0,a.jsxs)("p",{children:["Ghana: ",s.about?.ghanaLocation||"Kumasi, Ghana"]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg mb-4",children:s.contact?.quickLinks||"Quick Links"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l(),{href:`/${t}/services`,className:"block text-neutral-300 hover:text-white transition-colors",children:s.navigation.services}),(0,a.jsx)(l(),{href:`/${t}/how-it-works`,className:"block text-neutral-300 hover:text-white transition-colors",children:s.navigation.howItWorks}),(0,a.jsx)(l(),{href:`/${t}/contact`,className:"block text-neutral-300 hover:text-white transition-colors",children:s.navigation.contact})]})]})]}),(0,a.jsx)("div",{className:"border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400",children:(0,a.jsx)("p",{children:"\xa9 2025 EBAM MOTORS. All rights reserved."})})]})})]})}},59647:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let o={children:["",{children:["[locale]",{children:["stock",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56568)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\[locale]\\stock\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,11434)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\[locale]\\stock\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/stock/page",pathname:"/[locale]/stock",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63486:(e,t,s)=>{"use strict";s.d(t,{default:()=>q});var a=s(60687),r=s(43210),l=s(85814),n=s.n(l),i=s(11860),c=s(12941),o=s(94478),d=s(19080),x=s(99270),m=s(80462),p=s(62688);let h=(0,p.A)("bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]]),u=(0,p.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var g=s(78272),j=s(63213),b=s(8819),y=s(48730),f=s(88233);function v({isOpen:e,onClose:t,locale:s,currentFilters:l,onApplySearch:n}){let{user:c,savedSearches:o,saveSearch:d,removeSavedSearch:p}=(0,j.A)(),[h,u]=(0,r.useState)(""),[g,v]=(0,r.useState)(!1);if(!e||!c)return null;let N=e=>{n&&(n(e.filters),t())},w=e=>{confirm("en"===s?"Are you sure you want to delete this search?":"この検索を削除してもよろしいですか？")&&p(e)},k=e=>{let t=[];return"all"!==e.category&&t.push(`${"en"===s?"Category":"カテゴリー"}: ${e.category}`),e.searchTerm&&t.push(`${"en"===s?"Search":"検索"}: "${e.searchTerm}"`),(0!==e.priceRange[0]||1e6!==e.priceRange[1])&&t.push(`${"en"===s?"Price":"価格"}: \xa5${e.priceRange[0].toLocaleString()} - \xa5${e.priceRange[1].toLocaleString()}`),(2e3!==e.yearRange[0]||2025!==e.yearRange[1])&&t.push(`${"en"===s?"Year":"年式"}: ${e.yearRange[0]} - ${e.yearRange[1]}`),e.fuelTypes.length>0&&t.push(`${"en"===s?"Fuel":"燃料"}: ${e.fuelTypes.join(", ")}`),e.transmissionTypes.length>0&&t.push(`${"en"===s?"Transmission":"トランスミッション"}: ${e.transmissionTypes.join(", ")}`),t.length>0?t.join(" • "):"en"===s?"No filters applied":"フィルターが適用されていません"};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-neutral-800 flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"en"===s?"Saved Searches":"保存済み検索"})]}),(0,a.jsx)("button",{onClick:t,className:"text-neutral-600 hover:text-neutral-800 transition-colors",children:(0,a.jsx)(i.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[l&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-primary-50 rounded-lg border border-primary-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-primary-800 mb-2 flex items-center space-x-2",children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===s?"Save Current Search":"現在の検索を保存"})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("p",{className:"text-sm text-primary-700 mb-2",children:"en"===s?"Current filters:":"現在のフィルター:"}),(0,a.jsx)("p",{className:"text-xs text-primary-600 bg-white p-2 rounded border",children:k(l)})]}),g?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("input",{type:"text",value:h,onChange:e=>u(e.target.value),placeholder:"en"===s?"Enter search name...":"検索名を入力...",className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm",autoFocus:!0}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{l&&h.trim()&&(d(h.trim(),l),u(""),v(!1),alert("en"===s?"Search saved successfully!":"検索が正常に保存されました！"))},disabled:!h.trim(),className:"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"en"===s?"Save":"保存"}),(0,a.jsx)("button",{onClick:()=>{v(!1),u("")},className:"text-neutral-600 hover:text-neutral-800 transition-colors text-sm",children:"en"===s?"Cancel":"キャンセル"})]})]}):(0,a.jsx)("button",{onClick:()=>v(!0),className:"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm",children:"en"===s?"Save This Search":"この検索を保存"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-4",children:["en"===s?"Your Saved Searches":"保存済み検索",(0,a.jsxs)("span",{className:"ml-2 text-sm font-normal text-neutral-600",children:["(",o.length,")"]})]}),0===o.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(x.A,{className:"w-12 h-12 text-neutral-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-neutral-600 mb-2",children:"en"===s?"No saved searches yet":"保存済み検索はまだありません"}),(0,a.jsx)("p",{className:"text-sm text-neutral-500",children:"en"===s?"Apply some filters and save your search for quick access later":"フィルターを適用して検索を保存すると、後で簡単にアクセスできます"})]}):(0,a.jsx)("div",{className:"space-y-3",children:o.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:bg-neutral-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-neutral-800 mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mb-2",children:k(e.filters)}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-neutral-500",children:[(0,a.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:["en"===s?"Created":"作成日",": ",new Date(e.createdAt).toLocaleDateString()]})]}),(0,a.jsxs)("span",{children:["en"===s?"Last used":"最終使用",": ",new Date(e.lastUsed).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("button",{onClick:()=>N(e),className:"bg-primary-600 text-white px-3 py-1 rounded text-sm hover:bg-primary-700 transition-colors",children:"en"===s?"Apply":"適用"}),(0,a.jsx)("button",{onClick:()=>w(e.id),className:"text-red-600 hover:text-red-700 transition-colors p-1",title:"en"===s?"Delete search":"検索を削除",children:(0,a.jsx)(f.A,{className:"w-4 h-4"})})]})]})},e.id))})]})]}),(0,a.jsx)("div",{className:"border-t p-4 bg-neutral-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-neutral-600",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===s?"Tip: Save complex searches to find vehicles faster":"ヒント: 複雑な検索を保存して車両をより早く見つけましょう"})]}),(0,a.jsx)("button",{onClick:t,className:"text-primary-600 hover:text-primary-700 transition-colors font-medium",children:"en"===s?"Close":"閉じる"})]})})]})})}function N({locale:e,onCategoryChange:t,onSearchChange:s,onFiltersChange:l,categories:n,compact:c=!1}){let[o,d]=(0,r.useState)("all"),[p,b]=(0,r.useState)(""),[y,f]=(0,r.useState)(!1),[N,w]=(0,r.useState)(!1),{user:k}=(0,j.A)(),[C,A]=(0,r.useState)([0,1e6]),[S,_]=(0,r.useState)([2e3,2025]),[M,P]=(0,r.useState)([0,2e5]),[D,T]=(0,r.useState)([]),[I,$]=(0,r.useState)([]),[R,V]=(0,r.useState)([]);(0,r.useCallback)(()=>{l({category:o,searchTerm:p,priceRange:C,yearRange:S,mileageRange:M,fuelTypes:D,transmissionTypes:I,bodyConditions:R})},[o,p,C,S,M,D,I,R,l]);let G=e=>{d(e),t(e)},z=(e,t,s)=>{s(t.includes(e)?t.filter(t=>t!==e):[...t,e])},L=()=>{d("all"),b(""),A([0,1e6]),_([2e3,2025]),P([0,2e5]),T([]),$([]),V([])},q=()=>{let e=0;return"all"!==o&&e++,p&&e++,(0!==C[0]||1e6!==C[1])&&e++,(2e3!==S[0]||2025!==S[1])&&e++,(0!==M[0]||2e5!==M[1])&&e++,D.length>0&&e++,I.length>0&&e++,R.length>0&&e++,e};return c?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===e?"Price Range (\xa5)":"価格帯 (\xa5)"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"range",min:"0",max:"1000000",step:"10000",value:C[1],onChange:e=>A([0,parseInt(e.target.value)]),className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-neutral-600",children:[(0,a.jsx)("span",{children:"\xa50"}),(0,a.jsxs)("span",{children:["\xa5",C[1].toLocaleString()]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"en"===e?"Year Range":"年式"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"range",min:"2000",max:"2025",value:S[0],onChange:e=>_([parseInt(e.target.value),S[1]]),className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-neutral-600",children:[(0,a.jsx)("span",{children:S[0]}),(0,a.jsx)("span",{children:"2025"})]})]})]}),(0,a.jsx)("button",{onClick:L,className:"w-full text-sm text-primary-600 hover:text-primary-700 py-2 border border-primary-200 rounded-lg hover:bg-primary-50 transition-colors",children:"en"===e?"Clear Filters":"フィルターをクリア"})]}):(0,a.jsxs)("section",{className:"py-8 bg-neutral-50 border-b",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-neutral-800",children:"en"===e?"Search & Filter Stock":"在庫検索・絞り込み"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[q()>0&&(0,a.jsxs)("button",{onClick:L,className:"flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-lg hover:bg-white transition-colors",children:[(0,a.jsx)(i.A,{className:"w-4 h-4"}),"en"===e?`Clear Filters (${q()})`:`フィルタークリア (${q()})`]}),k&&(0,a.jsxs)("button",{onClick:()=>w(!0),className:"flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-lg hover:bg-white transition-colors",children:[(0,a.jsx)(h,{className:"w-4 h-4"}),"en"===e?"Saved Searches":"保存済み検索"]}),(0,a.jsxs)("button",{onClick:()=>f(!y),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"en"===e?"Advanced Filters":"詳細フィルター",y?(0,a.jsx)(u,{className:"w-4 h-4"}):(0,a.jsx)(g.A,{className:"w-4 h-4"})]})]})]}),(0,a.jsx)("div",{id:"search-section",className:"w-full",children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s(p)},className:"flex flex-col sm:flex-row gap-3 w-full",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5"}),(0,a.jsx)("input",{id:"search-input",type:"text",value:p,onChange:e=>{let t=e.target.value;b(t),s(t)},placeholder:"en"===e?"Search by make, model, year...":"メーカー、モデル、年式で検索...",className:"w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-base"})]}),(0,a.jsx)("button",{type:"submit",className:"px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium text-base whitespace-nowrap",children:"en"===e?"Search":"検索"})]})}),(0,a.jsxs)("div",{id:"filter-section",className:"w-full",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-neutral-700 mb-3",children:"en"===e?"Categories":"カテゴリー"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:n.map(e=>(0,a.jsxs)("button",{onClick:()=>G(e.id),className:`px-4 py-2 rounded-full border transition-colors duration-200 text-sm font-medium whitespace-nowrap ${o===e.id?"bg-primary-600 text-white border-primary-600":"bg-white border-neutral-300 hover:border-primary-500 hover:text-primary-600"}`,children:[e.name," (",e.count,")"]},e.id))})]}),y&&(0,a.jsx)("div",{className:"bg-white rounded-lg border border-neutral-200 p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700",children:"en"===e?"Price Range (\xa5)":"価格帯 (\xa5)"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"number",value:C[0],onChange:e=>A([parseInt(e.target.value)||0,C[1]]),placeholder:"Min",className:"w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),(0,a.jsx)("span",{className:"text-neutral-500",children:"-"}),(0,a.jsx)("input",{type:"number",value:C[1],onChange:e=>A([C[0],parseInt(e.target.value)||1e6]),placeholder:"Max",className:"w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsx)("input",{type:"range",min:"0",max:"1000000",step:"10000",value:C[0],onChange:e=>A([parseInt(e.target.value),C[1]]),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"}),(0,a.jsx)("input",{type:"range",min:"0",max:"1000000",step:"10000",value:C[1],onChange:e=>A([C[0],parseInt(e.target.value)]),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700",children:"en"===e?"Year Range":"年式"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"number",value:S[0],onChange:e=>_([parseInt(e.target.value)||2e3,S[1]]),placeholder:"From",min:"1990",max:"2025",className:"w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),(0,a.jsx)("span",{className:"text-neutral-500",children:"-"}),(0,a.jsx)("input",{type:"number",value:S[1],onChange:e=>_([S[0],parseInt(e.target.value)||2025]),placeholder:"To",min:"1990",max:"2025",className:"w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsx)("input",{type:"range",min:"1990",max:"2025",value:S[0],onChange:e=>_([parseInt(e.target.value),S[1]]),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"}),(0,a.jsx)("input",{type:"range",min:"1990",max:"2025",value:S[1],onChange:e=>_([S[0],parseInt(e.target.value)]),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700",children:"en"===e?"Mileage Range (km)":"走行距離 (km)"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"number",value:M[0],onChange:e=>P([parseInt(e.target.value)||0,M[1]]),placeholder:"Min",className:"w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),(0,a.jsx)("span",{className:"text-neutral-500",children:"-"}),(0,a.jsx)("input",{type:"number",value:M[1],onChange:e=>P([M[0],parseInt(e.target.value)||2e5]),placeholder:"Max",className:"w-full px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsx)("input",{type:"range",min:"0",max:"200000",step:"5000",value:M[0],onChange:e=>P([parseInt(e.target.value),M[1]]),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"}),(0,a.jsx)("input",{type:"range",min:"0",max:"200000",step:"5000",value:M[1],onChange:e=>P([M[0],parseInt(e.target.value)]),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700",children:"en"===e?"Fuel Type":"燃料タイプ"}),(0,a.jsx)("div",{className:"space-y-2",children:["Gasoline","Diesel","Hybrid","Electric","LPG"].map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.includes(e),onChange:()=>z(e,D,T),className:"w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"}),(0,a.jsx)("span",{className:"text-sm text-neutral-700",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700",children:"en"===e?"Transmission":"トランスミッション"}),(0,a.jsx)("div",{className:"space-y-2",children:["Manual","Automatic","CVT","Semi-Automatic"].map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.includes(e),onChange:()=>z(e,I,$),className:"w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"}),(0,a.jsx)("span",{className:"text-sm text-neutral-700",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700",children:"en"===e?"Body Condition":"ボディコンディション"}),(0,a.jsx)("div",{className:"space-y-2",children:["Excellent","Very Good","Good","Fair","Needs Work"].map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:R.includes(e),onChange:()=>z(e,R,V),className:"w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"}),(0,a.jsx)("span",{className:"text-sm text-neutral-700",children:e})]},e))})]})]})})]})}),(0,a.jsx)(v,{isOpen:N,onClose:()=>w(!1),locale:e,currentFilters:{category:o,searchTerm:p,priceRange:C,yearRange:S,mileageRange:M,fuelTypes:D,transmissionTypes:I,bodyConditions:R},onApplySearch:e=>{d(e.category),b(e.searchTerm),A(e.priceRange),_(e.yearRange),P(e.mileageRange),T(e.fuelTypes),$(e.transmissionTypes),V(e.bodyConditions),t(e.category),s(e.searchTerm)}})]})}var w=s(30474),k=s(67760),C=s(13861),A=s(28561);function S({items:e,locale:t}){let[s,l]=(0,r.useState)(null),[i,c]=(0,r.useState)({}),{user:o,isFavorite:d,addToFavorites:x,removeFromFavorites:m}=(0,j.A)(),p=e=>{l(e)},h=(e,s)=>{if(e.stopPropagation(),!o)return void alert("en"===t?"Please sign in to add favorites":"お気に入りに追加するにはサインインしてください");d(s.carId)?m(s.carId):x(s.carId)},u=()=>{l(null)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group cursor-pointer transform hover:scale-105",onClick:()=>p(e),children:[(0,a.jsxs)("div",{className:"relative overflow-hidden",onClick:()=>p(e),children:[(0,a.jsxs)("div",{className:"h-48 relative overflow-hidden",children:[(0,a.jsx)(w.default,{src:e.images&&e.images.length>0?e.images[i[e.id]||0]:e.image,alt:e.title,fill:!0,className:"object-cover group-hover:scale-110 transition-all duration-500 ease-out",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-300"}),e.images&&e.images.length>1&&(0,a.jsxs)("div",{className:"absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded-full text-xs",children:[(i[e.id]||0)+1,"/",e.images.length]})]}),e.title.includes("Toyota Voxy")&&(0,a.jsx)("div",{className:"absolute top-4 left-4 px-2 py-1 bg-primary-600 text-white text-xs font-semibold rounded-full",children:"8-Seater"}),(0,a.jsxs)("div",{className:"absolute top-4 right-4 flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:t=>h(t,e),className:`p-2 rounded-full transition-colors ${o&&d(e.carId)?"bg-red-100 text-red-600 hover:bg-red-200":"bg-white/80 text-neutral-600 hover:bg-white hover:text-red-600"}`,title:"en"===t?"Add to favorites":"お気に入りに追加",children:(0,a.jsx)(k.A,{className:`w-4 h-4 ${o&&d(e.carId)?"fill-current":""}`})}),(0,a.jsx)("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${"Available"===e.status?"bg-green-100 text-green-800":"Reserved"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.status})]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-neutral-800 mb-2 group-hover:text-primary-600 transition-colors duration-200",children:e.title}),(0,a.jsx)("p",{className:"text-2xl font-bold text-primary-600 mb-3",children:e.price}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:e.specs.map((e,t)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-neutral-100 text-neutral-600 text-xs rounded-full",children:e},t))}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("span",{className:"text-sm text-neutral-500",children:["\uD83D\uDCCD ",e.location]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:t=>{t.stopPropagation(),p(e)},className:"text-neutral-600 hover:text-primary-600 transition-colors duration-200",title:"en"===t?"Quick View":"クイックビュー",children:(0,a.jsx)(C.A,{className:"w-4 h-4"})}),(0,a.jsx)(n(),{href:`/${t}/stock/${e.carId}`,onClick:e=>e.stopPropagation(),className:"text-primary-600 hover:text-primary-700 font-medium text-sm transition-colors duration-200",children:"en"===t?"Details":"詳細"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n(),{href:`/${t}/stock/${e.carId}?quickbuy=true`,onClick:e=>e.stopPropagation(),className:"flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-200 flex items-center justify-center space-x-2",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===t?"Quick Buy":"クイック購入"})]}),(0,a.jsxs)("button",{onClick:t=>{t.stopPropagation(),p(e)},className:"px-4 py-2 border border-neutral-300 text-neutral-700 rounded-lg font-semibold text-sm hover:bg-neutral-50 transition-all duration-200 flex items-center space-x-1",children:[(0,a.jsx)(C.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===t?"View":"表示"})]})]})]})]},e.id))}),0===e.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-neutral-500 text-lg",children:"en"===t?"No items found in this category.":"このカテゴリーには商品がありません。"})}),s&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-slideInUp",children:[(0,a.jsxs)("div",{className:"sticky top-0 bg-white border-b border-neutral-200 px-4 sm:px-6 py-4 flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl sm:text-2xl font-heading font-bold text-neutral-800",children:s.title}),(0,a.jsx)("button",{onClick:u,className:"text-neutral-500 hover:text-neutral-700 text-2xl sm:text-3xl transition-all duration-300 hover:scale-110 hover:rotate-90 p-1",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"p-4 sm:p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6",children:[(0,a.jsxs)("div",{className:"relative h-48 sm:h-64 rounded-lg overflow-hidden animate-slideInLeft group",children:[(0,a.jsx)(w.default,{src:s.image,alt:s.title,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,a.jsxs)("div",{className:"space-y-4 animate-slideInRight",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-neutral-800 mb-2",children:s.title}),(0,a.jsx)("p",{className:"text-2xl sm:text-3xl font-bold text-primary-600 mb-4",children:s.price})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:s.specs.map((e,t)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full font-medium",children:e},t))}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,a.jsxs)("span",{className:"text-neutral-600",children:["\uD83D\uDCCD ",s.location]}),(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-semibold ${"Available"===s.status?"bg-green-100 text-green-800":"Reserved"===s.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:s.status})]})]})]}),"cars"===s.category&&(0,a.jsxs)("div",{className:"border-t border-neutral-200 pt-6",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-neutral-800 mb-4",children:"en"===t?"Vehicle Details":"車両詳細"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)("table",{className:"w-full text-sm",children:(0,a.jsxs)("tbody",{children:[(0,a.jsxs)("tr",{className:"border-b border-neutral-200",children:[(0,a.jsx)("td",{className:"bg-neutral-100 px-2 sm:px-3 py-2 font-medium text-neutral-700 w-1/3",children:"en"===t?"Stock ID":"在庫ID"}),(0,a.jsxs)("td",{className:"px-2 sm:px-3 py-2 text-neutral-800",children:["EBM",s.id.toString().padStart(6,"0")]})]}),(0,a.jsxs)("tr",{className:"border-b border-neutral-200",children:[(0,a.jsx)("td",{className:"bg-neutral-100 px-2 sm:px-3 py-2 font-medium text-neutral-700 w-1/3",children:"en"===t?"Color":"色"}),(0,a.jsx)("td",{className:"px-2 sm:px-3 py-2 text-neutral-800",children:({"toyota_voxy_2010_001.jpg":{en:"Silver Metallic",ja:"シルバーメタリック"},"toyota_voxy_2010_002.jpg":{en:"Dark Blue Metallic",ja:"ダークブルーメタリック"},"toyota_voxy_2010_003.jpg":{en:"White Pearl",ja:"ホワイトパール"},"toyota_voxy_2010_004.jpg":{en:"Black Metallic",ja:"ブラックメタリック"},"toyota_voxy_2010_005.jpg":{en:"Gray Metallic",ja:"グレーメタリック"},"toyota_voxy_2010_006.jpg":{en:"Red Metallic",ja:"レッドメタリック"},"toyota_voxy_2010_007.jpg":{en:"Blue Metallic",ja:"ブルーメタリック"},"toyota_voxy_2012_001.jpg":{en:"Pearl White",ja:"パールホワイト"},"toyota_voxy_2012_002.jpg":{en:"Attitude Black Mica",ja:"アティチュードブラックマイカ"},"toyota_voxy_2012_003.jpg":{en:"Silver Metallic",ja:"シルバーメタリック"},"toyota_voxy_2012_004.jpg":{en:"Dark Purple Mica Metallic",ja:"ダークパープルマイカメタリック"},"toyota_voxy_2012_005.jpg":{en:"Bordeaux Red Mica Metallic",ja:"ボルドーレッドマイカメタリック"},"toyota_noah_2010_001.jpg":{en:"Super White II",ja:"スーパーホワイトII"},"toyota_noah_2010_002.jpg":{en:"Silver Metallic",ja:"シルバーメタリック"},"toyota_noah_2010_003.jpg":{en:"Black Mica",ja:"ブラックマイカ"},"toyota_noah_2010_004.jpg":{en:"Dark Blue Mica Metallic",ja:"ダークブルーマイカメタリック"},"toyota_sienta_2014_001.jpg":{en:"White Pearl Crystal Shine",ja:"ホワイトパールクリスタルシャイン"},"toyota_sienta_2014_002.jpg":{en:"Silver Metallic",ja:"シルバーメタリック"},"toyota_sienta_2014_003.jpg":{en:"Black Mica",ja:"ブラックマイカ"},"toyota_sienta_2014_004.jpg":{en:"Blue Metallic",ja:"ブルーメタリック"},"toyota_vitz_2014_001.jpg":{en:"Super White II",ja:"スーパーホワイトII"},"toyota_vitz_2014_002.jpg":{en:"Silver Metallic",ja:"シルバーメタリック"},"toyota_vitz_2014_003.jpg":{en:"Black Mica",ja:"ブラックマイカ"},"toyota_vitz_2014_004.jpg":{en:"Red Mica Metallic",ja:"レッドマイカメタリック"},"toyota_yaris_2015_001.jpg":{en:"White Pearl Crystal Shine",ja:"ホワイトパールクリスタルシャイン"},"toyota_yaris_2015_002.jpg":{en:"Silver Metallic",ja:"シルバーメタリック"},"toyota_yaris_2015_003.jpg":{en:"Attitude Black Mica",ja:"アティチュードブラックマイカ"},"toyota_yaris_2015_004.jpg":{en:"Blue Mica Metallic",ja:"ブルーマイカメタリック"}}[s.image.split("/").pop()||""]||{en:"Standard Color",ja:"標準色"})[t]})]})]})})})]}),(0,a.jsx)("div",{className:"border-t border-neutral-200 pt-6",children:(0,a.jsxs)("div",{className:"bg-primary-50 rounded-lg p-4 text-center",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-primary-800 mb-2",children:"en"===t?"Interested in this item?":"この商品にご興味がありますか？"}),(0,a.jsx)("p",{className:"text-primary-600 mb-4",children:"en"===t?"Contact us for more details, pricing, and shipping information.":"詳細、価格、配送情報についてお問い合わせください。"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsxs)("a",{href:"https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G",target:"_blank",rel:"noopener noreferrer",className:"bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center",children:[(0,a.jsx)("span",{className:"mr-2",children:"\uD83D\uDCAC"}),"WhatsApp"]}),(0,a.jsx)("button",{onClick:u,className:"bg-neutral-200 hover:bg-neutral-300 text-neutral-700 px-6 py-2 rounded-lg font-semibold transition-colors duration-200",children:"en"===t?"Close":"閉じる"})]})]})})]})]})})]})}var _=s(12640),M=s(6943),P=s(25366);let D=(0,p.A)("arrow-up-narrow-wide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]),T=(0,p.A)("arrow-down-wide-narrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]]);function I({items:e,locale:t,hideSpecialSections:s=!1,children:l}){let[n,i]=(0,r.useState)("grid"),[c,o]=(0,r.useState)("date"),[x,m]=(0,r.useState)("desc"),p=(0,r.useMemo)(()=>e.map(e=>({...e,addedDate:e.addedDate||new Date(Date.now()-30*Math.random()*864e5).toISOString(),originalPrice:e.originalPrice||(Math.random()>.7?`\xa5${(parseInt(e.price.replace(/[¥,]/g,""))+Math.floor(1e5*Math.random())).toLocaleString()}`:void 0),stockQuantity:e.stockQuantity||Math.floor(5*Math.random())+1,popularity:e.popularity||Math.floor(100*Math.random())+1})),[e]),h=(0,r.useMemo)(()=>[...p].sort((e,t)=>{let s,a;switch(c){case"price":s=parseInt(e.price.replace(/[¥,]/g,""))||0,a=parseInt(t.price.replace(/[¥,]/g,""))||0;break;case"year":s=e.year||0,a=t.year||0;break;case"popularity":s=e.popularity||0,a=t.popularity||0;break;case"date":s=new Date(e.addedDate||0).getTime(),a=new Date(t.addedDate||0).getTime();break;case"mileage":s=e.mileage||0,a=t.mileage||0;break;default:return 0}return"asc"===x?s-a:a-s}),[p,c,x]),u=(0,r.useMemo)(()=>{let e=new Date;return e.setDate(e.getDate()-7),h.filter(t=>new Date(t.addedDate||0)>e)},[h]),g=(0,r.useMemo)(()=>h.filter(e=>e.originalPrice),[h]),j=e=>{c===e?m("asc"===x?"desc":"asc"):(o(e),m("desc"))};return(0,a.jsxs)("div",{className:"space-y-6",children:[!s&&u.length>0&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 border border-blue-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-800",children:"en"===t?"Recently Added":"最近追加された車両"}),(0,a.jsx)("span",{className:"bg-blue-600 text-white text-xs px-2 py-1 rounded-full",children:u.length})]}),(0,a.jsx)("p",{className:"text-blue-700 text-sm mb-4",children:"en"===t?"New vehicles added in the last 7 days":"過去7日間に追加された新しい車両"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:u.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg p-3 border",children:[(0,a.jsx)("div",{className:"font-medium text-neutral-800",children:e.title}),(0,a.jsx)("div",{className:"text-primary-600 font-bold",children:e.price}),(0,a.jsxs)("div",{className:"text-xs text-neutral-600",children:["en"===t?"Added":"追加日",": ",new Date(e.addedDate||"").toLocaleDateString()]})]},e.id))})]}),!s&&g.length>0&&(0,a.jsxs)("div",{className:"bg-red-50 rounded-lg p-6 border border-red-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(_.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-800",children:"en"===t?"Price Reduced":"価格下げ"}),(0,a.jsx)("span",{className:"bg-red-600 text-white text-xs px-2 py-1 rounded-full",children:g.length})]}),(0,a.jsx)("p",{className:"text-red-700 text-sm mb-4",children:"en"===t?"Vehicles with reduced prices - limited time offers!":"価格が下がった車両 - 期間限定オファー！"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:g.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg p-3 border",children:[(0,a.jsx)("div",{className:"font-medium text-neutral-800",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"text-primary-600 font-bold",children:e.price}),(0,a.jsx)("div",{className:"text-neutral-500 line-through text-sm",children:e.originalPrice})]}),(0,a.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"en"===t?"Price Reduced!":"価格下げ！"})]},e.id))})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg border p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-neutral-700",children:"en"===t?"View:":"表示:"}),(0,a.jsxs)("div",{className:"flex bg-neutral-100 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>i("grid"),className:`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${"grid"===n?"bg-white text-primary-600 shadow-sm":"text-neutral-600 hover:text-neutral-800"}`,children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===t?"Grid":"グリッド"})]}),(0,a.jsxs)("button",{onClick:()=>i("list"),className:`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${"list"===n?"bg-white text-primary-600 shadow-sm":"text-neutral-600 hover:text-neutral-800"}`,children:[(0,a.jsx)(P.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===t?"List":"リスト"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-neutral-700",children:"en"===t?"Sort by:":"ソート:"}),(0,a.jsx)("select",{value:c,onChange:e=>j(e.target.value),className:"border border-neutral-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[{value:"date",label:"en"===t?"Date Added":"追加日"},{value:"price",label:"en"===t?"Price":"価格"},{value:"year",label:"en"===t?"Year":"年式"},{value:"popularity",label:"en"===t?"Popularity":"人気"},{value:"mileage",label:"en"===t?"Mileage":"走行距離"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,a.jsx)("button",{onClick:()=>m("asc"===x?"desc":"asc"),className:"p-2 text-neutral-600 hover:text-primary-600 transition-colors",title:"asc"===x?"en"===t?"Ascending":"昇順":"en"===t?"Descending":"降順",children:"asc"===x?(0,a.jsx)(D,{className:"w-4 h-4"}):(0,a.jsx)(T,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-neutral-600",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[h.length," ","en"===t?"vehicles":"台"]})]})]})]})}),l({displayItems:h,viewMode:n,sortBy:c,sortOrder:x})]})}var $=s(97992),R=s(40228),V=s(51939),G=s(64338),z=s(84027);function L({items:e,locale:t,onItemClick:s}){let{user:r,isFavorite:l,addToFavorites:i,removeFromFavorites:c}=(0,j.A)(),o=(e,s)=>{if(e.stopPropagation(),!r)return void alert("en"===t?"Please sign in to add favorites":"お気に入りに追加するにはサインインしてください");l(s.carId)?c(s.carId):i(s.carId)},x=e=>{let t=new Date;return t.setDate(t.getDate()-7),new Date(e)>t},m=e=>e<=1?{color:"text-red-600",label:"en"===t?"Last one!":"最後の1台！"}:e<=3?{color:"text-yellow-600",label:"en"===t?"Limited stock":"在庫僅少"}:{color:"text-green-600",label:"en"===t?"In stock":"在庫あり"};return 0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(d.A,{className:"w-16 h-16 text-neutral-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-600 mb-2",children:"en"===t?"No vehicles found":"車両が見つかりません"}),(0,a.jsx)("p",{className:"text-neutral-500",children:"en"===t?"Try adjusting your search criteria":"検索条件を調整してみてください"})]}):(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>{let i=m(e.stockQuantity||1);return(0,a.jsx)("div",{className:"bg-white rounded-lg border hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden",onClick:()=>s(e),children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row",children:[(0,a.jsxs)("div",{className:"relative w-full md:w-80 h-48 md:h-40 flex-shrink-0",children:[(0,a.jsx)(w.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 320px"}),(0,a.jsxs)("div",{className:"absolute top-3 left-3 flex flex-col space-y-2",children:[x(e.addedDate||"")&&(0,a.jsxs)("span",{className:"bg-blue-600 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"en"===t?"New":"新着"})]}),e.originalPrice&&(0,a.jsxs)("span",{className:"bg-red-600 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1",children:[(0,a.jsx)(_.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"en"===t?"Price Cut":"値下げ"})]})]}),(0,a.jsxs)("div",{className:"absolute top-3 right-3 flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:t=>o(t,e),className:`p-2 rounded-full transition-colors ${r&&l(e.carId)?"bg-red-100 text-red-600 hover:bg-red-200":"bg-white/80 text-neutral-600 hover:bg-white hover:text-red-600"}`,children:(0,a.jsx)(k.A,{className:`w-4 h-4 ${r&&l(e.carId)?"fill-current":""}`})}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${"Available"===e.status?"bg-green-100 text-green-800":"Reserved"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.status})]})]}),(0,a.jsx)("div",{className:"flex-1 p-6",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-neutral-800 mb-1 hover:text-primary-600 transition-colors",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-neutral-600",children:[(0,a.jsx)($.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.location})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.originalPrice&&(0,a.jsx)("span",{className:"text-neutral-500 line-through text-sm",children:e.originalPrice}),(0,a.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:e.price})]}),(0,a.jsx)("div",{className:`text-xs font-medium ${i.color}`,children:i.label})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[e.year&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-neutral-600",children:[(0,a.jsx)(R.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.year})]}),e.mileage&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-neutral-600",children:[(0,a.jsx)(V.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[Math.floor(e.mileage/1e3),"k km"]})]}),e.fuelType&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-neutral-600",children:[(0,a.jsx)(G.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.fuelType})]}),e.transmission&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-neutral-600",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.transmission})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.specs.slice(0,4).map((e,t)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-neutral-100 text-neutral-600 text-xs rounded-full",children:e},t)),e.specs.length>4&&(0,a.jsxs)("span",{className:"px-2 py-1 bg-neutral-100 text-neutral-600 text-xs rounded-full",children:["+",e.specs.length-4," ","en"===t?"more":"その他"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-neutral-500",children:[e.addedDate&&(0,a.jsxs)("span",{children:["en"===t?"Added":"追加日",": ",new Date(e.addedDate).toLocaleDateString()]}),e.stockQuantity&&(0,a.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,a.jsx)(d.A,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[e.stockQuantity," ","en"===t?"available":"台在庫"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{onClick:t=>{t.stopPropagation(),s(e)},className:"flex items-center space-x-2 text-neutral-600 hover:text-primary-600 transition-colors border border-neutral-300 px-3 py-2 rounded-lg hover:bg-neutral-50",children:[(0,a.jsx)(C.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"en"===t?"View":"表示"})]}),(0,a.jsxs)(n(),{href:`/${t}/stock/${e.carId}?quickbuy=true`,onClick:e=>e.stopPropagation(),className:"flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===t?"Quick Buy":"クイック購入"})]})]})]})]})})]})},e.id)})})}function q({items:e,locale:t}){let[s,l]=(0,r.useState)("cars"),[p,h]=(0,r.useState)(""),[u,g]=(0,r.useState)(null),[j,b]=(0,r.useState)(!1),[y,f]=(0,r.useState)({category:"cars",searchTerm:"",priceRange:[0,1e6],yearRange:[2e3,2025],mileageRange:[0,2e5],fuelTypes:[],transmissionTypes:[],bodyConditions:[]}),v=(0,r.useCallback)(e=>{l(e)},[]),w=(0,r.useCallback)(e=>{h(e)},[]),k=(0,r.useCallback)(e=>{f(e)},[]),C=e=>parseInt(e.replace(/[¥,]/g,""))||0,A=e=>{let t=e.match(/(\d{4})/);return t?parseInt(t[1]):2e3},_=e=>{let t=e.find(e=>e.includes("k km"));if(t){let e=t.match(/(\d+)k km/);return e?1e3*parseInt(e[1]):0}return 0},M=e=>{let t=["Gasoline","Diesel","Hybrid","Electric","LPG"];return e.find(e=>t.includes(e))||"Gasoline"},P=e=>{let t=["Manual","Automatic","CVT","Semi-Automatic"];return e.find(e=>t.includes(e))||"Automatic"},D=e.filter(e=>{if("all"!==y.category&&e.category!==y.category||y.searchTerm&&!e.title.toLowerCase().includes(y.searchTerm.toLowerCase()))return!1;if("cars"===e.category){let t=C(e.price);if(t<y.priceRange[0]||t>y.priceRange[1])return!1;let s=A(e.title);if(s<y.yearRange[0]||s>y.yearRange[1])return!1;let a=_(e.specs);if(a<y.mileageRange[0]||a>y.mileageRange[1])return!1;if(y.fuelTypes.length>0){let t=M(e.specs);if(!y.fuelTypes.includes(t))return!1}if(y.transmissionTypes.length>0){let t=P(e.specs);if(!y.transmissionTypes.includes(t))return!1}if(y.bodyConditions.length>0){let t=["Excellent","Very Good","Good","Fair","Needs Work"],s=t[e.id%t.length];if(!y.bodyConditions.includes(s))return!1}}return!0}),T=[{id:"all",name:"en"===t?"All Items":"全商品",count:D.length},{id:"cars",name:"en"===t?"Cars":"車",count:D.filter(e=>"cars"===e.category).length}],$=e=>{g(e)};return(0,a.jsxs)("div",{className:"flex min-h-screen",children:[(0,a.jsx)("button",{onClick:()=>b(!j),className:"lg:hidden fixed top-24 left-4 z-50 bg-white shadow-lg rounded-lg p-3 border",children:j?(0,a.jsx)(i.A,{className:"w-5 h-5"}):(0,a.jsx)(c.A,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:`
        fixed lg:static inset-y-0 left-0 z-40 w-80 bg-white border-r border-neutral-200 transform transition-transform duration-300 ease-in-out
        ${j?"translate-x-0":"-translate-x-full lg:translate-x-0"}
      `,children:(0,a.jsxs)("div",{className:"h-full overflow-y-auto pt-20 lg:pt-6",children:[(0,a.jsxs)("div",{className:"px-6 pb-4 border-b border-neutral-200",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-neutral-800",children:"en"===t?"Browse Stock":"在庫を見る"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mt-1",children:"en"===t?"Select a category to view items":"カテゴリを選択してアイテムを表示"})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-neutral-700 uppercase tracking-wide mb-4",children:"en"===t?"Categories":"カテゴリ"}),(0,a.jsx)("nav",{className:"space-y-2",children:[{id:"cars",name:"en"===t?"Cars":"車",icon:o.A,count:e.filter(e=>"cars"===e.category).length},{id:"all",name:"en"===t?"All Items":"全商品",icon:d.A,count:e.length}].map(e=>{let t=e.icon,r=s===e.id;return(0,a.jsxs)("button",{onClick:()=>{l(e.id),f(t=>({...t,category:e.id})),b(!1)},className:`
                      w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-all duration-200
                      ${r?"bg-primary-100 text-primary-700 border border-primary-200":"text-neutral-700 hover:bg-neutral-50 hover:text-neutral-900"}
                    `,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(t,{className:`w-5 h-5 ${r?"text-primary-600":"text-neutral-500"}`}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]}),(0,a.jsx)("span",{className:`
                      text-xs px-2 py-1 rounded-full
                      ${r?"bg-primary-200 text-primary-700":"bg-neutral-200 text-neutral-600"}
                    `,children:e.count})]},e.id)})})]}),(0,a.jsxs)("div",{className:"px-6 pb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-neutral-700 uppercase tracking-wide mb-4",children:"en"===t?"Quick Search":"クイック検索"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"en"===t?"Search items...":"アイテムを検索...",value:p,onChange:e=>{h(e.target.value),f(t=>({...t,searchTerm:e.target.value}))},className:"w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),"cars"===s&&(0,a.jsxs)("div",{className:"px-6 pb-6 border-t border-neutral-200 pt-6",children:[(0,a.jsxs)("h3",{className:"text-sm font-semibold text-neutral-700 uppercase tracking-wide mb-4 flex items-center",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"en"===t?"Car Filters":"車のフィルター"]}),(0,a.jsx)(N,{locale:t,onCategoryChange:v,onSearchChange:w,onFiltersChange:k,categories:T,compact:!0})]})]})}),j&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-30 lg:hidden",onClick:()=>b(!1)}),(0,a.jsxs)("div",{className:"flex-1 lg:ml-0 flex",children:[(0,a.jsxs)("div",{className:"flex-1 p-6 pt-20 lg:pt-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl lg:text-3xl font-bold text-neutral-800 mb-2",children:T.find(e=>e.id===s)?.name||("en"===t?"Stock":"在庫")}),(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===t?`Showing ${D.length} items`:`${D.length}件のアイテムを表示中`})]}),(0,a.jsx)(I,{items:D,locale:t,hideSpecialSections:!0,children:({displayItems:e,viewMode:r})=>"grid"===r?(0,a.jsx)(S,{items:e,locale:t,filteredCategory:s,searchTerm:p}):(0,a.jsx)(L,{items:e,locale:t,onItemClick:$})})]}),(0,a.jsx)("div",{className:"hidden xl:block w-80 p-6 pt-20 lg:pt-6 border-l border-neutral-200 bg-neutral-50",children:(0,a.jsxs)("div",{className:"sticky top-6 space-y-6",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-neutral-800 mb-4",children:"en"===t?"Featured":"注目"}),(()=>{let e=D.filter(e=>{let t=new Date;return t.setDate(t.getDate()-7),new Date(e.addedDate||0)>t});return e.length>0&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full"}),(0,a.jsx)("h3",{className:"text-sm font-semibold text-blue-800",children:"en"===t?"Recently Added":"最近追加"}),(0,a.jsx)("span",{className:"bg-blue-600 text-white text-xs px-2 py-1 rounded-full",children:e.length})]}),(0,a.jsx)("p",{className:"text-blue-700 text-xs mb-3",children:"en"===t?"New in the last 7 days":"過去7日間の新着"}),(0,a.jsx)("div",{className:"space-y-2",children:e.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg p-3 border",children:[(0,a.jsx)("div",{className:"font-medium text-sm text-neutral-800 truncate",children:e.title}),(0,a.jsx)("div",{className:"text-primary-600 font-bold text-sm",children:e.price}),(0,a.jsx)("div",{className:"text-xs text-neutral-600",children:new Date(e.addedDate||"").toLocaleDateString()})]},e.id))}),e.length>3&&(0,a.jsx)("button",{className:"text-blue-600 text-xs mt-2 hover:text-blue-700",children:"en"===t?`View all ${e.length}`:`全${e.length}件を見る`})]})})(),(()=>{let e=D.filter(e=>e.originalPrice);return e.length>0&&(0,a.jsxs)("div",{className:"bg-red-50 rounded-lg p-4 border border-red-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-600 rounded-full"}),(0,a.jsx)("h3",{className:"text-sm font-semibold text-red-800",children:"en"===t?"Price Reduced":"価格下げ"}),(0,a.jsx)("span",{className:"bg-red-600 text-white text-xs px-2 py-1 rounded-full",children:e.length})]}),(0,a.jsx)("p",{className:"text-red-700 text-xs mb-3",children:"en"===t?"Great deals available":"お得な価格"}),(0,a.jsx)("div",{className:"space-y-2",children:e.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg p-3 border",children:[(0,a.jsx)("div",{className:"font-medium text-sm text-neutral-800 truncate",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"text-primary-600 font-bold text-sm",children:e.price}),(0,a.jsx)("div",{className:"text-neutral-500 line-through text-xs",children:e.originalPrice})]}),(0,a.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"en"===t?"Price Reduced!":"価格下げ！"})]},e.id))}),e.length>3&&(0,a.jsx)("button",{className:"text-red-600 text-xs mt-2 hover:text-red-700",children:"en"===t?`View all ${e.length}`:`全${e.length}件を見る`})]})})()]})})]}),u&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-neutral-800",children:u.title}),(0,a.jsx)("button",{onClick:()=>g(null),className:"text-neutral-600 hover:text-neutral-800 transition-colors",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===t?"Quick view modal - implement detailed view here":"クイックビューモーダル - 詳細ビューをここに実装"}),(0,a.jsx)(n(),{href:`/${t}/stock/${u.carId}`,className:"inline-block mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors",children:"en"===t?"View Full Details":"詳細を見る"})]})]})})})]})}},65189:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,63486)),Promise.resolve().then(s.bind(s,3181)),Promise.resolve().then(s.bind(s,46841))},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,7445,1658,5814,8898,6533,1033,5839,1937,3181,6488],()=>s(59647));module.exports=a})();