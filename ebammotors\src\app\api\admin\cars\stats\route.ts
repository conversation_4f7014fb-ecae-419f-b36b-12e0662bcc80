import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { sql } from '@vercel/postgres';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get car statistics
    const stats = await getCarStats();

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching car stats:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch car statistics' },
      { status: 500 }
    );
  }
}

async function getCarStats() {
  try {
    // Get total cars
    const totalResult = await sql`SELECT COUNT(*) as count FROM cars`;
    const total = parseInt(totalResult.rows[0]?.count || '0');

    // Get available cars
    const availableResult = await sql`SELECT COUNT(*) as count FROM cars WHERE LOWER(status) = 'available'`;
    const available = parseInt(availableResult.rows[0]?.count || '0');

    // Get sold cars
    const soldResult = await sql`SELECT COUNT(*) as count FROM cars WHERE LOWER(status) = 'sold'`;
    const sold = parseInt(soldResult.rows[0]?.count || '0');

    // Get featured cars
    const featuredResult = await sql`SELECT COUNT(*) as count FROM cars WHERE is_featured = true`;
    const featured = parseInt(featuredResult.rows[0]?.count || '0');

    // Get low stock cars (stock_quantity <= 1)
    const lowStockResult = await sql`SELECT COUNT(*) as count FROM cars WHERE stock_quantity <= 1 AND LOWER(status) = 'available'`;
    const lowStock = parseInt(lowStockResult.rows[0]?.count || '0');

    // Get cars by make (top 5)
    const makeStatsResult = await sql`
      SELECT make, COUNT(*) as count 
      FROM cars 
      GROUP BY make 
      ORDER BY count DESC 
      LIMIT 5
    `;

    // Get cars by status
    const statusStatsResult = await sql`
      SELECT status, COUNT(*) as count 
      FROM cars 
      GROUP BY status 
      ORDER BY count DESC
    `;

    // Get recent additions (last 30 days)
    const recentResult = await sql`
      SELECT COUNT(*) as count 
      FROM cars 
      WHERE created_at >= NOW() - INTERVAL '30 days'
    `;
    const recentAdditions = parseInt(recentResult.rows[0]?.count || '0');

    // Get average price
    const avgPriceResult = await sql`
      SELECT AVG(price) as avg_price 
      FROM cars 
      WHERE LOWER(status) = 'available'
    `;
    const averagePrice = parseFloat(avgPriceResult.rows[0]?.avg_price || '0');

    // Get price range
    const priceRangeResult = await sql`
      SELECT MIN(price) as min_price, MAX(price) as max_price 
      FROM cars 
      WHERE LOWER(status) = 'available'
    `;
    const minPrice = parseFloat(priceRangeResult.rows[0]?.min_price || '0');
    const maxPrice = parseFloat(priceRangeResult.rows[0]?.max_price || '0');

    // Get cars by year (distribution)
    const yearStatsResult = await sql`
      SELECT 
        CASE 
          WHEN year >= 2020 THEN '2020+'
          WHEN year >= 2015 THEN '2015-2019'
          WHEN year >= 2010 THEN '2010-2014'
          WHEN year >= 2005 THEN '2005-2009'
          ELSE 'Before 2005'
        END as year_range,
        COUNT(*) as count
      FROM cars
      GROUP BY year_range
      ORDER BY 
        CASE 
          WHEN year >= 2020 THEN 1
          WHEN year >= 2015 THEN 2
          WHEN year >= 2010 THEN 3
          WHEN year >= 2005 THEN 4
          ELSE 5
        END
    `;

    return {
      total,
      available,
      sold,
      featured,
      lowStock,
      recentAdditions,
      averagePrice: Math.round(averagePrice),
      priceRange: {
        min: minPrice,
        max: maxPrice
      },
      makeStats: makeStatsResult.rows.map(row => ({
        make: row.make,
        count: parseInt(row.count)
      })),
      statusStats: statusStatsResult.rows.map(row => ({
        status: row.status,
        count: parseInt(row.count)
      })),
      yearStats: yearStatsResult.rows.map(row => ({
        yearRange: row.year_range,
        count: parseInt(row.count)
      }))
    };
  } catch (error) {
    console.error('Error calculating car stats:', error);
    return {
      total: 0,
      available: 0,
      sold: 0,
      featured: 0,
      lowStock: 0,
      recentAdditions: 0,
      averagePrice: 0,
      priceRange: { min: 0, max: 0 },
      makeStats: [],
      statusStats: [],
      yearStats: []
    };
  }
}
