// Admin authentication utility for EBAM Motors
// Simple authentication for admin features

import { NextRequest } from 'next/server';

export interface AdminAuthResult {
  isValid: boolean;
  user?: {
    id: string;
    role: string;
    permissions: string[];
  };
  error?: string;
}

// Simple admin authentication using environment variable
export function getAdminAuth(request: NextRequest): AdminAuthResult {
  try {
    // Check for admin password in headers or query params
    const authHeader = request.headers.get('authorization');
    const adminPassword = request.nextUrl.searchParams.get('admin_password');
    
    // Get admin password from environment
    const expectedPassword = process.env.ADMIN_PASSWORD || 'admin123';
    
    let providedPassword: string | null = null;
    
    // Check Authorization header (Bearer token)
    if (authHeader && authHeader.startsWith('Bearer ')) {
      providedPassword = authHeader.substring(7);
    }
    
    // Check query parameter
    if (adminPassword) {
      providedPassword = adminPassword;
    }
    
    // Check for admin session cookie
    const adminCookie = request.cookies.get('admin_session');
    if (adminCookie && adminCookie.value === expectedPassword) {
      providedPassword = adminCookie.value;
    }
    
    // Validate password
    if (providedPassword === expectedPassword) {
      return {
        isValid: true,
        user: {
          id: 'admin',
          role: 'administrator',
          permissions: ['read', 'write', 'delete', 'admin'],
        },
      };
    }
    
    return {
      isValid: false,
      error: 'Invalid admin credentials',
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Authentication error',
    };
  }
}

// Check if user has specific permission
export function hasPermission(authResult: AdminAuthResult, permission: string): boolean {
  return authResult.isValid && authResult.user?.permissions.includes(permission) === true;
}

// Middleware for admin routes
export function requireAdmin(request: NextRequest): AdminAuthResult {
  const authResult = getAdminAuth(request);
  
  if (!authResult.isValid) {
    throw new Error('Admin authentication required');
  }
  
  return authResult;
}

// Generate admin session token (simple implementation)
export function generateAdminToken(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Validate admin session
export function validateAdminSession(token: string): boolean {
  // In a real implementation, you would check against a database or cache
  // For now, just check if it matches the admin password
  return token === (process.env.ADMIN_PASSWORD || 'admin123');
}

// Create admin login response
export function createAdminLoginResponse(success: boolean, token?: string) {
  if (success && token) {
    return {
      success: true,
      token,
      user: {
        id: 'admin',
        role: 'administrator',
        permissions: ['read', 'write', 'delete', 'admin'],
      },
    };
  }
  
  return {
    success: false,
    error: 'Invalid credentials',
  };
}

// Admin authentication for API routes
export function authenticateAdmin(request: NextRequest): boolean {
  const authResult = getAdminAuth(request);
  return authResult.isValid;
}

// Get admin user info
export function getAdminUser(request: NextRequest) {
  const authResult = getAdminAuth(request);
  return authResult.user;
}

// Check if request is from admin
export function isAdmin(request: NextRequest): boolean {
  return getAdminAuth(request).isValid;
}

// Admin role constants
export const ADMIN_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MODERATOR: 'moderator',
  VIEWER: 'viewer',
} as const;

// Admin permissions
export const ADMIN_PERMISSIONS = {
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete',
  ADMIN: 'admin',
  MODERATE: 'moderate',
  ANALYTICS: 'analytics',
  NOTIFICATIONS: 'notifications',
  USERS: 'users',
  ORDERS: 'orders',
  CARS: 'cars',
} as const;

// Default admin configuration
export const DEFAULT_ADMIN_CONFIG = {
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
  requireTwoFactor: false,
  allowedIPs: [], // Empty array means all IPs allowed
};
