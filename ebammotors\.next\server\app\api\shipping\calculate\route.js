(()=>{var e={};e.id=8305,e.ids=[8305],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},24407:(e,t,a)=>{"use strict";a.d(t,{N1:()=>d,U:()=>s,UQ:()=>u,Uv:()=>o,ZK:()=>i,_4:()=>c,gY:()=>r});let n=[{id:"bank_transfer_ghana",type:"bank_transfer",name:"Bank Transfer (Ghana)",description:"Direct bank transfer to our Ghana account",icon:"\uD83C\uDFE6",enabled:!0,config:{bankName:"Ghana Commercial Bank",accountNumber:"*************",accountName:"EBAM Motors Ghana Ltd",swiftCode:"GCBLGHAC"}},{id:"bank_transfer_japan",type:"bank_transfer",name:"Bank Transfer (Japan)",description:"Direct bank transfer to our Japan account",icon:"\uD83C\uDFE6",enabled:!0,config:{bankName:"Mizuho Bank",accountNumber:"*************",accountName:"EBAM Motors Japan KK",swiftCode:"MHCBJPJT"}},{id:"mtn_mobile_money",type:"mobile_money",name:"MTN Mobile Money",description:"Pay using MTN Mobile Money",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"mtn",number:"+************"}},{id:"vodafone_cash",type:"mobile_money",name:"Vodafone Cash",description:"Pay using Vodafone Cash",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"vodafone",number:"+************"}},{id:"airtel_money",type:"mobile_money",name:"AirtelTigo Money",description:"Pay using AirtelTigo Money",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"airtel",number:"+************"}},{id:"stripe_card",type:"stripe",name:"Credit/Debit Card",description:"Pay securely with your credit or debit card",icon:"\uD83D\uDCB3",enabled:!0,config:{publishableKey:process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}},{id:"cash_agent_kumasi",type:"cash_agent",name:"Cash Payment (Kumasi)",description:"Pay cash through our agent in Kumasi",icon:"\uD83D\uDCB5",enabled:!0,config:{agentLocations:["Kumasi Central Market","Adum Shopping Center","KNUST Campus"]}},{id:"cash_agent_accra",type:"cash_agent",name:"Cash Payment (Accra)",description:"Pay cash through our agent in Accra",icon:"\uD83D\uDCB5",enabled:!0,config:{agentLocations:["Makola Market","Osu Oxford Street","East Legon"]}}],s=[{id:"sea_freight_standard",name:"Sea Freight (Standard)",description:"Most economical option, 4-6 weeks delivery",estimatedDays:35,basePrice:15e4,pricePerKg:50,maxWeight:2e3,trackingIncluded:!0,insuranceIncluded:!0},{id:"sea_freight_express",name:"Sea Freight (Express)",description:"Faster sea shipping, 3-4 weeks delivery",estimatedDays:25,basePrice:2e5,pricePerKg:75,maxWeight:2e3,trackingIncluded:!0,insuranceIncluded:!0},{id:"air_freight",name:"Air Freight",description:"Fastest option, 1-2 weeks delivery",estimatedDays:10,basePrice:5e5,pricePerKg:200,maxWeight:500,trackingIncluded:!0,insuranceIncluded:!0}],i={compact:1200,sedan:1400,suv:1800,van:1600,truck:2500,motorcycle:200,default:1500};function r(e){return n.find(t=>t.id===e)||null}function o(e){return s.find(t=>t.id===e)||null}function c(e,t,a="ghana"){let n=o(e);if(!n)return 0;let s=i[t.toLowerCase()]||i.default,r=n.pricePerKg?s*n.pricePerKg:0,d=1;return"ghana"!==a.toLowerCase()&&(d=1.2),Math.round((n.basePrice+r)*d)}function d(e){let t=o(e);if(!t)return"";let a=new Date;return a.setDate(a.getDate()+t.estimatedDays),a.toISOString().split("T")[0]}function u(e,t,a){let n=r(e);if(!n)return"";switch(n.type){case"bank_transfer":return`Transfer \xa5${t.toLocaleString()} to:
Bank: ${n.config?.bankName}
Account: ${n.config?.accountNumber}
Name: ${n.config?.accountName}
Reference: ${a}`;case"mobile_money":return`Send \xa5${t.toLocaleString()} to ${n.config?.number}
Reference: ${a}
Network: ${n.name}`;case"cash_agent":return`Visit one of our agent locations with \xa5${t.toLocaleString()}:
${n.config?.agentLocations?.join(", ")}
Reference: ${a}`;case"stripe":return`You will be redirected to secure payment page to complete your payment of \xa5${t.toLocaleString()}.`;default:return`Payment amount: \xa5${t.toLocaleString()}
Order reference: ${a}`}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52381:(e,t,a)=>{"use strict";a.d(t,{OZ:()=>d,Qb:()=>i,TX:()=>s,WC:()=>u,gP:()=>r,hj:()=>c,wq:()=>o});var n=a(24407);function s(e,t){let a=[];for(let s of n.U){let i=e.weight||n.ZK[e.category.toLowerCase()]||n.ZK.default;if(s.maxWeight&&i>s.maxWeight)continue;let r=(0,n._4)(s.id,e.category,t.country),o=(0,n.N1)(s.id);a.push({method:s,cost:r,estimatedDelivery:o,weight:i,dimensions:e.dimensions})}return a.sort((e,t)=>e.cost-t.cost)}function i(e,t="balanced"){if(0===e.length)return null;switch(t){case"cost":default:return e[0];case"speed":return e.reduce((e,t)=>t.method.estimatedDays<e.method.estimatedDays?t:e);case"balanced":let a=e.map(e=>({calculation:e,score:e.cost/1e5+e.method.estimatedDays/10}));return a.sort((e,t)=>e.score-t.score),a[0].calculation}}function r(e,t,a=!0,n=!0){let s=a?Math.max(.02*t,1e4):0,i=25e3*!!n;return{baseShipping:e,insurance:Math.round(s),handling:i,total:Math.round(e+s+i)}}function o(e){let t=[{stage:"Order Processing",description:"Vehicle inspection and export documentation",estimatedDays:3},{stage:"Port Preparation",description:"Vehicle preparation and loading at port",estimatedDays:5}];return e.id.includes("air")?[...t,{stage:"Air Transport",description:"Flight to destination airport",estimatedDays:2},{stage:"Customs Clearance",description:"Import customs and documentation",estimatedDays:3},{stage:"Final Delivery",description:"Transport to final destination",estimatedDays:2}]:[...t,{stage:"Sea Transport",description:"Shipping to destination port",estimatedDays:e.estimatedDays-15},{stage:"Port Arrival",description:"Arrival at destination port",estimatedDays:e.estimatedDays-10},{stage:"Customs Clearance",description:"Import customs and documentation",estimatedDays:e.estimatedDays-5},{stage:"Final Delivery",description:"Transport to final destination",estimatedDays:e.estimatedDays}]}function c(e){let t=[];return e.country&&""!==e.country.trim()||t.push("Country is required"),e.city&&""!==e.city.trim()||t.push("City is required"),["ghana","nigeria","kenya","south africa","ivory coast"].includes(e.country.toLowerCase())||t.push(`Shipping to ${e.country} is not currently available`),{valid:0===t.length,errors:t}}function d(e){let t=[];switch(e.country.toLowerCase()){case"ghana":t.push("Vehicles must be less than 10 years old"),t.push("Right-hand drive vehicles only");break;case"nigeria":t.push("Vehicles must be less than 15 years old"),t.push("Additional import duties may apply");break;case"kenya":t.push("Vehicles must be less than 8 years old"),t.push("Pre-shipment inspection required");break;default:t.push("Please contact us for specific import requirements")}return t}function u(e){let t=new Date,a=new Date(t),n=new Date(t),s=e.id.includes("air")?1:3;return a.setDate(a.getDate()+e.estimatedDays-s),n.setDate(n.getDate()+e.estimatedDays+s),{earliest:a.toISOString().split("T")[0],latest:n.toISOString().split("T")[0]}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70544:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var n={};a.r(n),a.d(n,{GET:()=>u,POST:()=>d});var s=a(96559),i=a(48088),r=a(37719),o=a(32190),c=a(52381);async function d(e){try{let{vehicle:t,destination:a,preference:n="balanced"}=await e.json();if(!t||!a)return o.NextResponse.json({success:!1,message:"Vehicle and destination information required"},{status:400});let s=(0,c.hj)(a);if(!s.valid)return o.NextResponse.json({success:!1,message:"Invalid destination",errors:s.errors},{status:400});let i=(0,c.TX)(t,a);if(0===i.length)return o.NextResponse.json({success:!1,message:"No shipping options available for this vehicle and destination"},{status:400});let r=(0,c.Qb)(i,n),d=i.map(e=>{let a=t.price||5e5,n=(0,c.gP)(e.cost,a,!0,!0),s=(0,c.wq)(e.method),i=(0,c.WC)(e.method);return{...e,costs:n,timeline:s,dateRange:i,isRecommended:r?.method.id===e.method.id}}),u=(0,c.OZ)(a);return o.NextResponse.json({success:!0,data:{options:d,recommended:d.find(e=>e.isRecommended),restrictions:u,destination:a,vehicle:{category:t.category,estimatedWeight:d[0]?.weight||1500}}})}catch(e){return console.error("Error calculating shipping:",e),o.NextResponse.json({success:!1,message:"Failed to calculate shipping options"},{status:500})}}async function u(e){try{let{searchParams:t}=new URL(e.url),a=t.get("category"),n=t.get("country"),s=t.get("city");if(!a||!n||!s)return o.NextResponse.json({success:!1,message:"Vehicle category, country, and city are required"},{status:400});let i={country:n,city:s},r=(0,c.TX)({category:a,year:2020},i),d=(0,c.OZ)(i),u=r.map(e=>({methodId:e.method.id,methodName:e.method.name,estimatedCost:e.cost,estimatedDays:e.method.estimatedDays,estimatedDelivery:e.estimatedDelivery}));return o.NextResponse.json({success:!0,data:{estimates:u,restrictions:d,supportedDestination:r.length>0}})}catch(e){return console.error("Error getting shipping estimates:",e),o.NextResponse.json({success:!1,message:"Failed to get shipping estimates"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/shipping/calculate/route",pathname:"/api/shipping/calculate",filename:"route",bundlePath:"app/api/shipping/calculate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\shipping\\calculate\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:g}=p;function h(){return(0,r.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[4447,580],()=>a(70544));module.exports=n})();