(()=>{var e={};e.id=9218,e.ids=[9218],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17198:(e,t,s)=>{"use strict";s.d(t,{l:()=>c});var a=s(33873),l=s.n(a);let r={"toyota-voxy":{name:"Toyota Voxy",basePrice:3e5,seats:"8-seater",transmission:"Automatic",fuelType:"Gasoline"},"toyota-noah":{name:"Toyota Noah",basePrice:35e4,seats:"8-seater",transmission:"Automatic",fuelType:"Gasoline"},"toyota-sienta":{name:"Toyota Sienta",basePrice:32e4,seats:"7-seater",transmission:"CVT",fuelType:"Hybrid"},"toyota-vitz":{name:"Toyota Vitz",basePrice:325e3,seats:"5-seater",transmission:"Manual",fuelType:"Gasoline"},"toyota-yaris":{name:"Toyota Yaris",basePrice:55e4,seats:"5-seater",transmission:"CVT",fuelType:"Hybrid"}},n={yearAdjustment:{old:-5e3,new:5e3}};function i(){return[{id:1,category:"cars",title:"Toyota Voxy 2015",price:"\xa5450,000",location:"Japan",status:"Available",image:"/car-models/voxy/voxy-2015-001/1.jpg",images:["/car-models/voxy/voxy-2015-001/1.jpg","/car-models/voxy/voxy-2015-001/2.jpg"],specs:["8 seats","CVT","85k km","Gasoline","Excellent"],carId:"voxy-2015-001",year:2015,mileage:85e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Excellent"},{id:2,category:"cars",title:"Toyota Noah 2016",price:"\xa5480,000",location:"Japan",status:"Available",image:"/car-models/noah/noah-2016-001/1.jpg",images:["/car-models/noah/noah-2016-001/1.jpg","/car-models/noah/noah-2016-001/2.jpg"],specs:["8 seats","CVT","72k km","Gasoline","Very Good"],carId:"noah-2016-001",year:2016,mileage:72e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Very Good"},{id:3,category:"cars",title:"Toyota Sienta 2017",price:"\xa5520,000",location:"Japan",status:"Available",image:"/car-models/sienta/sienta-2017-001/1.jpg",images:["/car-models/sienta/sienta-2017-001/1.jpg","/car-models/sienta/sienta-2017-001/2.jpg"],specs:["7 seats","CVT","58k km","Gasoline","Excellent"],carId:"sienta-2017-001",year:2017,mileage:58e3,fuelType:"Gasoline",transmission:"CVT",bodyCondition:"Excellent"}]}async function c(){let e=[],t=l().join(process.cwd(),"public","car-models");try{let a=await Promise.resolve().then(s.t.bind(s,29021,23));if(!a.existsSync(t))return console.warn("Car models directory not found:",t),i();let c=a.readdirSync(t),o=1;for(let s of c){let i=l().join(t,s);if(!a.statSync(i).isDirectory())continue;let c=r[s];if(c)for(let t of a.readdirSync(i)){let d=l().join(i,t);if(!a.statSync(d).isDirectory())continue;let x=a.readdirSync(d).filter(e=>[".jpg",".jpeg",".JPG",".JPEG",".png",".PNG"].includes(l().extname(e))).sort();if(0===x.length)continue;let m=function(e){let t=e.match(/(\d{4})/);return t?parseInt(t[1]):2010}(t),h=function(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t&=t;return Math.floor(Math.abs(t)/0x7fffffff*9e4+3e4)}(t),u=function(e){let t=["Excellent","Very Good","Good","Fair","Needs Work"],s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t),s&=s;return t[Math.abs(s)%t.length]}(t),p=function(e,t){let s=r[e];if(!s)return 0;let a=s.basePrice;return t<=2010?a+=n.yearAdjustment.old:t>=2012&&(a+=n.yearAdjustment.new),Math.max(a,0)}(s,m),b=x.map(e=>`/car-models/${s}/${t}/${e}`),g=t.split("").reduce((e,t)=>e+t.charCodeAt(0),0),j=g%4==0,y=g%5==0,v=j?g%7:g%30+7,f=new Date;f.setDate(f.getDate()-v);let N={id:o++,category:"cars",title:`${c.name} ${m}`,price:`\xa5${p.toLocaleString()}`,location:"Japan",status:t.length%10==0?"Reserved":"Available",image:b[0],images:b,specs:[c.seats,c.transmission,`${Math.floor(h/1e3)}k km`,c.fuelType,u],carId:t,year:m,mileage:h,fuelType:c.fuelType,transmission:c.transmission,bodyCondition:u,addedDate:f.toISOString(),originalPrice:y?`\xa5${(p+2e4).toLocaleString()}`:void 0,stockQuantity:g%3+1,popularity:g%100+1};e.push(N)}}return e}catch(e){return console.error("Error generating stock from file system:",e),i()}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29991:(e,t,s)=>{Promise.resolve().then(s.bind(s,54173))},33873:e=>{"use strict";e.exports=require("path")},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},53034:(e,t,s)=>{"use strict";s.d(t,{default:()=>er});var a=s(60687),l=s(43210),r=s(85814),n=s.n(r),i=s(13861),c=s(84027),o=s(10022),d=s(62688);let x=(0,d.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),m=(0,d.A)("git-compare",[["circle",{cx:"18",cy:"18",r:"3",key:"1xkwt0"}],["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M13 6h3a2 2 0 0 1 2 2v7",key:"1yeb86"}],["path",{d:"M11 18H8a2 2 0 0 1-2-2V9",key:"19pyzm"}]]),h=(0,d.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var u=s(67760);let p=(0,d.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var b=s(40228),g=s(51939),j=s(64338),y=s(97992),v=s(99891),f=s(3181),N=s(30474);let w=(0,d.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),k=(0,d.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),C=(0,d.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var A=s(11860),S=s(31158);function M({vehicle:e,locale:t}){let[s,r]=(0,l.useState)(0),[n,i]=(0,l.useState)(!1),[c,o]=(0,l.useState)(0),d=e.images||[e.image],x=e=>{o(e),i(!0)},m=async t=>{try{let s=await fetch(t),a=await s.blob(),l=window.URL.createObjectURL(a),r=document.createElement("a");r.href=l,r.download=`${e.title}-${c+1}.jpg`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(l),document.body.removeChild(r)}catch(e){console.error("Error downloading image:",e)}};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"relative bg-white rounded-lg overflow-hidden border group",children:(0,a.jsxs)("div",{className:"aspect-[4/3] relative",children:[(0,a.jsx)(N.default,{src:d[s],alt:`${e.title} - Image ${s+1}`,fill:!0,className:"object-cover cursor-pointer",onClick:()=>x(s)}),d.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{r(e=>(e-1+d.length)%d.length)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70",children:(0,a.jsx)(w,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>{r(e=>(e+1)%d.length)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70",children:(0,a.jsx)(k,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)(C,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{className:"absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[s+1," / ",d.length]})]})}),d.length>1&&(0,a.jsx)("div",{className:"grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2",children:d.map((t,l)=>(0,a.jsx)("button",{onClick:()=>r(l),className:`relative aspect-square rounded-lg overflow-hidden border-2 transition-all ${l===s?"border-primary-600 ring-2 ring-primary-200":"border-neutral-200 hover:border-neutral-300"}`,children:(0,a.jsx)(N.default,{src:t,alt:`${e.title} - Thumbnail ${l+1}`,fill:!0,className:"object-cover"})},l))}),n&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"relative w-full h-full flex items-center justify-center p-4",children:[(0,a.jsx)("button",{onClick:()=>i(!1),className:"absolute top-4 right-4 text-white p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors z-10",children:(0,a.jsx)(A.A,{className:"w-6 h-6"})}),(0,a.jsx)("button",{onClick:()=>m(d[c]),className:"absolute top-4 right-16 text-white p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors z-10",children:(0,a.jsx)(S.A,{className:"w-6 h-6"})}),d.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{o(e=>(e-1+d.length)%d.length)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full bg-black/50 hover:bg-black/70 transition-colors",children:(0,a.jsx)(w,{className:"w-8 h-8"})}),(0,a.jsx)("button",{onClick:()=>{o(e=>(e+1)%d.length)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full bg-black/50 hover:bg-black/70 transition-colors",children:(0,a.jsx)(k,{className:"w-8 h-8"})})]}),(0,a.jsx)("div",{className:"relative max-w-full max-h-full",children:(0,a.jsx)(N.default,{src:d[c],alt:`${e.title} - Image ${c+1}`,width:1200,height:900,className:"object-contain max-w-full max-h-full"})}),(0,a.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-center",children:(0,a.jsxs)("div",{className:"bg-black/50 px-4 py-2 rounded-lg",children:[(0,a.jsx)("div",{className:"font-medium",children:e.title}),(0,a.jsxs)("div",{className:"text-sm opacity-75",children:["en"===t?"Image":"画像"," ",c+1," ","en"===t?"of":"/"," ",d.length]})]})})]})})]})}var T=s(94478),I=s(41312),P=s(82679),V=s(98971);function $({vehicle:e,locale:t}){let s={make:e.title.split(" ")[0]||"Toyota",model:e.title.split(" ").slice(1,-1).join(" ")||"Unknown",year:e.year||2010,mileage:e.mileage||5e4,engine:"Hybrid"===e.fuelType?"1.8L Hybrid":"Diesel"===e.fuelType?"2.0L Diesel":"1.5L Gasoline",fuelType:e.fuelType||"Gasoline",transmission:e.transmission||"Automatic",drivetrain:"Front-Wheel Drive",seating:e.specs.find(e=>e.includes("seater"))?.replace("-seater"," seats")||"5 seats",doors:"4 doors",bodyType:"Sedan",bodyCondition:e.bodyCondition||"Good",interiorCondition:e.bodyCondition||"Good",airConditioning:"Yes",powerSteering:"Yes",registrationStatus:"Valid",inspectionStatus:"Passed",exportReady:"Yes",color:["White","Silver","Black","Blue","Red"][e.id%5],fuelTankCapacity:"50L",groundClearance:"150mm"},l=[{title:"en"===t?"Basic Information":"基本情報",icon:T.A,items:[{label:"en"===t?"Make":"メーカー",value:s.make},{label:"en"===t?"Model":"モデル",value:s.model},{label:"en"===t?"Year":"年式",value:s.year.toString()},{label:"en"===t?"Body Type":"ボディタイプ",value:s.bodyType},{label:"en"===t?"Color":"色",value:s.color},{label:"en"===t?"Doors":"ドア数",value:s.doors}]},{title:"en"===t?"Engine & Performance":"エンジン・性能",icon:c.A,items:[{label:"en"===t?"Engine":"エンジン",value:s.engine},{label:"en"===t?"Fuel Type":"燃料タイプ",value:s.fuelType},{label:"en"===t?"Transmission":"トランスミッション",value:s.transmission},{label:"en"===t?"Drivetrain":"駆動方式",value:s.drivetrain},{label:"en"===t?"Fuel Tank":"燃料タンク",value:s.fuelTankCapacity}]},{title:"en"===t?"Dimensions & Capacity":"寸法・容量",icon:I.A,items:[{label:"en"===t?"Seating Capacity":"乗車定員",value:s.seating},{label:"en"===t?"Mileage":"走行距離",value:`${Math.floor(s.mileage/1e3)}k km`},{label:"en"===t?"Ground Clearance":"最低地上高",value:s.groundClearance}]},{title:"en"===t?"Condition & Features":"状態・装備",icon:v.A,items:[{label:"en"===t?"Body Condition":"ボディ状態",value:s.bodyCondition},{label:"en"===t?"Interior Condition":"内装状態",value:s.interiorCondition},{label:"en"===t?"Air Conditioning":"エアコン",value:s.airConditioning},{label:"en"===t?"Power Steering":"パワーステアリング",value:s.powerSteering}]},{title:"en"===t?"Documentation & Export":"書類・輸出",icon:P.A,items:[{label:"en"===t?"Registration":"登録",value:s.registrationStatus},{label:"en"===t?"Inspection":"車検",value:s.inspectionStatus},{label:"en"===t?"Export Ready":"輸出準備",value:s.exportReady},{label:"en"===t?"Location":"所在地",value:e.location}]}];return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-neutral-800 mb-2",children:"en"===t?"Detailed Specifications":"詳細仕様"}),(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===t?"Complete technical specifications and features for this vehicle.":"この車両の完全な技術仕様と機能。"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:l.map((e,t)=>{let s=e.icon;return(0,a.jsxs)("div",{className:"bg-white rounded-lg border overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-neutral-50 px-6 py-4 border-b",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-primary-100 rounded-lg",children:(0,a.jsx)(s,{className:"w-5 h-5 text-primary-600"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:e.title})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"space-y-4",children:e.items.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-neutral-100 last:border-b-0",children:[(0,a.jsx)("span",{className:"text-neutral-600 font-medium",children:e.label}),(0,a.jsx)("span",{className:"text-neutral-800 font-semibold",children:e.value})]},t))})})]},t)})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,a.jsx)(V.A,{className:"w-5 h-5 text-primary-600"}),(0,a.jsx)("span",{children:"en"===t?"Standard Features":"標準装備"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:["en"===t?"Power Windows":"パワーウィンドウ","en"===t?"Central Locking":"セントラルロック","en"===t?"ABS Brakes":"ABSブレーキ","en"===t?"Airbags":"エアバッグ","en"===t?"Radio/CD Player":"ラジオ/CDプレーヤー","en"===t?"Electric Mirrors":"電動ミラー"].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-neutral-700",children:e})]},t))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("span",{children:"en"===t?"Fuel Economy":"燃費"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"Hybrid"===s.fuelType?"25":"Diesel"===s.fuelType?"18":"15"}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"km/L City":"km/L 市街地"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"Hybrid"===s.fuelType?"28":"Diesel"===s.fuelType?"22":"18"}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"km/L Highway":"km/L 高速道路"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"Hybrid"===s.fuelType?"26":"Diesel"===s.fuelType?"20":"16"}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"km/L Combined":"km/L 総合"})]})]})]})]})}var D=s(5336),R=s(58869);function E({vehicle:e,locale:t}){let[s,r]=(0,l.useState)(null),n=(()=>{let t=new Date().getFullYear()-(e.year||2010);return{previousOwners:Math.min(Math.floor(t/3)+1,3),serviceRecords:Math.floor(1.5*t)+2,accidentHistory:"Excellent"===e.bodyCondition?"None":"Needs Work"===e.bodyCondition?"Minor":"None",lastInspection:"2024-01-15",registrationHistory:[{date:"2024-01-15",type:"Inspection",status:"Passed",location:"Tokyo"},{date:"2023-06-20",type:"Registration Renewal",status:"Completed",location:"Tokyo"},{date:"2023-01-10",type:"Inspection",status:"Passed",location:"Tokyo"}]}})(),c=[{id:"inspection",title:"en"===t?"Vehicle Inspection Report":"車両検査報告書",date:"2024-01-15",status:"passed",description:"en"===t?"Comprehensive 150-point inspection":"包括的な150項目検査",icon:D.A},{id:"history",title:"en"===t?"Ownership History":"所有者履歴",date:"2024-01-10",status:"available",description:"en"===t?"Complete ownership and registration history":"完全な所有者・登録履歴",icon:R.A},{id:"maintenance",title:"en"===t?"Maintenance Records":"メンテナンス記録",date:"2024-01-05",status:"available",description:"en"===t?"Service and maintenance history":"サービス・メンテナンス履歴",icon:P.A},{id:"export",title:"en"===t?"Export Certification":"輸出証明書",date:"2024-01-20",status:"ready",description:"en"===t?"Export readiness and documentation":"輸出準備と書類",icon:v.A}],d=e=>{let s=c.find(t=>t.id===e)?.title||"Report";alert(`${"en"===t?"Downloading":"ダウンロード中"}: ${s}`)},x=e=>{r(e)};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-neutral-800 mb-2",children:"en"===t?"Vehicle History & Reports":"車両履歴・レポート"}),(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===t?"Complete history, inspection reports, and documentation for this vehicle.":"この車両の完全な履歴、検査報告書、書類。"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:n.previousOwners}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Previous Owners":"前所有者数"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:n.serviceRecords}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Service Records":"サービス記録"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"None"===n.accidentHistory?"0":"1"}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Accidents":"事故歴"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"✓"}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Export Ready":"輸出準備完了"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b bg-neutral-50",children:(0,a.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-primary-600"}),(0,a.jsx)("span",{children:"en"===t?"Available Reports":"利用可能なレポート"})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"space-y-4",children:c.map(e=>{let s=e.icon;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-neutral-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${"passed"===e.status?"bg-green-100":"ready"===e.status?"bg-blue-100":"bg-neutral-100"}`,children:(0,a.jsx)(s,{className:`w-5 h-5 ${"passed"===e.status?"text-green-600":"ready"===e.status?"text-blue-600":"text-neutral-600"}`})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-800",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-neutral-500 mt-1",children:["en"===t?"Updated":"更新日",": ",e.date]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>x(e.id),className:"p-2 text-neutral-600 hover:text-primary-600 transition-colors",title:"en"===t?"View Report":"レポートを表示",children:(0,a.jsx)(i.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>d(e.id),className:"p-2 text-neutral-600 hover:text-primary-600 transition-colors",title:"en"===t?"Download Report":"レポートをダウンロード",children:(0,a.jsx)(S.A,{className:"w-4 h-4"})})]})]},e.id)})})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b bg-neutral-50",children:(0,a.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 flex items-center space-x-2",children:[(0,a.jsx)(b.A,{className:"w-5 h-5 text-primary-600"}),(0,a.jsx)("span",{children:"en"===t?"Registration Timeline":"登録履歴タイムライン"})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"space-y-6",children:n.registrationHistory.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsxs)("div",{className:"flex-shrink-0",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-primary-600 rounded-full mt-2"}),t<n.registrationHistory.length-1&&(0,a.jsx)("div",{className:"w-px h-12 bg-neutral-200 ml-1 mt-1"})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-800",children:e.type}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"Passed"===e.status||"Completed"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:e.status})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-sm text-neutral-600",children:[(0,a.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,a.jsx)(b.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:e.date})]}),(0,a.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:e.location})]})]})]})]},t))})})]}),s&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:c.find(e=>e.id===s)?.title}),(0,a.jsx)("button",{onClick:()=>r(null),className:"text-neutral-600 hover:text-neutral-800",children:"\xd7"})]}),(0,a.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"w-16 h-16 text-neutral-400 mx-auto mb-4"}),(0,a.jsx)("h4",{className:"text-lg font-semibold text-neutral-800 mb-2",children:"en"===t?"Report Preview":"レポートプレビュー"}),(0,a.jsx)("p",{className:"text-neutral-600 mb-6",children:"en"===t?"This is a preview of the report. The full report contains detailed information about the vehicle.":"これはレポートのプレビューです。完全なレポートには車両の詳細情報が含まれています。"}),(0,a.jsx)("button",{onClick:()=>d(s),className:"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors",children:"en"===t?"Download Full Report":"完全なレポートをダウンロード"})]})})]})})]})}let G=(0,d.A)("mouse-pointer",[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]]),L=(0,d.A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),z=(0,d.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),H=(0,d.A)("rotate-cw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);var O=s(96882);let q=(0,d.A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);function F({vehicle:e,locale:t}){let[s,r]=(0,l.useState)(0),[n,i]=(0,l.useState)(!1),[c,o]=(0,l.useState)(!1),[d,m]=(0,l.useState)(!1),[h,u]=(0,l.useState)(!0),p=(0,l.useRef)(null),b=(0,l.useRef)(0),g=(0,l.useRef)(0),j=e.images||[e.image],y=j.length,v=()=>{o(!1)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-neutral-800 mb-2",children:"en"===t?"360\xb0 Vehicle View":"360\xb0車両ビュー"}),(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===t?"Drag to rotate, click play for auto-rotation, or use the controls below.":"ドラッグして回転、再生をクリックして自動回転、または下のコントロールを使用してください。"})]}),(0,a.jsxs)("div",{ref:p,className:`relative bg-white rounded-lg border overflow-hidden ${d?"fixed inset-0 z-50 rounded-none":""}`,children:[h&&(0,a.jsxs)("div",{className:"absolute top-4 left-4 right-4 bg-black/75 text-white p-4 rounded-lg z-20 animate-fadeIn",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(G,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"font-semibold",children:"en"===t?"How to use 360\xb0 View":"360\xb0ビューの使い方"})]}),(0,a.jsx)("p",{className:"text-sm",children:"en"===t?"Drag left or right to rotate the vehicle. Use controls below for auto-rotation.":"左右にドラッグして車両を回転させます。自動回転には下のコントロールを使用してください。"})]}),(0,a.jsxs)("div",{className:`relative ${d?"h-screen":"aspect-[16/10]"} cursor-grab active:cursor-grabbing select-none`,onMouseDown:e=>{o(!0),i(!1),b.current=e.clientX,g.current=s,u(!1)},onMouseMove:e=>{if(!c)return;let t=Math.floor((e.clientX-b.current)/3);r((g.current+t+y)%y)},onMouseUp:v,onMouseLeave:v,onTouchStart:e=>{o(!0),i(!1),b.current=e.touches[0].clientX,g.current=s,u(!1)},onTouchMove:e=>{if(!c)return;let t=Math.floor((e.touches[0].clientX-b.current)/3);r((g.current+t+y)%y)},onTouchEnd:()=>{o(!1)},children:[(0,a.jsx)(N.default,{src:j[s],alt:`${e.title} - 360\xb0 View Frame ${s+1}`,fill:!0,className:"object-contain bg-neutral-100",priority:!0,draggable:!1}),(0,a.jsxs)("div",{className:"absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[s+1," / ",y]}),c&&(0,a.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:"en"===t?"Rotating...":"回転中..."})]}),(0,a.jsxs)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/75 backdrop-blur-sm rounded-full p-2 flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{r(e=>(e-1+y)%y),i(!1),u(!1)},className:"p-2 text-white hover:text-primary-400 transition-colors",title:"en"===t?"Rotate Left":"左回転",children:(0,a.jsx)(x,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>{i(!n),u(!1)},className:"p-2 text-white hover:text-primary-400 transition-colors",title:n?"en"===t?"Pause":"一時停止":"en"===t?"Play":"再生",children:n?(0,a.jsx)(L,{className:"w-5 h-5"}):(0,a.jsx)(z,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>{r(e=>(e+1)%y),i(!1),u(!1)},className:"p-2 text-white hover:text-primary-400 transition-colors",title:"en"===t?"Rotate Right":"右回転",children:(0,a.jsx)(H,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:"w-px h-6 bg-white/30 mx-1"}),(0,a.jsx)("button",{onClick:()=>u(!h),className:"p-2 text-white hover:text-primary-400 transition-colors",title:"en"===t?"Show Instructions":"使い方を表示",children:(0,a.jsx)(O.A,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>{document.fullscreenElement?(document.exitFullscreen(),m(!1)):(p.current?.requestFullscreen(),m(!0))},className:"p-2 text-white hover:text-primary-400 transition-colors",title:"en"===t?"Fullscreen":"フルスクリーン",children:(0,a.jsx)(q,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4",children:"en"===t?"Exterior Highlights":"外装のハイライト"}),(0,a.jsx)("div",{className:"space-y-3",children:["en"===t?"Body condition: "+(e.bodyCondition||"Good"):"ボディ状態: "+(e.bodyCondition||"良好"),"en"===t?"Paint quality: Excellent":"塗装品質: 優秀","en"===t?"Rust inspection: Passed":"錆検査: 合格","en"===t?"Panel alignment: Good":"パネル整列: 良好"].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-neutral-700",children:e})]},t))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4",children:"en"===t?"View Options":"表示オプション"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors",children:[(0,a.jsx)("div",{className:"font-medium text-neutral-800",children:"en"===t?"Interior 360\xb0 View":"内装360\xb0ビュー"}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Coming soon":"近日公開"})]}),(0,a.jsxs)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors",children:[(0,a.jsx)("div",{className:"font-medium text-neutral-800",children:"en"===t?"Engine Bay View":"エンジンルームビュー"}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Coming soon":"近日公開"})]}),(0,a.jsxs)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors",children:[(0,a.jsx)("div",{className:"font-medium text-neutral-800",children:"en"===t?"Undercarriage View":"車体下部ビュー"}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Coming soon":"近日公開"})]})]})]})]})]})}var B=s(25541),U=s(12640);let _=(0,d.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Y=(0,d.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),J=(0,d.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var W=s(64398);function X({vehicle:e,locale:t,onClose:s,isModal:r=!1}){let[i,c]=(0,l.useState)([e]),[o,d]=(0,l.useState)([]),[x,m]=(0,l.useState)(!1),h=e=>{i.length<3&&!i.find(t=>t.carId===e.carId)&&(c([...i,e]),m(!1))},u=e=>{i.length>1&&c(i.filter(t=>t.carId!==e))},p=e=>parseInt(e.replace(/[¥,]/g,""))||0,b=(e,t)=>{let s=t.reduce((e,t)=>e+t,0)/t.length;return e>1.1*s?(0,a.jsx)(B.A,{className:"w-4 h-4 text-green-600"}):e<.9*s?(0,a.jsx)(U.A,{className:"w-4 h-4 text-red-600"}):(0,a.jsx)("div",{className:"w-4 h-4"})},g=[{label:"en"===t?"Price":"価格",getValue:e=>e.price,getNumericValue:e=>p(e.price),isNumeric:!0,lowerIsBetter:!0},{label:"en"===t?"Year":"年式",getValue:e=>e.year?.toString()||"N/A",getNumericValue:e=>e.year||0,isNumeric:!0,lowerIsBetter:!1},{label:"en"===t?"Mileage":"走行距離",getValue:e=>e.mileage?`${Math.floor(e.mileage/1e3)}k km`:"N/A",getNumericValue:e=>e.mileage||0,isNumeric:!0,lowerIsBetter:!0},{label:"en"===t?"Fuel Type":"燃料タイプ",getValue:e=>e.fuelType||"N/A",getNumericValue:()=>0,isNumeric:!1,lowerIsBetter:!1},{label:"en"===t?"Transmission":"トランスミッション",getValue:e=>e.transmission||"N/A",getNumericValue:()=>0,isNumeric:!1,lowerIsBetter:!1},{label:"en"===t?"Condition":"状態",getValue:e=>e.bodyCondition||"N/A",getNumericValue:()=>0,isNumeric:!1,lowerIsBetter:!1}],j=(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-neutral-800",children:"en"===t?"Vehicle Comparison":"車両比較"}),r&&s&&(0,a.jsx)("button",{onClick:s,className:"p-2 text-neutral-600 hover:text-neutral-800 transition-colors",children:(0,a.jsx)(A.A,{className:"w-6 h-6"})})]}),i.length<3&&(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("button",{onClick:()=>m(!x),className:"flex items-center space-x-2 px-3 sm:px-4 py-2 border-2 border-dashed border-neutral-300 rounded-lg text-neutral-600 hover:border-primary-500 hover:text-primary-600 transition-colors text-sm sm:text-base","aria-expanded":x,"aria-controls":"vehicle-selection",children:[(0,a.jsx)(_,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"en"===t?"Add Vehicle to Compare":"比較する車両を追加"}),(0,a.jsx)("span",{className:"sm:hidden",children:"en"===t?"Add Vehicle":"車両追加"})]})}),x&&(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-4 border",children:[(0,a.jsx)("h3",{className:"font-semibold text-neutral-800 mb-3",children:"en"===t?"Select Vehicle to Compare":"比較する車両を選択"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto",children:o.map(e=>(0,a.jsxs)("button",{onClick:()=>h(e),className:"flex items-center space-x-3 p-3 bg-white rounded-lg border hover:border-primary-500 transition-colors text-left",children:[(0,a.jsx)("div",{className:"relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0",children:(0,a.jsx)(N.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"font-medium text-neutral-800 truncate",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:e.price})]})]},e.carId))})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg border overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-neutral-50 border-b",children:[(0,a.jsx)("th",{className:"text-left p-2 sm:p-4 font-semibold text-neutral-800 min-w-[100px] sm:min-w-[120px] text-sm sm:text-base",children:"en"===t?"Specification":"仕様"}),i.map(s=>(0,a.jsx)("th",{className:"text-center p-2 sm:p-4 min-w-[150px] sm:min-w-[200px]",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"relative w-12 h-12 sm:w-16 sm:h-16 mx-auto rounded-lg overflow-hidden",children:(0,a.jsx)(N.default,{src:s.image,alt:s.title,fill:!0,className:"object-cover"})}),(0,a.jsx)("div",{className:"font-semibold text-neutral-800 text-xs sm:text-sm",children:s.title}),i.length>1&&s.carId!==e.carId&&(0,a.jsx)("button",{onClick:()=>u(s.carId),className:"text-red-600 hover:text-red-800 transition-colors p-1","aria-label":"en"===t?"Remove from comparison":"比較から削除",children:(0,a.jsx)(Y,{className:"w-3 h-3 sm:w-4 sm:h-4 mx-auto"})})]})},s.carId))]})}),(0,a.jsx)("tbody",{children:g.map((e,t)=>{let s=i.map(t=>e.getNumericValue(t)).filter(e=>e>0);return(0,a.jsxs)("tr",{className:"border-b hover:bg-neutral-50",children:[(0,a.jsx)("td",{className:"p-2 sm:p-4 font-medium text-neutral-700 text-sm sm:text-base",children:e.label}),i.map(t=>{let l=e.getValue(t),r=e.getNumericValue(t),n=s.filter(e=>e!==r);return(0,a.jsx)("td",{className:"p-2 sm:p-4 text-center",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1 sm:space-x-2",children:[(0,a.jsx)("span",{className:"font-semibold text-neutral-800 text-sm sm:text-base",children:l}),e.isNumeric&&n.length>0&&(0,a.jsx)("div",{className:"flex-shrink-0",children:b(r,n)})]})},t.carId)})]},t)})})]})})}),(0,a.jsx)("div",{className:"flex flex-wrap gap-3 justify-center",children:i.map(e=>(0,a.jsxs)(n(),{href:`/${t}/stock/${e.carId}`,className:"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:[(0,a.jsxs)("span",{children:["en"===t?"View":"表示"," ",e.title.split(" ")[0]]}),(0,a.jsx)(J,{className:"w-4 h-4"})]},e.carId))}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-blue-800 mb-2 flex items-center space-x-2",children:[(0,a.jsx)(W.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===t?"Comparison Tips":"比較のヒント"})]}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsxs)("li",{children:["• ","en"===t?"Green arrows indicate better values":"緑の矢印はより良い値を示します"]}),(0,a.jsxs)("li",{children:["• ","en"===t?"Red arrows indicate areas for consideration":"赤の矢印は検討すべき領域を示します"]}),(0,a.jsxs)("li",{children:["• ","en"===t?"Consider total cost including shipping":"送料を含む総費用を考慮してください"]})]})]})]});return r?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden",children:(0,a.jsx)("div",{className:"p-6 overflow-y-auto max-h-[90vh]",children:j})})}):(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 border",children:j})}var K=s(28561),Q=s(88059);let Z=(0,d.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),ee=(0,d.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var et=s(48730),es=s(63213);function ea({isOpen:e,onClose:t,vehicle:s,locale:r}){let{user:n,addPurchase:i}=(0,es.A)(),[c,o]=(0,l.useState)("details"),[d,x]=(0,l.useState)(!1),[m,h]=(0,l.useState)(!1),[u,p]=(0,l.useState)([]),[b,g]=(0,l.useState)(null),[j,f]=(0,l.useState)([]),[w,k]=(0,l.useState)(null),[C,S]=(0,l.useState)({street:"",city:"",state:"",country:"Ghana",postalCode:""}),[M,T]=(0,l.useState)(!1),[I,P]=(0,l.useState)(null);if(!e)return null;let V=async()=>{if(C.city&&C.country){T(!0);try{let e=await fetch("/api/shipping/calculate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({vehicle:{category:s.category||"sedan",year:s.year||2020,price:parseInt(s.price.replace(/[¥,]/g,""))||5e5},destination:{country:C.country,city:C.city},preference:"balanced"})}),t=await e.json();t.success&&(f(t.data.options),t.data.recommended&&k(t.data.recommended))}catch(e){console.error("Error calculating shipping:",e)}finally{T(!1)}}},$=async()=>{if(n&&b&&w){x(!0);try{let e=parseInt(s.price.replace(/[¥,]/g,""))||0,t=e+w.costs.total,a={customerId:n.id,customerInfo:{name:n.name,email:n.email,phone:n.phone||"",address:C},vehicle:{id:s.carId,title:s.title,price:e,currency:"JPY",images:s.images,specs:s.specs},payment:{method:b,amount:t,currency:"JPY",dueDate:new Date(Date.now()+2592e6).toISOString().split("T")[0]},shipping:{method:w.method,cost:w.costs.total,address:C,estimatedDelivery:w.estimatedDelivery},totalAmount:t,currency:"JPY"},l=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),r=await l.json();if(r.success)P(r.order.id),i({vehicleId:s.carId,vehicleTitle:s.title,price:s.price,status:"pending",trackingNumber:r.order.id}),h(!0),o("confirmation");else throw Error(r.message)}catch(e){console.error("Error creating order:",e),alert("Failed to create order. Please try again.")}finally{x(!1)}}},R=()=>{o("details"),h(!1),x(!1),g(null),k(null),f([]),P(null),t()};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-neutral-800 flex items-center space-x-2",children:[(0,a.jsx)(K.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"en"===r?"Purchase Vehicle":"車両購入"})]}),(0,a.jsx)("button",{onClick:R,className:"text-neutral-600 hover:text-neutral-800 transition-colors",children:(0,a.jsx)(A.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"p-6",children:["details"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-neutral-800 mb-3",children:"en"===r?"Vehicle Details":"車両詳細"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(N.default,{src:s.image,alt:s.title,width:80,height:80,className:"w-20 h-20 object-cover rounded-lg"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-neutral-800",children:s.title}),(0,a.jsx)("p",{className:"text-2xl font-bold text-primary-600",children:s.price}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:s.location})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-neutral-800",children:"en"===r?"Purchase Options":"購入オプション"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,a.jsx)(v.A,{className:"w-8 h-8 text-blue-600 mx-auto mb-2"}),(0,a.jsx)("h4",{className:"font-medium text-neutral-800 mb-1",children:"en"===r?"Inspection":"検査"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:"en"===r?"Professional inspection included":"プロの検査が含まれています"})]}),(0,a.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,a.jsx)(Q.A,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,a.jsx)("h4",{className:"font-medium text-neutral-800 mb-1",children:"en"===r?"Shipping":"配送"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:"en"===r?"Door-to-door delivery":"ドア・ツー・ドア配送"})]}),(0,a.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,a.jsx)(Z,{className:"w-8 h-8 text-purple-600 mx-auto mb-2"}),(0,a.jsx)("h4",{className:"font-medium text-neutral-800 mb-1",children:"en"===r?"Payment":"支払い"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:"en"===r?"Secure payment processing":"安全な決済処理"})]})]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"en"===r?"Important Information":"重要な情報"}),(0,a.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,a.jsxs)("li",{children:["• ","en"===r?"Final price includes all fees and taxes":"最終価格にはすべての手数料と税金が含まれています"]}),(0,a.jsxs)("li",{children:["• ","en"===r?"Delivery time: 4-6 weeks to Ghana":"配送時間：ガーナまで4-6週間"]}),(0,a.jsxs)("li",{children:["• ","en"===r?"Full refund if vehicle condition differs from description":"車両の状態が説明と異なる場合は全額返金"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,a.jsx)("button",{onClick:R,className:"px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors",children:"en"===r?"Cancel":"キャンセル"}),(0,a.jsx)("button",{onClick:()=>o("shipping"),className:"px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:"en"===r?"Configure Shipping":"配送設定"})]})]}),"shipping"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,a.jsx)(Q.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"en"===r?"Shipping Configuration":"配送設定"})]}),(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"font-medium text-neutral-800 mb-3 flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===r?"Delivery Address":"配送先住所"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"en"===r?"Street Address":"住所"}),(0,a.jsx)("input",{type:"text",value:C.street,onChange:e=>S(t=>({...t,street:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===r?"Enter street address":"住所を入力"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"en"===r?"City":"市区町村"}),(0,a.jsx)("input",{type:"text",value:C.city,onChange:e=>S(t=>({...t,city:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===r?"Enter city":"市区町村を入力"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"en"===r?"State/Region":"州/地域"}),(0,a.jsx)("input",{type:"text",value:C.state,onChange:e=>S(t=>({...t,state:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===r?"Enter state/region":"州/地域を入力"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"en"===r?"Country":"国"}),(0,a.jsxs)("select",{value:C.country,onChange:e=>S(t=>({...t,country:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"Ghana",children:"Ghana"}),(0,a.jsx)("option",{value:"Nigeria",children:"Nigeria"}),(0,a.jsx)("option",{value:"Kenya",children:"Kenya"}),(0,a.jsx)("option",{value:"South Africa",children:"South Africa"}),(0,a.jsx)("option",{value:"Ivory Coast",children:"Ivory Coast"})]})]})]}),(0,a.jsxs)("button",{onClick:V,disabled:!C.city||!C.country||M,className:"mt-4 flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(ee,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:M?"en"===r?"Calculating...":"計算中...":"en"===r?"Calculate Shipping":"配送料計算"})]})]}),j.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"font-medium text-neutral-800 flex items-center space-x-2",children:[(0,a.jsx)(et.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===r?"Shipping Options":"配送オプション"})]}),j.map((e,t)=>(0,a.jsxs)("div",{className:`border rounded-lg p-4 cursor-pointer transition-colors ${w?.method.id===e.method.id?"border-primary-500 bg-primary-50":"border-neutral-300 hover:border-neutral-400"}`,onClick:()=>k(e),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-neutral-800",children:e.method.name}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:e.method.description}),(0,a.jsxs)("p",{className:"text-sm text-neutral-500 mt-1",children:["en"===r?"Estimated delivery:":"配送予定:"," ",e.estimatedDelivery]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-primary-600",children:["\xa5",e.costs.total.toLocaleString()]}),(0,a.jsxs)("div",{className:"text-sm text-neutral-500",children:[e.method.estimatedDays," ","en"===r?"days":"日"]})]})]}),e.isRecommended&&(0,a.jsx)("div",{className:"mt-2 inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"en"===r?"Recommended":"推奨"})]},t))]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,a.jsx)("button",{onClick:()=>o("details"),className:"px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors",children:"en"===r?"Back":"戻る"}),(0,a.jsx)("button",{onClick:()=>o("payment"),disabled:!w,className:"px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"en"===r?"Continue to Payment":"支払いに進む"})]})]}),"payment"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,a.jsx)(Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"en"===r?"Payment Method":"支払い方法"})]}),(0,a.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-neutral-800 mb-3",children:"en"===r?"Order Summary":"注文概要"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:s.title}),(0,a.jsx)("span",{children:s.price})]}),w&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{children:["en"===r?"Shipping":"配送料"," (",w.method.name,")"]}),(0,a.jsxs)("span",{children:["\xa5",w.costs.total.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"border-t pt-2 flex justify-between font-bold",children:[(0,a.jsx)("span",{children:"en"===r?"Total":"合計"}),(0,a.jsxs)("span",{children:["\xa5",(parseInt(s.price.replace(/[¥,]/g,""))+w.costs.total).toLocaleString()]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-neutral-800",children:"en"===r?"Select Payment Method":"支払い方法を選択"}),u.map(e=>(0,a.jsx)("div",{className:`border rounded-lg p-4 cursor-pointer transition-colors ${b?.id===e.id?"border-primary-500 bg-primary-50":"border-neutral-300 hover:border-neutral-400"}`,onClick:()=>g(e),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-neutral-800",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:e.description})]})]})},e.id))]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("p",{className:"text-sm text-blue-700 mb-3",children:"en"===r?"By proceeding, you agree to create an order. Payment instructions will be provided after order creation.":"続行することで、注文の作成に同意したことになります。注文作成後に支払い指示が提供されます。"}),(0,a.jsx)("button",{onClick:$,disabled:d||!b,className:"w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:"en"===r?"Creating Order...":"注文作成中..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===r?"Create Order":"注文作成"})]})})]}),(0,a.jsx)("div",{className:"flex items-center justify-between pt-4",children:(0,a.jsx)("button",{onClick:()=>o("shipping"),disabled:d,className:"px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors disabled:opacity-50",children:"en"===r?"Back":"戻る"})})]}),"confirmation"===c&&m&&(0,a.jsxs)("div",{className:"space-y-6 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto",children:(0,a.jsx)(D.A,{className:"w-8 h-8 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-neutral-800 mb-2",children:"en"===r?"Order Created Successfully!":"注文作成完了！"}),(0,a.jsx)("p",{className:"text-neutral-600 mb-4",children:"en"===r?"Your order has been created and an invoice has been generated. Please complete payment to proceed.":"注文が作成され、請求書が生成されました。続行するには支払いを完了してください。"}),I&&(0,a.jsx)("div",{className:"bg-neutral-100 rounded-lg p-3 mb-4",children:(0,a.jsxs)("p",{className:"text-sm text-neutral-600",children:["en"===r?"Order ID:":"注文ID:"," ",(0,a.jsx)("span",{className:"font-mono font-medium",children:I})]})})]}),b&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-left",children:[(0,a.jsxs)("h4",{className:"font-medium text-blue-800 mb-2 flex items-center space-x-2",children:[(0,a.jsx)(Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"en"===r?"Payment Instructions":"支払い指示"})]}),(0,a.jsx)("div",{className:"text-sm text-blue-700 whitespace-pre-line",children:b&&w&&I&&(()=>{let e=parseInt(s.price.replace(/[¥,]/g,""))+w.costs.total;return`Payment Method: ${b.name}
Amount: \xa5${e.toLocaleString()}
Order Reference: ${I}

Please contact us for detailed payment instructions.`})()})]}),(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800 mb-2",children:"en"===r?"What happens next?":"次に何が起こりますか？"}),(0,a.jsxs)("ul",{className:"text-sm text-green-700 space-y-1 text-left",children:[(0,a.jsxs)("li",{children:["• ","en"===r?"Complete payment using the instructions above":"上記の指示に従って支払いを完了"]}),(0,a.jsxs)("li",{children:["• ","en"===r?"Vehicle inspection and preparation":"車両検査と準備"]}),(0,a.jsxs)("li",{children:["• ","en"===r?"Export documentation processing":"輸出書類処理"]}),(0,a.jsxs)("li",{children:["• ","en"===r?"Shipping arrangement":"配送手配"]}),(0,a.jsxs)("li",{children:["• ","en"===r?"Regular updates on delivery status":"配送状況の定期更新"]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:R,className:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors",children:"en"===r?"Close":"閉じる"}),I&&(0,a.jsx)("button",{onClick:()=>window.open(`/tracking?orderId=${I}`,"_blank"),className:"w-full bg-neutral-600 text-white py-2 px-6 rounded-lg hover:bg-neutral-700 transition-colors",children:"en"===r?"Track Order":"注文追跡"})]})]})]})]})})}s(89809);var el=s(76072);function er({vehicle:e,locale:t}){let[s,r]=(0,l.useState)("overview"),[d,N]=(0,l.useState)(!1),[w,k]=(0,l.useState)(!1),[C,A]=(0,l.useState)(!1),[S,T]=(0,l.useState)(!1),I=[{id:"overview",label:"en"===t?"Overview":"概要",icon:i.A},{id:"specs",label:"en"===t?"Specifications":"仕様",icon:c.A},{id:"history",label:"en"===t?"History & Reports":"履歴・レポート",icon:o.A},{id:"360view",label:"en"===t?"360\xb0 View":"360\xb0ビュー",icon:x},{id:"compare",label:"en"===t?"Compare":"比較",icon:m}],P=async()=>{if(navigator.share)try{await navigator.share({title:e.title,text:`Check out this ${e.title} at EBAM Motors`,url:window.location.href})}catch(e){}else navigator.clipboard.writeText(window.location.href),alert("en"===t?"Link copied to clipboard!":"リンクがクリップボードにコピーされました！")};return(0,a.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,a.jsx)(f.default,{}),(0,a.jsx)("div",{className:"bg-white border-b sticky top-16 z-40",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(n(),{href:`/${t}/stock`,className:"flex items-center space-x-2 text-neutral-600 hover:text-primary-600 transition-colors",children:[(0,a.jsx)(h,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"en"===t?"Back to Stock":"在庫に戻る"})]}),(0,a.jsx)("div",{className:"hidden sm:block w-px h-6 bg-neutral-300"}),(0,a.jsx)("h1",{className:"text-xl font-bold text-neutral-800",children:e.title})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>N(!d),className:`p-2 rounded-lg border transition-colors ${d?"bg-red-50 border-red-200 text-red-600":"bg-white border-neutral-300 text-neutral-600 hover:text-red-600"}`,children:(0,a.jsx)(u.A,{className:`w-5 h-5 ${d?"fill-current":""}`})}),(0,a.jsx)("button",{onClick:P,className:"p-2 rounded-lg border border-neutral-300 text-neutral-600 hover:text-primary-600 transition-colors",children:(0,a.jsx)(p,{className:"w-5 h-5"})}),(0,a.jsxs)("button",{onClick:()=>k(!0),className:"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2",children:[(0,a.jsx)(m,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"en"===t?"Compare":"比較"})]})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsx)(M,{vehicle:e,locale:t}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-neutral-600 mb-1",children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"en"===t?"Year":"年式"})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-neutral-800",children:e.year||"N/A"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-neutral-600 mb-1",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"en"===t?"Mileage":"走行距離"})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-neutral-800",children:e.mileage?`${Math.floor(e.mileage/1e3)}k km`:"N/A"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-neutral-600 mb-1",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"en"===t?"Fuel":"燃料"})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-neutral-800",children:e.fuelType||"N/A"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-neutral-600 mb-1",children:[(0,a.jsx)(c.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"en"===t?"Transmission":"トランスミッション"})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-neutral-800",children:e.transmission||"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 border shadow-sm",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-primary-600",children:e.price}),(0,a.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"FOB Price":"FOB価格"})]}),(0,a.jsxs)("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${"Available"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full mr-2 ${"Available"===e.status?"bg-green-500":"bg-yellow-500"}`}),e.status]}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-neutral-600",children:[(0,a.jsx)(y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:e.location})]}),(0,a.jsxs)("div",{className:"space-y-3 pt-4",children:[(0,a.jsx)("button",{onClick:()=>A(!0),className:"w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors",children:"en"===t?"Purchase Now":"今すぐ購入"}),(0,a.jsx)("button",{onClick:()=>T(!0),className:"w-full border border-primary-600 text-primary-600 py-3 px-4 rounded-lg font-semibold hover:bg-primary-50 transition-colors",children:"en"===t?"Request Quote":"見積もり依頼"})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 text-primary-600"}),(0,a.jsx)("span",{children:"en"===t?"Trust & Safety":"信頼・安全"})]}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"en"===t?"Verified Seller":"認証済み販売者"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"en"===t?"Inspection Available":"検査利用可能"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"en"===t?"Secure Payment":"安全な支払い"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-12",children:[(0,a.jsx)("div",{className:"border-b border-neutral-200",children:(0,a.jsx)("nav",{className:"flex space-x-8 overflow-x-auto",children:I.map(e=>{let t=e.icon;return(0,a.jsxs)("button",{onClick:()=>r(e.id),className:`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${s===e.id?"border-primary-600 text-primary-600":"border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300"}`,children:[(0,a.jsx)(t,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.label})]},e.id)})})}),(0,a.jsxs)("div",{className:"mt-8",children:["overview"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4",children:"en"===t?"Vehicle Overview":"車両概要"}),(0,a.jsx)("div",{className:"prose max-w-none",children:(0,a.jsx)("p",{className:"text-neutral-600",children:"en"===t?`This ${e.title} is in ${e.bodyCondition?.toLowerCase()} condition and ready for export. The vehicle has been thoroughly inspected and comes with all necessary documentation for international shipping.`:`この${e.title}は${e.bodyCondition}の状態で、輸出準備が整っています。車両は徹底的に検査され、国際輸送に必要なすべての書類が揃っています。`})})]}),"specs"===s&&(0,a.jsx)($,{vehicle:e,locale:t}),"history"===s&&(0,a.jsx)(E,{vehicle:e,locale:t}),"360view"===s&&(0,a.jsx)(F,{vehicle:e,locale:t}),"compare"===s&&(0,a.jsx)(X,{vehicle:e,locale:t})]})]})]}),w&&(0,a.jsx)(X,{vehicle:e,locale:t,onClose:()=>k(!1),isModal:!0}),C&&(0,a.jsx)(ea,{isOpen:C,onClose:()=>A(!1),vehicle:e,locale:t}),S&&(0,a.jsx)(el.A,{locale:t,isOpen:S,onClose:()=>T(!1),productInterest:`${e.title} - ${e.price}`})]})}},54173:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\[locale]\\\\stock\\\\[vehicleId]\\\\VehicleDetailPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\[locale]\\stock\\[vehicleId]\\VehicleDetailPage.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},82679:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},83998:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,generateStaticParams:()=>c});var a=s(37413),l=s(39916),r=s(17198),n=s(54173);async function i({params:e}){let{locale:t,vehicleId:s}=await e,i=(await (0,r.l)()).find(e=>e.carId===s);return i||(0,l.notFound)(),(0,a.jsx)(n.default,{vehicle:i,locale:t})}async function c(){return(await (0,r.l)()).filter(e=>"cars"===e.category).flatMap(e=>[{locale:"en",vehicleId:e.carId},{locale:"ja",vehicleId:e.carId}])}},88059:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},88331:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(65239),l=s(48088),r=s(88170),n=s.n(r),i=s(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let o={children:["",{children:["[locale]",{children:["stock",{children:["[vehicleId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83998)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\[locale]\\stock\\[vehicleId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,11434)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\[locale]\\stock\\[vehicleId]\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/[locale]/stock/[vehicleId]/page",pathname:"/[locale]/stock/[vehicleId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},95487:(e,t,s)=>{Promise.resolve().then(s.bind(s,53034))},96882:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},98971:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,7445,1658,5814,8898,6533,1033,5839,1937,3181],()=>s(88331));module.exports=a})();