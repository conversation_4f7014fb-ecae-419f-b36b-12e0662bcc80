import { NextRequest, NextResponse } from 'next/server';
import { 
  getAllCustomers, 
  getCustomerById, 
  getCustomerByEmail,
  updateCustomer,
  upsertCustomer,
  getCustomerOverview
} from '@/lib/crmStorage';
import { Customer, Order } from '@/types/payment';

// GET - Fetch customers
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');
    const customerId = searchParams.get('id');
    const email = searchParams.get('email');
    const overview = searchParams.get('overview') === 'true';

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get customer overview with stats
    if (customerId && overview) {
      const customerOverview = await getCustomerOverview(customerId);
      if (!customerOverview) {
        return NextResponse.json(
          { success: false, message: 'Customer not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, data: customerOverview });
    }

    // Get specific customer by ID
    if (customerId) {
      const customer = await getCustomerById(customerId);
      if (!customer) {
        return NextResponse.json(
          { success: false, message: 'Customer not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, customer });
    }

    // Get customer by email
    if (email) {
      const customer = await getCustomerByEmail(email);
      if (!customer) {
        return NextResponse.json(
          { success: false, message: 'Customer not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, customer });
    }

    // Get all customers
    const customers = await getAllCustomers();
    
    // Sort by most recent first
    customers.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({ success: true, customers });

  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
}

// POST - Create or update customer
export async function POST(request: NextRequest) {
  try {
    const customerData = await request.json();

    // Validate required fields
    if (!customerData.personalInfo?.name || !customerData.personalInfo?.email) {
      return NextResponse.json(
        { success: false, message: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Set defaults for new customer
    const newCustomerData = {
      status: 'active',
      segment: 'new',
      loyaltyPoints: 0,
      membershipTier: 'Bronze',
      totalSpent: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      acquisitionSource: 'website',
      tags: [],
      preferences: {
        notifications: {
          email: true,
          sms: false,
          push: true,
          marketing: false,
        },
        currency: 'JPY',
        communicationPreference: 'email',
      },
      address: {},
      ...customerData,
    } as Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>;

    const customer = await upsertCustomer(newCustomerData);

    // Log interaction for customer creation/update
    const { createInteraction } = await import('@/lib/crmStorage');
    await createInteraction({
      customerId: customer.id,
      type: 'support',
      direction: 'inbound',
      channel: 'website',
      content: 'Customer profile created/updated',
      subject: 'Customer Registration',
      tags: ['customer_registration', 'profile_update'],
      createdBy: 'system',
    });

    // Schedule automated follow-ups (3 days)
    const { scheduleAutoFollowupForCustomer } = await import('@/lib/automatedFollowup');
    await scheduleAutoFollowupForCustomer(customer.id, newCustomerData);

    return NextResponse.json({ 
      success: true, 
      message: 'Customer saved successfully',
      customer 
    });

  } catch (error) {
    console.error('Error creating/updating customer:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to save customer' },
      { status: 500 }
    );
  }
}

// PATCH - Update customer
export async function PATCH(request: NextRequest) {
  try {
    const { customerId, adminKey, ...updates } = await request.json();

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!customerId) {
      return NextResponse.json(
        { success: false, message: 'Customer ID is required' },
        { status: 400 }
      );
    }

    const success = await updateCustomer(customerId, updates);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Customer not found' },
        { status: 404 }
      );
    }

    // Log interaction for customer update
    const { createInteraction } = await import('@/lib/crmStorage');
    await createInteraction({
      customerId,
      type: 'support',
      direction: 'outbound',
      channel: 'website',
      content: `Customer profile updated: ${Object.keys(updates).join(', ')}`,
      subject: 'Profile Update',
      tags: ['profile_update', 'admin_action'],
      createdBy: 'admin',
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Customer updated successfully' 
    });

  } catch (error) {
    console.error('Error updating customer:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update customer' },
      { status: 500 }
    );
  }
}

// Utility function to sync customer data from orders
export async function syncCustomerFromOrder(orderData: Order) {
  try {
    const customerData = {
      personalInfo: {
        name: orderData.customerInfo.name,
        email: orderData.customerInfo.email,
        phone: orderData.customerInfo.phone,
      },
      address: orderData.customerInfo.address,
      preferences: {
        notifications: {
          email: true,
          sms: false,
          push: true,
          marketing: false,
        },
        currency: orderData.currency || 'JPY',
        communicationPreference: 'email' as const,
      },
      status: 'active' as const,
      segment: 'regular' as const,
      loyaltyPoints: 0,
      membershipTier: 'Bronze' as const,
      totalSpent: orderData.totalAmount,
      totalOrders: 1,
      averageOrderValue: orderData.totalAmount,
      acquisitionSource: 'order',
      tags: ['customer'],
    };

    const customer = await upsertCustomer(customerData);
    
    // Update customer stats if they already existed
    if (customer) {
      const existingCustomer = await getCustomerById(customer.id);
      if (existingCustomer && existingCustomer.totalOrders > 0) {
        await updateCustomer(customer.id, {
          totalSpent: existingCustomer.totalSpent + orderData.totalAmount,
          totalOrders: existingCustomer.totalOrders + 1,
          averageOrderValue: (existingCustomer.totalSpent + orderData.totalAmount) / (existingCustomer.totalOrders + 1),
          lastOrderDate: new Date().toISOString(),
        });
      }
    }

    return customer;
  } catch (error) {
    console.error('Error syncing customer from order:', error);
    return null;
  }
}
