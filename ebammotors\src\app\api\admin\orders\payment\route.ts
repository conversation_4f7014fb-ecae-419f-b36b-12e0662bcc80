import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { updatePaymentStatus, getOrderById } from '@/lib/orderStorage';
import { markInvoiceAsPaid } from '@/lib/invoiceGenerator';
import { emailService, PaymentConfirmationData } from '@/lib/resendService';

export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { orderId, paymentStatus, transactionId, notes } = await request.json();

    if (!orderId || !paymentStatus) {
      return NextResponse.json(
        { success: false, message: 'Order ID and payment status are required' },
        { status: 400 }
      );
    }

    // Get current order
    const currentOrder = await getOrderById(orderId);
    if (!currentOrder) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // Prepare payment update data
    const paymentUpdateData: any = {
      status: paymentStatus,
    };

    if (transactionId) {
      paymentUpdateData.transactionId = transactionId;
    }

    if (paymentStatus === 'completed') {
      paymentUpdateData.paidAt = new Date().toISOString();
    }

    // Update payment status
    const success = await updatePaymentStatus(orderId, paymentUpdateData);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Failed to update payment status' },
        { status: 500 }
      );
    }

    // Mark invoice as paid if payment is completed
    if (paymentStatus === 'completed') {
      try {
        await markInvoiceAsPaid(orderId);
      } catch (invoiceError) {
        console.error('Failed to mark invoice as paid:', invoiceError);
        // Continue without failing the payment update
      }
    }

    // Send payment confirmation email
    if (paymentStatus === 'completed') {
      try {
        const updatedOrder = await getOrderById(orderId);
        if (updatedOrder) {
          const paymentConfirmationData: PaymentConfirmationData = {
            customerName: updatedOrder.customerInfo.name,
            orderNumber: updatedOrder.orderNumber,
            vehicleTitle: updatedOrder.vehicle.title,
            paymentAmount: updatedOrder.payment.amount,
            paymentMethod: updatedOrder.payment.method.name,
            transactionId: transactionId || updatedOrder.payment.transactionId,
            paidAt: new Date().toLocaleDateString(),
            orderUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/tracking?order=${updatedOrder.orderNumber}`
          };

          await emailService.sendPaymentConfirmation(
            updatedOrder.customerInfo.email,
            paymentConfirmationData
          );
        }
      } catch (emailError) {
        console.error('Failed to send payment confirmation email:', emailError);
        // Don't fail the payment update if email fails
      }
    }

    return NextResponse.json({
      success: true,
      message: `Payment status updated to ${paymentStatus}`
    });

  } catch (error) {
    console.error('Error updating payment status:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update payment status' },
      { status: 500 }
    );
  }
}
