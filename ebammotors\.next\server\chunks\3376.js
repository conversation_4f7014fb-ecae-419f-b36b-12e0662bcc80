exports.id=3376,exports.ids=[3376],exports.modules={2968:(e,t,r)=>{"use strict";let s,i=r(74075),n=r(46471),o=r(52346),{kStatusCode:a}=r(32435),h=Buffer[Symbol.species],l=Buffer.from([0,0,255,255]),c=Symbol("permessage-deflate"),u=Symbol("total-length"),f=Symbol("callback"),d=Symbol("buffers"),p=Symbol("error");class y{constructor(e,t,r){this._maxPayload=0|r,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,s||(s=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[f];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,r=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!r)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(r.server_no_context_takeover=!0),t.clientNoContextTakeover&&(r.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(r.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?r.client_max_window_bits=t.clientMaxWindowBits:(!0===r.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete r.client_max_window_bits,r}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let r=e[t];if(r.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(r=r[0],"client_max_window_bits"===t){if(!0!==r){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else if("server_max_window_bits"===t){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==r)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=r})}),e}decompress(e,t,r){s.add(s=>{this._decompress(e,t,(e,t)=>{s(),r(e,t)})})}compress(e,t,r){s.add(s=>{this._compress(e,t,(e,t)=>{s(),r(e,t)})})}_decompress(e,t,r){let s=this._isServer?"client":"server";if(!this._inflate){let e=`${s}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=i.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[c]=this,this._inflate[u]=0,this._inflate[d]=[],this._inflate.on("error",b),this._inflate.on("data",g)}this._inflate[f]=r,this._inflate.write(e),t&&this._inflate.write(l),this._inflate.flush(()=>{let e=this._inflate[p];if(e){this._inflate.close(),this._inflate=null,r(e);return}let i=n.concat(this._inflate[d],this._inflate[u]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[u]=0,this._inflate[d]=[],t&&this.params[`${s}_no_context_takeover`]&&this._inflate.reset()),r(null,i)})}_compress(e,t,r){let s=this._isServer?"server":"client";if(!this._deflate){let e=`${s}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=i.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[u]=0,this._deflate[d]=[],this._deflate.on("data",m)}this._deflate[f]=r,this._deflate.write(e),this._deflate.flush(i.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=n.concat(this._deflate[d],this._deflate[u]);t&&(e=new h(e.buffer,e.byteOffset,e.length-4)),this._deflate[f]=null,this._deflate[u]=0,this._deflate[d]=[],t&&this.params[`${s}_no_context_takeover`]&&this._deflate.reset(),r(null,e)})}}function m(e){this[d].push(e),this[u]+=e.length}function g(e){if(this[u]+=e.length,this[c]._maxPayload<1||this[u]<=this[c]._maxPayload)return void this[d].push(e);this[p]=RangeError("Max payload size exceeded"),this[p].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[p][a]=1009,this.removeListener("data",g),this.reset()}function b(e){if(this[c]._inflate=null,this[p])return void this[f](this[p]);e[a]=1007,this[f](e)}e.exports=y},15997:(e,t,r)=>{"use strict";let{Writable:s}=r(27910),i=r(2968),{BINARY_TYPES:n,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:h}=r(32435),{concat:l,toArrayBuffer:c,unmask:u}=r(46471),{isValidStatusCode:f,isValidUTF8:d}=r(58571),p=Buffer[Symbol.species];class y extends s{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||n[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[h]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,r){if(8===this._opcode&&0==this._state)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new p(t.buffer,t.byteOffset+e,t.length-e),new p(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let r=this._buffers[0],s=t.length-e;e>=r.length?t.set(this._buffers.shift(),s):(t.set(new Uint8Array(r.buffer,r.byteOffset,e),s),this._buffers[0]=new p(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0)return void e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));let r=(64&t[0])==64;if(r&&!this._extensions[i.extensionName])return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(r)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(!this._fragmented)return void e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));this._compressed=r}else{if(!(this._opcode>7)||!(this._opcode<11))return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));if(!this._fin)return void e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));if(r)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength)return void e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"))}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked)return void e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"))}else if(this._masked)return void e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),r=t.readUInt32BE(0);if(r>2097151)return void e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));this._payloadLength=0x100000000*r+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return void e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&u(t,this._mask)}if(this._opcode>7)return void this.controlMessage(t,e);if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[i.extensionName].decompress(e,this._fin,(e,r)=>{if(e)return t(e);if(r.length){if(this._messageLength+=r.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return void t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(r)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let s;s="nodebuffer"===this._binaryType?l(r,t):"arraybuffer"===this._binaryType?c(l(r,t)):"blob"===this._binaryType?new Blob(r):r,this._allowSynchronousEvents?(this.emit("message",s,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",s,!0),this._state=0,this.startLoop(e)}))}else{let s=l(r,t);if(!this._skipUTF8Validation&&!d(s))return void e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));5===this._state||this._allowSynchronousEvents?(this.emit("message",s,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",s,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let r=e.readUInt16BE(0);if(!f(r))return void t(this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));let s=new p(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!d(s))return void t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));this._loop=!1,this.emit("conclude",r,s),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,r,s,i){this._loop=!1,this._errored=!0;let n=new e(r?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(n,this.createError),n.code=i,n[a]=s,n}}e.exports=y},16809:(e,t,r)=>{"use strict";let s=r(94735),i=r(81630),{Duplex:n}=r(27910),{createHash:o}=r(55511),a=r(46987),h=r(2968),l=r(91596),c=r(32113),{GUID:u,kWebSocket:f}=r(32435),d=/^[+/0-9A-Za-z]{22}==$/;class p extends s{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:c,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=i.createServer((e,t)=>{let r=i.STATUS_CODES[426];t.writeHead(426,{"Content-Length":r.length,"Content-Type":"text/plain"}),t.end(r)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let r of Object.keys(t))e.on(r,t[r]);return function(){for(let r of Object.keys(t))e.removeListener(r,t[r])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,r,s)=>{this.handleUpgrade(t,r,s,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(y,this);return}if(e&&this.once("close",e),1!==this._state)if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(y,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{y(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,r,s){t.on("error",m);let i=e.headers["sec-websocket-key"],n=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method)return void b(this,e,t,405,"Invalid HTTP method");if(void 0===n||"websocket"!==n.toLowerCase())return void b(this,e,t,400,"Invalid Upgrade header");if(void 0===i||!d.test(i))return void b(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");if(13!==o&&8!==o)return void b(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header",{"Sec-WebSocket-Version":"13, 8"});if(!this.shouldHandle(e))return void g(t,400);let c=e.headers["sec-websocket-protocol"],u=new Set;if(void 0!==c)try{u=l.parse(c)}catch(r){b(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let f=e.headers["sec-websocket-extensions"],p={};if(this.options.perMessageDeflate&&void 0!==f){let r=new h(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(f);e[h.extensionName]&&(r.accept(e[h.extensionName]),p[h.extensionName]=r)}catch(r){b(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let n={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length)return void this.options.verifyClient(n,(n,o,a,h)=>{if(!n)return g(t,o||401,a,h);this.completeUpgrade(p,i,u,e,t,r,s)});if(!this.options.verifyClient(n))return g(t,401)}this.completeUpgrade(p,i,u,e,t,r,s)}completeUpgrade(e,t,r,s,i,n,l){if(!i.readable||!i.writable)return i.destroy();if(i[f])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return g(i,503);let c=o("sha1").update(t+u).digest("base64"),d=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${c}`],p=new this.options.WebSocket(null,void 0,this.options);if(r.size){let e=this.options.handleProtocols?this.options.handleProtocols(r,s):r.values().next().value;e&&(d.push(`Sec-WebSocket-Protocol: ${e}`),p._protocol=e)}if(e[h.extensionName]){let t=e[h.extensionName].params,r=a.format({[h.extensionName]:[t]});d.push(`Sec-WebSocket-Extensions: ${r}`),p._extensions=e}this.emit("headers",d,s),i.write(d.concat("\r\n").join("\r\n")),i.removeListener("error",m),p.setSocket(i,n,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(p),p.on("close",()=>{this.clients.delete(p),this._shouldEmitClose&&!this.clients.size&&process.nextTick(y,this)})),l(p,s)}}function y(e){e._state=2,e.emit("close")}function m(){this.destroy()}function g(e,t,r,s){r=r||i.STATUS_CODES[t],s={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(r),...s},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${i.STATUS_CODES[t]}\r
`+Object.keys(s).map(e=>`${e}: ${s[e]}`).join("\r\n")+"\r\n\r\n"+r)}function b(e,t,r,s,i,n){if(e.listenerCount("wsClientError")){let s=Error(i);Error.captureStackTrace(s,b),e.emit("wsClientError",s,r,t)}else g(r,s,i,n)}e.exports=p},32113:(e,t,r)=>{"use strict";let s=r(94735),i=r(55591),n=r(81630),o=r(91645),a=r(34631),{randomBytes:h,createHash:l}=r(55511),{Duplex:c,Readable:u}=r(27910),{URL:f}=r(79551),d=r(2968),p=r(15997),y=r(95773),{isBlob:m}=r(58571),{BINARY_TYPES:g,EMPTY_BUFFER:b,GUID:_,kForOnEventAttribute:v,kListener:w,kStatusCode:S,kWebSocket:E,NOOP:x}=r(32435),{EventTarget:{addEventListener:C,removeEventListener:k}}=r(56510),{format:A,parse:T}=r(46987),{toBuffer:L}=r(46471),I=Symbol("kAborted"),P=[8,13],R=["CONNECTING","OPEN","CLOSING","CLOSED"],O=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class B extends s{constructor(e,t,r){super(),this._binaryType=g[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=b,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=B.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(r=t,t=[]):t=[t]),function e(t,r,s,o){let a,c,u,p,y={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:P[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=y.autoPong,!P.includes(y.protocolVersion))throw RangeError(`Unsupported protocol version: ${y.protocolVersion} (supported versions: ${P.join(", ")})`);if(r instanceof f)a=r;else try{a=new f(r)}catch(e){throw SyntaxError(`Invalid URL: ${r}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let m="wss:"===a.protocol,g="ws+unix:"===a.protocol;if("ws:"===a.protocol||m||g?g&&!a.pathname?c="The URL's pathname is empty":a.hash&&(c="The URL contains a fragment identifier"):c='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https:", or "ws+unix:"',c){let e=SyntaxError(c);if(0!==t._redirects)return void M(t,e);throw e}let b=m?443:80,v=h(16).toString("base64"),w=m?i.request:n.request,S=new Set;if(y.createConnection=y.createConnection||(m?U:N),y.defaultPort=y.defaultPort||b,y.port=a.port||b,y.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,y.headers={...y.headers,"Sec-WebSocket-Version":y.protocolVersion,"Sec-WebSocket-Key":v,Connection:"Upgrade",Upgrade:"websocket"},y.path=a.pathname+a.search,y.timeout=y.handshakeTimeout,y.perMessageDeflate&&(u=new d(!0!==y.perMessageDeflate?y.perMessageDeflate:{},!1,y.maxPayload),y.headers["Sec-WebSocket-Extensions"]=A({[d.extensionName]:u.offer()})),s.length){for(let e of s){if("string"!=typeof e||!O.test(e)||S.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");S.add(e)}y.headers["Sec-WebSocket-Protocol"]=s.join(",")}if(y.origin&&(y.protocolVersion<13?y.headers["Sec-WebSocket-Origin"]=y.origin:y.headers.Origin=y.origin),(a.username||a.password)&&(y.auth=`${a.username}:${a.password}`),g){let e=y.path.split(":");y.socketPath=e[0],y.path=e[1]}if(y.followRedirects){if(0===t._redirects){t._originalIpc=g,t._originalSecure=m,t._originalHostOrSocketPath=g?y.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,r]of Object.entries(e))o.headers[t.toLowerCase()]=r}else if(0===t.listenerCount("redirect")){let e=g?!!t._originalIpc&&y.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||m)||(delete y.headers.authorization,delete y.headers.cookie,e||delete y.headers.host,y.auth=void 0)}y.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(y.auth).toString("base64")),p=t._req=w(y),t._redirects&&t.emit("redirect",t.url,p)}else p=t._req=w(y);y.timeout&&p.on("timeout",()=>{D(t,p,"Opening handshake has timed out")}),p.on("error",e=>{null===p||p[I]||(p=t._req=null,M(t,e))}),p.on("response",i=>{let n=i.headers.location,a=i.statusCode;if(n&&y.followRedirects&&a>=300&&a<400){let i;if(++t._redirects>y.maxRedirects)return void D(t,p,"Maximum redirects exceeded");p.abort();try{i=new f(n,r)}catch(e){M(t,SyntaxError(`Invalid URL: ${n}`));return}e(t,i,s,o)}else t.emit("unexpected-response",p,i)||D(t,p,`Unexpected server response: ${i.statusCode}`)}),p.on("upgrade",(e,r,s)=>{let i;if(t.emit("upgrade",e),t.readyState!==B.CONNECTING)return;p=t._req=null;let n=e.headers.upgrade;if(void 0===n||"websocket"!==n.toLowerCase())return void D(t,r,"Invalid Upgrade header");let o=l("sha1").update(v+_).digest("base64");if(e.headers["sec-websocket-accept"]!==o)return void D(t,r,"Invalid Sec-WebSocket-Accept header");let a=e.headers["sec-websocket-protocol"];if(void 0!==a?S.size?S.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":S.size&&(i="Server sent no subprotocol"),i)return void D(t,r,i);a&&(t._protocol=a);let h=e.headers["sec-websocket-extensions"];if(void 0!==h){let e;if(!u)return void D(t,r,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");try{e=T(h)}catch(e){D(t,r,"Invalid Sec-WebSocket-Extensions header");return}let s=Object.keys(e);if(1!==s.length||s[0]!==d.extensionName)return void D(t,r,"Server indicated an extension that was not requested");try{u.accept(e[d.extensionName])}catch(e){D(t,r,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[d.extensionName]=u}t.setSocket(r,s,{allowSynchronousEvents:y.allowSynchronousEvents,generateMask:y.generateMask,maxPayload:y.maxPayload,skipUTF8Validation:y.skipUTF8Validation})}),y.finishRequest?y.finishRequest(p,t):p.end()}(this,e,t,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){g.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,r){let s=new p({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation}),i=new y(e,this._extensions,r.generateMask);this._receiver=s,this._sender=i,this._socket=e,s[E]=this,i[E]=this,e[E]=this,s.on("conclude",Q),s.on("drain",j),s.on("error",q),s.on("message",$),s.on("ping",G),s.on("pong",V),i.onerror=z,e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",Y),e.on("data",Z),e.on("end",X),e.on("error",J),this._readyState=B.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=B.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[d.extensionName]&&this._extensions[d.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=B.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==B.CLOSED){if(this.readyState===B.CONNECTING)return void D(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===B.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=B.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),K(this)}}pause(){this.readyState!==B.CONNECTING&&this.readyState!==B.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,r){if(this.readyState===B.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==B.OPEN)return void F(this,e,r);void 0===t&&(t=!this._isServer),this._sender.ping(e||b,t,r)}pong(e,t,r){if(this.readyState===B.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==B.OPEN)return void F(this,e,r);void 0===t&&(t=!this._isServer),this._sender.pong(e||b,t,r)}resume(){this.readyState!==B.CONNECTING&&this.readyState!==B.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,r){if(this.readyState===B.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(r=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==B.OPEN)return void F(this,e,r);let s={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[d.extensionName]||(s.compress=!1),this._sender.send(e||b,s,r)}terminate(){if(this.readyState!==B.CLOSED){if(this.readyState===B.CONNECTING)return void D(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=B.CLOSING,this._socket.destroy())}}}function M(e,t){e._readyState=B.CLOSING,e._errorEmitted=!0,e.emit("error",t),e.emitClose()}function N(e){return e.path=e.socketPath,o.connect(e)}function U(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function D(e,t,r){e._readyState=B.CLOSING;let s=Error(r);Error.captureStackTrace(s,D),t.setHeader?(t[I]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(M,e,s)):(t.destroy(s),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function F(e,t,r){if(t){let r=m(t)?t.size:L(t).length;e._socket?e._sender._bufferedBytes+=r:e._bufferedAmount+=r}if(r){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${R[e.readyState]})`);process.nextTick(r,t)}}function Q(e,t){let r=this[E];r._closeFrameReceived=!0,r._closeMessage=t,r._closeCode=e,void 0!==r._socket[E]&&(r._socket.removeListener("data",Z),process.nextTick(H,r._socket),1005===e?r.close():r.close(e,t))}function j(){let e=this[E];e.isPaused||e._socket.resume()}function q(e){let t=this[E];void 0!==t._socket[E]&&(t._socket.removeListener("data",Z),process.nextTick(H,t._socket),t.close(e[S])),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e))}function W(){this[E].emitClose()}function $(e,t){this[E].emit("message",e,t)}function G(e){let t=this[E];t._autoPong&&t.pong(e,!this._isServer,x),t.emit("ping",e)}function V(e){this[E].emit("pong",e)}function H(e){e.resume()}function z(e){let t=this[E];t.readyState!==B.CLOSED&&(t.readyState===B.OPEN&&(t._readyState=B.CLOSING,K(t)),this._socket.end(),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e)))}function K(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),3e4)}function Y(){let e,t=this[E];this.removeListener("close",Y),this.removeListener("data",Z),this.removeListener("end",X),t._readyState=B.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[E]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",W),t._receiver.on("finish",W))}function Z(e){this[E]._receiver.write(e)||this.pause()}function X(){let e=this[E];e._readyState=B.CLOSING,e._receiver.end(),this.end()}function J(){let e=this[E];this.removeListener("error",J),this.on("error",x),e&&(e._readyState=B.CLOSING,this.destroy())}Object.defineProperty(B,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(B.prototype,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(B,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(B.prototype,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(B,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(B.prototype,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(B,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),Object.defineProperty(B.prototype,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(B.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(B.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[v])return t[w];return null},set(t){for(let t of this.listeners(e))if(t[v]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[v]:!0})}})}),B.prototype.addEventListener=C,B.prototype.removeEventListener=k,e.exports=B},32435:e=>{"use strict";let t=["nodebuffer","arraybuffer","fragments"],r="undefined"!=typeof Blob;r&&t.push("blob"),e.exports={BINARY_TYPES:t,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:r,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},32714:(e,t,r)=>{"use strict";try{e.exports=r(34172)(__dirname)}catch(t){e.exports=r(85514)}},34172:(e,t,r)=>{let s=require;"function"==typeof s.addon?e.exports=s.addon.bind(s):e.exports=r(64082)},37796:(e,t,r)=>{"use strict";r(32113);let{Duplex:s}=r(27910);function i(e){e.emit("close")}function n(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let r=!0,a=new s({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,r){let s=!r&&a._readableState.objectMode?t.toString():t;a.push(s)||e.pause()}),e.once("error",function(e){a.destroyed||(r=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,s){if(e.readyState===e.CLOSED){s(t),process.nextTick(i,a);return}let n=!1;e.once("error",function(e){n=!0,s(e)}),e.once("close",function(){n||s(t),process.nextTick(i,a)}),r&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING)return void e.once("open",function(){a._final(t)});null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,r,s){if(e.readyState===e.CONNECTING)return void e.once("open",function(){a._write(t,r,s)});e.send(t,s)},a.on("end",n),a.on("error",o),a}},46471:(e,t,r)=>{"use strict";let{EMPTY_BUFFER:s}=r(32435),i=Buffer[Symbol.species];function n(e,t,r,s,i){for(let n=0;n<i;n++)r[s+n]=e[n]^t[3&n]}function o(e,t){for(let r=0;r<e.length;r++)e[r]^=t[3&r]}function a(e){let t;return(a.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?t=new i(e):ArrayBuffer.isView(e)?t=new i(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),a.readOnly=!1),t)}if(e.exports={concat:function(e,t){if(0===e.length)return s;if(1===e.length)return e[0];let r=Buffer.allocUnsafe(t),n=0;for(let t=0;t<e.length;t++){let s=e[t];r.set(s,n),n+=s.length}return n<t?new i(r.buffer,r.byteOffset,n):r},mask:n,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:a,unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=r(32714);e.exports.mask=function(e,r,s,i,o){o<48?n(e,r,s,i,o):t.mask(e,r,s,i,o)},e.exports.unmask=function(e,r){e.length<32?o(e,r):t.unmask(e,r)}}catch(e){}},46987:(e,t,r)=>{"use strict";let{tokenChars:s}=r(58571);function i(e,t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)}e.exports={format:function(e){return Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>[t].concat(Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,r,n=Object.create(null),o=Object.create(null),a=!1,h=!1,l=!1,c=-1,u=-1,f=-1,d=0;for(;d<e.length;d++)if(u=e.charCodeAt(d),void 0===t)if(-1===f&&1===s[u])-1===c&&(c=d);else if(0!==d&&(32===u||9===u))-1===f&&-1!==c&&(f=d);else if(59===u||44===u){if(-1===c)throw SyntaxError(`Unexpected character at index ${d}`);-1===f&&(f=d);let r=e.slice(c,f);44===u?(i(n,r,o),o=Object.create(null)):t=r,c=f=-1}else throw SyntaxError(`Unexpected character at index ${d}`);else if(void 0===r)if(-1===f&&1===s[u])-1===c&&(c=d);else if(32===u||9===u)-1===f&&-1!==c&&(f=d);else if(59===u||44===u){if(-1===c)throw SyntaxError(`Unexpected character at index ${d}`);-1===f&&(f=d),i(o,e.slice(c,f),!0),44===u&&(i(n,t,o),o=Object.create(null),t=void 0),c=f=-1}else if(61===u&&-1!==c&&-1===f)r=e.slice(c,d),c=f=-1;else throw SyntaxError(`Unexpected character at index ${d}`);else if(h){if(1!==s[u])throw SyntaxError(`Unexpected character at index ${d}`);-1===c?c=d:a||(a=!0),h=!1}else if(l)if(1===s[u])-1===c&&(c=d);else if(34===u&&-1!==c)l=!1,f=d;else if(92===u)h=!0;else throw SyntaxError(`Unexpected character at index ${d}`);else if(34===u&&61===e.charCodeAt(d-1))l=!0;else if(-1===f&&1===s[u])-1===c&&(c=d);else if(-1!==c&&(32===u||9===u))-1===f&&(f=d);else if(59===u||44===u){if(-1===c)throw SyntaxError(`Unexpected character at index ${d}`);-1===f&&(f=d);let s=e.slice(c,f);a&&(s=s.replace(/\\/g,""),a=!1),i(o,r,s),44===u&&(i(n,t,o),o=Object.create(null),t=void 0),r=void 0,c=f=-1}else throw SyntaxError(`Unexpected character at index ${d}`);if(-1===c||l||32===u||9===u)throw SyntaxError("Unexpected end of input");-1===f&&(f=d);let p=e.slice(c,f);return void 0===t?i(n,p,o):(void 0===r?i(o,p,!0):a?i(o,r,p.replace(/\\/g,"")):i(o,r,p),i(n,t,o)),n}}},52346:e=>{"use strict";let t=Symbol("kDone"),r=Symbol("kRun");class s{constructor(e){this[t]=()=>{this.pending--,this[r]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[r]()}[r](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=s},56510:(e,t,r)=>{"use strict";let{kForOnEventAttribute:s,kListener:i}=r(32435),n=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),h=Symbol("kMessage"),l=Symbol("kReason"),c=Symbol("kTarget"),u=Symbol("kType"),f=Symbol("kWasClean");class d{constructor(e){this[c]=null,this[u]=e}get target(){return this[c]}get type(){return this[u]}}Object.defineProperty(d.prototype,"target",{enumerable:!0}),Object.defineProperty(d.prototype,"type",{enumerable:!0});class p extends d{constructor(e,t={}){super(e),this[n]=void 0===t.code?0:t.code,this[l]=void 0===t.reason?"":t.reason,this[f]=void 0!==t.wasClean&&t.wasClean}get code(){return this[n]}get reason(){return this[l]}get wasClean(){return this[f]}}Object.defineProperty(p.prototype,"code",{enumerable:!0}),Object.defineProperty(p.prototype,"reason",{enumerable:!0}),Object.defineProperty(p.prototype,"wasClean",{enumerable:!0});class y extends d{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[h]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[h]}}Object.defineProperty(y.prototype,"error",{enumerable:!0}),Object.defineProperty(y.prototype,"message",{enumerable:!0});class m extends d{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function g(e,t,r){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,r):e.call(t,r)}Object.defineProperty(m.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:p,ErrorEvent:y,Event:d,EventTarget:{addEventListener(e,t,r={}){let n;for(let n of this.listeners(e))if(!r[s]&&n[i]===t&&!n[s])return;if("message"===e)n=function(e,r){let s=new m("message",{data:r?e:e.toString()});s[c]=this,g(t,this,s)};else if("close"===e)n=function(e,r){let s=new p("close",{code:e,reason:r.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});s[c]=this,g(t,this,s)};else if("error"===e)n=function(e){let r=new y("error",{error:e,message:e.message});r[c]=this,g(t,this,r)};else{if("open"!==e)return;n=function(){let e=new d("open");e[c]=this,g(t,this,e)}}n[s]=!!r[s],n[i]=t,r.once?this.once(e,n):this.on(e,n)},removeEventListener(e,t){for(let r of this.listeners(e))if(r[i]===t&&!r[s]){this.removeListener(e,r);break}}},MessageEvent:m}},58571:(e,t,r)=>{"use strict";let{isUtf8:s}=r(79428),{hasBlob:i}=r(32435);function n(e){let t=e.length,r=0;for(;r<t;)if((128&e[r])==0)r++;else if((224&e[r])==192){if(r+1===t||(192&e[r+1])!=128||(254&e[r])==192)return!1;r+=2}else if((240&e[r])==224){if(r+2>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||224===e[r]&&(224&e[r+1])==128||237===e[r]&&(224&e[r+1])==160)return!1;r+=3}else{if((248&e[r])!=240||r+3>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||(192&e[r+3])!=128||240===e[r]&&(240&e[r+1])==128||244===e[r]&&e[r+1]>143||e[r]>244)return!1;r+=4}return!0}if(e.exports={isBlob:function(e){return i&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&("Blob"===e[Symbol.toStringTag]||"File"===e[Symbol.toStringTag])},isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:n,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},s)e.exports.isValidUTF8=function(e){return e.length<24?n(e):s(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=r(47990);e.exports.isValidUTF8=function(e){return e.length<32?n(e):t(e)}}catch(e){}},64082:(e,t,r)=>{var s=r(29021),i=r(33873),n=r(21820),o=require,a=process.config&&process.config.variables||{},h=!!process.env.PREBUILDS_ONLY,l=process.versions.modules,c=process.versions&&process.versions.electron||process.env.ELECTRON_RUN_AS_NODE||"undefined"!=typeof window&&window.process&&"renderer"===window.process.type?"electron":process.versions&&process.versions.nw?"node-webkit":"node",u=process.env.npm_config_arch||n.arch(),f=process.env.npm_config_platform||n.platform(),d=process.env.LIBC||("linux"===f&&s.existsSync("/etc/alpine-release")?"musl":"glibc"),p=process.env.ARM_VERSION||("arm64"===u?"8":a.arm_version)||"",y=(process.versions.uv||"").split(".")[0];function m(e){return o(m.resolve(e))}function g(e){try{return s.readdirSync(e)}catch(e){return[]}}function b(e,t){var r=g(e).filter(t);return r[0]&&i.join(e,r[0])}function _(e){return/\.node$/.test(e)}function v(e){var t=e.split("-");if(2===t.length){var r=t[0],s=t[1].split("+");if(r&&s.length&&s.every(Boolean))return{name:e,platform:r,architectures:s}}}function w(e,t){return function(r){return null!=r&&r.platform===e&&r.architectures.includes(t)}}function S(e,t){return e.architectures.length-t.architectures.length}function E(e){var t=e.split("."),r=t.pop(),s={file:e,specificity:0};if("node"===r){for(var i=0;i<t.length;i++){var n=t[i];if("node"===n||"electron"===n||"node-webkit"===n)s.runtime=n;else if("napi"===n)s.napi=!0;else if("abi"===n.slice(0,3))s.abi=n.slice(3);else if("uv"===n.slice(0,2))s.uv=n.slice(2);else if("armv"===n.slice(0,4))s.armv=n.slice(4);else{if("glibc"!==n&&"musl"!==n)continue;s.libc=n}s.specificity++}return s}}function x(e,t){return function(r){var s;return null!=r&&(!r.runtime||r.runtime===e||!!("node"===(s=r).runtime&&s.napi))&&(!r.abi||r.abi===t||!!r.napi)&&(!r.uv||r.uv===y)&&(!r.armv||r.armv===p)&&(!r.libc||r.libc===d)&&!0}}function C(e){return function(t,r){return t.runtime!==r.runtime?t.runtime===e?-1:1:t.abi!==r.abi?t.abi?-1:1:t.specificity!==r.specificity?t.specificity>r.specificity?-1:1:0}}e.exports=m,m.resolve=m.path=function(e){e=i.resolve(e||".");try{var t=o(i.join(e,"package.json")).name.toUpperCase().replace(/-/g,"_");process.env[t+"_PREBUILD"]&&(e=process.env[t+"_PREBUILD"])}catch(e){}if(!h){var r=b(i.join(e,"build/Release"),_);if(r)return r;var s=b(i.join(e,"build/Debug"),_);if(s)return s}var n=m(e);if(n)return n;var a=m(i.dirname(process.execPath));if(a)return a;throw Error("No native build was found for "+["platform="+f,"arch="+u,"runtime="+c,"abi="+l,"uv="+y,p?"armv="+p:"","libc="+d,"node="+process.versions.node,process.versions.electron?"electron="+process.versions.electron:"","webpack=true"].filter(Boolean).join(" ")+"\n    loaded from: "+e+"\n");function m(e){var t=g(i.join(e,"prebuilds")).map(v).filter(w(f,u)).sort(S)[0];if(t){var r=i.join(e,"prebuilds",t.name),s=g(r).map(E).filter(x(c,l)).sort(C(c))[0];if(s)return i.join(r,s.file)}}},m.parseTags=E,m.matchTags=x,m.compareTags=C,m.parseTuple=v,m.matchTuple=w,m.compareTuples=S},83376:(e,t,r)=>{"use strict";r.d(t,{sql:()=>td});var s,i,n,o,a,h,l=Object.create,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,f=Object.getOwnPropertyNames,d=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,y=(e,t,r)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,m=(e,t)=>c(e,"name",{value:t,configurable:!0}),g=(e,t)=>()=>(e&&(t=e(e=0)),t),b=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),_=(e,t)=>{for(var r in t)c(e,r,{get:t[r],enumerable:!0})},v=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of f(t))p.call(e,i)||i===r||c(e,i,{get:()=>t[i],enumerable:!(s=u(t,i))||s.enumerable});return e},w=(e,t,r)=>(r=null!=e?l(d(e)):{},v(!t&&e&&e.__esModule?r:c(r,"default",{value:e,enumerable:!0}),e)),S=e=>v(c({},"__esModule",{value:!0}),e),E=(e,t,r)=>y(e,"symbol"!=typeof t?t+"":t,r),x=b(e=>{A(),e.byteLength=h,e.toByteArray=c,e.fromByteArray=d;var t,r,s=[],i=[],n="u">typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(t=0,r=o.length;t<r;++t)s[t]=o[t],i[o.charCodeAt(t)]=t;function a(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var s=r===t?0:4-r%4;return[r,s]}function h(e){var t=a(e),r=t[0],s=t[1];return(r+s)*3/4-s}function l(e,t,r){return(t+r)*3/4-r}function c(e){var t,r,s=a(e),o=s[0],h=s[1],c=new n(l(e,o,h)),u=0,f=h>0?o-4:o;for(r=0;r<f;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],c[u++]=t>>16&255,c[u++]=t>>8&255,c[u++]=255&t;return 2===h&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,c[u++]=255&t),1===h&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,c[u++]=t>>8&255,c[u++]=255&t),c}function u(e){return s[e>>18&63]+s[e>>12&63]+s[e>>6&63]+s[63&e]}function f(e,t,r){for(var s=[],i=t;i<r;i+=3)s.push(u((e[i]<<16&0xff0000)+(e[i+1]<<8&65280)+(255&e[i+2])));return s.join("")}function d(e){for(var t,r=e.length,i=r%3,n=[],o=0,a=r-i;o<a;o+=16383)n.push(f(e,o,o+16383>a?a:o+16383));return 1===i?n.push(s[(t=e[r-1])>>2]+s[t<<4&63]+"=="):2===i&&n.push(s[(t=(e[r-2]<<8)+e[r-1])>>10]+s[t>>4&63]+s[t<<2&63]+"="),n.join("")}i[45]=62,i[95]=63,m(a,"getLens"),m(h,"byteLength"),m(l,"_byteLength"),m(c,"toByteArray"),m(u,"tripletToBase64"),m(f,"encodeChunk"),m(d,"fromByteArray")}),C=b(e=>{A(),e.read=function(e,t,r,s,i){var n,o,a=8*i-s-1,h=(1<<a)-1,l=h>>1,c=-7,u=r?i-1:0,f=r?-1:1,d=e[t+u];for(u+=f,n=d&(1<<-c)-1,d>>=-c,c+=a;c>0;n=256*n+e[t+u],u+=f,c-=8);for(o=n&(1<<-c)-1,n>>=-c,c+=s;c>0;o=256*o+e[t+u],u+=f,c-=8);if(0===n)n=1-l;else{if(n===h)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,s),n-=l}return(d?-1:1)*o*Math.pow(2,n-s)},e.write=function(e,t,r,s,i,n){var o,a,h,l=8*n-i-1,c=(1<<l)-1,u=c>>1,f=5960464477539062e-23*(23===i),d=s?0:n-1,p=s?1:-1,y=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(a=+!!isNaN(t),o=c):(o=Math.floor(Math.log(t)/Math.LN2),t*(h=Math.pow(2,-o))<1&&(o--,h*=2),o+u>=1?t+=f/h:t+=f*Math.pow(2,1-u),t*h>=2&&(o++,h/=2),o+u>=c?(a=0,o=c):o+u>=1?(a=(t*h-1)*Math.pow(2,i),o+=u):(a=t*Math.pow(2,u-1)*Math.pow(2,i),o=0));i>=8;e[r+d]=255&a,d+=p,a/=256,i-=8);for(o=o<<i|a,l+=i;l>0;e[r+d]=255&o,d+=p,o/=256,l-=8);e[r+d-p]|=128*y}}),k=b(e=>{A();var t=x(),r=C(),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function i(){try{let e=new Uint8Array(1),t={foo:m(function(){return 42},"foo")};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch{return!1}}function n(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');let t=new Uint8Array(e);return Object.setPrototypeOf(t,o.prototype),t}function o(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return a(e,t,r)}function a(e,t,r){if("string"==typeof e)return u(e,t);if(ArrayBuffer.isView(e))return d(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(eo(e,ArrayBuffer)||e&&eo(e.buffer,ArrayBuffer)||"u">typeof SharedArrayBuffer&&(eo(e,SharedArrayBuffer)||e&&eo(e.buffer,SharedArrayBuffer)))return p(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');let s=e.valueOf&&e.valueOf();if(null!=s&&s!==e)return o.from(s,t,r);let i=y(e);if(i)return i;if("u">typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return o.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function h(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function l(e,t,r){return h(e),e<=0?n(e):void 0!==t?"string"==typeof r?n(e).fill(t,r):n(e).fill(t):n(e)}function c(e){return h(e),n(e<0?0:0|g(e))}function u(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!o.isEncoding(t))throw TypeError("Unknown encoding: "+t);let r=0|_(e,t),s=n(r),i=s.write(e,t);return i!==r&&(s=s.slice(0,i)),s}function f(e){let t=e.length<0?0:0|g(e.length),r=n(t);for(let s=0;s<t;s+=1)r[s]=255&e[s];return r}function d(e){if(eo(e,Uint8Array)){let t=new Uint8Array(e);return p(t.buffer,t.byteOffset,t.byteLength)}return f(e)}function p(e,t,r){let s;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(s=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),o.prototype),s}function y(e){if(o.isBuffer(e)){let t=0|g(e.length),r=n(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||ea(e.length)?n(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}function g(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function b(e){return+e!=e&&(e=0),o.alloc(+e)}function _(e,t){if(o.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||eo(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);let r=e.length,s=arguments.length>2&&!0===arguments[2];if(!s&&0===r)return 0;let i=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return et(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return ei(e).length;default:if(i)return s?-1:et(e).length;t=(""+t).toLowerCase(),i=!0}}function v(e,t,r){let s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0)||(r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return U(this,t,r);case"utf8":case"utf-8":return O(this,t,r);case"ascii":return M(this,t,r);case"latin1":case"binary":return N(this,t,r);case"base64":return R(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return D(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function w(e,t,r){let s=e[t];e[t]=e[r],e[r]=s}function S(e,t,r,s,i){if(0===e.length)return -1;if("string"==typeof r?(s=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),ea(r*=1)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return -1;r=e.length-1}else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=o.from(t,s)),o.isBuffer(t))return 0===t.length?-1:E(e,t,r,s,i);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):E(e,[t],r,s,i);throw TypeError("val must be string, number or Buffer")}function E(e,t,r,s,i){let n,o=1,a=e.length,h=t.length;if(void 0!==s&&("ucs2"===(s=String(s).toLowerCase())||"ucs-2"===s||"utf16le"===s||"utf-16le"===s)){if(e.length<2||t.length<2)return -1;o=2,a/=2,h/=2,r/=2}function l(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(m(l,"read"),i){let s=-1;for(n=r;n<a;n++)if(l(e,n)===l(t,-1===s?0:n-s)){if(-1===s&&(s=n),n-s+1===h)return s*o}else -1!==s&&(n-=n-s),s=-1}else for(r+h>a&&(r=a-h),n=r;n>=0;n--){let r=!0;for(let s=0;s<h;s++)if(l(e,n+s)!==l(t,s)){r=!1;break}if(r)return n}return -1}function k(e,t,r,s){let i;r=Number(r)||0;let n=e.length-r;s?(s=Number(s))>n&&(s=n):s=n;let o=t.length;for(s>o/2&&(s=o/2),i=0;i<s;++i){let s=parseInt(t.substr(2*i,2),16);if(ea(s))break;e[r+i]=s}return i}function T(e,t,r,s){return en(et(t,e.length-r),e,r,s)}function L(e,t,r,s){return en(er(t),e,r,s)}function I(e,t,r,s){return en(ei(t),e,r,s)}function P(e,t,r,s){return en(es(t,e.length-r),e,r,s)}function R(e,r,s){return 0===r&&s===e.length?t.fromByteArray(e):t.fromByteArray(e.slice(r,s))}function O(e,t,r){r=Math.min(e.length,r);let s=[],i=t;for(;i<r;){let t=e[i],n=null,o=t>239?4:t>223?3:t>191?2:1;if(i+o<=r){let r,s,a,h;switch(o){case 1:t<128&&(n=t);break;case 2:(192&(r=e[i+1]))==128&&(h=(31&t)<<6|63&r)>127&&(n=h);break;case 3:r=e[i+1],s=e[i+2],(192&r)==128&&(192&s)==128&&(h=(15&t)<<12|(63&r)<<6|63&s)>2047&&(h<55296||h>57343)&&(n=h);break;case 4:r=e[i+1],s=e[i+2],a=e[i+3],(192&r)==128&&(192&s)==128&&(192&a)==128&&(h=(15&t)<<18|(63&r)<<12|(63&s)<<6|63&a)>65535&&h<1114112&&(n=h)}}null===n?(n=65533,o=1):n>65535&&(n-=65536,s.push(n>>>10&1023|55296),n=56320|1023&n),s.push(n),i+=o}return B(s)}function B(e){let t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);let r="",s=0;for(;s<t;)r+=String.fromCharCode.apply(String,e.slice(s,s+=4096));return r}function M(e,t,r){let s="";r=Math.min(e.length,r);for(let i=t;i<r;++i)s+=String.fromCharCode(127&e[i]);return s}function N(e,t,r){let s="";r=Math.min(e.length,r);for(let i=t;i<r;++i)s+=String.fromCharCode(e[i]);return s}function U(e,t,r){let s=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>s)&&(r=s);let i="";for(let s=t;s<r;++s)i+=eh[e[s]];return i}function D(e,t,r){let s=e.slice(t,r),i="";for(let e=0;e<s.length-1;e+=2)i+=String.fromCharCode(s[e]+256*s[e+1]);return i}function F(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function Q(e,t,r,s,i,n){if(!o.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<n)throw RangeError('"value" argument is out of bounds');if(r+s>e.length)throw RangeError("Index out of range")}function j(e,t,r,s,i){Y(t,s,i,e,r,7);let n=Number(t&BigInt(0xffffffff));e[r++]=n,n>>=8,e[r++]=n,n>>=8,e[r++]=n,n>>=8,e[r++]=n;let o=Number(t>>BigInt(32)&BigInt(0xffffffff));return e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,r}function q(e,t,r,s,i){Y(t,s,i,e,r,7);let n=Number(t&BigInt(0xffffffff));e[r+7]=n,n>>=8,e[r+6]=n,n>>=8,e[r+5]=n,n>>=8,e[r+4]=n;let o=Number(t>>BigInt(32)&BigInt(0xffffffff));return e[r+3]=o,o>>=8,e[r+2]=o,o>>=8,e[r+1]=o,o>>=8,e[r]=o,r+8}function W(e,t,r,s,i,n){if(r+s>e.length||r<0)throw RangeError("Index out of range")}function $(e,t,s,i,n){return t*=1,s>>>=0,n||W(e,t,s,4,34028234663852886e22,-34028234663852886e22),r.write(e,t,s,i,23,4),s+4}function G(e,t,s,i,n){return t*=1,s>>>=0,n||W(e,t,s,8,17976931348623157e292,-17976931348623157e292),r.write(e,t,s,i,52,8),s+8}e.Buffer=o,e.SlowBuffer=b,e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,o.TYPED_ARRAY_SUPPORT=i(),!o.TYPED_ARRAY_SUPPORT&&"u">typeof console&&"function"==typeof console.error&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),m(i,"typedArraySupport"),Object.defineProperty(o.prototype,"parent",{enumerable:!0,get:m(function(){if(o.isBuffer(this))return this.buffer},"get")}),Object.defineProperty(o.prototype,"offset",{enumerable:!0,get:m(function(){if(o.isBuffer(this))return this.byteOffset},"get")}),m(n,"createBuffer"),m(o,"Buffer"),o.poolSize=8192,m(a,"from"),o.from=function(e,t,r){return a(e,t,r)},Object.setPrototypeOf(o.prototype,Uint8Array.prototype),Object.setPrototypeOf(o,Uint8Array),m(h,"assertSize"),m(l,"alloc"),o.alloc=function(e,t,r){return l(e,t,r)},m(c,"allocUnsafe"),o.allocUnsafe=function(e){return c(e)},o.allocUnsafeSlow=function(e){return c(e)},m(u,"fromString"),m(f,"fromArrayLike"),m(d,"fromArrayView"),m(p,"fromArrayBuffer"),m(y,"fromObject"),m(g,"checked"),m(b,"SlowBuffer"),o.isBuffer=m(function(e){return null!=e&&!0===e._isBuffer&&e!==o.prototype},"isBuffer"),o.compare=m(function(e,t){if(eo(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),eo(t,Uint8Array)&&(t=o.from(t,t.offset,t.byteLength)),!o.isBuffer(e)||!o.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let r=e.length,s=t.length;for(let i=0,n=Math.min(r,s);i<n;++i)if(e[i]!==t[i]){r=e[i],s=t[i];break}return r<s?-1:+(s<r)},"compare"),o.isEncoding=m(function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},"isEncoding"),o.concat=m(function(e,t){let r;if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return o.alloc(0);if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;let s=o.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){let t=e[r];if(eo(t,Uint8Array))i+t.length>s.length?(o.isBuffer(t)||(t=o.from(t)),t.copy(s,i)):Uint8Array.prototype.set.call(s,t,i);else if(o.isBuffer(t))t.copy(s,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=t.length}return s},"concat"),m(_,"byteLength"),o.byteLength=_,m(v,"slowToString"),o.prototype._isBuffer=!0,m(w,"swap"),o.prototype.swap16=m(function(){let e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)w(this,t,t+1);return this},"swap16"),o.prototype.swap32=m(function(){let e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)w(this,t,t+3),w(this,t+1,t+2);return this},"swap32"),o.prototype.swap64=m(function(){let e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)w(this,t,t+7),w(this,t+1,t+6),w(this,t+2,t+5),w(this,t+3,t+4);return this},"swap64"),o.prototype.toString=m(function(){let e=this.length;return 0===e?"":0==arguments.length?O(this,0,e):v.apply(this,arguments)},"toString"),o.prototype.toLocaleString=o.prototype.toString,o.prototype.equals=m(function(e){if(!o.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===o.compare(this,e)},"equals"),o.prototype.inspect=m(function(){let t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},"inspect"),s&&(o.prototype[s]=o.prototype.inspect),o.prototype.compare=m(function(e,t,r,s,i){if(eo(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),!o.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===s&&(s=0),void 0===i&&(i=this.length),t<0||r>e.length||s<0||i>this.length)throw RangeError("out of range index");if(s>=i&&t>=r)return 0;if(s>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,s>>>=0,i>>>=0,this===e)return 0;let n=i-s,a=r-t,h=Math.min(n,a),l=this.slice(s,i),c=e.slice(t,r);for(let e=0;e<h;++e)if(l[e]!==c[e]){n=l[e],a=c[e];break}return n<a?-1:+(a<n)},"compare"),m(S,"bidirectionalIndexOf"),m(E,"arrayIndexOf"),o.prototype.includes=m(function(e,t,r){return -1!==this.indexOf(e,t,r)},"includes"),o.prototype.indexOf=m(function(e,t,r){return S(this,e,t,r,!0)},"indexOf"),o.prototype.lastIndexOf=m(function(e,t,r){return S(this,e,t,r,!1)},"lastIndexOf"),m(k,"hexWrite"),m(T,"utf8Write"),m(L,"asciiWrite"),m(I,"base64Write"),m(P,"ucs2Write"),o.prototype.write=m(function(e,t,r,s){if(void 0===t)s="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)s=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===s&&(s="utf8")):(s=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");s||(s="utf8");let n=!1;for(;;)switch(s){case"hex":return k(this,e,t,r);case"utf8":case"utf-8":return T(this,e,t,r);case"ascii":case"latin1":case"binary":return L(this,e,t,r);case"base64":return I(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return P(this,e,t,r);default:if(n)throw TypeError("Unknown encoding: "+s);s=(""+s).toLowerCase(),n=!0}},"write"),o.prototype.toJSON=m(function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},"toJSON"),m(R,"base64Slice"),m(O,"utf8Slice"),m(B,"decodeCodePointsArray"),m(M,"asciiSlice"),m(N,"latin1Slice"),m(U,"hexSlice"),m(D,"utf16leSlice"),o.prototype.slice=m(function(e,t){let r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);let s=this.subarray(e,t);return Object.setPrototypeOf(s,o.prototype),s},"slice"),m(F,"checkOffset"),o.prototype.readUintLE=o.prototype.readUIntLE=m(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let s=this[e],i=1,n=0;for(;++n<t&&(i*=256);)s+=this[e+n]*i;return s},"readUIntLE"),o.prototype.readUintBE=o.prototype.readUIntBE=m(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let s=this[e+--t],i=1;for(;t>0&&(i*=256);)s+=this[e+--t]*i;return s},"readUIntBE"),o.prototype.readUint8=o.prototype.readUInt8=m(function(e,t){return e>>>=0,t||F(e,1,this.length),this[e]},"readUInt8"),o.prototype.readUint16LE=o.prototype.readUInt16LE=m(function(e,t){return e>>>=0,t||F(e,2,this.length),this[e]|this[e+1]<<8},"readUInt16LE"),o.prototype.readUint16BE=o.prototype.readUInt16BE=m(function(e,t){return e>>>=0,t||F(e,2,this.length),this[e]<<8|this[e+1]},"readUInt16BE"),o.prototype.readUint32LE=o.prototype.readUInt32LE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},"readUInt32LE"),o.prototype.readUint32BE=o.prototype.readUInt32BE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},"readUInt32BE"),o.prototype.readBigUInt64LE=el(m(function(e){Z(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&X(e,this.length-8);let s=t+256*this[++e]+65536*this[++e]+0x1000000*this[++e],i=this[++e]+256*this[++e]+65536*this[++e]+0x1000000*r;return BigInt(s)+(BigInt(i)<<BigInt(32))},"readBigUInt64LE")),o.prototype.readBigUInt64BE=el(m(function(e){Z(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&X(e,this.length-8);let s=0x1000000*t+65536*this[++e]+256*this[++e]+this[++e],i=0x1000000*this[++e]+65536*this[++e]+256*this[++e]+r;return(BigInt(s)<<BigInt(32))+BigInt(i)},"readBigUInt64BE")),o.prototype.readIntLE=m(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let s=this[e],i=1,n=0;for(;++n<t&&(i*=256);)s+=this[e+n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*t)),s},"readIntLE"),o.prototype.readIntBE=m(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let s=t,i=1,n=this[e+--s];for(;s>0&&(i*=256);)n+=this[e+--s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},"readIntBE"),o.prototype.readInt8=m(function(e,t){return e>>>=0,t||F(e,1,this.length),128&this[e]?-((255-this[e]+1)*1):this[e]},"readInt8"),o.prototype.readInt16LE=m(function(e,t){e>>>=0,t||F(e,2,this.length);let r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},"readInt16LE"),o.prototype.readInt16BE=m(function(e,t){e>>>=0,t||F(e,2,this.length);let r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},"readInt16BE"),o.prototype.readInt32LE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},"readInt32LE"),o.prototype.readInt32BE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},"readInt32BE"),o.prototype.readBigInt64LE=el(m(function(e){Z(e>>>=0,"offset");let t=this[e],r=this[e+7];return(void 0===t||void 0===r)&&X(e,this.length-8),(BigInt(this[e+4]+256*this[e+5]+65536*this[e+6]+(r<<24))<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+0x1000000*this[++e])},"readBigInt64LE")),o.prototype.readBigInt64BE=el(m(function(e){Z(e>>>=0,"offset");let t=this[e],r=this[e+7];return(void 0===t||void 0===r)&&X(e,this.length-8),(BigInt((t<<24)+65536*this[++e]+256*this[++e]+this[++e])<<BigInt(32))+BigInt(0x1000000*this[++e]+65536*this[++e]+256*this[++e]+r)},"readBigInt64BE")),o.prototype.readFloatLE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),r.read(this,e,!0,23,4)},"readFloatLE"),o.prototype.readFloatBE=m(function(e,t){return e>>>=0,t||F(e,4,this.length),r.read(this,e,!1,23,4)},"readFloatBE"),o.prototype.readDoubleLE=m(function(e,t){return e>>>=0,t||F(e,8,this.length),r.read(this,e,!0,52,8)},"readDoubleLE"),o.prototype.readDoubleBE=m(function(e,t){return e>>>=0,t||F(e,8,this.length),r.read(this,e,!1,52,8)},"readDoubleBE"),m(Q,"checkInt"),o.prototype.writeUintLE=o.prototype.writeUIntLE=m(function(e,t,r,s){if(e*=1,t>>>=0,r>>>=0,!s){let s=Math.pow(2,8*r)-1;Q(this,e,t,r,s,0)}let i=1,n=0;for(this[t]=255&e;++n<r&&(i*=256);)this[t+n]=e/i&255;return t+r},"writeUIntLE"),o.prototype.writeUintBE=o.prototype.writeUIntBE=m(function(e,t,r,s){if(e*=1,t>>>=0,r>>>=0,!s){let s=Math.pow(2,8*r)-1;Q(this,e,t,r,s,0)}let i=r-1,n=1;for(this[t+i]=255&e;--i>=0&&(n*=256);)this[t+i]=e/n&255;return t+r},"writeUIntBE"),o.prototype.writeUint8=o.prototype.writeUInt8=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,1,255,0),this[t]=255&e,t+1},"writeUInt8"),o.prototype.writeUint16LE=o.prototype.writeUInt16LE=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},"writeUInt16LE"),o.prototype.writeUint16BE=o.prototype.writeUInt16BE=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},"writeUInt16BE"),o.prototype.writeUint32LE=o.prototype.writeUInt32LE=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},"writeUInt32LE"),o.prototype.writeUint32BE=o.prototype.writeUInt32BE=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},"writeUInt32BE"),m(j,"wrtBigUInt64LE"),m(q,"wrtBigUInt64BE"),o.prototype.writeBigUInt64LE=el(m(function(e,t=0){return j(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))},"writeBigUInt64LE")),o.prototype.writeBigUInt64BE=el(m(function(e,t=0){return q(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))},"writeBigUInt64BE")),o.prototype.writeIntLE=m(function(e,t,r,s){if(e*=1,t>>>=0,!s){let s=Math.pow(2,8*r-1);Q(this,e,t,r,s-1,-s)}let i=0,n=1,o=0;for(this[t]=255&e;++i<r&&(n*=256);)e<0&&0===o&&0!==this[t+i-1]&&(o=1),this[t+i]=(e/n|0)-o&255;return t+r},"writeIntLE"),o.prototype.writeIntBE=m(function(e,t,r,s){if(e*=1,t>>>=0,!s){let s=Math.pow(2,8*r-1);Q(this,e,t,r,s-1,-s)}let i=r-1,n=1,o=0;for(this[t+i]=255&e;--i>=0&&(n*=256);)e<0&&0===o&&0!==this[t+i+1]&&(o=1),this[t+i]=(e/n|0)-o&255;return t+r},"writeIntBE"),o.prototype.writeInt8=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},"writeInt8"),o.prototype.writeInt16LE=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},"writeInt16LE"),o.prototype.writeInt16BE=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},"writeInt16BE"),o.prototype.writeInt32LE=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},"writeInt32LE"),o.prototype.writeInt32BE=m(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},"writeInt32BE"),o.prototype.writeBigInt64LE=el(m(function(e,t=0){return j(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},"writeBigInt64LE")),o.prototype.writeBigInt64BE=el(m(function(e,t=0){return q(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},"writeBigInt64BE")),m(W,"checkIEEE754"),m($,"writeFloat"),o.prototype.writeFloatLE=m(function(e,t,r){return $(this,e,t,!0,r)},"writeFloatLE"),o.prototype.writeFloatBE=m(function(e,t,r){return $(this,e,t,!1,r)},"writeFloatBE"),m(G,"writeDouble"),o.prototype.writeDoubleLE=m(function(e,t,r){return G(this,e,t,!0,r)},"writeDoubleLE"),o.prototype.writeDoubleBE=m(function(e,t,r){return G(this,e,t,!1,r)},"writeDoubleBE"),o.prototype.copy=m(function(e,t,r,s){if(!o.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),s||0===s||(s=this.length),t>=e.length&&(t=e.length),t||(t=0),s>0&&s<r&&(s=r),s===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(s<0)throw RangeError("sourceEnd out of bounds");s>this.length&&(s=this.length),e.length-t<s-r&&(s=e.length-t+r);let i=s-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,s):Uint8Array.prototype.set.call(e,this.subarray(r,s),t),i},"copy"),o.prototype.fill=m(function(e,t,r,s){let i;if("string"==typeof e){if("string"==typeof t?(s=t,t=0,r=this.length):"string"==typeof r&&(s=r,r=this.length),void 0!==s&&"string"!=typeof s)throw TypeError("encoding must be a string");if("string"==typeof s&&!o.isEncoding(s))throw TypeError("Unknown encoding: "+s);if(1===e.length){let t=e.charCodeAt(0);("utf8"===s&&t<128||"latin1"===s)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{let n=o.isBuffer(e)?e:o.from(e,s),a=n.length;if(0===a)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=n[i%a]}return this},"fill");var V={};function H(e,t,r){var s;V[e]=(m(s=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}},"NodeError"),s)}function z(e){let t="",r=e.length,s=+("-"===e[0]);for(;r>=s+4;r-=3)t=`_${e.slice(r-3,r)}${t}`;return`${e.slice(0,r)}${t}`}function K(e,t,r){Z(t,"offset"),(void 0===e[t]||void 0===e[t+r])&&X(t,e.length-(r+1))}function Y(e,t,r,s,i,n){if(e>r||e<t){let s="bigint"==typeof t?"n":"",i;throw i=n>3?0===t||t===BigInt(0)?`>= 0${s} and < 2${s} ** ${(n+1)*8}${s}`:`>= -(2${s} ** ${(n+1)*8-1}${s}) and \
< 2 ** ${(n+1)*8-1}${s}`:`>= ${t}${s} and <= ${r}${s}`,new V.ERR_OUT_OF_RANGE("value",i,e)}K(s,i,n)}function Z(e,t){if("number"!=typeof e)throw new V.ERR_INVALID_ARG_TYPE(t,"number",e)}function X(e,t,r){throw Math.floor(e)!==e?(Z(e,r),new V.ERR_OUT_OF_RANGE(r||"offset","an integer",e)):t<0?new V.ERR_BUFFER_OUT_OF_BOUNDS:new V.ERR_OUT_OF_RANGE(r||"offset",`>= ${+!!r} and <= ${t}`,e)}m(H,"E"),H("ERR_BUFFER_OUT_OF_BOUNDS",function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),H("ERR_INVALID_ARG_TYPE",function(e,t){return`The "${e}" argum\
ent must be of type number. Received type ${typeof t}`},TypeError),H("ERR_OUT_OF_RANGE",function(e,t,r){let s=`The value of "${e}" is out of range.`,i=r;return Number.isInteger(r)&&Math.abs(r)>0x100000000?i=z(String(r)):"bigint"==typeof r&&(i=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(i=z(i)),i+="n"),s+=` It\
 must be ${t}. Received ${i}`},RangeError),m(z,"addNumericalSeparator"),m(K,"checkBounds"),m(Y,"checkIntBI"),m(Z,"validateNumber"),m(X,"boundsError");var J=/[^+/0-9A-Za-z-_]/g;function ee(e){if((e=(e=e.split("=")[0]).trim().replace(J,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}function et(e,t){t=t||1/0;let r,s=e.length,i=null,n=[];for(let o=0;o<s;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===s){(t-=3)>-1&&n.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&n.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&n.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;n.push(r)}else if(r<2048){if((t-=2)<0)break;n.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;n.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;n.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return n}function er(e){let t=[];for(let r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function es(e,t){let r,s,i=[];for(let n=0;n<e.length&&!((t-=2)<0);++n)s=(r=e.charCodeAt(n))>>8,i.push(r%256),i.push(s);return i}function ei(e){return t.toByteArray(ee(e))}function en(e,t,r,s){let i;for(i=0;i<s&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function eo(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function ea(e){return e!=e}m(ee,"base64clean"),m(et,"utf8ToBytes"),m(er,"asciiToBytes"),m(es,"utf16leToBytes"),m(ei,"base64ToBytes"),m(en,"blitBuffer"),m(eo,"isInstance"),m(ea,"numberIsNaN");var eh=function(){let e="0123456789abcdef",t=Array(256);for(let r=0;r<16;++r){let s=16*r;for(let i=0;i<16;++i)t[s+i]=e[r]+e[i]}return t}();function el(e){return typeof BigInt>"u"?ec:e}function ec(){throw Error("BigInt not supported")}m(el,"defineBigIntMethod"),m(ec,"BufferBigIntNotDefined")}),A=g(()=>{i=globalThis,n=globalThis.setImmediate??(e=>setTimeout(e,0)),globalThis.clearImmediate??(e=>clearTimeout(e)),(o=globalThis.crypto??{}).subtle??(o.subtle={}),a="function"==typeof globalThis.Buffer&&"function"==typeof globalThis.Buffer.allocUnsafe?globalThis.Buffer:k().Buffer,(h=globalThis.process??{}).env??(h.env={});try{h.nextTick(()=>{})}catch{let e=Promise.resolve();h.nextTick=e.then.bind(e)}}),T=b((e,t)=>{A();var r,s="object"==typeof Reflect?Reflect:null,i=s&&"function"==typeof s.apply?s.apply:m(function(e,t,r){return Function.prototype.apply.call(e,t,r)},"ReflectApply");function n(e){console&&console.warn&&console.warn(e)}r=s&&"function"==typeof s.ownKeys?s.ownKeys:Object.getOwnPropertySymbols?m(function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))},"ReflectOwnKeys"):m(function(e){return Object.getOwnPropertyNames(e)},"ReflectOwnKeys"),m(n,"ProcessEmitWarning");var o=Number.isNaN||m(function(e){return e!=e},"NumberIsNaN");function a(){a.init.call(this)}m(a,"EventEmitter"),t.exports=a,t.exports.once=v,a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var h=10;function l(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function u(e,t,r,s){var i,o,a;if(l(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),a=o[t]),void 0===a)a=o[t]=r,++e._eventsCount;else if("function"==typeof a?a=o[t]=s?[r,a]:[a,r]:s?a.unshift(r):a.push(r),(i=c(e))>0&&a.length>i&&!a.warned){a.warned=!0;var h=Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");h.name="MaxListenersExceededWarning",h.emitter=e,h.type=t,h.count=a.length,n(h)}return e}function f(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(e,t,r){var s={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=f.bind(s);return i.listener=r,s.wrapFn=i,i}function p(e,t,r){var s=e._events;if(void 0===s)return[];var i=s[t];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?_(i):g(i,i.length)}function y(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function g(e,t){for(var r=Array(t),s=0;s<t;++s)r[s]=e[s];return r}function b(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function _(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}function v(e,t){return new Promise(function(r,s){function i(r){e.removeListener(t,n),s(r)}function n(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}m(i,"errorListener"),m(n,"resolver"),S(e,t,n,{once:!0}),"error"!==t&&w(e,i,{once:!0})})}function w(e,t,r){"function"==typeof e.on&&S(e,"error",t,r)}function S(e,t,r,s){if("function"==typeof e.on)s.once?e.once(t,r):e.on(t,r);else if("function"==typeof e.addEventListener)e.addEventListener(t,m(function i(n){s.once&&e.removeEventListener(t,i),r(n)},"wrapListener"));else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}m(l,"checkListener"),Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:m(function(){return h},"get"),set:m(function(e){if("number"!=typeof e||e<0||o(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");h=e},"set")}),a.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=m(function(e){if("number"!=typeof e||e<0||o(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},"setMaxListeners"),m(c,"_getMaxListeners"),a.prototype.getMaxListeners=m(function(){return c(this)},"getMaxListeners"),a.prototype.emit=m(function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var s="error"===e,n=this._events;if(void 0!==n)s=s&&void 0===n.error;else if(!s)return!1;if(s){if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var o,a=Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var h=n[e];if(void 0===h)return!1;if("function"==typeof h)i(h,this,t);else for(var l=h.length,c=g(h,l),r=0;r<l;++r)i(c[r],this,t);return!0},"emit"),m(u,"_addListener"),a.prototype.addListener=m(function(e,t){return u(this,e,t,!1)},"addListener"),a.prototype.on=a.prototype.addListener,a.prototype.prependListener=m(function(e,t){return u(this,e,t,!0)},"prependListener"),m(f,"onceWrapper"),m(d,"_onceWrap"),a.prototype.once=m(function(e,t){return l(t),this.on(e,d(this,e,t)),this},"once"),a.prototype.prependOnceListener=m(function(e,t){return l(t),this.prependListener(e,d(this,e,t)),this},"prependOnceListener"),a.prototype.removeListener=m(function(e,t){var r,s,i,n,o;if(l(t),void 0===(s=this._events)||void 0===(r=s[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete s[e],s.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,n=r.length-1;n>=0;n--)if(r[n]===t||r[n].listener===t){o=r[n].listener,i=n;break}if(i<0)return this;0===i?r.shift():b(r,i),1===r.length&&(s[e]=r[0]),void 0!==s.removeListener&&this.emit("removeListener",e,o||t)}return this},"removeListener"),a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=m(function(e){var t,r,s;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0==arguments.length){var i,n=Object.keys(r);for(s=0;s<n.length;++s)"removeListener"!==(i=n[s])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(s=t.length-1;s>=0;s--)this.removeListener(e,t[s]);return this},"removeAllListeners"),m(p,"_listeners"),a.prototype.listeners=m(function(e){return p(this,e,!0)},"listeners"),a.prototype.rawListeners=m(function(e){return p(this,e,!1)},"rawListeners"),a.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):y.call(e,t)},a.prototype.listenerCount=y,m(y,"listenerCount"),a.prototype.eventNames=m(function(){return this._eventsCount>0?r(this._events):[]},"eventNames"),m(g,"arrayClone"),m(b,"spliceOne"),m(_,"unwrapListeners"),m(v,"once"),m(w,"addErrorHandlerIfEventEmitter"),m(S,"eventTargetAgnosticAddListener")}),L={};_(L,{default:()=>I});var I,P=g(()=>{A(),I={}});function R(e){let t=0x6a09e667,r=0xbb67ae85,s=0x3c6ef372,i=0xa54ff53a,n=0x510e527f,o=0x9b05688c,a=0x1f83d9ab,h=0x5be0cd19,l=0,c=0,u=[0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2],f=m((e,t)=>e>>>t|e<<32-t,"rrot"),d=new Uint32Array(64),p=new Uint8Array(64),y=m(()=>{for(let e=0,t=0;e<16;e++,t+=4)d[e]=p[t]<<24|p[t+1]<<16|p[t+2]<<8|p[t+3];for(let e=16;e<64;e++){let t=f(d[e-15],7)^f(d[e-15],18)^d[e-15]>>>3,r=f(d[e-2],17)^f(d[e-2],19)^d[e-2]>>>10;d[e]=d[e-16]+t+d[e-7]+r|0}let e=t,l=r,y=s,m=i,g=n,b=o,_=a,v=h;for(let t=0;t<64;t++){let r=v+(f(g,6)^f(g,11)^f(g,25))+(g&b^~g&_)+u[t]+d[t]|0,s=(f(e,2)^f(e,13)^f(e,22))+(e&l^e&y^l&y)|0;v=_,_=b,b=g,g=m+r|0,m=y,y=l,l=e,e=r+s|0}t=t+e|0,r=r+l|0,s=s+y|0,i=i+m|0,n=n+g|0,o=o+b|0,a=a+_|0,h=h+v|0,c=0},"process"),g=m(e=>{"string"==typeof e&&(e=new TextEncoder().encode(e));for(let t=0;t<e.length;t++)p[c++]=e[t],64===c&&y();l+=e.length},"add"),b=m(()=>{if(p[c++]=128,64==c&&y(),c+8>64){for(;c<64;)p[c++]=0;y()}for(;c<58;)p[c++]=0;let e=8*l;p[c++]=e/0x10000000000&255,p[c++]=e/0x100000000&255,p[c++]=e>>>24,p[c++]=e>>>16&255,p[c++]=e>>>8&255,p[c++]=255&e,y();let u=new Uint8Array(32);return u[0]=t>>>24,u[1]=t>>>16&255,u[2]=t>>>8&255,u[3]=255&t,u[4]=r>>>24,u[5]=r>>>16&255,u[6]=r>>>8&255,u[7]=255&r,u[8]=s>>>24,u[9]=s>>>16&255,u[10]=s>>>8&255,u[11]=255&s,u[12]=i>>>24,u[13]=i>>>16&255,u[14]=i>>>8&255,u[15]=255&i,u[16]=n>>>24,u[17]=n>>>16&255,u[18]=n>>>8&255,u[19]=255&n,u[20]=o>>>24,u[21]=o>>>16&255,u[22]=o>>>8&255,u[23]=255&o,u[24]=a>>>24,u[25]=a>>>16&255,u[26]=a>>>8&255,u[27]=255&a,u[28]=h>>>24,u[29]=h>>>16&255,u[30]=h>>>8&255,u[31]=255&h,u},"digest");return void 0===e?{add:g,digest:b}:(g(e),b())}var O,B,M=g(()=>{A(),m(R,"sha256")}),N=g(()=>{A(),m(O=class e{constructor(){E(this,"_dataLength",0),E(this,"_bufferLength",0),E(this,"_state",new Int32Array(4)),E(this,"_buffer",new ArrayBuffer(68)),E(this,"_buffer8"),E(this,"_buffer32"),this._buffer8=new Uint8Array(this._buffer,0,68),this._buffer32=new Uint32Array(this._buffer,0,17),this.start()}static hashByteArray(e,t=!1){return this.onePassHasher.start().appendByteArray(e).end(t)}static hashStr(e,t=!1){return this.onePassHasher.start().appendStr(e).end(t)}static hashAsciiStr(e,t=!1){return this.onePassHasher.start().appendAsciiStr(e).end(t)}static _hex(t){let r=e.hexChars,s=e.hexOut,i,n,o,a;for(a=0;a<4;a+=1)for(n=8*a,i=t[a],o=0;o<8;o+=2)s[n+1+o]=r.charAt(15&i),i>>>=4,s[n+0+o]=r.charAt(15&i),i>>>=4;return s.join("")}static _md5cycle(e,t){let r=e[0],s=e[1],i=e[2],n=e[3];r+=(s&i|~s&n)+t[0]-0x28955b88|0,n+=((r=(r<<7|r>>>25)+s|0)&s|~r&i)+t[1]-0x173848aa|0,i+=((n=(n<<12|n>>>20)+r|0)&r|~n&s)+t[2]+0x242070db|0,s+=((i=(i<<17|i>>>15)+n|0)&n|~i&r)+t[3]-0x3e423112|0,r+=((s=(s<<22|s>>>10)+i|0)&i|~s&n)+t[4]-0xa83f051|0,n+=((r=(r<<7|r>>>25)+s|0)&s|~r&i)+t[5]+0x4787c62a|0,i+=((n=(n<<12|n>>>20)+r|0)&r|~n&s)+t[6]-0x57cfb9ed|0,s+=((i=(i<<17|i>>>15)+n|0)&n|~i&r)+t[7]-0x2b96aff|0,r+=((s=(s<<22|s>>>10)+i|0)&i|~s&n)+t[8]+0x698098d8|0,n+=((r=(r<<7|r>>>25)+s|0)&s|~r&i)+t[9]-0x74bb0851|0,i+=((n=(n<<12|n>>>20)+r|0)&r|~n&s)+t[10]-42063|0,s+=((i=(i<<17|i>>>15)+n|0)&n|~i&r)+t[11]-0x76a32842|0,r+=((s=(s<<22|s>>>10)+i|0)&i|~s&n)+t[12]+0x6b901122|0,n+=((r=(r<<7|r>>>25)+s|0)&s|~r&i)+t[13]-0x2678e6d|0,i+=((n=(n<<12|n>>>20)+r|0)&r|~n&s)+t[14]-0x5986bc72|0,s+=((i=(i<<17|i>>>15)+n|0)&n|~i&r)+t[15]+0x49b40821|0,r+=((s=(s<<22|s>>>10)+i|0)&n|i&~n)+t[1]-0x9e1da9e|0,n+=((r=(r<<5|r>>>27)+s|0)&i|s&~i)+t[6]-0x3fbf4cc0|0,i+=((n=(n<<9|n>>>23)+r|0)&s|r&~s)+t[11]+0x265e5a51|0,s+=((i=(i<<14|i>>>18)+n|0)&r|n&~r)+t[0]-0x16493856|0,r+=((s=(s<<20|s>>>12)+i|0)&n|i&~n)+t[5]-0x29d0efa3|0,n+=((r=(r<<5|r>>>27)+s|0)&i|s&~i)+t[10]+0x2441453|0,i+=((n=(n<<9|n>>>23)+r|0)&s|r&~s)+t[15]-0x275e197f|0,s+=((i=(i<<14|i>>>18)+n|0)&r|n&~r)+t[4]-0x182c0438|0,r+=((s=(s<<20|s>>>12)+i|0)&n|i&~n)+t[9]+0x21e1cde6|0,n+=((r=(r<<5|r>>>27)+s|0)&i|s&~i)+t[14]-0x3cc8f82a|0,i+=((n=(n<<9|n>>>23)+r|0)&s|r&~s)+t[3]-0xb2af279|0,s+=((i=(i<<14|i>>>18)+n|0)&r|n&~r)+t[8]+0x455a14ed|0,r+=((s=(s<<20|s>>>12)+i|0)&n|i&~n)+t[13]-0x561c16fb|0,n+=((r=(r<<5|r>>>27)+s|0)&i|s&~i)+t[2]-0x3105c08|0,i+=((n=(n<<9|n>>>23)+r|0)&s|r&~s)+t[7]+0x676f02d9|0,s+=((i=(i<<14|i>>>18)+n|0)&r|n&~r)+t[12]-0x72d5b376|0,r+=((s=(s<<20|s>>>12)+i|0)^i^n)+t[5]-378558|0,n+=((r=(r<<4|r>>>28)+s|0)^s^i)+t[8]-0x788e097f|0,i+=((n=(n<<11|n>>>21)+r|0)^r^s)+t[11]+0x6d9d6122|0,s+=((i=(i<<16|i>>>16)+n|0)^n^r)+t[14]-0x21ac7f4|0,r+=((s=(s<<23|s>>>9)+i|0)^i^n)+t[1]-0x5b4115bc|0,n+=((r=(r<<4|r>>>28)+s|0)^s^i)+t[4]+0x4bdecfa9|0,i+=((n=(n<<11|n>>>21)+r|0)^r^s)+t[7]-0x944b4a0|0,s+=((i=(i<<16|i>>>16)+n|0)^n^r)+t[10]-0x41404390|0,r+=((s=(s<<23|s>>>9)+i|0)^i^n)+t[13]+0x289b7ec6|0,n+=((r=(r<<4|r>>>28)+s|0)^s^i)+t[0]-0x155ed806|0,i+=((n=(n<<11|n>>>21)+r|0)^r^s)+t[3]-0x2b10cf7b|0,s+=((i=(i<<16|i>>>16)+n|0)^n^r)+t[6]+0x4881d05|0,r+=((s=(s<<23|s>>>9)+i|0)^i^n)+t[9]-0x262b2fc7|0,n+=((r=(r<<4|r>>>28)+s|0)^s^i)+t[12]-0x1924661b|0,i+=((n=(n<<11|n>>>21)+r|0)^r^s)+t[15]+0x1fa27cf8|0,s+=((i=(i<<16|i>>>16)+n|0)^n^r)+t[2]-0x3b53a99b|0,s=(s<<23|s>>>9)+i|0,r+=(i^(s|~n))+t[0]-0xbd6ddbc|0,r=(r<<6|r>>>26)+s|0,n+=(s^(r|~i))+t[7]+0x432aff97|0,n=(n<<10|n>>>22)+r|0,i+=(r^(n|~s))+t[14]-0x546bdc59|0,i=(i<<15|i>>>17)+n|0,s+=(n^(i|~r))+t[5]-0x36c5fc7|0,s=(s<<21|s>>>11)+i|0,r+=(i^(s|~n))+t[12]+0x655b59c3|0,r=(r<<6|r>>>26)+s|0,n+=(s^(r|~i))+t[3]-0x70f3336e|0,n=(n<<10|n>>>22)+r|0,i+=(r^(n|~s))+t[10]-1051523|0,i=(i<<15|i>>>17)+n|0,s+=(n^(i|~r))+t[1]-0x7a7ba22f|0,s=(s<<21|s>>>11)+i|0,r+=(i^(s|~n))+t[8]+0x6fa87e4f|0,r=(r<<6|r>>>26)+s|0,n+=(s^(r|~i))+t[15]-0x1d31920|0,n=(n<<10|n>>>22)+r|0,i+=(r^(n|~s))+t[6]-0x5cfebcec|0,i=(i<<15|i>>>17)+n|0,s+=(n^(i|~r))+t[13]+0x4e0811a1|0,s=(s<<21|s>>>11)+i|0,r+=(i^(s|~n))+t[4]-0x8ac817e|0,r=(r<<6|r>>>26)+s|0,n+=(s^(r|~i))+t[11]-0x42c50dcb|0,n=(n<<10|n>>>22)+r|0,i+=(r^(n|~s))+t[2]+0x2ad7d2bb|0,i=(i<<15|i>>>17)+n|0,s+=(n^(i|~r))+t[9]-0x14792c6f|0,s=(s<<21|s>>>11)+i|0,e[0]=r+e[0]|0,e[1]=s+e[1]|0,e[2]=i+e[2]|0,e[3]=n+e[3]|0}start(){return this._dataLength=0,this._bufferLength=0,this._state.set(e.stateIdentity),this}appendStr(t){let r=this._buffer8,s=this._buffer32,i=this._bufferLength,n,o;for(o=0;o<t.length;o+=1){if((n=t.charCodeAt(o))<128)r[i++]=n;else if(n<2048)r[i++]=(n>>>6)+192,r[i++]=63&n|128;else if(n<55296||n>56319)r[i++]=(n>>>12)+224,r[i++]=n>>>6&63|128,r[i++]=63&n|128;else{if((n=(n-55296)*1024+(t.charCodeAt(++o)-56320)+65536)>1114111)throw Error("Unicode standard supports code points up to U+10FFFF");r[i++]=(n>>>18)+240,r[i++]=n>>>12&63|128,r[i++]=n>>>6&63|128,r[i++]=63&n|128}i>=64&&(this._dataLength+=64,e._md5cycle(this._state,s),i-=64,s[0]=s[16])}return this._bufferLength=i,this}appendAsciiStr(t){let r=this._buffer8,s=this._buffer32,i=this._bufferLength,n,o=0;for(;;){for(n=Math.min(t.length-o,64-i);n--;)r[i++]=t.charCodeAt(o++);if(i<64)break;this._dataLength+=64,e._md5cycle(this._state,s),i=0}return this._bufferLength=i,this}appendByteArray(t){let r=this._buffer8,s=this._buffer32,i=this._bufferLength,n,o=0;for(;;){for(n=Math.min(t.length-o,64-i);n--;)r[i++]=t[o++];if(i<64)break;this._dataLength+=64,e._md5cycle(this._state,s),i=0}return this._bufferLength=i,this}getState(){let e=this._state;return{buffer:String.fromCharCode.apply(null,Array.from(this._buffer8)),buflen:this._bufferLength,length:this._dataLength,state:[e[0],e[1],e[2],e[3]]}}setState(e){let t=e.buffer,r=e.state,s=this._state,i;for(this._dataLength=e.length,this._bufferLength=e.buflen,s[0]=r[0],s[1]=r[1],s[2]=r[2],s[3]=r[3],i=0;i<t.length;i+=1)this._buffer8[i]=t.charCodeAt(i)}end(t=!1){let r=this._bufferLength,s=this._buffer8,i=this._buffer32,n=(r>>2)+1;this._dataLength+=r;let o=8*this._dataLength;if(s[r]=128,s[r+1]=s[r+2]=s[r+3]=0,i.set(e.buffer32Identity.subarray(n),n),r>55&&(e._md5cycle(this._state,i),i.set(e.buffer32Identity)),o<=0xffffffff)i[14]=o;else{let e=o.toString(16).match(/(.*?)(.{0,8})$/);if(null===e)return;let t=parseInt(e[2],16),r=parseInt(e[1],16)||0;i[14]=t,i[15]=r}return e._md5cycle(this._state,i),t?this._state:e._hex(this._state)}},"Md5"),E(O,"stateIdentity",new Int32Array([0x67452301,-0x10325477,-0x67452302,0x10325476])),E(O,"buffer32Identity",new Int32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),E(O,"hexChars","0123456789abcdef"),E(O,"hexOut",[]),E(O,"onePassHasher",new O),B=O}),U={};function D(e){return o.getRandomValues(a.alloc(e))}function F(e){if("sha256"===e)return{update:m(function(e){return{digest:m(function(){return a.from(R(e))},"digest")}},"update")};if("md5"===e)return{update:m(function(e){return{digest:m(function(){return"string"==typeof e?B.hashStr(e):B.hashByteArray(e)},"digest")}},"update")};throw Error(`Hash type '${e}' not supported`)}function Q(e,t){if("sha256"!==e)throw Error(`Only sha256 is supported (requested: '${e}')`);return{update:m(function(e){return{digest:m(function(){"string"==typeof t&&(t=new TextEncoder().encode(t)),"string"==typeof e&&(e=new TextEncoder().encode(e));let r=t.length;if(r>64)t=R(t);else if(r<64){let e=new Uint8Array(64);e.set(t),t=e}let s=new Uint8Array(64),i=new Uint8Array(64);for(let e=0;e<64;e++)s[e]=54^t[e],i[e]=92^t[e];let n=new Uint8Array(e.length+64);n.set(s,0),n.set(e,64);let o=new Uint8Array(96);return o.set(i,0),o.set(R(n),64),a.from(R(o))},"digest")}},"update")}}_(U,{createHash:()=>F,createHmac:()=>Q,randomBytes:()=>D});var j=g(()=>{A(),M(),N(),m(D,"randomBytes"),m(F,"createHash"),m(Q,"createHmac")}),q=b(e=>{A(),e.parse=function(e,t){return new r(e,t).parse()};var t=class e{constructor(e,t){this.source=e,this.transform=t||s,this.position=0,this.entries=[],this.recorded=[],this.dimension=0}isEof(){return this.position>=this.source.length}nextCharacter(){var e=this.source[this.position++];return"\\"===e?{value:this.source[this.position++],escaped:!0}:{value:e,escaped:!1}}record(e){this.recorded.push(e)}newEntry(e){var t;(this.recorded.length>0||e)&&("NULL"!==(t=this.recorded.join(""))||e||(t=null),null!==t&&(t=this.transform(t)),this.entries.push(t),this.recorded=[])}consumeDimensions(){if("["===this.source[0])for(;!this.isEof()&&"="!==this.nextCharacter().value;);}parse(t){var r,s,i;for(this.consumeDimensions();!this.isEof();)if("{"!==(r=this.nextCharacter()).value||i){if("}"!==r.value||i)'"'!==r.value||r.escaped?","!==r.value||i?this.record(r.value):this.newEntry():(i&&this.newEntry(!0),i=!i);else if(this.dimension--,!this.dimension&&(this.newEntry(),t))return this.entries}else this.dimension++,this.dimension>1&&(s=new e(this.source.substr(this.position-1),this.transform),this.entries.push(s.parse(!0)),this.position+=s.position-2);if(0!==this.dimension)throw Error("array dimension not balanced");return this.entries}};m(t,"ArrayParser");var r=t;function s(e){return e}m(s,"identity")}),W=b((e,t)=>{A();var r=q();t.exports={create:m(function(e,t){return{parse:m(function(){return r.parse(e,t)},"parse")}},"create")}}),$=b((e,t)=>{A();var r=/(\d{1,})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})(\.\d{1,})?.*?( BC)?$/,s=/^(\d{1,})-(\d{2})-(\d{2})( BC)?$/,i=/([Z+-])(\d{2})?:?(\d{2})?:?(\d{2})?/,n=/^-?infinity$/;function o(e){var t=s.exec(e);if(t){var r=parseInt(t[1],10);t[4]&&(r=h(r));var i=new Date(r,parseInt(t[2],10)-1,t[3]);return l(r)&&i.setFullYear(r),i}}function a(e){if(e.endsWith("+00"))return 0;var t=i.exec(e.split(" ")[1]);if(t){var r=t[1];return"Z"===r?0:(3600*parseInt(t[2],10)+60*parseInt(t[3]||0,10)+parseInt(t[4]||0,10))*("-"===r?-1:1)*1e3}}function h(e){return-(e-1)}function l(e){return e>=0&&e<100}t.exports=m(function(e){if(n.test(e))return Number(e.replace("i","I"));var t=r.exec(e);if(!t)return o(e)||null;var s=!!t[8],i=parseInt(t[1],10);s&&(i=h(i));var c=parseInt(t[2],10)-1,u=t[3],f=parseInt(t[4],10),d=parseInt(t[5],10),p=parseInt(t[6],10),y=t[7];y=y?1e3*parseFloat(y):0;var m,g=a(e);return null!=g?(m=new Date(Date.UTC(i,c,u,f,d,p,y)),l(i)&&m.setUTCFullYear(i),0!==g&&m.setTime(m.getTime()-g)):(m=new Date(i,c,u,f,d,p,y),l(i)&&m.setFullYear(i)),m},"parseDate"),m(o,"getDate"),m(a,"timeZoneOffset"),m(h,"bcYearToNegativeYear"),m(l,"is0To99")}),G=b((e,t)=>{A(),t.exports=s;var r=Object.prototype.hasOwnProperty;function s(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)r.call(s,i)&&(e[i]=s[i])}return e}m(s,"extend")}),V=b((e,t)=>{A();var r=G();function s(e){if(!(this instanceof s))return new s(e);r(this,d(e))}t.exports=s,m(s,"PostgresInterval");var i=["seconds","minutes","hours","days","months","years"];s.prototype.toPostgres=function(){var e=i.filter(this.hasOwnProperty,this);return this.milliseconds&&0>e.indexOf("seconds")&&e.push("seconds"),0===e.length?"0":e.map(function(e){var t=this[e]||0;return"seconds"===e&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/\.?0+$/,"")),t+" "+e},this).join(" ")};var n={years:"Y",months:"M",days:"D",hours:"H",minutes:"M",seconds:"S"},o=["years","months","days"],a=["hours","minutes","seconds"];s.prototype.toISOString=s.prototype.toISO=function(){return"P"+o.map(e,this).join("")+"T"+a.map(e,this).join("");function e(e){var t=this[e]||0;return"seconds"===e&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/0+$/,"")),t+n[e]}};var h="([+-]?\\d+)",l=new RegExp([h+"\\s+years?",h+"\\s+mons?",h+"\\s+days?","([+-])?([\\d]*):(\\d\\d):(\\d\\d)\\.?(\\d{1,6})?"].map(function(e){return"("+e+")?"}).join("\\s*")),c={years:2,months:4,days:6,hours:9,minutes:10,seconds:11,milliseconds:12},u=["hours","minutes","seconds","milliseconds"];function f(e){return parseInt(e+"000000".slice(e.length),10)/1e3}function d(e){if(!e)return{};var t=l.exec(e),r="-"===t[8];return Object.keys(c).reduce(function(e,s){var i=t[c[s]];return i&&(i="milliseconds"===s?f(i):parseInt(i,10))&&(r&&~u.indexOf(s)&&(i*=-1),e[s]=i),e},{})}m(f,"parseMilliseconds"),m(d,"parse")}),H=b((e,t)=>{A(),t.exports=m(function(e){if(/^\\x/.test(e))return new a(e.substr(2),"hex");for(var t="",r=0;r<e.length;)if("\\"!==e[r])t+=e[r],++r;else if(/[0-7]{3}/.test(e.substr(r+1,3)))t+=String.fromCharCode(parseInt(e.substr(r+1,3),8)),r+=4;else{for(var s=1;r+s<e.length&&"\\"===e[r+s];)s++;for(var i=0;i<Math.floor(s/2);++i)t+="\\";r+=2*Math.floor(s/2)}return new a(t,"binary")},"parseBytea")}),z=b((e,t)=>{A();var r=q(),s=W(),i=$(),n=V(),o=H();function a(e){return m(function(t){return null===t?t:e(t)},"nullAllowed")}function h(e){return null===e?e:"TRUE"===e||"t"===e||"true"===e||"y"===e||"yes"===e||"on"===e||"1"===e}function l(e){return e?r.parse(e,h):null}function c(e){return parseInt(e,10)}function u(e){return e?r.parse(e,a(c)):null}function f(e){return e?r.parse(e,a(function(e){return w(e).trim()})):null}m(a,"allowNull"),m(h,"parseBool"),m(l,"parseBoolArray"),m(c,"parseBaseTenInt"),m(u,"parseIntegerArray"),m(f,"parseBigIntegerArray");var d=m(function(e){return e?s.create(e,function(e){return null!==e&&(e=E(e)),e}).parse():null},"parsePointArray"),p=m(function(e){return e?s.create(e,function(e){return null!==e&&(e=parseFloat(e)),e}).parse():null},"parseFloatArray"),y=m(function(e){return e?s.create(e).parse():null},"parseStringArray"),g=m(function(e){return e?s.create(e,function(e){return null!==e&&(e=i(e)),e}).parse():null},"parseDateArray"),b=m(function(e){return e?s.create(e,function(e){return null!==e&&(e=n(e)),e}).parse():null},"parseIntervalArray"),_=m(function(e){return e?r.parse(e,a(o)):null},"parseByteAArray"),v=m(function(e){return parseInt(e,10)},"parseInteger"),w=m(function(e){var t=String(e);return/^\d+$/.test(t)?t:e},"parseBigInteger"),S=m(function(e){return e?r.parse(e,a(JSON.parse)):null},"parseJsonArray"),E=m(function(e){return"("!==e[0]?null:{x:parseFloat((e=e.substring(1,e.length-1).split(","))[0]),y:parseFloat(e[1])}},"parsePoint"),x=m(function(e){if("<"!==e[0]&&"("!==e[1])return null;for(var t="(",r="",s=!1,i=2;i<e.length-1;i++){if(s||(t+=e[i]),")"===e[i]){s=!0;continue}s&&","!==e[i]&&(r+=e[i])}var n=E(t);return n.radius=parseFloat(r),n},"parseCircle");t.exports={init:m(function(e){e(20,w),e(21,v),e(23,v),e(26,v),e(700,parseFloat),e(701,parseFloat),e(16,h),e(1082,i),e(1114,i),e(1184,i),e(600,E),e(651,y),e(718,x),e(1e3,l),e(1001,_),e(1005,u),e(1007,u),e(1028,u),e(1016,f),e(1017,d),e(1021,p),e(1022,p),e(1231,p),e(1014,y),e(1015,y),e(1008,y),e(1009,y),e(1040,y),e(1041,y),e(1115,g),e(1182,g),e(1185,g),e(1186,n),e(1187,b),e(17,o),e(114,JSON.parse.bind(JSON)),e(3802,JSON.parse.bind(JSON)),e(199,S),e(3807,S),e(3907,y),e(2951,y),e(791,y),e(1183,y),e(1270,y)},"init")}}),K=b((e,t)=>{function r(e){var t=e.readInt32BE(0),r=e.readUInt32BE(4),s="";t<0&&(t=~t+(0===r),r=~r+1>>>0,s="-");var i,n,o,a,h,l,c="";if(i=t%1e6,t=t/1e6>>>0,r=(n=0x100000000*i+r)/1e6>>>0,o=""+(n-1e6*r),0===r&&0===t)return s+o+c;for(a="",h=6-o.length,l=0;l<h;l++)a+="0";if(c=a+o+c,i=t%1e6,t=t/1e6>>>0,r=(n=0x100000000*i+r)/1e6>>>0,o=""+(n-1e6*r),0===r&&0===t)return s+o+c;for(a="",h=6-o.length,l=0;l<h;l++)a+="0";if(c=a+o+c,i=t%1e6,t=t/1e6>>>0,r=(n=0x100000000*i+r)/1e6>>>0,o=""+(n-1e6*r),0===r&&0===t)return s+o+c;for(a="",h=6-o.length,l=0;l<h;l++)a+="0";return c=a+o+c,s+(o=""+(n=0x100000000*(i=t%1e6)+r)%1e6)+c}A(),m(r,"readInt8"),t.exports=r}),Y=b((e,t)=>{A();var r=K(),s=m(function(e,t,r,s,i){r=r||0,s=s||!1,i=i||function(e,t,r){return e*Math.pow(2,r)+t};var n=r>>3,o=m(function(e){return s?255&~e:e},"inv"),a=255,h=8-r%8;t<h&&(a=255<<8-t&255,h=t),r&&(a>>=r%8);var l=0;r%8+t>=8&&(l=i(0,o(e[n])&a,h));for(var c=t+r>>3,u=n+1;u<c;u++)l=i(l,o(e[u]),8);var f=(t+r)%8;return f>0&&(l=i(l,o(e[c])>>8-f,f)),l},"parseBits"),i=m(function(e,t,r){var i=Math.pow(2,r-1)-1,n=s(e,1),o=s(e,r,1);if(0===o)return 0;var a=1,h=s(e,t,r+1,!1,m(function(e,t,r){0===e&&(e=1);for(var s=1;s<=r;s++)a/=2,(t&1<<r-s)>0&&(e+=a);return e},"parsePrecisionBits"));return o==Math.pow(2,r+1)-1?0===h?0===n?1/0:-1/0:NaN:(0===n?1:-1)*Math.pow(2,o-i)*h},"parseFloatFromBits"),n=m(function(e){return 1==s(e,1)?-1*(s(e,15,1,!0)+1):s(e,15,1)},"parseInt16"),o=m(function(e){return 1==s(e,1)?-1*(s(e,31,1,!0)+1):s(e,31,1)},"parseInt32"),a=m(function(e){return i(e,23,8)},"parseFloat32"),h=m(function(e){return i(e,52,11)},"parseFloat64"),l=m(function(e){var t=s(e,16,32);if(49152==t)return NaN;for(var r=Math.pow(1e4,s(e,16,16)),i=0,n=s(e,16),o=0;o<n;o++)i+=s(e,16,64+16*o)*r,r/=1e4;var a=Math.pow(10,s(e,16,48));return(0===t?1:-1)*Math.round(i*a)/a},"parseNumeric"),c=m(function(e,t){var r=s(t,1),i=s(t,63,1),n=new Date((0===r?1:-1)*i/1e3+9466848e5);return e||n.setTime(n.getTime()+6e4*n.getTimezoneOffset()),n.usec=i%1e3,n.getMicroSeconds=function(){return this.usec},n.setMicroSeconds=function(e){this.usec=e},n.getUTCMicroSeconds=function(){return this.usec},n},"parseDate"),u=m(function(e){for(var t=s(e,32),r=(s(e,32,32),s(e,32,64)),i=96,n=[],o=0;o<t;o++)n[o]=s(e,32,i),i+=64;var a=m(function(t){var r,n=s(e,32,i);return(i+=32,0xffffffff==n)?null:23==t||20==t?(r=s(e,8*n,i),i+=8*n,r):25==t?e.toString(this.encoding,i>>3,(i+=n<<3)>>3):void console.log("ERROR: ElementType not implemented: "+t)},"parseElement"),h=m(function(e,t){var r,s=[];if(e.length>1){var i=e.shift();for(r=0;r<i;r++)s[r]=h(e,t);e.unshift(i)}else for(r=0;r<e[0];r++)s[r]=a(t);return s},"parse");return h(n,r)},"parseArray"),f=m(function(e){return e.toString("utf8")},"parseText"),d=m(function(e){return null===e?null:s(e,8)>0},"parseBool");t.exports={init:m(function(e){e(20,r),e(21,n),e(23,o),e(26,o),e(1700,l),e(700,a),e(701,h),e(16,d),e(1114,c.bind(null,!1)),e(1184,c.bind(null,!0)),e(1e3,u),e(1007,u),e(1016,u),e(1008,u),e(1009,u),e(25,f)},"init")}}),Z=b((e,t)=>{A(),t.exports={BOOL:16,BYTEA:17,CHAR:18,INT8:20,INT2:21,INT4:23,REGPROC:24,TEXT:25,OID:26,TID:27,XID:28,CID:29,JSON:114,XML:142,PG_NODE_TREE:194,SMGR:210,PATH:602,POLYGON:604,CIDR:650,FLOAT4:700,FLOAT8:701,ABSTIME:702,RELTIME:703,TINTERVAL:704,CIRCLE:718,MACADDR8:774,MONEY:790,MACADDR:829,INET:869,ACLITEM:1033,BPCHAR:1042,VARCHAR:1043,DATE:1082,TIME:1083,TIMESTAMP:1114,TIMESTAMPTZ:1184,INTERVAL:1186,TIMETZ:1266,BIT:1560,VARBIT:1562,NUMERIC:1700,REFCURSOR:1790,REGPROCEDURE:2202,REGOPER:2203,REGOPERATOR:2204,REGCLASS:2205,REGTYPE:2206,UUID:2950,TXID_SNAPSHOT:2970,PG_LSN:3220,PG_NDISTINCT:3361,PG_DEPENDENCIES:3402,TSVECTOR:3614,TSQUERY:3615,GTSVECTOR:3642,REGCONFIG:3734,REGDICTIONARY:3769,JSONB:3802,REGNAMESPACE:4089,REGROLE:4096}}),X=b(e=>{A();var t=z(),r=Y(),s=W(),i=Z();e.getTypeParser=a,e.setTypeParser=h,e.arrayParser=s,e.builtins=i;var n={text:{},binary:{}};function o(e){return String(e)}function a(e,t){return n[t=t||"text"]&&n[t][e]||o}function h(e,t,r){"function"==typeof t&&(r=t,t="text"),n[t][e]=r}m(o,"noParse"),m(a,"getTypeParser"),m(h,"setTypeParser"),t.init(function(e,t){n.text[e]=t}),r.init(function(e,t){n.binary[e]=t})}),J=b((e,t)=>{A(),t.exports={host:"localhost",user:"win32"===h.platform?h.env.USERNAME:h.env.USER,database:void 0,password:null,connectionString:void 0,port:5432,rows:0,binary:!1,max:10,idleTimeoutMillis:3e4,client_encoding:"",ssl:!1,application_name:void 0,fallback_application_name:void 0,options:void 0,parseInputDatesAsUTC:!1,statement_timeout:!1,lock_timeout:!1,idle_in_transaction_session_timeout:!1,query_timeout:!1,connect_timeout:0,keepalives:1,keepalives_idle:0};var r=X(),s=r.getTypeParser(20,"text"),i=r.getTypeParser(1016,"text");t.exports.__defineSetter__("parseInt8",function(e){r.setTypeParser(20,"text",e?r.getTypeParser(23,"text"):s),r.setTypeParser(1016,"text",e?r.getTypeParser(1007,"text"):i)})}),ee=b((e,t)=>{A();var r=(j(),S(U)),s=J();function i(e){return'"'+e.replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"'}function n(e){for(var t="{",r=0;r<e.length;r++)r>0&&(t+=","),null===e[r]||typeof e[r]>"u"?t+="NULL":Array.isArray(e[r])?t+=n(e[r]):e[r]instanceof a?t+="\\\\x"+e[r].toString("hex"):t+=i(o(e[r]));return t+"}"}m(i,"escapeElement"),m(n,"arrayString");var o=m(function(e,t){if(null==e)return null;if(e instanceof a)return e;if(ArrayBuffer.isView(e)){var r=a.from(e.buffer,e.byteOffset,e.byteLength);return r.length===e.byteLength?r:r.slice(e.byteOffset,e.byteOffset+e.byteLength)}return e instanceof Date?s.parseInputDatesAsUTC?u(e):c(e):Array.isArray(e)?n(e):"object"==typeof e?h(e,t):e.toString()},"prepareValue");function h(e,t){if(e&&"function"==typeof e.toPostgres){if(-1!==(t=t||[]).indexOf(e))throw Error('circular reference detected while preparing "'+e+'" for query');return t.push(e),o(e.toPostgres(o),t)}return JSON.stringify(e)}function l(e,t){for(e=""+e;e.length<t;)e="0"+e;return e}function c(e){var t=-e.getTimezoneOffset(),r=e.getFullYear(),s=r<1;s&&(r=Math.abs(r)+1);var i=l(r,4)+"-"+l(e.getMonth()+1,2)+"-"+l(e.getDate(),2)+"T"+l(e.getHours(),2)+":"+l(e.getMinutes(),2)+":"+l(e.getSeconds(),2)+"."+l(e.getMilliseconds(),3);return t<0?(i+="-",t*=-1):i+="+",i+=l(Math.floor(t/60),2)+":"+l(t%60,2),s&&(i+=" BC"),i}function u(e){var t=e.getUTCFullYear(),r=t<1;r&&(t=Math.abs(t)+1);var s=l(t,4)+"-"+l(e.getUTCMonth()+1,2)+"-"+l(e.getUTCDate(),2)+"T"+l(e.getUTCHours(),2)+":"+l(e.getUTCMinutes(),2)+":"+l(e.getUTCSeconds(),2)+"."+l(e.getUTCMilliseconds(),3);return s+="+00:00",r&&(s+=" BC"),s}function f(e,t,r){return e="string"==typeof e?{text:e}:e,t&&("function"==typeof t?e.callback=t:e.values=t),r&&(e.callback=r),e}m(h,"prepareObject"),m(l,"pad"),m(c,"dateToString"),m(u,"dateToStringUTC"),m(f,"normalizeQueryConfig");var d=m(function(e){return r.createHash("md5").update(e,"utf-8").digest("hex")},"md5"),p=m(function(e,t,r){var s=d(t+e);return"md5"+d(a.concat([a.from(s),r]))},"postgresMd5PasswordHash");t.exports={prepareValue:m(function(e){return o(e)},"prepareValueWrapper"),normalizeQueryConfig:f,postgresMd5PasswordHash:p,md5:d}}),et=b((e,t)=>{A();var r=(j(),S(U));function s(e){if(-1===e.indexOf("SCRAM-SHA-256"))throw Error("SASL: Only mechanism SCRAM-SHA-256 is currently supported");let t=r.randomBytes(18).toString("base64");return{mechanism:"SCRAM-SHA-256",clientNonce:t,response:"n,,n=*,r="+t,message:"SASLInitialResponse"}}function i(e,t,r){if("SASLInitialResponse"!==e.message)throw Error("SASL: Last message was not SASLInitialResponse");if("string"!=typeof t)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string");if("string"!=typeof r)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: serverData must be a string");let s=c(r);if(s.nonce.startsWith(e.clientNonce)){if(s.nonce.length===e.clientNonce.length)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce is too short")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce");var i=y(t,a.from(s.salt,"base64"),s.iteration),n=p(i,"Client Key"),o=d(n),h="n=*,r="+e.clientNonce,l="r="+s.nonce+",s="+s.salt+",i="+s.iteration,u="c=biws,r="+s.nonce,m=h+","+l+","+u,g=f(n,p(o,m)).toString("base64"),b=p(i,"Server Key"),_=p(b,m);e.message="SASLResponse",e.serverSignature=_.toString("base64"),e.response=u+",p="+g}function n(e,t){if("SASLResponse"!==e.message)throw Error("SASL: Last message was not SASLResponse");if("string"!=typeof t)throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: serverData must be a string");let{serverSignature:r}=u(t);if(r!==e.serverSignature)throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature does not match")}function o(e){if("string"!=typeof e)throw TypeError("SASL: text must be a string");return e.split("").map((t,r)=>e.charCodeAt(r)).every(e=>e>=33&&e<=43||e>=45&&e<=126)}function h(e){return/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.test(e)}function l(e){if("string"!=typeof e)throw TypeError("SASL: attribute pairs text must be a string");return new Map(e.split(",").map(e=>{if(!/^.=/.test(e))throw Error("SASL: Invalid attribute pair entry");return[e[0],e.substring(2)]}))}function c(e){let t=l(e),r=t.get("r");if(r){if(!o(r))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce must only contain printable characters")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing");let s=t.get("s");if(s){if(!h(s))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt must be base64")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing");let i=t.get("i");if(i){if(!/^[1-9][0-9]*$/.test(i))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: invalid iteration count")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: iteration missing");return{nonce:r,salt:s,iteration:parseInt(i,10)}}function u(e){let t=l(e).get("v");if(t){if(!h(t))throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature must be base64")}else throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature is missing");return{serverSignature:t}}function f(e,t){if(!a.isBuffer(e))throw TypeError("first argument must be a Buffer");if(!a.isBuffer(t))throw TypeError("second argument must be a Buffer");if(e.length!==t.length)throw Error("Buffer lengths must match");if(0===e.length)throw Error("Buffers cannot be empty");return a.from(e.map((r,s)=>e[s]^t[s]))}function d(e){return r.createHash("sha256").update(e).digest()}function p(e,t){return r.createHmac("sha256",e).update(t).digest()}function y(e,t,r){for(var s=p(e,a.concat([t,a.from([0,0,0,1])])),i=s,n=0;n<r-1;n++)i=f(i,s=p(e,s));return i}m(s,"startSession"),m(i,"continueSession"),m(n,"finalizeSession"),m(o,"isPrintableChars"),m(h,"isBase64"),m(l,"parseAttributePairs"),m(c,"parseServerFirstMessage"),m(u,"parseServerFinalMessage"),m(f,"xorBuffers"),m(d,"sha256"),m(p,"hmacSha256"),m(y,"Hi"),t.exports={startSession:s,continueSession:i,finalizeSession:n}}),er={};function es(...e){return e.join("/")}_(er,{join:()=>es});var ei=g(()=>{A(),m(es,"join")}),en={};function eo(e,t){t(Error("No filesystem"))}_(en,{stat:()=>eo});var ea=g(()=>{A(),m(eo,"stat")}),eh={};_(eh,{default:()=>el});var el,ec=g(()=>{A(),el={}}),eu={};_(eu,{StringDecoder:()=>ed});var ef,ed,ep=g(()=>{A(),m(ef=class{constructor(e){E(this,"td"),this.td=new TextDecoder(e)}write(e){return this.td.decode(e,{stream:!0})}end(e){return this.td.decode(e)}},"StringDecoder"),ed=ef}),ey=b((e,t)=>{A();var{Transform:r}=(ec(),S(eh)),{StringDecoder:s}=(ep(),S(eu)),i=Symbol("last"),n=Symbol("decoder");function o(e,t,r){let s;if(this.overflow){if(1===(s=this[n].write(e).split(this.matcher)).length)return r();s.shift(),this.overflow=!1}else this[i]+=this[n].write(e),s=this[i].split(this.matcher);this[i]=s.pop();for(let e=0;e<s.length;e++)try{h(this,this.mapper(s[e]))}catch(e){return r(e)}if(this.overflow=this[i].length>this.maxLength,this.overflow&&!this.skipOverflow)return void r(Error("maximum buffer reached"));r()}function a(e){if(this[i]+=this[n].end(),this[i])try{h(this,this.mapper(this[i]))}catch(t){return e(t)}e()}function h(e,t){void 0!==t&&e.push(t)}function l(e){return e}function c(e,t,h){switch(e=e||/\r?\n/,t=t||l,h=h||{},arguments.length){case 1:"function"==typeof e?(t=e,e=/\r?\n/):"object"!=typeof e||e instanceof RegExp||e[Symbol.split]||(h=e,e=/\r?\n/);break;case 2:"function"==typeof e?(h=t,t=e,e=/\r?\n/):"object"==typeof t&&(h=t,t=l)}(h=Object.assign({},h)).autoDestroy=!0,h.transform=o,h.flush=a,h.readableObjectMode=!0;let c=new r(h);return c[i]="",c[n]=new s("utf8"),c.matcher=e,c.mapper=t,c.maxLength=h.maxLength,c.skipOverflow=h.skipOverflow||!1,c.overflow=!1,c._destroy=function(e,t){this._writableState.errorEmitted=!1,t(e)},c}m(o,"transform"),m(a,"flush"),m(h,"push"),m(l,"noop"),m(c,"split"),t.exports=c}),em=b((e,t)=>{A();var r=(ei(),S(er)),s=(ec(),S(eh)).Stream,i=ey(),n=(P(),S(L)),o="win32"===h.platform,a=h.stderr;function l(e){return(61440&e)==32768}m(l,"isRegFile");var c=["host","port","database","user","password"],u=c.length,f=c[u-1];function d(){if(a instanceof s&&!0===a.writable){var e=Array.prototype.slice.call(arguments).concat(`
`);a.write(n.format.apply(n,e))}}m(d,"warn"),Object.defineProperty(t.exports,"isWin",{get:m(function(){return o},"get"),set:m(function(e){o=e},"set")}),t.exports.warnTo=function(e){var t=a;return a=e,t},t.exports.getFileName=function(e){var t=e||h.env;return t.PGPASSFILE||(o?r.join(t.APPDATA||"./","postgresql","pgpass.conf"):r.join(t.HOME||"./",".pgpass"))},t.exports.usePgPass=function(e,t){return!Object.prototype.hasOwnProperty.call(h.env,"PGPASSWORD")&&(!!o||(t=t||"<unkn>",l(e.mode)?!(63&e.mode)||(d('WARNING: password file "%s" has group or world access; permissions should be u=rw (0600) or less',t),!1):(d('WARNING: password file "%s" is not a plain file',t),!1)))};var p=t.exports.match=function(e,t){return c.slice(0,-1).reduce(function(r,s,i){return 1==i&&Number(e[s]||5432)===Number(t[s])?r&&!0:r&&("*"===t[s]||t[s]===e[s])},!0)};t.exports.getPassword=function(e,t,r){var s,n=t.pipe(i());function o(t){var r=y(t);r&&g(r)&&p(e,r)&&(s=r[f],n.end())}m(o,"onLine");var a=m(function(){t.destroy(),r(s)},"onEnd"),h=m(function(e){t.destroy(),d("WARNING: error on reading file: %s",e),r(void 0)},"onErr");t.on("error",h),n.on("data",o).on("end",a).on("error",h)};var y=t.exports.parseLine=function(e){if(e.length<11||e.match(/^\s+#/))return null;for(var t="",r="",s=0,i=0,n={},o=m(function(t,r,s){var i=e.substring(r,s);Object.hasOwnProperty.call(h.env,"PGPASS_NO_DEESCAPE")||(i=i.replace(/\\([:\\])/g,"$1")),n[c[t]]=i},"addToObj"),a=0;a<e.length-1;a+=1){if(t=e.charAt(a+1),r=e.charAt(a),s==u-1){o(s,i);break}a>=0&&":"==t&&"\\"!==r&&(o(s,i,a+1),i=a+2,s+=1)}return n=Object.keys(n).length===u?n:null},g=t.exports.isValidEntry=function(e){for(var t={0:function(e){return e.length>0},1:function(e){return"*"===e||isFinite(e=Number(e))&&e>0&&e<0x20000000000000&&Math.floor(e)===e},2:function(e){return e.length>0},3:function(e){return e.length>0},4:function(e){return e.length>0}},r=0;r<c.length;r+=1)if(!(0,t[r])(e[c[r]]||""))return!1;return!0}}),eg=b((e,t)=>{A(),ei(),S(er);var r=(ea(),S(en)),s=em();t.exports=function(e,t){var i=s.getFileName();r.stat(i,function(n,o){if(n||!s.usePgPass(o,i))return t(void 0);var a=r.createReadStream(i);s.getPassword(e,a,t)})},t.exports.warnTo=s.warnTo}),eb=b((e,t)=>{A();var r=X();function s(e){this._types=e||r,this.text={},this.binary={}}m(s,"TypeOverrides"),s.prototype.getOverrides=function(e){switch(e){case"text":return this.text;case"binary":return this.binary;default:return{}}},s.prototype.setTypeParser=function(e,t,r){"function"==typeof t&&(r=t,t="text"),this.getOverrides(t)[e]=r},s.prototype.getTypeParser=function(e,t){return t=t||"text",this.getOverrides(t)[e]||this._types.getTypeParser(e,t)},t.exports=s}),e_={};_(e_,{default:()=>ev});var ev,ew=g(()=>{A(),ev={}}),eS={};function eE(e,t=!1){let{protocol:r}=new URL(e),{username:s,password:i,host:n,hostname:o,port:a,pathname:h,search:l,searchParams:c,hash:u}=new URL("http:"+e.substring(r.length));i=decodeURIComponent(i),s=decodeURIComponent(s),h=decodeURIComponent(h);let f=s+":"+i,d=t?Object.fromEntries(c.entries()):l;return{href:e,protocol:r,auth:f,username:s,password:i,host:n,hostname:o,port:a,pathname:h,search:l,query:d,hash:u}}_(eS,{parse:()=>eE});var ex=g(()=>{A(),m(eE,"parse")}),eC=b((e,t)=>{A();var r=(ex(),S(eS)),s=(ea(),S(en));function i(e){if("/"===e.charAt(0)){var t=e.split(" ");return{host:t[0],database:t[1]}}var i=r.parse(/ |%[^a-f0-9]|%[a-f0-9][^a-f0-9]/i.test(e)?encodeURI(e).replace(/\%25(\d\d)/g,"%$1"):e,!0),t=i.query;for(var n in t)Array.isArray(t[n])&&(t[n]=t[n][t[n].length-1]);var o=(i.auth||":").split(":");if(t.user=o[0],t.password=o.splice(1).join(":"),t.port=i.port,"socket:"==i.protocol)return t.host=decodeURI(i.pathname),t.database=i.query.db,t.client_encoding=i.query.encoding,t;t.host||(t.host=i.hostname);var a=i.pathname;if(!t.host&&a&&/^%2f/i.test(a)){var h=a.split("/");t.host=decodeURIComponent(h[0]),a=h.splice(1).join("/")}switch(a&&"/"===a.charAt(0)&&(a=a.slice(1)||null),t.database=a&&decodeURI(a),("true"===t.ssl||"1"===t.ssl)&&(t.ssl=!0),"0"===t.ssl&&(t.ssl=!1),(t.sslcert||t.sslkey||t.sslrootcert||t.sslmode)&&(t.ssl={}),t.sslcert&&(t.ssl.cert=s.readFileSync(t.sslcert).toString()),t.sslkey&&(t.ssl.key=s.readFileSync(t.sslkey).toString()),t.sslrootcert&&(t.ssl.ca=s.readFileSync(t.sslrootcert).toString()),t.sslmode){case"disable":t.ssl=!1;break;case"prefer":case"require":case"verify-ca":case"verify-full":break;case"no-verify":t.ssl.rejectUnauthorized=!1}return t}m(i,"parse"),t.exports=i,i.parse=i}),ek=b((e,t)=>{A();var r=(ew(),S(e_)),s=J(),i=eC().parse,n=m(function(e,t,r){return void 0===r?r=h.env["PG"+e.toUpperCase()]:!1===r||(r=h.env[r]),t[e]||r||s[e]},"val"),o=m(function(){switch(h.env.PGSSLMODE){case"disable":return!1;case"prefer":case"require":case"verify-ca":case"verify-full":return!0;case"no-verify":return{rejectUnauthorized:!1}}return s.ssl},"readSSLConfigFromEnvironment"),a=m(function(e){return"'"+(""+e).replace(/\\/g,"\\\\").replace(/'/g,"\\'")+"'"},"quoteParamValue"),l=m(function(e,t,r){var s=t[r];null!=s&&e.push(r+"="+a(s))},"add"),c=class{constructor(e){(e="string"==typeof e?i(e):e||{}).connectionString&&(e=Object.assign({},e,i(e.connectionString))),this.user=n("user",e),this.database=n("database",e),void 0===this.database&&(this.database=this.user),this.port=parseInt(n("port",e),10),this.host=n("host",e),Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:n("password",e)}),this.binary=n("binary",e),this.options=n("options",e),this.ssl=typeof e.ssl>"u"?o():e.ssl,"string"==typeof this.ssl&&"true"===this.ssl&&(this.ssl=!0),"no-verify"===this.ssl&&(this.ssl={rejectUnauthorized:!1}),this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,"key",{enumerable:!1}),this.client_encoding=n("client_encoding",e),this.replication=n("replication",e),this.isDomainSocket=!(this.host||"").indexOf("/"),this.application_name=n("application_name",e,"PGAPPNAME"),this.fallback_application_name=n("fallback_application_name",e,!1),this.statement_timeout=n("statement_timeout",e,!1),this.lock_timeout=n("lock_timeout",e,!1),this.idle_in_transaction_session_timeout=n("idle_in_transaction_session_timeout",e,!1),this.query_timeout=n("query_timeout",e,!1),void 0===e.connectionTimeoutMillis?this.connect_timeout=h.env.PGCONNECT_TIMEOUT||0:this.connect_timeout=Math.floor(e.connectionTimeoutMillis/1e3),!1===e.keepAlive?this.keepalives=0:!0===e.keepAlive&&(this.keepalives=1),"number"==typeof e.keepAliveInitialDelayMillis&&(this.keepalives_idle=Math.floor(e.keepAliveInitialDelayMillis/1e3))}getLibpqConnectionString(e){var t=[];l(t,this,"user"),l(t,this,"password"),l(t,this,"port"),l(t,this,"application_name"),l(t,this,"fallback_application_name"),l(t,this,"connect_timeout"),l(t,this,"options");var s="object"==typeof this.ssl?this.ssl:this.ssl?{sslmode:this.ssl}:{};if(l(t,s,"sslmode"),l(t,s,"sslca"),l(t,s,"sslkey"),l(t,s,"sslcert"),l(t,s,"sslrootcert"),this.database&&t.push("dbname="+a(this.database)),this.replication&&t.push("replication="+a(this.replication)),this.host&&t.push("host="+a(this.host)),this.isDomainSocket)return e(null,t.join(" "));this.client_encoding&&t.push("client_encoding="+a(this.client_encoding)),r.lookup(this.host,function(r,s){return r?e(r,null):(t.push("hostaddr="+a(s)),e(null,t.join(" ")))})}};m(c,"ConnectionParameters"),t.exports=c}),eA=b((e,t)=>{A();var r=X(),s=/^([A-Za-z]+)(?: (\d+))?(?: (\d+))?/,i=class{constructor(e,t){this.command=null,this.rowCount=null,this.oid=null,this.rows=[],this.fields=[],this._parsers=void 0,this._types=t,this.RowCtor=null,this.rowAsArray="array"===e,this.rowAsArray&&(this.parseRow=this._parseRowAsArray)}addCommandComplete(e){var t;(t=e.text?s.exec(e.text):s.exec(e.command))&&(this.command=t[1],t[3]?(this.oid=parseInt(t[2],10),this.rowCount=parseInt(t[3],10)):t[2]&&(this.rowCount=parseInt(t[2],10)))}_parseRowAsArray(e){for(var t=Array(e.length),r=0,s=e.length;r<s;r++){var i=e[r];null!==i?t[r]=this._parsers[r](i):t[r]=null}return t}parseRow(e){for(var t={},r=0,s=e.length;r<s;r++){var i=e[r],n=this.fields[r].name;null!==i?t[n]=this._parsers[r](i):t[n]=null}return t}addRow(e){this.rows.push(e)}addFields(e){this.fields=e,this.fields.length&&(this._parsers=Array(e.length));for(var t=0;t<e.length;t++){var s=e[t];this._types?this._parsers[t]=this._types.getTypeParser(s.dataTypeID,s.format||"text"):this._parsers[t]=r.getTypeParser(s.dataTypeID,s.format||"text")}}};m(i,"Result"),t.exports=i}),eT=b((e,t)=>{A();var{EventEmitter:r}=T(),s=eA(),i=ee(),n=class extends r{constructor(e,t,r){super(),e=i.normalizeQueryConfig(e,t,r),this.text=e.text,this.values=e.values,this.rows=e.rows,this.types=e.types,this.name=e.name,this.binary=e.binary,this.portal=e.portal||"",this.callback=e.callback,this._rowMode=e.rowMode,h.domain&&e.callback&&(this.callback=h.domain.bind(e.callback)),this._result=new s(this._rowMode,this.types),this._results=this._result,this.isPreparedStatement=!1,this._canceledDueToError=!1,this._promise=null}requiresPreparation(){return!!this.name||!!this.rows||!!this.text&&!!this.values&&this.values.length>0}_checkForMultirow(){this._result.command&&(Array.isArray(this._results)||(this._results=[this._result]),this._result=new s(this._rowMode,this.types),this._results.push(this._result))}handleRowDescription(e){this._checkForMultirow(),this._result.addFields(e.fields),this._accumulateRows=this.callback||!this.listeners("row").length}handleDataRow(e){let t;if(!this._canceledDueToError){try{t=this._result.parseRow(e.fields)}catch(e){this._canceledDueToError=e;return}this.emit("row",t,this._result),this._accumulateRows&&this._result.addRow(t)}}handleCommandComplete(e,t){this._checkForMultirow(),this._result.addCommandComplete(e),this.rows&&t.sync()}handleEmptyQuery(e){this.rows&&e.sync()}handleError(e,t){if(this._canceledDueToError&&(e=this._canceledDueToError,this._canceledDueToError=!1),this.callback)return this.callback(e);this.emit("error",e)}handleReadyForQuery(e){if(this._canceledDueToError)return this.handleError(this._canceledDueToError,e);if(this.callback)try{this.callback(null,this._results)}catch(e){h.nextTick(()=>{throw e})}this.emit("end",this._results)}submit(e){if("string"!=typeof this.text&&"string"!=typeof this.name)return Error("A query must have either text or a name. Supplying neither is unsupported.");let t=e.parsedStatements[this.name];return this.text&&t&&this.text!==t?Error(`Prepared statements must be \
unique - '${this.name}' was used for a different statement`):this.values&&!Array.isArray(this.values)?Error("Query values must be an array"):(this.requiresPreparation()?this.prepare(e):e.query(this.text),null)}hasBeenParsed(e){return this.name&&e.parsedStatements[this.name]}handlePortalSuspended(e){this._getRows(e,this.rows)}_getRows(e,t){e.execute({portal:this.portal,rows:t}),t?e.flush():e.sync()}prepare(e){this.isPreparedStatement=!0,this.hasBeenParsed(e)||e.parse({text:this.text,name:this.name,types:this.types});try{e.bind({portal:this.portal,statement:this.name,values:this.values,binary:this.binary,valueMapper:i.prepareValue})}catch(t){this.handleError(t,e);return}e.describe({type:"P",name:this.portal||""}),this._getRows(e,this.rows)}handleCopyInResponse(e){e.sendCopyFail("No source stream defined")}handleCopyData(e,t){}};m(n,"Query"),t.exports=n}),eL={};function eI(e){return 0}_(eL,{Socket:()=>eB,isIP:()=>eI});var eP,eR,eO,eB,eM=g(()=>{A(),eP=w(T(),1),m(eI,"isIP"),eR=m(e=>e.replace(/^[^.]+\./,"api."),"transformHost"),eO=class e extends eP.EventEmitter{constructor(){super(...arguments),E(this,"opts",{}),E(this,"connecting",!1),E(this,"pending",!0),E(this,"writable",!0),E(this,"encrypted",!1),E(this,"authorized",!1),E(this,"destroyed",!1),E(this,"ws",null),E(this,"writeBuffer"),E(this,"tlsState",0),E(this,"tlsRead"),E(this,"tlsWrite")}static get poolQueryViaFetch(){return e.opts.poolQueryViaFetch??e.defaults.poolQueryViaFetch}static set poolQueryViaFetch(t){e.opts.poolQueryViaFetch=t}static get fetchEndpoint(){return e.opts.fetchEndpoint??e.defaults.fetchEndpoint}static set fetchEndpoint(t){e.opts.fetchEndpoint=t}static get fetchConnectionCache(){return!0}static set fetchConnectionCache(e){console.warn("The `fetchConnectionCache` option is deprecated (now always `true`)")}static get fetchFunction(){return e.opts.fetchFunction??e.defaults.fetchFunction}static set fetchFunction(t){e.opts.fetchFunction=t}static get webSocketConstructor(){return e.opts.webSocketConstructor??e.defaults.webSocketConstructor}static set webSocketConstructor(t){e.opts.webSocketConstructor=t}get webSocketConstructor(){return this.opts.webSocketConstructor??e.webSocketConstructor}set webSocketConstructor(e){this.opts.webSocketConstructor=e}static get wsProxy(){return e.opts.wsProxy??e.defaults.wsProxy}static set wsProxy(t){e.opts.wsProxy=t}get wsProxy(){return this.opts.wsProxy??e.wsProxy}set wsProxy(e){this.opts.wsProxy=e}static get coalesceWrites(){return e.opts.coalesceWrites??e.defaults.coalesceWrites}static set coalesceWrites(t){e.opts.coalesceWrites=t}get coalesceWrites(){return this.opts.coalesceWrites??e.coalesceWrites}set coalesceWrites(e){this.opts.coalesceWrites=e}static get useSecureWebSocket(){return e.opts.useSecureWebSocket??e.defaults.useSecureWebSocket}static set useSecureWebSocket(t){e.opts.useSecureWebSocket=t}get useSecureWebSocket(){return this.opts.useSecureWebSocket??e.useSecureWebSocket}set useSecureWebSocket(e){this.opts.useSecureWebSocket=e}static get forceDisablePgSSL(){return e.opts.forceDisablePgSSL??e.defaults.forceDisablePgSSL}static set forceDisablePgSSL(t){e.opts.forceDisablePgSSL=t}get forceDisablePgSSL(){return this.opts.forceDisablePgSSL??e.forceDisablePgSSL}set forceDisablePgSSL(e){this.opts.forceDisablePgSSL=e}static get disableSNI(){return e.opts.disableSNI??e.defaults.disableSNI}static set disableSNI(t){e.opts.disableSNI=t}get disableSNI(){return this.opts.disableSNI??e.disableSNI}set disableSNI(e){this.opts.disableSNI=e}static get pipelineConnect(){return e.opts.pipelineConnect??e.defaults.pipelineConnect}static set pipelineConnect(t){e.opts.pipelineConnect=t}get pipelineConnect(){return this.opts.pipelineConnect??e.pipelineConnect}set pipelineConnect(e){this.opts.pipelineConnect=e}static get subtls(){return e.opts.subtls??e.defaults.subtls}static set subtls(t){e.opts.subtls=t}get subtls(){return this.opts.subtls??e.subtls}set subtls(e){this.opts.subtls=e}static get pipelineTLS(){return e.opts.pipelineTLS??e.defaults.pipelineTLS}static set pipelineTLS(t){e.opts.pipelineTLS=t}get pipelineTLS(){return this.opts.pipelineTLS??e.pipelineTLS}set pipelineTLS(e){this.opts.pipelineTLS=e}static get rootCerts(){return e.opts.rootCerts??e.defaults.rootCerts}static set rootCerts(t){e.opts.rootCerts=t}get rootCerts(){return this.opts.rootCerts??e.rootCerts}set rootCerts(e){this.opts.rootCerts=e}wsProxyAddrForHost(e,t){let r=this.wsProxy;if(void 0===r)throw Error("No WebSocket proxy is configured. Please see https://github.com/neondatabase/serverless/blob/main/CONFIG.md#wsproxy-string--host-string-port-number--string--string");return"function"==typeof r?r(e,t):`${r}?address=${e}:${t}`}setNoDelay(){return this}setKeepAlive(){return this}ref(){return this}unref(){return this}connect(e,t,r){this.connecting=!0,r&&this.once("connect",r);let s=m(()=>{this.connecting=!1,this.pending=!1,this.emit("connect"),this.emit("ready")},"handleWebSocketOpen"),i=m((e,t=!1)=>{e.binaryType="arraybuffer",e.addEventListener("error",e=>{this.emit("error",e),this.emit("close")}),e.addEventListener("message",e=>{if(0===this.tlsState){let t=a.from(e.data);this.emit("data",t)}}),e.addEventListener("close",()=>{this.emit("close")}),t?s():e.addEventListener("open",s)},"configureWebSocket"),n;try{n=this.wsProxyAddrForHost(t,"string"==typeof e?parseInt(e,10):e)}catch(e){this.emit("error",e),this.emit("close");return}try{let e=(this.useSecureWebSocket?"wss:":"ws:")+"//"+n;if(void 0!==this.webSocketConstructor)this.ws=new this.webSocketConstructor(e),i(this.ws);else try{this.ws=new WebSocket(e),i(this.ws)}catch{this.ws=new __unstable_WebSocket(e),i(this.ws)}}catch(e){fetch((this.useSecureWebSocket?"https:":"http:")+"//"+n,{headers:{Upgrade:"websocket"}}).then(t=>{if(this.ws=t.webSocket,null==this.ws)throw e;this.ws.accept(),i(this.ws,!0)}).catch(e=>{this.emit("error",Error(`All attempts to open a WebSocket to\
 connect to the database failed. Please refer to https://github.com/neondatabase\
/serverless/blob/main/CONFIG.md#websocketconstructor-typeof-websocket--undefined\
. Details: ${e.message}`)),this.emit("close")})}}async startTls(e){if(void 0===this.subtls)throw Error("For Postgres SSL connections, you must set `neonConfig.subtls` to the subtls library. See https://github.com/neondatabase/serverless/blob/main/CONFIG.md for more information.");this.tlsState=1;let t=this.subtls.TrustedCert.fromPEM(this.rootCerts),r=new this.subtls.WebSocketReadQueue(this.ws),s=r.read.bind(r),i=this.rawWrite.bind(this),[n,o]=await this.subtls.startTls(e,t,s,i,{useSNI:!this.disableSNI,expectPreData:this.pipelineTLS?new Uint8Array([83]):void 0});this.tlsRead=n,this.tlsWrite=o,this.tlsState=2,this.encrypted=!0,this.authorized=!0,this.emit("secureConnection",this),this.tlsReadLoop()}async tlsReadLoop(){for(;;){let e=await this.tlsRead();if(void 0===e)break;{let t=a.from(e);this.emit("data",t)}}}rawWrite(e){if(!this.coalesceWrites)return void this.ws.send(e);if(void 0===this.writeBuffer)this.writeBuffer=e,setTimeout(()=>{this.ws.send(this.writeBuffer),this.writeBuffer=void 0},0);else{let t=new Uint8Array(this.writeBuffer.length+e.length);t.set(this.writeBuffer),t.set(e,this.writeBuffer.length),this.writeBuffer=t}}write(e,t="utf8",r=e=>{}){return 0===e.length?r():("string"==typeof e&&(e=a.from(e,t)),0===this.tlsState?(this.rawWrite(e),r()):1===this.tlsState?this.once("secureConnection",()=>{this.write(e,t,r)}):(this.tlsWrite(e),r())),!0}end(e=a.alloc(0),t="utf8",r=()=>{}){return this.write(e,t,()=>{this.ws.close(),r()}),this}destroy(){return this.destroyed=!0,this.end()}},m(eO,"Socket"),E(eO,"defaults",{poolQueryViaFetch:!1,fetchEndpoint:m(e=>"https://"+eR(e)+"/sql","fetchEndpoint"),fetchConnectionCache:!0,fetchFunction:void 0,webSocketConstructor:void 0,wsProxy:m(e=>e+"/v2","wsProxy"),useSecureWebSocket:!0,forceDisablePgSSL:!0,coalesceWrites:!0,pipelineConnect:"password",subtls:void 0,rootCerts:"",pipelineTLS:!1,disableSNI:!1}),E(eO,"opts",{}),eB=eO}),eN=b(e=>{A(),Object.defineProperty(e,"__esModule",{value:!0}),e.NoticeMessage=e.DataRowMessage=e.CommandCompleteMessage=e.ReadyForQueryMessage=e.NotificationResponseMessage=e.BackendKeyDataMessage=e.AuthenticationMD5Password=e.ParameterStatusMessage=e.ParameterDescriptionMessage=e.RowDescriptionMessage=e.Field=e.CopyResponse=e.CopyDataMessage=e.DatabaseError=e.copyDone=e.emptyQuery=e.replicationStart=e.portalSuspended=e.noData=e.closeComplete=e.bindComplete=e.parseComplete=void 0,e.parseComplete={name:"parseComplete",length:5},e.bindComplete={name:"bindComplete",length:5},e.closeComplete={name:"closeComplete",length:5},e.noData={name:"noData",length:5},e.portalSuspended={name:"portalSuspended",length:5},e.replicationStart={name:"replicationStart",length:4},e.emptyQuery={name:"emptyQuery",length:4},e.copyDone={name:"copyDone",length:4};var t=class extends Error{constructor(e,t,r){super(e),this.length=t,this.name=r}};m(t,"DatabaseError"),e.DatabaseError=t;var r=class{constructor(e,t){this.length=e,this.chunk=t,this.name="copyData"}};m(r,"CopyDataMessage"),e.CopyDataMessage=r;var s=class{constructor(e,t,r,s){this.length=e,this.name=t,this.binary=r,this.columnTypes=Array(s)}};m(s,"CopyResponse"),e.CopyResponse=s;var i=class{constructor(e,t,r,s,i,n,o){this.name=e,this.tableID=t,this.columnID=r,this.dataTypeID=s,this.dataTypeSize=i,this.dataTypeModifier=n,this.format=o}};m(i,"Field"),e.Field=i;var n=class{constructor(e,t){this.length=e,this.fieldCount=t,this.name="rowDescription",this.fields=Array(this.fieldCount)}};m(n,"RowDescriptionMessage"),e.RowDescriptionMessage=n;var o=class{constructor(e,t){this.length=e,this.parameterCount=t,this.name="parameterDescription",this.dataTypeIDs=Array(this.parameterCount)}};m(o,"ParameterDescriptionMessage"),e.ParameterDescriptionMessage=o;var a=class{constructor(e,t,r){this.length=e,this.parameterName=t,this.parameterValue=r,this.name="parameterStatus"}};m(a,"ParameterStatusMessage"),e.ParameterStatusMessage=a;var h=class{constructor(e,t){this.length=e,this.salt=t,this.name="authenticationMD5Password"}};m(h,"AuthenticationMD5Password"),e.AuthenticationMD5Password=h;var l=class{constructor(e,t,r){this.length=e,this.processID=t,this.secretKey=r,this.name="backendKeyData"}};m(l,"BackendKeyDataMessage"),e.BackendKeyDataMessage=l;var c=class{constructor(e,t,r,s){this.length=e,this.processId=t,this.channel=r,this.payload=s,this.name="notification"}};m(c,"NotificationResponseMessage"),e.NotificationResponseMessage=c;var u=class{constructor(e,t){this.length=e,this.status=t,this.name="readyForQuery"}};m(u,"ReadyForQueryMessage"),e.ReadyForQueryMessage=u;var f=class{constructor(e,t){this.length=e,this.text=t,this.name="commandComplete"}};m(f,"CommandCompleteMessage"),e.CommandCompleteMessage=f;var d=class{constructor(e,t){this.length=e,this.fields=t,this.name="dataRow",this.fieldCount=t.length}};m(d,"DataRowMessage"),e.DataRowMessage=d;var p=class{constructor(e,t){this.length=e,this.message=t,this.name="notice"}};m(p,"NoticeMessage"),e.NoticeMessage=p}),eU=b(e=>{A(),Object.defineProperty(e,"__esModule",{value:!0}),e.Writer=void 0;var t=class{constructor(e=256){this.size=e,this.offset=5,this.headerPosition=0,this.buffer=a.allocUnsafe(e)}ensure(e){if(this.buffer.length-this.offset<e){var t=this.buffer,r=t.length+(t.length>>1)+e;this.buffer=a.allocUnsafe(r),t.copy(this.buffer)}}addInt32(e){return this.ensure(4),this.buffer[this.offset++]=e>>>24&255,this.buffer[this.offset++]=e>>>16&255,this.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addInt16(e){return this.ensure(2),this.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addCString(e){if(e){var t=a.byteLength(e);this.ensure(t+1),this.buffer.write(e,this.offset,"utf-8"),this.offset+=t}else this.ensure(1);return this.buffer[this.offset++]=0,this}addString(e=""){var t=a.byteLength(e);return this.ensure(t),this.buffer.write(e,this.offset),this.offset+=t,this}add(e){return this.ensure(e.length),e.copy(this.buffer,this.offset),this.offset+=e.length,this}join(e){if(e){this.buffer[this.headerPosition]=e;let t=this.offset-(this.headerPosition+1);this.buffer.writeInt32BE(t,this.headerPosition+1)}return this.buffer.slice(5*!e,this.offset)}flush(e){var t=this.join(e);return this.offset=5,this.headerPosition=0,this.buffer=a.allocUnsafe(this.size),t}};m(t,"Writer"),e.Writer=t}),eD=b(e=>{A(),Object.defineProperty(e,"__esModule",{value:!0}),e.serialize=void 0;var t=eU(),r=new t.Writer,s=m(e=>{for(let t of(r.addInt16(3).addInt16(0),Object.keys(e)))r.addCString(t).addCString(e[t]);r.addCString("client_encoding").addCString("UTF8");var s=r.addCString("").flush(),i=s.length+4;return new t.Writer().addInt32(i).add(s).flush()},"startup"),i=m(()=>{let e=a.allocUnsafe(8);return e.writeInt32BE(8,0),e.writeInt32BE(0x4d2162f,4),e},"requestSsl"),n=m(e=>r.addCString(e).flush(112),"password"),o=m(function(e,t){return r.addCString(e).addInt32(a.byteLength(t)).addString(t),r.flush(112)},"sendSASLInitialResponseMessage"),h=m(function(e){return r.addString(e).flush(112)},"sendSCRAMClientFinalMessage"),l=m(e=>r.addCString(e).flush(81),"query"),c=[],u=m(e=>{let t=e.name||"";t.length>63&&(console.error("Warning! Postgres only supports 63 characters for query names."),console.error("You supplied %s (%s)",t,t.length),console.error("This can cause conflicts and silent errors executing queries"));let s=e.types||c;for(var i=s.length,n=r.addCString(t).addCString(e.text).addInt16(i),o=0;o<i;o++)n.addInt32(s[o]);return r.flush(80)},"parse"),f=new t.Writer,d=m(function(e,t){for(let s=0;s<e.length;s++){let i=t?t(e[s],s):e[s];null==i?(r.addInt16(0),f.addInt32(-1)):i instanceof a?(r.addInt16(1),f.addInt32(i.length),f.add(i)):(r.addInt16(0),f.addInt32(a.byteLength(i)),f.addString(i))}},"writeValues"),p=m((e={})=>{let t=e.portal||"",s=e.statement||"",i=e.binary||!1,n=e.values||c,o=n.length;return r.addCString(t).addCString(s),r.addInt16(o),d(n,e.valueMapper),r.addInt16(o),r.add(f.flush()),r.addInt16(+!!i),r.flush(66)},"bind"),y=a.from([69,0,0,0,9,0,0,0,0,0]),g=m(e=>{if(!e||!e.portal&&!e.rows)return y;let t=e.portal||"",r=e.rows||0,s=a.byteLength(t),i=4+s+1+4,n=a.allocUnsafe(1+i);return n[0]=69,n.writeInt32BE(i,1),n.write(t,5,"utf-8"),n[s+5]=0,n.writeUInt32BE(r,n.length-4),n},"execute"),b=m((e,t)=>{let r=a.allocUnsafe(16);return r.writeInt32BE(16,0),r.writeInt16BE(1234,4),r.writeInt16BE(5678,6),r.writeInt32BE(e,8),r.writeInt32BE(t,12),r},"cancel"),_=m((e,t)=>{let r=4+a.byteLength(t)+1,s=a.allocUnsafe(1+r);return s[0]=e,s.writeInt32BE(r,1),s.write(t,5,"utf-8"),s[r]=0,s},"cstringMessage"),v=r.addCString("P").flush(68),w=r.addCString("S").flush(68),S=m(e=>e.name?_(68,`${e.type}${e.name||""}`):"P"===e.type?v:w,"describe"),E=m(e=>_(67,`${e.type}${e.name||""}`),"close"),x=m(e=>r.add(e).flush(100),"copyData"),C=m(e=>_(102,e),"copyFail"),k=m(e=>a.from([e,0,0,0,4]),"codeOnlyBuffer"),T=k(72),L=k(83),I=k(88),P=k(99);e.serialize={startup:s,password:n,requestSsl:i,sendSASLInitialResponseMessage:o,sendSCRAMClientFinalMessage:h,query:l,parse:u,bind:p,execute:g,describe:S,close:E,flush:m(()=>T,"flush"),sync:m(()=>L,"sync"),end:m(()=>I,"end"),copyData:x,copyDone:m(()=>P,"copyDone"),copyFail:C,cancel:b}}),eF=b(e=>{A(),Object.defineProperty(e,"__esModule",{value:!0}),e.BufferReader=void 0;var t=a.allocUnsafe(0),r=class{constructor(e=0){this.offset=e,this.buffer=t,this.encoding="utf-8"}setBuffer(e,t){this.offset=e,this.buffer=t}int16(){let e=this.buffer.readInt16BE(this.offset);return this.offset+=2,e}byte(){let e=this.buffer[this.offset];return this.offset++,e}int32(){let e=this.buffer.readInt32BE(this.offset);return this.offset+=4,e}string(e){let t=this.buffer.toString(this.encoding,this.offset,this.offset+e);return this.offset+=e,t}cstring(){let e=this.offset,t=e;for(;0!==this.buffer[t++];);return this.offset=t,this.buffer.toString(this.encoding,e,t-1)}bytes(e){let t=this.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t}};m(r,"BufferReader"),e.BufferReader=r}),eQ=b(e=>{A(),Object.defineProperty(e,"__esModule",{value:!0}),e.Parser=void 0;var t=eN(),r=eF(),s=a.allocUnsafe(0),i=class{constructor(e){if(this.buffer=s,this.bufferLength=0,this.bufferOffset=0,this.reader=new r.BufferReader,e?.mode==="binary")throw Error("Binary mode not supported yet");this.mode=e?.mode||"text"}parse(e,t){this.mergeBuffer(e);let r=this.bufferOffset+this.bufferLength,i=this.bufferOffset;for(;i+5<=r;){let e=this.buffer[i],s=this.buffer.readUInt32BE(i+1),n=1+s;if(n+i<=r)t(this.handlePacket(i+5,e,s,this.buffer)),i+=n;else break}i===r?(this.buffer=s,this.bufferLength=0,this.bufferOffset=0):(this.bufferLength=r-i,this.bufferOffset=i)}mergeBuffer(e){if(this.bufferLength>0){let t=this.bufferLength+e.byteLength;if(t+this.bufferOffset>this.buffer.byteLength){let e;if(t<=this.buffer.byteLength&&this.bufferOffset>=this.bufferLength)e=this.buffer;else{let r=2*this.buffer.byteLength;for(;t>=r;)r*=2;e=a.allocUnsafe(r)}this.buffer.copy(e,0,this.bufferOffset,this.bufferOffset+this.bufferLength),this.buffer=e,this.bufferOffset=0}e.copy(this.buffer,this.bufferOffset+this.bufferLength),this.bufferLength=t}else this.buffer=e,this.bufferOffset=0,this.bufferLength=e.byteLength}handlePacket(e,r,s,i){switch(r){case 50:return t.bindComplete;case 49:return t.parseComplete;case 51:return t.closeComplete;case 110:return t.noData;case 115:return t.portalSuspended;case 99:return t.copyDone;case 87:return t.replicationStart;case 73:return t.emptyQuery;case 68:return this.parseDataRowMessage(e,s,i);case 67:return this.parseCommandCompleteMessage(e,s,i);case 90:return this.parseReadyForQueryMessage(e,s,i);case 65:return this.parseNotificationMessage(e,s,i);case 82:return this.parseAuthenticationResponse(e,s,i);case 83:return this.parseParameterStatusMessage(e,s,i);case 75:return this.parseBackendKeyData(e,s,i);case 69:return this.parseErrorMessage(e,s,i,"error");case 78:return this.parseErrorMessage(e,s,i,"notice");case 84:return this.parseRowDescriptionMessage(e,s,i);case 116:return this.parseParameterDescriptionMessage(e,s,i);case 71:return this.parseCopyInMessage(e,s,i);case 72:return this.parseCopyOutMessage(e,s,i);case 100:return this.parseCopyData(e,s,i);default:return new t.DatabaseError("received invalid response: "+r.toString(16),s,"error")}}parseReadyForQueryMessage(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.string(1);return new t.ReadyForQueryMessage(r,i)}parseCommandCompleteMessage(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.cstring();return new t.CommandCompleteMessage(r,i)}parseCopyData(e,r,s){let i=s.slice(e,e+(r-4));return new t.CopyDataMessage(r,i)}parseCopyInMessage(e,t,r){return this.parseCopyMessage(e,t,r,"copyInResponse")}parseCopyOutMessage(e,t,r){return this.parseCopyMessage(e,t,r,"copyOutResponse")}parseCopyMessage(e,r,s,i){this.reader.setBuffer(e,s);let n=0!==this.reader.byte(),o=this.reader.int16(),a=new t.CopyResponse(r,i,n,o);for(let e=0;e<o;e++)a.columnTypes[e]=this.reader.int16();return a}parseNotificationMessage(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.int32(),n=this.reader.cstring(),o=this.reader.cstring();return new t.NotificationResponseMessage(r,i,n,o)}parseRowDescriptionMessage(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.int16(),n=new t.RowDescriptionMessage(r,i);for(let e=0;e<i;e++)n.fields[e]=this.parseField();return n}parseField(){let e=this.reader.cstring(),r=this.reader.int32(),s=this.reader.int16(),i=this.reader.int32(),n=this.reader.int16(),o=this.reader.int32(),a=0===this.reader.int16()?"text":"binary";return new t.Field(e,r,s,i,n,o,a)}parseParameterDescriptionMessage(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.int16(),n=new t.ParameterDescriptionMessage(r,i);for(let e=0;e<i;e++)n.dataTypeIDs[e]=this.reader.int32();return n}parseDataRowMessage(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.int16(),n=Array(i);for(let e=0;e<i;e++){let t=this.reader.int32();n[e]=-1===t?null:this.reader.string(t)}return new t.DataRowMessage(r,n)}parseParameterStatusMessage(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.cstring(),n=this.reader.cstring();return new t.ParameterStatusMessage(r,i,n)}parseBackendKeyData(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.int32(),n=this.reader.int32();return new t.BackendKeyDataMessage(r,i,n)}parseAuthenticationResponse(e,r,s){this.reader.setBuffer(e,s);let i=this.reader.int32(),n={name:"authenticationOk",length:r};switch(i){case 0:break;case 3:8===n.length&&(n.name="authenticationCleartextPassword");break;case 5:if(12===n.length){n.name="authenticationMD5Password";let e=this.reader.bytes(4);return new t.AuthenticationMD5Password(r,e)}break;case 10:let o;n.name="authenticationSASL",n.mechanisms=[];do(o=this.reader.cstring())&&n.mechanisms.push(o);while(o);break;case 11:n.name="authenticationSASLContinue",n.data=this.reader.string(r-8);break;case 12:n.name="authenticationSASLFinal",n.data=this.reader.string(r-8);break;default:throw Error("Unknown authenticationOk message type "+i)}return n}parseErrorMessage(e,r,s,i){this.reader.setBuffer(e,s);let n={},o=this.reader.string(1);for(;"\0"!==o;)n[o]=this.reader.cstring(),o=this.reader.string(1);let a=n.M,h="notice"===i?new t.NoticeMessage(r,a):new t.DatabaseError(a,r,i);return h.severity=n.S,h.code=n.C,h.detail=n.D,h.hint=n.H,h.position=n.P,h.internalPosition=n.p,h.internalQuery=n.q,h.where=n.W,h.schema=n.s,h.table=n.t,h.column=n.c,h.dataType=n.d,h.constraint=n.n,h.file=n.F,h.line=n.L,h.routine=n.R,h}};m(i,"Parser"),e.Parser=i}),ej=b(e=>{A(),Object.defineProperty(e,"__esModule",{value:!0}),e.DatabaseError=e.serialize=e.parse=void 0;var t=eN();Object.defineProperty(e,"DatabaseError",{enumerable:!0,get:m(function(){return t.DatabaseError},"get")});var r=eD();Object.defineProperty(e,"serialize",{enumerable:!0,get:m(function(){return r.serialize},"get")});var s=eQ();function i(e,t){let r=new s.Parser;return e.on("data",e=>r.parse(e,t)),new Promise(t=>e.on("end",()=>t()))}m(i,"parse"),e.parse=i}),eq={};function eW({socket:e,servername:t}){return e.startTls(t),e}_(eq,{connect:()=>eW});var e$=g(()=>{A(),m(eW,"connect")}),eG=b((e,t)=>{A();var r=(eM(),S(eL)),s=T().EventEmitter,{parse:i,serialize:n}=ej(),o=n.flush(),a=n.sync(),h=n.end(),l=class extends s{constructor(e){super(),e=e||{},this.stream=e.stream||new r.Socket,this._keepAlive=e.keepAlive,this._keepAliveInitialDelayMillis=e.keepAliveInitialDelayMillis,this.lastBuffer=!1,this.parsedStatements={},this.ssl=e.ssl||!1,this._ending=!1,this._emitMessage=!1;var t=this;this.on("newListener",function(e){"message"===e&&(t._emitMessage=!0)})}connect(e,t){var s=this;this._connecting=!0,this.stream.setNoDelay(!0),this.stream.connect(e,t),this.stream.once("connect",function(){s._keepAlive&&s.stream.setKeepAlive(!0,s._keepAliveInitialDelayMillis),s.emit("connect")});let i=m(function(e){s._ending&&("ECONNRESET"===e.code||"EPIPE"===e.code)||s.emit("error",e)},"reportStreamError");if(this.stream.on("error",i),this.stream.on("close",function(){s.emit("end")}),!this.ssl)return this.attachListeners(this.stream);this.stream.once("data",function(e){switch(e.toString("utf8")){case"S":break;case"N":return s.stream.end(),s.emit("error",Error("The server does not support SSL connections"));default:return s.stream.end(),s.emit("error",Error("There was an error establishing an SSL connection"))}var n=(e$(),S(eq));let o={socket:s.stream};!0!==s.ssl&&(Object.assign(o,s.ssl),"key"in s.ssl&&(o.key=s.ssl.key)),0===r.isIP(t)&&(o.servername=t);try{s.stream=n.connect(o)}catch(e){return s.emit("error",e)}s.attachListeners(s.stream),s.stream.on("error",i),s.emit("sslconnect")})}attachListeners(e){e.on("end",()=>{this.emit("end")}),i(e,e=>{var t="error"===e.name?"errorMessage":e.name;this._emitMessage&&this.emit("message",e),this.emit(t,e)})}requestSsl(){this.stream.write(n.requestSsl())}startup(e){this.stream.write(n.startup(e))}cancel(e,t){this._send(n.cancel(e,t))}password(e){this._send(n.password(e))}sendSASLInitialResponseMessage(e,t){this._send(n.sendSASLInitialResponseMessage(e,t))}sendSCRAMClientFinalMessage(e){this._send(n.sendSCRAMClientFinalMessage(e))}_send(e){return!!this.stream.writable&&this.stream.write(e)}query(e){this._send(n.query(e))}parse(e){this._send(n.parse(e))}bind(e){this._send(n.bind(e))}execute(e){this._send(n.execute(e))}flush(){this.stream.writable&&this.stream.write(o)}sync(){this._ending=!0,this._send(o),this._send(a)}ref(){this.stream.ref()}unref(){this.stream.unref()}end(){return(this._ending=!0,this._connecting&&this.stream.writable)?this.stream.write(h,()=>{this.stream.end()}):void this.stream.end()}close(e){this._send(n.close(e))}describe(e){this._send(n.describe(e))}sendCopyFromChunk(e){this._send(n.copyData(e))}endCopyFrom(){this._send(n.copyDone())}sendCopyFail(e){this._send(n.copyFail(e))}};m(l,"Connection"),t.exports=l}),eV=b((e,t)=>{A();var r=T().EventEmitter,s=(P(),S(L),ee()),n=et(),o=eg(),a=eb(),l=ek(),c=eT(),u=J(),f=eG(),d=class extends r{constructor(e){super(),this.connectionParameters=new l(e),this.user=this.connectionParameters.user,this.database=this.connectionParameters.database,this.port=this.connectionParameters.port,this.host=this.connectionParameters.host,Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:this.connectionParameters.password}),this.replication=this.connectionParameters.replication;var t=e||{};this._Promise=t.Promise||i.Promise,this._types=new a(t.types),this._ending=!1,this._connecting=!1,this._connected=!1,this._connectionError=!1,this._queryable=!0,this.connection=t.connection||new f({stream:t.stream,ssl:this.connectionParameters.ssl,keepAlive:t.keepAlive||!1,keepAliveInitialDelayMillis:t.keepAliveInitialDelayMillis||0,encoding:this.connectionParameters.client_encoding||"utf8"}),this.queryQueue=[],this.binary=t.binary||u.binary,this.processID=null,this.secretKey=null,this.ssl=this.connectionParameters.ssl||!1,this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,"key",{enumerable:!1}),this._connectionTimeoutMillis=t.connectionTimeoutMillis||0}_errorAllQueries(e){let t=m(t=>{h.nextTick(()=>{t.handleError(e,this.connection)})},"enqueueError");this.activeQuery&&(t(this.activeQuery),this.activeQuery=null),this.queryQueue.forEach(t),this.queryQueue.length=0}_connect(e){var t=this,r=this.connection;if(this._connectionCallback=e,this._connecting||this._connected){let t=Error("Client has already been connected. You cannot reuse a client.");h.nextTick(()=>{e(t)});return}this._connecting=!0,this.connectionTimeoutHandle,this._connectionTimeoutMillis>0&&(this.connectionTimeoutHandle=setTimeout(()=>{r._ending=!0,r.stream.destroy(Error("timeout expired"))},this._connectionTimeoutMillis)),this.host&&0===this.host.indexOf("/")?r.connect(this.host+"/.s.PGSQL."+this.port):r.connect(this.port,this.host),r.on("connect",function(){t.ssl?r.requestSsl():r.startup(t.getStartupConf())}),r.on("sslconnect",function(){r.startup(t.getStartupConf())}),this._attachListeners(r),r.once("end",()=>{let e=this._ending?Error("Connection terminated"):Error("Connection terminated unexpectedly");clearTimeout(this.connectionTimeoutHandle),this._errorAllQueries(e),this._ending||(this._connecting&&!this._connectionError?this._connectionCallback?this._connectionCallback(e):this._handleErrorEvent(e):this._connectionError||this._handleErrorEvent(e)),h.nextTick(()=>{this.emit("end")})})}connect(e){return e?void this._connect(e):new this._Promise((e,t)=>{this._connect(r=>{r?t(r):e()})})}_attachListeners(e){e.on("authenticationCleartextPassword",this._handleAuthCleartextPassword.bind(this)),e.on("authenticationMD5Password",this._handleAuthMD5Password.bind(this)),e.on("authenticationSASL",this._handleAuthSASL.bind(this)),e.on("authenticationSASLContinue",this._handleAuthSASLContinue.bind(this)),e.on("authenticationSASLFinal",this._handleAuthSASLFinal.bind(this)),e.on("backendKeyData",this._handleBackendKeyData.bind(this)),e.on("error",this._handleErrorEvent.bind(this)),e.on("errorMessage",this._handleErrorMessage.bind(this)),e.on("readyForQuery",this._handleReadyForQuery.bind(this)),e.on("notice",this._handleNotice.bind(this)),e.on("rowDescription",this._handleRowDescription.bind(this)),e.on("dataRow",this._handleDataRow.bind(this)),e.on("portalSuspended",this._handlePortalSuspended.bind(this)),e.on("emptyQuery",this._handleEmptyQuery.bind(this)),e.on("commandComplete",this._handleCommandComplete.bind(this)),e.on("parseComplete",this._handleParseComplete.bind(this)),e.on("copyInResponse",this._handleCopyInResponse.bind(this)),e.on("copyData",this._handleCopyData.bind(this)),e.on("notification",this._handleNotification.bind(this))}_checkPgPass(e){let t=this.connection;"function"==typeof this.password?this._Promise.resolve().then(()=>this.password()).then(r=>{if(void 0!==r){if("string"!=typeof r)return void t.emit("error",TypeError("Password must be a string"));this.connectionParameters.password=this.password=r}else this.connectionParameters.password=this.password=null;e()}).catch(e=>{t.emit("error",e)}):null!==this.password?e():o(this.connectionParameters,t=>{void 0!==t&&(this.connectionParameters.password=this.password=t),e()})}_handleAuthCleartextPassword(e){this._checkPgPass(()=>{this.connection.password(this.password)})}_handleAuthMD5Password(e){this._checkPgPass(()=>{let t=s.postgresMd5PasswordHash(this.user,this.password,e.salt);this.connection.password(t)})}_handleAuthSASL(e){this._checkPgPass(()=>{this.saslSession=n.startSession(e.mechanisms),this.connection.sendSASLInitialResponseMessage(this.saslSession.mechanism,this.saslSession.response)})}_handleAuthSASLContinue(e){n.continueSession(this.saslSession,this.password,e.data),this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)}_handleAuthSASLFinal(e){n.finalizeSession(this.saslSession,e.data),this.saslSession=null}_handleBackendKeyData(e){this.processID=e.processID,this.secretKey=e.secretKey}_handleReadyForQuery(e){this._connecting&&(this._connecting=!1,this._connected=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback&&(this._connectionCallback(null,this),this._connectionCallback=null),this.emit("connect"));let{activeQuery:t}=this;this.activeQuery=null,this.readyForQuery=!0,t&&t.handleReadyForQuery(this.connection),this._pulseQueryQueue()}_handleErrorWhileConnecting(e){if(!this._connectionError){if(this._connectionError=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback)return this._connectionCallback(e);this.emit("error",e)}}_handleErrorEvent(e){if(this._connecting)return this._handleErrorWhileConnecting(e);this._queryable=!1,this._errorAllQueries(e),this.emit("error",e)}_handleErrorMessage(e){if(this._connecting)return this._handleErrorWhileConnecting(e);let t=this.activeQuery;if(!t)return void this._handleErrorEvent(e);this.activeQuery=null,t.handleError(e,this.connection)}_handleRowDescription(e){this.activeQuery.handleRowDescription(e)}_handleDataRow(e){this.activeQuery.handleDataRow(e)}_handlePortalSuspended(e){this.activeQuery.handlePortalSuspended(this.connection)}_handleEmptyQuery(e){this.activeQuery.handleEmptyQuery(this.connection)}_handleCommandComplete(e){this.activeQuery.handleCommandComplete(e,this.connection)}_handleParseComplete(e){this.activeQuery.name&&(this.connection.parsedStatements[this.activeQuery.name]=this.activeQuery.text)}_handleCopyInResponse(e){this.activeQuery.handleCopyInResponse(this.connection)}_handleCopyData(e){this.activeQuery.handleCopyData(e,this.connection)}_handleNotification(e){this.emit("notification",e)}_handleNotice(e){this.emit("notice",e)}getStartupConf(){var e=this.connectionParameters,t={user:e.user,database:e.database},r=e.application_name||e.fallback_application_name;return r&&(t.application_name=r),e.replication&&(t.replication=""+e.replication),e.statement_timeout&&(t.statement_timeout=String(parseInt(e.statement_timeout,10))),e.lock_timeout&&(t.lock_timeout=String(parseInt(e.lock_timeout,10))),e.idle_in_transaction_session_timeout&&(t.idle_in_transaction_session_timeout=String(parseInt(e.idle_in_transaction_session_timeout,10))),e.options&&(t.options=e.options),t}cancel(e,t){if(e.activeQuery===t){var r=this.connection;this.host&&0===this.host.indexOf("/")?r.connect(this.host+"/.s.PGSQL."+this.port):r.connect(this.port,this.host),r.on("connect",function(){r.cancel(e.processID,e.secretKey)})}else -1!==e.queryQueue.indexOf(t)&&e.queryQueue.splice(e.queryQueue.indexOf(t),1)}setTypeParser(e,t,r){return this._types.setTypeParser(e,t,r)}getTypeParser(e,t){return this._types.getTypeParser(e,t)}escapeIdentifier(e){return'"'+e.replace(/"/g,'""')+'"'}escapeLiteral(e){for(var t=!1,r="'",s=0;s<e.length;s++){var i=e[s];"'"===i?r+=i+i:"\\"===i?(r+=i+i,t=!0):r+=i}return r+="'",!0===t&&(r=" E"+r),r}_pulseQueryQueue(){if(!0===this.readyForQuery)if(this.activeQuery=this.queryQueue.shift(),this.activeQuery){this.readyForQuery=!1,this.hasExecuted=!0;let e=this.activeQuery.submit(this.connection);e&&h.nextTick(()=>{this.activeQuery.handleError(e,this.connection),this.readyForQuery=!0,this._pulseQueryQueue()})}else this.hasExecuted&&(this.activeQuery=null,this.emit("drain"))}query(e,t,r){var s,i,n,o,a;if(null==e)throw TypeError("Client was passed a null or undefined query");return"function"==typeof e.submit?(n=e.query_timeout||this.connectionParameters.query_timeout,i=s=e,"function"==typeof t&&(s.callback=s.callback||t)):(n=this.connectionParameters.query_timeout,(s=new c(e,t,r)).callback||(i=new this._Promise((e,t)=>{s.callback=(r,s)=>r?t(r):e(s)}))),n&&(a=s.callback,o=setTimeout(()=>{var e=Error("Query read timeout");h.nextTick(()=>{s.handleError(e,this.connection)}),a(e),s.callback=()=>{};var t=this.queryQueue.indexOf(s);t>-1&&this.queryQueue.splice(t,1),this._pulseQueryQueue()},n),s.callback=(e,t)=>{clearTimeout(o),a(e,t)}),this.binary&&!s.binary&&(s.binary=!0),s._result&&!s._result._types&&(s._result._types=this._types),this._queryable?this._ending?h.nextTick(()=>{s.handleError(Error("Client was closed and is not queryable"),this.connection)}):(this.queryQueue.push(s),this._pulseQueryQueue()):h.nextTick(()=>{s.handleError(Error("Client has encountered a connection error and is not queryable"),this.connection)}),i}ref(){this.connection.ref()}unref(){this.connection.unref()}end(e){if(this._ending=!0,!this.connection._connecting)if(!e)return this._Promise.resolve();else e();if(this.activeQuery||!this._queryable?this.connection.stream.destroy():this.connection.end(),!e)return new this._Promise(e=>{this.connection.once("end",e)});this.connection.once("end",e)}};m(d,"Client"),d.Query=c,t.exports=d}),eH=b((e,t)=>{A();var r=T().EventEmitter,s=m(function(){},"NOOP"),o=m((e,t)=>{let r=e.findIndex(t);return -1===r?void 0:e.splice(r,1)[0]},"removeWhere"),a=class{constructor(e,t,r){this.client=e,this.idleListener=t,this.timeoutId=r}};m(a,"IdleItem");var l=class{constructor(e){this.callback=e}};function c(){throw Error("Release called on client which has already been released to the pool.")}function u(e,t){let r,s;return t?{callback:t,result:void 0}:{callback:m(function(e,t){e?r(e):s(t)},"cb"),result:new e(function(e,t){s=e,r=t}).catch(e=>{throw Error.captureStackTrace(e),e})}}function f(e,t){return m(function r(s){s.client=t,t.removeListener("error",r),t.on("error",()=>{e.log("additional client error after disconnection due to error",s)}),e._remove(t),e.emit("error",s,t)},"idleListener")}m(l,"PendingItem"),m(c,"throwOnDoubleRelease"),m(u,"promisify"),m(f,"makeIdleListener");var d=class extends r{constructor(e,t){super(),this.options=Object.assign({},e),null!=e&&"password"in e&&Object.defineProperty(this.options,"password",{configurable:!0,enumerable:!1,writable:!0,value:e.password}),null!=e&&e.ssl&&e.ssl.key&&Object.defineProperty(this.options.ssl,"key",{enumerable:!1}),this.options.max=this.options.max||this.options.poolSize||10,this.options.maxUses=this.options.maxUses||1/0,this.options.allowExitOnIdle=this.options.allowExitOnIdle||!1,this.options.maxLifetimeSeconds=this.options.maxLifetimeSeconds||0,this.log=this.options.log||function(){},this.Client=this.options.Client||t||e1().Client,this.Promise=this.options.Promise||i.Promise,typeof this.options.idleTimeoutMillis>"u"&&(this.options.idleTimeoutMillis=1e4),this._clients=[],this._idle=[],this._expired=new WeakSet,this._pendingQueue=[],this._endCallback=void 0,this.ending=!1,this.ended=!1}_isFull(){return this._clients.length>=this.options.max}_pulseQueue(){if(this.log("pulse queue"),this.ended)return void this.log("pulse queue ended");if(this.ending){this.log("pulse queue on ending"),this._idle.length&&this._idle.slice().map(e=>{this._remove(e.client)}),this._clients.length||(this.ended=!0,this._endCallback());return}if(!this._pendingQueue.length)return void this.log("no queued requests");if(!this._idle.length&&this._isFull())return;let e=this._pendingQueue.shift();if(this._idle.length){let t=this._idle.pop();clearTimeout(t.timeoutId);let r=t.client;r.ref&&r.ref();let s=t.idleListener;return this._acquireClient(r,e,s,!1)}if(!this._isFull())return this.newClient(e);throw Error("unexpected condition")}_remove(e){let t=o(this._idle,t=>t.client===e);void 0!==t&&clearTimeout(t.timeoutId),this._clients=this._clients.filter(t=>t!==e),e.end(),this.emit("remove",e)}connect(e){if(this.ending){let t=Error("Cannot use a pool after calling end on the pool");return e?e(t):this.Promise.reject(t)}let t=u(this.Promise,e),r=t.result;if(this._isFull()||this._idle.length){if(this._idle.length&&h.nextTick(()=>this._pulseQueue()),!this.options.connectionTimeoutMillis)return this._pendingQueue.push(new l(t.callback)),r;let e=m((e,r,s)=>{clearTimeout(i),t.callback(e,r,s)},"queueCallback"),s=new l(e),i=setTimeout(()=>{o(this._pendingQueue,t=>t.callback===e),s.timedOut=!0,t.callback(Error("timeout exceeded when trying to connect"))},this.options.connectionTimeoutMillis);return this._pendingQueue.push(s),r}return this.newClient(new l(t.callback)),r}newClient(e){let t=new this.Client(this.options);this._clients.push(t);let r=f(this,t);this.log("checking client timeout");let i,n=!1;this.options.connectionTimeoutMillis&&(i=setTimeout(()=>{this.log("ending client due to timeout"),n=!0,t.connection?t.connection.stream.destroy():t.end()},this.options.connectionTimeoutMillis)),this.log("connecting new client"),t.connect(o=>{if(i&&clearTimeout(i),t.on("error",r),o)this.log("client failed to connect",o),this._clients=this._clients.filter(e=>e!==t),n&&(o.message="Connection terminated due to connection timeout"),this._pulseQueue(),e.timedOut||e.callback(o,void 0,s);else{if(this.log("new client connected"),0!==this.options.maxLifetimeSeconds){let e=setTimeout(()=>{this.log("ending client due to expired lifetime"),this._expired.add(t),-1!==this._idle.findIndex(e=>e.client===t)&&this._acquireClient(t,new l((e,t,r)=>r()),r,!1)},1e3*this.options.maxLifetimeSeconds);e.unref(),t.once("end",()=>clearTimeout(e))}return this._acquireClient(t,e,r,!0)}})}_acquireClient(e,t,r,i){i&&this.emit("connect",e),this.emit("acquire",e),e.release=this._releaseOnce(e,r),e.removeListener("error",r),t.timedOut?i&&this.options.verify?this.options.verify(e,e.release):e.release():i&&this.options.verify?this.options.verify(e,r=>{if(r)return e.release(r),t.callback(r,void 0,s);t.callback(void 0,e,e.release)}):t.callback(void 0,e,e.release)}_releaseOnce(e,t){let r=!1;return s=>{r&&c(),r=!0,this._release(e,t,s)}}_release(e,t,r){let s;if(e.on("error",t),e._poolUseCount=(e._poolUseCount||0)+1,this.emit("release",r,e),r||this.ending||!e._queryable||e._ending||e._poolUseCount>=this.options.maxUses){e._poolUseCount>=this.options.maxUses&&this.log("remove expended client"),this._remove(e),this._pulseQueue();return}if(this._expired.has(e)){this.log("remove expired client"),this._expired.delete(e),this._remove(e),this._pulseQueue();return}this.options.idleTimeoutMillis&&(s=setTimeout(()=>{this.log("remove idle client"),this._remove(e)},this.options.idleTimeoutMillis),this.options.allowExitOnIdle&&s.unref()),this.options.allowExitOnIdle&&e.unref(),this._idle.push(new a(e,t,s)),this._pulseQueue()}query(e,t,r){if("function"==typeof e){let t=u(this.Promise,e);return n(function(){return t.callback(Error("Passing a function as the first parameter to pool.query is not supported"))}),t.result}"function"==typeof t&&(r=t,t=void 0);let s=u(this.Promise,r);return r=s.callback,this.connect((s,i)=>{if(s)return r(s);let n=!1,o=m(e=>{n||(n=!0,i.release(e),r(e))},"onError");i.once("error",o),this.log("dispatching query");try{i.query(e,t,(e,t)=>{if(this.log("query dispatched"),i.removeListener("error",o),!n)return n=!0,i.release(e),e?r(e):r(void 0,t)})}catch(e){return i.release(e),r(e)}}),s.result}end(e){if(this.log("ending"),this.ending){let t=Error("Called end on pool more than once");return e?e(t):this.Promise.reject(t)}this.ending=!0;let t=u(this.Promise,e);return this._endCallback=t.callback,this._pulseQueue(),t.result}get waitingCount(){return this._pendingQueue.length}get idleCount(){return this._idle.length}get expiredCount(){return this._clients.reduce((e,t)=>e+ +!!this._expired.has(t),0)}get totalCount(){return this._clients.length}};m(d,"Pool"),t.exports=d}),ez={};_(ez,{default:()=>eK});var eK,eY=g(()=>{A(),eK={}}),eZ=b((e,t)=>{t.exports={name:"pg",version:"8.8.0",description:"PostgreSQL client - pure javascript & libpq with the same API",keywords:["database","libpq","pg","postgre","postgres","postgresql","rdbms"],homepage:"https://github.com/brianc/node-postgres",repository:{type:"git",url:"git://github.com/brianc/node-postgres.git",directory:"packages/pg"},author:"Brian Carlson <<EMAIL>>",main:"./lib",dependencies:{"buffer-writer":"2.0.0","packet-reader":"1.0.0","pg-connection-string":"^2.5.0","pg-pool":"^3.5.2","pg-protocol":"^1.5.0","pg-types":"^2.1.0",pgpass:"1.x"},devDependencies:{async:"2.6.4",bluebird:"3.5.2",co:"4.6.0","pg-copy-streams":"0.3.0"},peerDependencies:{"pg-native":">=3.0.1"},peerDependenciesMeta:{"pg-native":{optional:!0}},scripts:{test:"make test-all"},files:["lib","SPONSORS.md"],license:"MIT",engines:{node:">= 8.0.0"},gitHead:"c99fb2c127ddf8d712500db2c7b9a5491a178655"}}),eX=b((e,t)=>{A();var r=T().EventEmitter,s=(P(),S(L)),i=ee(),o=t.exports=function(e,t,s){r.call(this),e=i.normalizeQueryConfig(e,t,s),this.text=e.text,this.values=e.values,this.name=e.name,this.callback=e.callback,this.state="new",this._arrayMode="array"===e.rowMode,this._emitRowEvents=!1,this.on("newListener",(function(e){"row"===e&&(this._emitRowEvents=!0)}).bind(this))};s.inherits(o,r);var a={sqlState:"code",statementPosition:"position",messagePrimary:"message",context:"where",schemaName:"schema",tableName:"table",columnName:"column",dataTypeName:"dataType",constraintName:"constraint",sourceFile:"file",sourceLine:"line",sourceFunction:"routine"};o.prototype.handleError=function(e){var t=this.native.pq.resultErrorFields();if(t)for(var r in t)e[a[r]||r]=t[r];this.callback?this.callback(e):this.emit("error",e),this.state="error"},o.prototype.then=function(e,t){return this._getPromise().then(e,t)},o.prototype.catch=function(e){return this._getPromise().catch(e)},o.prototype._getPromise=function(){return this._promise||(this._promise=new Promise((function(e,t){this._once("end",e),this._once("error",t)}).bind(this))),this._promise},o.prototype.submit=function(e){this.state="running";var t=this;this.native=e.native,e.native.arrayMode=this._arrayMode;var r=m(function(r,s,i){if(e.native.arrayMode=!1,n(function(){t.emit("_done")}),r)return t.handleError(r);t._emitRowEvents&&(i.length>1?s.forEach((e,r)=>{e.forEach(e=>{t.emit("row",e,i[r])})}):s.forEach(function(e){t.emit("row",e,i)})),t.state="end",t.emit("end",i),t.callback&&t.callback(null,i)},"after");if(h.domain&&(r=h.domain.bind(r)),this.name){this.name.length>63&&(console.error("Warning! Postgres only supports 63 characters for query names."),console.error("You supplied %s (%s)",this.name,this.name.length),console.error("This can cause conflicts and silent errors executing queries"));var s=(this.values||[]).map(i.prepareValue);if(e.namedQueries[this.name]){if(this.text&&e.namedQueries[this.name]!==this.text){let e=Error(`Pre\
pared statements must be unique - '${this.name}' was used for a different statem\
ent`);return r(e)}return e.native.execute(this.name,s,r)}return e.native.prepare(this.name,this.text,s.length,function(i){return i?r(i):(e.namedQueries[t.name]=t.text,t.native.execute(t.name,s,r))})}if(this.values){if(!Array.isArray(this.values)){let e=Error("Query values must be an array");return r(e)}var o=this.values.map(i.prepareValue);e.native.query(this.text,o,r)}else e.native.query(this.text,r)}}),eJ=b((e,t)=>{A();var r=(eY(),S(ez)),s=eb(),n=(eZ(),T().EventEmitter),o=(P(),S(L)),a=ek(),l=eX(),c=t.exports=function(e){n.call(this),e=e||{},this._Promise=e.Promise||i.Promise,this._types=new s(e.types),this.native=new r({types:this._types}),this._queryQueue=[],this._ending=!1,this._connecting=!1,this._connected=!1,this._queryable=!0;var t=this.connectionParameters=new a(e);this.user=t.user,Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:t.password}),this.database=t.database,this.host=t.host,this.port=t.port,this.namedQueries={}};c.Query=l,o.inherits(c,n),c.prototype._errorAllQueries=function(e){let t=m(t=>{h.nextTick(()=>{t.native=this.native,t.handleError(e)})},"enqueueError");this._hasActiveQuery()&&(t(this._activeQuery),this._activeQuery=null),this._queryQueue.forEach(t),this._queryQueue.length=0},c.prototype._connect=function(e){var t=this;if(this._connecting)return void h.nextTick(()=>e(Error("Client has already been connected. You cannot reuse a client.")));this._connecting=!0,this.connectionParameters.getLibpqConnectionString(function(r,s){if(r)return e(r);t.native.connect(s,function(r){if(r)return t.native.end(),e(r);t._connected=!0,t.native.on("error",function(e){t._queryable=!1,t._errorAllQueries(e),t.emit("error",e)}),t.native.on("notification",function(e){t.emit("notification",{channel:e.relname,payload:e.extra})}),t.emit("connect"),t._pulseQueryQueue(!0),e()})})},c.prototype.connect=function(e){return e?void this._connect(e):new this._Promise((e,t)=>{this._connect(r=>{r?t(r):e()})})},c.prototype.query=function(e,t,r){var s,i,n,o,a;if(null==e)throw TypeError("Client was passed a null or undefined query");if("function"==typeof e.submit)n=e.query_timeout||this.connectionParameters.query_timeout,i=s=e,"function"==typeof t&&(e.callback=t);else if(n=this.connectionParameters.query_timeout,!(s=new l(e,t,r)).callback){let e,t;i=new this._Promise((r,s)=>{e=r,t=s}),s.callback=(r,s)=>r?t(r):e(s)}return n&&(a=s.callback,o=setTimeout(()=>{var e=Error("Query read timeout");h.nextTick(()=>{s.handleError(e,this.connection)}),a(e),s.callback=()=>{};var t=this._queryQueue.indexOf(s);t>-1&&this._queryQueue.splice(t,1),this._pulseQueryQueue()},n),s.callback=(e,t)=>{clearTimeout(o),a(e,t)}),this._queryable?this._ending?(s.native=this.native,h.nextTick(()=>{s.handleError(Error("Client was closed and is not queryable"))})):(this._queryQueue.push(s),this._pulseQueryQueue()):(s.native=this.native,h.nextTick(()=>{s.handleError(Error("Client has encountered a connection error and is not queryable"))})),i},c.prototype.end=function(e){var t,r=this;return this._ending=!0,this._connected||this.once("connect",this.end.bind(this,e)),e||(t=new this._Promise(function(t,r){e=m(e=>e?r(e):t(),"cb")})),this.native.end(function(){r._errorAllQueries(Error("Connection terminated")),h.nextTick(()=>{r.emit("end"),e&&e()})}),t},c.prototype._hasActiveQuery=function(){return this._activeQuery&&"error"!==this._activeQuery.state&&"end"!==this._activeQuery.state},c.prototype._pulseQueryQueue=function(e){if(this._connected&&!this._hasActiveQuery()){var t=this._queryQueue.shift();if(!t){e||this.emit("drain");return}this._activeQuery=t,t.submit(this);var r=this;t.once("_done",function(){r._pulseQueryQueue()})}},c.prototype.cancel=function(e){this._activeQuery===e?this.native.cancel(function(){}):-1!==this._queryQueue.indexOf(e)&&this._queryQueue.splice(this._queryQueue.indexOf(e),1)},c.prototype.ref=function(){},c.prototype.unref=function(){},c.prototype.setTypeParser=function(e,t,r){return this._types.setTypeParser(e,t,r)},c.prototype.getTypeParser=function(e,t){return this._types.getTypeParser(e,t)}}),e0=b((e,t)=>{A(),t.exports=eJ()}),e1=b((e,t)=>{A();var r=eV(),s=J(),i=eG(),n=eH(),{DatabaseError:o}=ej(),a=m(e=>{var t;return m(t=class extends n{constructor(t){super(t,e)}},"BoundPool"),t},"poolFactory"),l=m(function(e){this.defaults=s,this.Client=e,this.Query=this.Client.Query,this.Pool=a(this.Client),this._pools=[],this.Connection=i,this.types=X(),this.DatabaseError=o},"PG");"u">typeof h.env.NODE_PG_FORCE_NATIVE?t.exports=new l(e0()):(t.exports=new l(r),Object.defineProperty(t.exports,"native",{configurable:!0,enumerable:!1,get(){var e=null;try{e=new l(e0())}catch(e){if("MODULE_NOT_FOUND"!==e.code)throw e}return Object.defineProperty(t.exports,"native",{value:e}),e}}))});A();var e2=w(e1());eM(),A(),ex(),eM();var e6=w(ee()),e5=w(eb()),e8=class extends Error{constructor(){super(...arguments),E(this,"name","NeonDbError"),E(this,"severity"),E(this,"code"),E(this,"detail"),E(this,"hint"),E(this,"position"),E(this,"internalPosition"),E(this,"internalQuery"),E(this,"where"),E(this,"schema"),E(this,"table"),E(this,"column"),E(this,"dataType"),E(this,"constraint"),E(this,"file"),E(this,"line"),E(this,"routine"),E(this,"sourceError")}};m(e8,"NeonDbError");var e3="transaction() expects an array of queries, or a function returning an array of queries",e4=["severity","code","detail","hint","position","internalPosition","internalQuery","where","schema","table","column","dataType","constraint","file","line","routine"];function e7(e,{arrayMode:t,fullResults:r,fetchOptions:s,isolationLevel:i,readOnly:n,deferrable:o,queryCallback:a,resultCallback:h}={}){let l;if(!e)throw Error("No database connection string was provided to `neon()`. Perhaps an environment variable has not been set?");try{l=eE(e)}catch{throw Error("Database connection string provided to `neon()` is not a valid URL. Connection string: "+String(e))}let{protocol:c,username:u,password:f,hostname:d,port:p,pathname:y}=l;if("postgres:"!==c&&"postgresql:"!==c||!u||!f||!d||!y)throw Error("Database connection string format for `neon()` should be: postgresql://user:<EMAIL>/dbname?option=value");function g(e,...t){let r,s;if("string"==typeof e)r=e,s=t[1],t=t[0]??[];else{r="";for(let s=0;s<e.length;s++)r+=e[s],s<t.length&&(r+="$"+(s+1))}let i={query:r,params:t=t.map(e=>(0,e6.prepareValue)(e))};return a&&a(i),e9(b,i,s)}async function b(a,l,c){let u,{fetchEndpoint:f,fetchFunction:y}=eB,m="function"==typeof f?f(d,p):f,g=Array.isArray(a)?{queries:a}:a,b=s??{},_=t??!1,v=r??!1,w=i,S=n,E=o;void 0!==c&&(void 0!==c.fetchOptions&&(b={...b,...c.fetchOptions}),void 0!==c.arrayMode&&(_=c.arrayMode),void 0!==c.fullResults&&(v=c.fullResults),void 0!==c.isolationLevel&&(w=c.isolationLevel),void 0!==c.readOnly&&(S=c.readOnly),void 0!==c.deferrable&&(E=c.deferrable)),void 0===l||Array.isArray(l)||void 0===l.fetchOptions||(b={...b,...l.fetchOptions});let x={"Neon-Connection-String":e,"Neon-Raw-Text-Output":"true","Neon-Array-Mode":"true"};Array.isArray(a)&&(void 0!==w&&(x["Neon-Batch-Isolation-Level"]=w),void 0!==S&&(x["Neon-Batch-Read-Only"]=String(S)),void 0!==E&&(x["Neon-Batch-Deferrable"]=String(E)));try{u=await (y??fetch)(m,{method:"POST",body:JSON.stringify(g),headers:x,...b})}catch(t){let e=new e8(`Error connecting to database: ${t.message}`);throw e.sourceError=t,e}if(u.ok){let e=await u.json();if(Array.isArray(a)){let t=e.results;if(!Array.isArray(t))throw new e8("Neon internal error: unexpected result format");return t.map((e,t)=>{let r=l[t]??{};return te(e,{arrayMode:r.arrayMode??_,fullResults:r.fullResults??v,parameterizedQuery:a[t],resultCallback:h,types:r.types})})}{let t=l??{};return te(e,{arrayMode:t.arrayMode??_,fullResults:t.fullResults??v,parameterizedQuery:a,resultCallback:h,types:t.types})}}{let{status:e}=u;if(400===e){let e=await u.json(),t=new e8(e.message);for(let r of e4)t[r]=e[r]??void 0;throw t}{let t=await u.text();throw new e8(`Server erro\
r (HTTP status ${e}): ${t}`)}}}return m(g,"resolve"),g.transaction=async(e,t)=>{if("function"==typeof e&&(e=e(g)),!Array.isArray(e))throw Error(e3);return e.forEach(e=>{if("NeonQueryPromise"!==e[Symbol.toStringTag])throw Error(e3)}),b(e.map(e=>e.parameterizedQuery),e.map(e=>e.opts??{}),t)},m(b,"execute"),g}function e9(e,t,r){return{[Symbol.toStringTag]:"NeonQueryPromise",parameterizedQuery:t,opts:r,then:m((s,i)=>e(t,r).then(s,i),"then"),catch:m(s=>e(t,r).catch(s),"catch"),finally:m(s=>e(t,r).finally(s),"finally")}}function te(e,{arrayMode:t,fullResults:r,parameterizedQuery:s,resultCallback:i,types:n}){let o=new e5.default(n),a=e.fields.map(e=>e.name),h=e.fields.map(e=>o.getTypeParser(e.dataTypeID)),l=!0===t?e.rows.map(e=>e.map((e,t)=>null===e?null:h[t](e))):e.rows.map(e=>Object.fromEntries(e.map((e,t)=>[a[t],null===e?null:h[t](e)])));return i&&i(s,e,l,{arrayMode:t,fullResults:r}),r?(e.viaNeonFetch=!0,e.rowAsArray=t,e.rows=l,e._parsers=h,e._types=o,e):l}m(e7,"neon"),m(e9,"createNeonQueryPromise"),m(te,"processQueryResult");var tt=w(ek()),tr=w(e1()),ts=class extends e2.Client{constructor(e){super(e),this.config=e}get neonConfig(){return this.connection.stream}connect(e){let{neonConfig:t}=this;t.forceDisablePgSSL&&(this.ssl=this.connection.ssl=!1),this.ssl&&t.useSecureWebSocket&&console.warn("SSL is enabled for both Postgres (e.g. ?sslmode=require in the connection string + forceDisablePgSSL = false) and the WebSocket tunnel (useSecureWebSocket = true). Double encryption will increase latency and CPU usage. It may be appropriate to disable SSL in the Postgres connection parameters or set forceDisablePgSSL = true.");let r=this.config?.host!==void 0||this.config?.connectionString!==void 0||void 0!==h.env.PGHOST,s=h.env.USER??h.env.USERNAME;if(!r&&"localhost"===this.host&&this.user===s&&this.database===s&&null===this.password)throw Error(`No datab\
ase host or connection string was set, and key parameters have default values (h\
ost: localhost, user: ${s}, db: ${s}, password: null). Is an environment variabl\
e missing? Alternatively, if you intended to connect with these parameters, plea\
se set the host to 'localhost' explicitly.`);let i=super.connect(e),n=t.pipelineTLS&&this.ssl,o="password"===t.pipelineConnect;if(!n&&!t.pipelineConnect)return i;let a=this.connection;if(n&&a.on("connect",()=>a.stream.emit("data","S")),o){a.removeAllListeners("authenticationCleartextPassword"),a.removeAllListeners("readyForQuery"),a.once("readyForQuery",()=>a.on("readyForQuery",this._handleReadyForQuery.bind(this)));let e=this.ssl?"sslconnect":"connect";a.on(e,()=>{this._handleAuthCleartextPassword(),this._handleReadyForQuery()})}return i}async _handleAuthSASLContinue(e){let t=this.saslSession,r=this.password,s=e.data;if("SASLInitialResponse"!==t.message||"string"!=typeof r||"string"!=typeof s)throw Error("SASL: protocol error");let i=Object.fromEntries(s.split(",").map(e=>{if(!/^.=/.test(e))throw Error("SASL: Invalid attribute pair entry");return[e[0],e.substring(2)]})),n=i.r,h=i.s,l=i.i;if(!n||!/^[!-+--~]+$/.test(n))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing/unprintable");if(!h||!/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.test(h))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing/not base64");if(!l||!/^[1-9][0-9]*$/.test(l))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: missing/invalid iteration count");if(!n.startsWith(t.clientNonce))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce");if(n.length===t.clientNonce.length)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce is too short");let c=parseInt(l,10),u=a.from(h,"base64"),f=new TextEncoder,d=f.encode(r),p=await o.subtle.importKey("raw",d,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),y=new Uint8Array(await o.subtle.sign("HMAC",p,a.concat([u,a.from([0,0,0,1])]))),m=y;for(var g=0;g<c-1;g++)y=new Uint8Array(await o.subtle.sign("HMAC",p,y)),m=a.from(m.map((e,t)=>m[t]^y[t]));let b=m,_=await o.subtle.importKey("raw",b,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),v=new Uint8Array(await o.subtle.sign("HMAC",_,f.encode("Client Key"))),w=await o.subtle.digest("SHA-256",v),S="n=*,r="+t.clientNonce,E="r="+n+",s="+h+",i="+c,x="c=biws,r="+n,C=S+","+E+","+x,k=await o.subtle.importKey("raw",w,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]);var A=new Uint8Array(await o.subtle.sign("HMAC",k,f.encode(C))),T=a.from(v.map((e,t)=>v[t]^A[t])).toString("base64");let L=await o.subtle.importKey("raw",b,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),I=await o.subtle.sign("HMAC",L,f.encode("Server Key")),P=await o.subtle.importKey("raw",I,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]);var R=a.from(await o.subtle.sign("HMAC",P,f.encode(C)));t.message="SASLResponse",t.serverSignature=R.toString("base64"),t.response=x+",p="+T,this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)}};m(ts,"NeonClient");var ti=ts;function tn(e,t){let r,s;return t?{callback:t,result:void 0}:{callback:m(function(e,t){e?r(e):s(t)},"cb"),result:new e(function(e,t){s=e,r=t})}}m(tn,"promisify");var to=class extends e2.Pool{constructor(){super(...arguments),E(this,"Client",ti),E(this,"hasFetchUnsupportedListeners",!1)}on(e,t){return"error"!==e&&(this.hasFetchUnsupportedListeners=!0),super.on(e,t)}query(e,t,r){if(!eB.poolQueryViaFetch||this.hasFetchUnsupportedListeners||"function"==typeof e)return super.query(e,t,r);"function"==typeof t&&(r=t,t=void 0);let s=tn(this.Promise,r);r=s.callback;try{let s=new tt.default(this.options),i=encodeURIComponent,n=encodeURI,o=`postgresql://${i(s.user)}:${i(s.password)}@${i(s.host)}/${n(s.database)}`,a="string"==typeof e?e:e.text,h=t??e.values??[];e7(o,{fullResults:!0,arrayMode:"array"===e.rowMode})(a,h,{types:e.types??this.options?.types}).then(e=>r(void 0,e)).catch(e=>r(e))}catch(e){r(e)}return s.result}};m(to,"NeonPool");var ta=to;tr.ClientBase,tr.Connection,tr.DatabaseError,tr.Query,tr.defaults,tr.types;var th=class extends Error{constructor(e,t){super(`VercelPostgresError - '${e}': ${t}`),this.code=e,this.name="VercelPostgresError"}};function tl(e,...t){var r,s,i;if(!(Array.isArray(i=e)&&"raw"in i&&Array.isArray(i.raw))||!Array.isArray(t))throw new th("incorrect_tagged_template_call","It looks like you tried to call `sql` as a function. Make sure to use it as a tagged template.\n	Example: sql`SELECT * FROM users`, not sql('SELECT * FROM users')");let n=null!=(r=e[0])?r:"";for(let t=1;t<e.length;t++)n+=`$${t}${null!=(s=e[t])?s:""}`;return[n,t]}var tc=class extends ti{async sql(e,...t){let[r,s]=tl(e,...t);return this.query(r,s)}},tu=class extends ta{constructor(e){var t;super(e),this.Client=tc,this.connectionString=null!=(t=e.connectionString)?t:""}async sql(e,...t){let[r,s]=tl(e,...t);return e7(this.connectionString,{fullResults:!0})(r,s)}connect(e){return super.connect(e)}};function tf(e){var t;let r=null!=(t=null==e?void 0:e.connectionString)?t:function(e="pool"){let t;switch(e){case"pool":t=process.env.POSTGRES_URL;break;case"direct":t=process.env.POSTGRES_URL_NON_POOLING;break;default:throw new th("invalid_connection_type",`Unhandled type: ${e}`)}return"undefined"===t&&(t=void 0),t}("pool");if(!r)throw new th("missing_connection_string","You did not supply a 'connectionString' and no 'POSTGRES_URL' env var was found.");if(!function(e){try{let t=e.replace(/^postgresql:\/\//,"https://");return"localhost"===new URL(t).hostname}catch(e){if(e instanceof TypeError||"object"==typeof e&&null!==e&&"message"in e&&"string"==typeof e.message&&"Invalid URL"===e.message)return!1;throw e}}(r)&&!r.includes("-pooler."))throw new th("invalid_connection_string","This connection string is meant to be used with a direct connection. Make sure to use a pooled connection string or try `createClient()` instead.");let s=null==e?void 0:e.maxUses,i=null==e?void 0:e.max;return"undefined"!=typeof EdgeRuntime&&(s&&1!==s&&console.warn("@vercel/postgres: Overriding `maxUses` to 1 because the EdgeRuntime does not support client reuse."),i&&1e4!==i&&console.warn("@vercel/postgres: Overriding `max` to 10,000 because the EdgeRuntime does not support client reuse."),s=1,i=1e4),new tu({...e,connectionString:r,maxUses:s,max:i})}var td=new Proxy(()=>{},{get(e,t){s||(s=tf());let r=Reflect.get(s,t);return"function"==typeof r?r.bind(s):r},apply:(e,t,r)=>(s||(s=tf()),s.sql(...r))});r(37796),r(15997),r(95773);var tp=r(32113);r(16809),eB&&(eB.webSocketConstructor=tp)},85514:e=>{"use strict";e.exports={mask:(e,t,r,s,i)=>{for(var n=0;n<i;n++)r[s+n]=e[n]^t[3&n]},unmask:(e,t)=>{let r=e.length;for(var s=0;s<r;s++)e[s]^=t[3&s]}}},91596:(e,t,r)=>{"use strict";let{tokenChars:s}=r(58571);e.exports={parse:function(e){let t=new Set,r=-1,i=-1,n=0;for(;n<e.length;n++){let o=e.charCodeAt(n);if(-1===i&&1===s[o])-1===r&&(r=n);else if(0!==n&&(32===o||9===o))-1===i&&-1!==r&&(i=n);else if(44===o){if(-1===r)throw SyntaxError(`Unexpected character at index ${n}`);-1===i&&(i=n);let s=e.slice(r,i);if(t.has(s))throw SyntaxError(`The "${s}" subprotocol is duplicated`);t.add(s),r=i=-1}else throw SyntaxError(`Unexpected character at index ${n}`)}if(-1===r||-1!==i)throw SyntaxError("Unexpected end of input");let o=e.slice(r,n);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},95773:(e,t,r)=>{"use strict";let s,{Duplex:i}=r(27910),{randomFillSync:n}=r(55511),o=r(2968),{EMPTY_BUFFER:a,kWebSocket:h,NOOP:l}=r(32435),{isBlob:c,isValidStatusCode:u}=r(58571),{mask:f,toBuffer:d}=r(46471),p=Symbol("kByteLength"),y=Buffer.alloc(4),m=8192;class g{constructor(e,t,r){this._extensions=t||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=l,this[h]=void 0}static frame(e,t){let r,i,o=!1,a=2,h=!1;t.mask&&(r=t.maskBuffer||y,t.generateMask?t.generateMask(r):(8192===m&&(void 0===s&&(s=Buffer.alloc(8192)),n(s,0,8192),m=0),r[0]=s[m++],r[1]=s[m++],r[2]=s[m++],r[3]=s[m++]),h=(r[0]|r[1]|r[2]|r[3])==0,a=6),"string"==typeof e?i=(!t.mask||h)&&void 0!==t[p]?t[p]:(e=Buffer.from(e)).length:(i=e.length,o=t.mask&&t.readOnly&&!h);let l=i;i>=65536?(a+=8,l=127):i>125&&(a+=2,l=126);let c=Buffer.allocUnsafe(o?i+a:a);return(c[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(c[0]|=64),c[1]=l,126===l?c.writeUInt16BE(i,2):127===l&&(c[2]=c[3]=0,c.writeUIntBE(i,4,6)),t.mask)?(c[1]|=128,c[a-4]=r[0],c[a-3]=r[1],c[a-2]=r[2],c[a-1]=r[3],h)?[c,e]:o?(f(e,r,c,a,i),[c]):(f(e,r,e,0,i),[c,e]):[c,e]}close(e,t,r,s){let i;if(void 0===e)i=a;else if("number"==typeof e&&u(e))if(void 0!==t&&t.length){let r=Buffer.byteLength(t);if(r>123)throw RangeError("The message must not be greater than 123 bytes");(i=Buffer.allocUnsafe(2+r)).writeUInt16BE(e,0),"string"==typeof t?i.write(t,2):i.set(t,2)}else(i=Buffer.allocUnsafe(2)).writeUInt16BE(e,0);else throw TypeError("First argument must be a valid error code number");let n={[p]:i.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,i,!1,n,s]):this.sendFrame(g.frame(i,n),s)}ping(e,t,r){let s,i;if("string"==typeof e?(s=Buffer.byteLength(e),i=!1):c(e)?(s=e.size,i=!1):(s=(e=d(e)).length,i=d.readOnly),s>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[p]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,n,r]):this.getBlobData(e,!1,n,r):0!==this._state?this.enqueue([this.dispatch,e,!1,n,r]):this.sendFrame(g.frame(e,n),r)}pong(e,t,r){let s,i;if("string"==typeof e?(s=Buffer.byteLength(e),i=!1):c(e)?(s=e.size,i=!1):(s=(e=d(e)).length,i=d.readOnly),s>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[p]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,n,r]):this.getBlobData(e,!1,n,r):0!==this._state?this.enqueue([this.dispatch,e,!1,n,r]):this.sendFrame(g.frame(e,n),r)}send(e,t,r){let s,i,n=this._extensions[o.extensionName],a=t.binary?2:1,h=t.compress;"string"==typeof e?(s=Buffer.byteLength(e),i=!1):c(e)?(s=e.size,i=!1):(s=(e=d(e)).length,i=d.readOnly),this._firstFragment?(this._firstFragment=!1,h&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(h=s>=n._threshold),this._compress=h):(h=!1,a=0),t.fin&&(this._firstFragment=!0);let l={[p]:s,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:h};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,this._compress,l,r]):this.getBlobData(e,this._compress,l,r):0!==this._state?this.enqueue([this.dispatch,e,this._compress,l,r]):this.dispatch(e,this._compress,l,r)}getBlobData(e,t,r,s){this._bufferedBytes+=r[p],this._state=2,e.arrayBuffer().then(e=>{if(this._socket.destroyed){let e=Error("The socket was closed while the blob was being read");process.nextTick(b,this,e,s);return}this._bufferedBytes-=r[p];let i=d(e);t?this.dispatch(i,t,r,s):(this._state=0,this.sendFrame(g.frame(i,r),s),this.dequeue())}).catch(e=>{process.nextTick(_,this,e,s)})}dispatch(e,t,r,s){if(!t)return void this.sendFrame(g.frame(e,r),s);let i=this._extensions[o.extensionName];this._bufferedBytes+=r[p],this._state=1,i.compress(e,r.fin,(e,t)=>{if(this._socket.destroyed)return void b(this,Error("The socket was closed while data was being compressed"),s);this._bufferedBytes-=r[p],this._state=0,r.readOnly=!1,this.sendFrame(g.frame(t,r),s),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][p],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][p],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}function b(e,t,r){"function"==typeof r&&r(t);for(let r=0;r<e._queue.length;r++){let s=e._queue[r],i=s[s.length-1];"function"==typeof i&&i(t)}}function _(e,t,r){b(e,t,r),e.onerror(t)}e.exports=g}};