(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4177],{2260:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(5155),a=r(2115),n=r(5695);function u(){let e=(0,n.useRouter)();return(0,a.useEffect)(()=>{e.replace("/admin/dashboard?section=health")},[e]),(0,t.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Redirecting to system health monitoring..."})]})})}},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},5985:(e,s,r)=>{Promise.resolve().then(r.bind(r,2260))}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(5985)),_N_E=e.O()}]);