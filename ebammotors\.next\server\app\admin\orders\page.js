(()=>{var e={};e.id=4798,e.ids=[4798],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11095:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["admin",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58322)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\orders\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\orders\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/orders/page",pathname:"/admin/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19330:(e,t,r)=>{Promise.resolve().then(r.bind(r,58322))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37482:(e,t,r)=>{Promise.resolve().then(r.bind(r,74652))},37652:(e,t,r)=>{Promise.resolve().then(r.bind(r,99111))},44263:()=>{},58322:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\admin\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\orders\\page.tsx","default")},61193:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(60687),a=r(85814),n=r.n(a),o=r(43210);function i({children:e}){let[t,r]=(0,o.useState)(!1);return t?(0,s.jsxs)("div",{className:"min-h-screen bg-neutral-50 admin-layout",children:[(0,s.jsx)("header",{className:"bg-white/95 backdrop-blur-sm shadow-sm border-b border-neutral-200 sticky top-0 z-50 transition-all duration-300",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-sm",children:"A"})}),(0,s.jsx)("h1",{className:"text-xl font-bold text-neutral-800",children:"EBAM Motors Admin"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("span",{className:"text-sm text-neutral-600",children:"Admin Panel"}),(0,s.jsx)(n(),{href:"/",className:"text-sm text-primary-600 hover:text-primary-700 font-medium",children:"← Back to Website"})]})]})})}),(0,s.jsx)("main",{className:"py-8",children:e}),(0,s.jsx)("footer",{className:"bg-white border-t border-neutral-200 mt-auto",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsx)("div",{className:"text-center text-sm text-neutral-500",children:"EBAM Motors Admin Panel - Handle with care"})})})]}):(0,s.jsx)("div",{className:"min-h-screen bg-neutral-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading admin panel..."})]})})}r(44263)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74652:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(60687);r(43210);var a=r(16189);function n(){return(0,a.useRouter)(),(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Redirecting to orders management..."})]})})}},79551:e=>{"use strict";e.exports=require("url")},90388:(e,t,r)=>{Promise.resolve().then(r.bind(r,61193))},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\layout.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7445,1658,5814,5839],()=>r(11095));module.exports=s})();