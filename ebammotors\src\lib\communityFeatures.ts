// Community Features for EBAM Motors
// Forums, user-generated content, loyalty programs, and customer engagement

export interface ForumPost {
  id: string;
  title: string;
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  category: string;
  tags: string[];
  likes: number;
  replies: number;
  views: number;
  isPinned: boolean;
  isLocked: boolean;
  createdAt: string;
  updatedAt: string;
  lastReplyAt?: string;
  images?: string[];
}

export interface ForumReply {
  id: string;
  postId: string;
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  likes: number;
  createdAt: string;
  updatedAt: string;
  parentReplyId?: string;
  images?: string[];
}

export interface LoyaltyProgram {
  id: string;
  userId: string;
  points: number;
  tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  totalSpent: number;
  totalOrders: number;
  joinDate: string;
  lastActivity: string;
  benefits: string[];
  nextTierPoints: number;
  referralCode: string;
  referrals: number;
}

export interface UserGeneratedContent {
  id: string;
  type: 'photo' | 'video' | 'review' | 'story';
  title: string;
  content: string;
  mediaUrl?: string;
  thumbnailUrl?: string;
  authorId: string;
  authorName: string;
  carId?: string;
  carModel?: string;
  tags: string[];
  likes: number;
  shares: number;
  comments: number;
  isApproved: boolean;
  isFeatured: boolean;
  createdAt: string;
  location?: string;
}

export interface CustomerTestimonial {
  id: string;
  customerId: string;
  customerName: string;
  customerLocation: string;
  customerAvatar?: string;
  orderId?: string;
  carModel?: string;
  rating: number;
  title: string;
  content: string;
  images?: string[];
  videoUrl?: string;
  isVerified: boolean;
  isFeatured: boolean;
  createdAt: string;
  helpfulVotes: number;
}

// Forum categories
export const FORUM_CATEGORIES = [
  { id: 'general', name: 'General Discussion', description: 'General topics about cars and EBAM Motors' },
  { id: 'buying-guide', name: 'Buying Guide', description: 'Tips and advice for buying used cars from Japan' },
  { id: 'shipping', name: 'Shipping & Logistics', description: 'Questions about shipping and delivery' },
  { id: 'maintenance', name: 'Car Maintenance', description: 'Car maintenance tips and advice' },
  { id: 'success-stories', name: 'Success Stories', description: 'Share your EBAM Motors experience' },
  { id: 'technical', name: 'Technical Support', description: 'Technical questions and support' },
];

// Loyalty program tiers and benefits
export const LOYALTY_TIERS = {
  Bronze: {
    minPoints: 0,
    benefits: ['5% discount on shipping', 'Priority customer support'],
    color: '#CD7F32',
  },
  Silver: {
    minPoints: 1000,
    benefits: ['10% discount on shipping', 'Free vehicle inspection report', 'Early access to new arrivals'],
    color: '#C0C0C0',
  },
  Gold: {
    minPoints: 5000,
    benefits: ['15% discount on shipping', 'Free extended warranty', 'Personal car consultant', 'VIP customer events'],
    color: '#FFD700',
  },
  Platinum: {
    minPoints: 15000,
    benefits: ['20% discount on shipping', 'Free premium insurance', 'Dedicated account manager', 'Exclusive car previews'],
    color: '#E5E4E2',
  },
};

// Get forum posts
export const getForumPosts = async (
  category?: string,
  page: number = 1,
  limit: number = 20,
  sortBy: 'latest' | 'popular' | 'replies' = 'latest'
): Promise<{
  posts: ForumPost[];
  total: number;
  page: number;
  totalPages: number;
}> => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sortBy,
    });
    
    if (category) params.set('category', category);

    const response = await fetch(`/api/community/forum/posts?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch forum posts');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching forum posts:', error);
    throw error;
  }
};

// Create forum post
export const createForumPost = async (
  title: string,
  content: string,
  category: string,
  tags: string[] = [],
  images: string[] = []
): Promise<ForumPost> => {
  try {
    const response = await fetch('/api/community/forum/posts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title,
        content,
        category,
        tags,
        images,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to create forum post');
    }

    const data = await response.json();
    return data.post;
  } catch (error) {
    console.error('Error creating forum post:', error);
    throw error;
  }
};

// Get user loyalty program status
export const getLoyaltyStatus = async (userId: string): Promise<LoyaltyProgram> => {
  try {
    const response = await fetch(`/api/community/loyalty/${userId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch loyalty status');
    }

    const data = await response.json();
    return data.loyalty;
  } catch (error) {
    console.error('Error fetching loyalty status:', error);
    throw error;
  }
};

// Add loyalty points
export const addLoyaltyPoints = async (
  userId: string,
  points: number,
  reason: string,
  orderId?: string
): Promise<void> => {
  try {
    await fetch('/api/community/loyalty/points', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        points,
        reason,
        orderId,
      }),
    });
  } catch (error) {
    console.error('Error adding loyalty points:', error);
    throw error;
  }
};

// Get user-generated content
export const getUserGeneratedContent = async (
  type?: 'photo' | 'video' | 'review' | 'story',
  featured: boolean = false,
  limit: number = 20
): Promise<UserGeneratedContent[]> => {
  try {
    const params = new URLSearchParams({
      limit: limit.toString(),
      featured: featured.toString(),
    });
    
    if (type) params.set('type', type);

    const response = await fetch(`/api/community/ugc?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch user-generated content');
    }

    const data = await response.json();
    return data.content;
  } catch (error) {
    console.error('Error fetching user-generated content:', error);
    return [];
  }
};

// Submit user-generated content
export const submitUserContent = async (
  type: 'photo' | 'video' | 'review' | 'story',
  title: string,
  content: string,
  mediaUrl?: string,
  carId?: string,
  tags: string[] = []
): Promise<UserGeneratedContent> => {
  try {
    const response = await fetch('/api/community/ugc', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type,
        title,
        content,
        mediaUrl,
        carId,
        tags,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to submit content');
    }

    const data = await response.json();
    return data.content;
  } catch (error) {
    console.error('Error submitting user content:', error);
    throw error;
  }
};

// Get customer testimonials
export const getCustomerTestimonials = async (
  featured: boolean = false,
  limit: number = 10
): Promise<CustomerTestimonial[]> => {
  try {
    const params = new URLSearchParams({
      limit: limit.toString(),
      featured: featured.toString(),
    });

    const response = await fetch(`/api/community/testimonials?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch testimonials');
    }

    const data = await response.json();
    return data.testimonials;
  } catch (error) {
    console.error('Error fetching testimonials:', error);
    return [];
  }
};

// Submit customer testimonial
export const submitTestimonial = async (
  orderId: string,
  rating: number,
  title: string,
  content: string,
  images: string[] = [],
  videoUrl?: string
): Promise<CustomerTestimonial> => {
  try {
    const response = await fetch('/api/community/testimonials', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orderId,
        rating,
        title,
        content,
        images,
        videoUrl,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to submit testimonial');
    }

    const data = await response.json();
    return data.testimonial;
  } catch (error) {
    console.error('Error submitting testimonial:', error);
    throw error;
  }
};

// Calculate loyalty tier
export const calculateLoyaltyTier = (points: number): keyof typeof LOYALTY_TIERS => {
  if (points >= LOYALTY_TIERS.Platinum.minPoints) return 'Platinum';
  if (points >= LOYALTY_TIERS.Gold.minPoints) return 'Gold';
  if (points >= LOYALTY_TIERS.Silver.minPoints) return 'Silver';
  return 'Bronze';
};

// Get points needed for next tier
export const getPointsToNextTier = (currentPoints: number): number => {
  const currentTier = calculateLoyaltyTier(currentPoints);
  const tiers = Object.entries(LOYALTY_TIERS);
  const currentIndex = tiers.findIndex(([tier]) => tier === currentTier);
  
  if (currentIndex < tiers.length - 1) {
    const nextTier = tiers[currentIndex + 1][1];
    return nextTier.minPoints - currentPoints;
  }
  
  return 0; // Already at highest tier
};

// Generate referral code
export const generateReferralCode = (userId: string): string => {
  const prefix = 'EBAM';
  const suffix = userId.slice(-4).toUpperCase();
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}${suffix}${random}`;
};

// Track community engagement
export const trackCommunityEngagement = async (
  action: string,
  contentType: string,
  contentId: string,
  userId?: string
): Promise<void> => {
  try {
    await fetch('/api/community/engagement', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action,
        contentType,
        contentId,
        userId,
        timestamp: new Date().toISOString(),
      }),
    });
  } catch (error) {
    console.error('Error tracking community engagement:', error);
  }
};
