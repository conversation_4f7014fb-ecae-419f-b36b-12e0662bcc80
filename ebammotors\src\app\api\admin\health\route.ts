import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import {
  getHealthStatus,
  getPerformanceStats,
  getRecentErrors,
  checkDatabaseHealth,
  getMemoryUsage
} from '@/lib/monitoring';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const detailed = searchParams.get('detailed') === 'true';
    const timeRange = parseInt(searchParams.get('timeRange') || '3600000'); // Default 1 hour

    // Get comprehensive health information
    const [healthStatus, performanceStats, recentErrors, dbHealth, memoryUsage] = await Promise.all([
      getHealthStatus(),
      getPerformanceStats(timeRange),
      getRecentErrors(20),
      checkDatabaseHealth(),
      getMemoryUsage()
    ]);

    // Format the response to match the expected structure
    const healthData = {
      status: healthStatus.status,
      timestamp: healthStatus.timestamp,
      uptime: healthStatus.uptime,
      issues: healthStatus.issues,
      stats: healthStatus.stats,
      database: dbHealth,
      memory: memoryUsage,
      recentErrors: recentErrors,
      performance: performanceStats,
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        uptime: process.uptime(),
        pid: process.pid
      }
    };

    const response = NextResponse.json(healthData);

    // Add security headers
    const { getSecurityHeaders } = await import('@/lib/security');
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error) {
    console.error('Health check error:', error);
    
    const response = NextResponse.json(
      { 
        success: false, 
        message: 'Health check failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );

    // Add security headers even for errors
    const { getSecurityHeaders } = await import('@/lib/security');
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  }
}
