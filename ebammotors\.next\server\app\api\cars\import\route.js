(()=>{var e={};e.id=8929,e.ids=[843,7990,8929],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,r)=>{"use strict";r.d(t,{$z:()=>l,AE:()=>u,PY:()=>n,getAllCars:()=>o,iU:()=>p,jB:()=>c,pe:()=>d});var s=r(83376);let i=()=>!!(process.env.POSTGRES_URL||process.env.DATABASE_URL);function a(e){return{id:e.id,car_id:e.car_id,make:e.make,model:e.model,year:e.year,title:e.title,price:e.price,original_price:e.original_price,currency:e.currency||"JPY",mileage:e.mileage,fuel_type:e.fuel_type,transmission:e.transmission,engine_size:e.engine_size,drive_type:e.drive_type,seats:e.seats,doors:e.doors,body_type:e.body_type,body_condition:e.body_condition,interior_condition:e.interior_condition,exterior_color:e.exterior_color,interior_color:e.interior_color,main_image:e.main_image,images:e.images||[],image_folder:e.image_folder,specs:e.specs||[],features:e.features||[],status:e.status,stock_quantity:e.stock_quantity,location:e.location,slug:e.slug,description:e.description,meta_title:e.meta_title,meta_description:e.meta_description,view_count:e.view_count,popularity_score:e.popularity_score,is_featured:e.is_featured,is_recently_added:e.is_recently_added,is_price_reduced:e.is_price_reduced,import_batch_id:e.import_batch_id,import_source:e.import_source,import_notes:e.import_notes,added_date:e.added_date,updated_date:e.updated_date,sold_date:e.sold_date,created_at:e.created_at,updated_at:e.updated_at}}async function o(e={},t=1,r=50){if(!i())throw Error("Database not available");try{let i=[],o=[],n=1;e.make&&(i.push(`make ILIKE $${n}`),o.push(`%${e.make}%`),n++),e.model&&(i.push(`model ILIKE $${n}`),o.push(`%${e.model}%`),n++),e.year_min&&(i.push(`year >= $${n}`),o.push(e.year_min),n++),e.year_max&&(i.push(`year <= $${n}`),o.push(e.year_max),n++),e.price_min&&(i.push(`price >= $${n}`),o.push(e.price_min),n++),e.price_max&&(i.push(`price <= $${n}`),o.push(e.price_max),n++),e.mileage_max&&(i.push(`mileage <= $${n}`),o.push(e.mileage_max),n++),e.fuel_type&&(i.push(`fuel_type = $${n}`),o.push(e.fuel_type),n++),e.transmission&&(i.push(`transmission = $${n}`),o.push(e.transmission),n++),e.body_condition&&(i.push(`body_condition = $${n}`),o.push(e.body_condition),n++),e.status&&(i.push(`status = $${n}`),o.push(e.status),n++),void 0!==e.is_featured&&(i.push(`is_featured = $${n}`),o.push(e.is_featured),n++),void 0!==e.is_recently_added&&(i.push(`is_recently_added = $${n}`),o.push(e.is_recently_added),n++),e.search_query&&(i.push(`(
        title ILIKE $${n} OR 
        make ILIKE $${n} OR 
        model ILIKE $${n} OR 
        description ILIKE $${n}
      )`),o.push(`%${e.search_query}%`),n++);let c=i.length>0?`WHERE ${i.join(" AND ")}`:"",l=`SELECT COUNT(*) as total FROM cars ${c}`,u=await s.sql.query(l,o),p=parseInt(u.rows[0].total),d=`
      SELECT * FROM cars 
      ${c}
      ORDER BY added_date DESC, created_at DESC
      LIMIT $${n} OFFSET $${n+1}
    `;o.push(r,(t-1)*r);let{rows:m}=await s.sql.query(d,o);return{cars:m.map(a),total_count:p,page:t,per_page:r,total_pages:Math.ceil(p/r),filters_applied:e}}catch(e){throw console.error("Error fetching cars:",e),Error("Failed to fetch cars from database")}}async function n(e){if(!i())throw Error("Database not available");try{let{rows:t}=await (0,s.sql)`
      SELECT * FROM cars WHERE id = ${e}
    `;if(0===t.length)return null;return a(t[0])}catch(e){throw console.error("Error fetching car by ID:",e),Error("Failed to fetch car from database")}}async function c(e){if(!i())throw Error("Database not available");try{let{rows:t}=await (0,s.sql)`
      SELECT * FROM cars WHERE car_id = ${e}
    `;if(0===t.length)return null;return a(t[0])}catch(e){throw console.error("Error fetching car by car_id:",e),Error("Failed to fetch car from database")}}async function l(e){if(!i())throw Error("Database not available");try{let t=e.slug||function(e,t,r,s){let i=`${e}-${t}-${r}`.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,""),a=s.split("_").pop()||Math.random().toString(36).substr(2,5);return`${i}-${a}`}(e.make,e.model,e.year,e.car_id),{rows:r}=await (0,s.sql)`
      INSERT INTO cars (
        car_id, make, model, year, title, price, original_price, currency,
        mileage, fuel_type, transmission, engine_size, drive_type, seats, doors, body_type,
        body_condition, interior_condition, exterior_color, interior_color,
        main_image, images, image_folder, specs, features,
        status, stock_quantity, location, slug, description, meta_title, meta_description,
        is_featured, import_batch_id, import_source, import_notes
      ) VALUES (
        ${e.car_id}, ${e.make}, ${e.model}, ${e.year}, ${e.title},
        ${e.price}, ${e.original_price||null}, ${e.currency||"JPY"},
        ${e.mileage||null}, ${e.fuel_type||null}, ${e.transmission||null},
        ${e.engine_size||null}, ${e.drive_type||null}, ${e.seats||null},
        ${e.doors||null}, ${e.body_type||null},
        ${e.body_condition||"Good"}, ${e.interior_condition||"Good"},
        ${e.exterior_color||null}, ${e.interior_color||null},
        ${e.main_image||null}, ${JSON.stringify(e.images||[])}, ${e.image_folder||null},
        ${JSON.stringify(e.specs||[])}, ${JSON.stringify(e.features||[])},
        ${e.status||"Available"}, ${e.stock_quantity||1}, ${e.location||"Japan"},
        ${t}, ${e.description||null}, ${e.meta_title||null}, ${e.meta_description||null},
        ${e.is_featured||!1}, ${e.import_batch_id||null}, ${e.import_source||"Manual"},
        ${e.import_notes||null}
      )
      RETURNING *
    `;return a(r[0])}catch(t){if(console.error("Error creating car:",t),t instanceof Error&&t.message.includes("duplicate key"))throw Error(`Car with ID '${e.car_id}' already exists`);throw Error("Failed to create car in database")}}async function u(e,t){if(!i())throw Error("Database not available");try{let r=[],i=[],o=1;if(Object.entries(t).forEach(([e,t])=>{void 0!==t&&("images"===e||"specs"===e||"features"===e?(r.push(`${e} = $${o}`),i.push(JSON.stringify(t))):(r.push(`${e} = $${o}`),i.push(t)),o++)}),0===r.length)throw Error("No fields to update");r.push("updated_at = NOW()"),r.push("updated_date = NOW()"),i.push(e);let n=`
      UPDATE cars
      SET ${r.join(", ")}
      WHERE id = $${o}
      RETURNING *
    `,{rows:c}=await s.sql.query(n,i);if(0===c.length)return null;return a(c[0])}catch(e){throw console.error("Error updating car:",e),Error("Failed to update car in database")}}async function p(e){if(!i())throw Error("Database not available");try{return(await (0,s.sql)`
      DELETE FROM cars WHERE id = ${e}
    `).rowCount>0}catch(e){throw console.error("Error deleting car:",e),Error("Failed to delete car from database")}}async function d(e){if(!i())throw Error("Database not available");if(0===e.length)return 0;try{let t=e.map((e,t)=>`$${t+1}`).join(","),r=`DELETE FROM cars WHERE id IN (${t})`;return(await s.sql.query(r,e)).rowCount||0}catch(e){throw console.error("Error bulk deleting cars:",e),Error("Failed to bulk delete cars from database")}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{"use strict";e.exports=require("os")},23870:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(55511);let i={randomUUID:s.randomUUID},a=new Uint8Array(256),o=a.length,n=[];for(let e=0;e<256;++e)n.push((e+256).toString(16).slice(1));let c=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();let c=(e=e||{}).random??e.rng?.()??(o>a.length-16&&((0,s.randomFillSync)(a),o=0),a.slice(o,o+=16));if(c.length<16)throw Error("Random bytes length must be >= 16");if(c[6]=15&c[6]|64,c[8]=63&c[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=c[e];return t}return function(e,t=0){return(n[e[t+0]]+n[e[t+1]]+n[e[t+2]]+n[e[t+3]]+"-"+n[e[t+4]]+n[e[t+5]]+"-"+n[e[t+6]]+n[e[t+7]]+"-"+n[e[t+8]]+n[e[t+9]]+"-"+n[e[t+10]]+n[e[t+11]]+n[e[t+12]]+n[e[t+13]]+n[e[t+14]]+n[e[t+15]]).toLowerCase()}(c)}},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99882:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>y,serverHooks:()=>w,workAsyncStorage:()=>$,workUnitAsyncStorage:()=>E});var s={};r.r(s),r.d(s,{GET:()=>g,POST:()=>h});var i=r(96559),a=r(48088),o=r(37719),n=r(32190),c=r(83376),l=r(6710),u=r(23870);async function p(e,t,r,s){try{let{rows:i}=await (0,c.sql)`
      INSERT INTO csv_imports (
        batch_id, filename, total_rows, imported_by, status
      ) VALUES (
        ${e}, ${t}, ${r}, ${s}, 'processing'
      )
      RETURNING *
    `;return{id:i[0].id,batch_id:i[0].batch_id,filename:i[0].filename,total_rows:i[0].total_rows,successful_imports:i[0].successful_imports,failed_imports:i[0].failed_imports,status:i[0].status,error_log:i[0].error_log||[],import_summary:i[0].import_summary||{},imported_by:i[0].imported_by,started_at:i[0].started_at,completed_at:i[0].completed_at,created_at:i[0].created_at}}catch(e){throw console.error("Error creating CSV import record:",e),Error("Failed to create CSV import record")}}async function d(e,t){try{let r=[],s=[],i=1;void 0!==t.successful_imports&&(r.push(`successful_imports = $${i}`),s.push(t.successful_imports),i++),void 0!==t.failed_imports&&(r.push(`failed_imports = $${i}`),s.push(t.failed_imports),i++),t.status&&(r.push(`status = $${i}`),s.push(t.status),i++),t.error_log&&(r.push(`error_log = $${i}`),s.push(JSON.stringify(t.error_log)),i++),t.import_summary&&(r.push(`import_summary = $${i}`),s.push(JSON.stringify(t.import_summary)),i++),("completed"===t.status||"failed"===t.status)&&r.push("completed_at = NOW()"),s.push(e);let a=`
      UPDATE csv_imports
      SET ${r.join(", ")}
      WHERE batch_id = $${i}
    `;await c.sql.query(a,s)}catch(e){throw console.error("Error updating CSV import record:",e),Error("Failed to update CSV import record")}}async function m(e,t,r){let s=(0,u.A)(),i=[],a=[],o=0,n=0;try{let c=function(e){let t=e.trim().split("\n");if(t.length<2)throw Error("CSV file must contain at least a header row and one data row");let r=t[0].split(",").map(e=>e.trim().toLowerCase()),s=[],i=["car_id","make","model","year","price"].filter(e=>!r.includes(e));if(i.length>0)throw Error(`Missing required headers: ${i.join(", ")}`);for(let e=1;e<t.length;e++){let i=t[e].split(",").map(e=>e.trim());if(i.length!==r.length)throw Error(`Row ${e+1}: Column count mismatch. Expected ${r.length}, got ${i.length}`);let a={};r.forEach((e,t)=>{a[e]=i[t]}),s.push(a)}return s}(e);await p(s,t,c.length,r);for(let e=0;e<c.length;e++){let t=c[e],r=e+2;try{let e=function(e,t){let r=[];e.car_id&&""!==e.car_id.trim()||r.push(`Row ${t}: car_id is required`),e.make&&""!==e.make.trim()||r.push(`Row ${t}: make is required`),e.model&&""!==e.model.trim()||r.push(`Row ${t}: model is required`);let s="string"==typeof e.year?parseInt(e.year):e.year;(!s||isNaN(s)||s<1990||s>2030)&&r.push(`Row ${t}: year must be a valid number between 1990 and 2030`);let i="string"==typeof e.price?parseFloat(e.price.replace(/[,¥$]/g,"")):e.price;if((!i||isNaN(i)||i<=0)&&r.push(`Row ${t}: price must be a positive number`),e.mileage){let s="string"==typeof e.mileage?parseInt(e.mileage):e.mileage;(isNaN(s)||s<0)&&r.push(`Row ${t}: mileage must be a non-negative number`)}if(e.seats){let s="string"==typeof e.seats?parseInt(e.seats):e.seats;(isNaN(s)||s<1||s>50)&&r.push(`Row ${t}: seats must be between 1 and 50`)}if(e.doors){let s="string"==typeof e.doors?parseInt(e.doors):e.doors;(isNaN(s)||s<1||s>10)&&r.push(`Row ${t}: doors must be between 1 and 10`)}if(e.stock_quantity){let s="string"==typeof e.stock_quantity?parseInt(e.stock_quantity):e.stock_quantity;(isNaN(s)||s<0)&&r.push(`Row ${t}: stock_quantity must be a non-negative number`)}return e.is_featured&&"string"==typeof e.is_featured&&(["true","false","1","0","yes","no"].includes(e.is_featured.toLowerCase())||r.push(`Row ${t}: is_featured must be true/false, 1/0, or yes/no`)),r}(t,r);if(e.length>0){i.push(...e),n++;continue}let c=function(e,t){let r="string"==typeof e.year?parseInt(e.year):e.year,s="string"==typeof e.price?parseFloat(e.price.replace(/[,¥$]/g,"")):e.price,i=e.original_price?"string"==typeof e.original_price?parseFloat(e.original_price.replace(/[,¥$]/g,"")):e.original_price:void 0,a=e.mileage?"string"==typeof e.mileage?parseInt(e.mileage):e.mileage:void 0,o=e.seats?"string"==typeof e.seats?parseInt(e.seats):e.seats:void 0,n=e.doors?"string"==typeof e.doors?parseInt(e.doors):e.doors:void 0,c=e.stock_quantity?"string"==typeof e.stock_quantity?parseInt(e.stock_quantity):e.stock_quantity:1,l=!!e.is_featured&&["true","1","yes"].includes(String(e.is_featured).toLowerCase()),u=e.images?e.images.split(",").map(e=>e.trim()).filter(e=>e):[],p=e.specs?e.specs.split(",").map(e=>e.trim()).filter(e=>e):[],d=e.features?e.features.split(",").map(e=>e.trim()).filter(e=>e):[],m=`${e.make} ${e.model} ${r}`;return{car_id:e.car_id.trim(),make:e.make.trim(),model:e.model.trim(),year:r,title:m,price:s,original_price:i,currency:"JPY",mileage:a,fuel_type:e.fuel_type?.trim(),transmission:e.transmission?.trim(),engine_size:e.engine_size?.trim(),drive_type:e.drive_type?.trim(),seats:o,doors:n,body_type:e.body_type?.trim(),body_condition:e.body_condition?.trim()||"Good",interior_condition:e.interior_condition?.trim()||"Good",exterior_color:e.exterior_color?.trim(),interior_color:e.interior_color?.trim(),main_image:e.main_image?.trim(),images:u,image_folder:e.image_folder?.trim(),specs:p,features:d,status:e.status?.trim()||"Available",stock_quantity:c,location:e.location?.trim()||"Japan",description:e.description?.trim(),is_featured:l,import_batch_id:t,import_source:"CSV"}}(t,s),u=await (0,l.$z)(c);a.push(u),o++}catch(t){let e=t instanceof Error?t.message:"Unknown error";i.push(`Row ${r}: ${e}`),n++}}let u=0===n?"completed":0===o?"failed":"completed";return await d(s,{successful_imports:o,failed_imports:n,status:u,error_log:i,import_summary:{total_processed:c.length,successful_imports:o,failed_imports:n,success_rate:c.length>0?(o/c.length*100).toFixed(2)+"%":"0%",makes_imported:[...new Set(a.map(e=>e.make))],models_imported:[...new Set(a.map(e=>e.model))],price_range:a.length>0?{min:Math.min(...a.map(e=>e.price)),max:Math.max(...a.map(e=>e.price))}:null}}),{success:o>0,batch_id:s,total_rows:c.length,successful_imports:o,failed_imports:n,errors:i,imported_cars:a}}catch(e){throw await d(s,{status:"failed",error_log:[e instanceof Error?e.message:"Unknown error"]}),e}}async function _(e){try{let{rows:t}=await (0,c.sql)`
      SELECT * FROM csv_imports WHERE batch_id = ${e}
    `;if(0===t.length)return null;let r=t[0];return{id:r.id,batch_id:r.batch_id,filename:r.filename,total_rows:r.total_rows,successful_imports:r.successful_imports,failed_imports:r.failed_imports,status:r.status,error_log:r.error_log||[],import_summary:r.import_summary||{},imported_by:r.imported_by,started_at:r.started_at,completed_at:r.completed_at,created_at:r.created_at}}catch(e){throw console.error("Error fetching CSV import:",e),Error("Failed to fetch CSV import record")}}async function f(e=1,t=20){try{let r=await (0,c.sql)`SELECT COUNT(*) as total FROM csv_imports`,s=parseInt(r.rows[0].total),{rows:i}=await (0,c.sql)`
      SELECT * FROM csv_imports
      ORDER BY started_at DESC
      LIMIT ${t} OFFSET ${(e-1)*t}
    `;return{imports:i.map(e=>({id:e.id,batch_id:e.batch_id,filename:e.filename,total_rows:e.total_rows,successful_imports:e.successful_imports,failed_imports:e.failed_imports,status:e.status,error_log:e.error_log||[],import_summary:e.import_summary||{},imported_by:e.imported_by,started_at:e.started_at,completed_at:e.completed_at,created_at:e.created_at})),total_count:s,page:e,per_page:t,total_pages:Math.ceil(s/t)}}catch(e){throw console.error("Error fetching CSV imports:",e),Error("Failed to fetch CSV imports")}}async function h(e){try{let t=await e.formData(),r=t.get("adminKey"),s=process.env.ADMIN_PASSWORD||"admin123";if(r!==s)return n.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let i=t.get("csvFile");if(!i)return n.NextResponse.json({success:!1,message:"CSV file is required"},{status:400});if(!i.name.toLowerCase().endsWith(".csv"))return n.NextResponse.json({success:!1,message:"File must be a CSV file"},{status:400});if(i.size>0xa00000)return n.NextResponse.json({success:!1,message:"File size must be less than 10MB"},{status:400});let a=await i.text();if(!a.trim())return n.NextResponse.json({success:!1,message:"CSV file is empty"},{status:400});let o=t.get("importedBy")||"admin",c=await m(a,i.name,o);return n.NextResponse.json({success:c.success,message:c.success?`Successfully imported ${c.successful_imports} cars`:"Import completed with errors",batch_id:c.batch_id,total_rows:c.total_rows,successful_imports:c.successful_imports,failed_imports:c.failed_imports,errors:c.errors,imported_cars:c.imported_cars.map(e=>({id:e.id,car_id:e.car_id,title:e.title,make:e.make,model:e.model,year:e.year,price:e.price,status:e.status}))})}catch(t){console.error("Error processing CSV import:",t);let e="Failed to process CSV import";return t instanceof Error&&(e=t.message),n.NextResponse.json({success:!1,message:e},{status:500})}}async function g(e){try{let{searchParams:t}=new URL(e.url),r=t.get("adminKey"),s=t.get("batch_id"),i=process.env.ADMIN_PASSWORD||"admin123";if(r!==i)return n.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(s){let e=await _(s);if(!e)return n.NextResponse.json({success:!1,message:"Import record not found"},{status:404});return n.NextResponse.json({success:!0,import:e})}{let e=parseInt(t.get("page")||"1"),r=parseInt(t.get("per_page")||"20"),s=await f(e,r);return n.NextResponse.json({success:!0,...s})}}catch(e){return console.error("Error fetching import records:",e),n.NextResponse.json({success:!1,message:"Failed to fetch import records"},{status:500})}}let y=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/cars/import/route",pathname:"/api/cars/import",filename:"route",bundlePath:"app/api/cars/import/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\cars\\import\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:$,workUnitAsyncStorage:E,serverHooks:w}=y;function b(){return(0,o.patchFetch)({workAsyncStorage:$,workUnitAsyncStorage:E})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,3376],()=>r(99882));module.exports=s})();