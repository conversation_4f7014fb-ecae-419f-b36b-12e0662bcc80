import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { processAutomatedFollowups, triggerFollowupProcessing } from '@/lib/automatedFollowup';

// POST - Manually trigger follow-up processing
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('🔄 Manual follow-up processing triggered by admin');
    
    const result = await triggerFollowupProcessing();

    return NextResponse.json({
      success: true,
      message: 'Follow-up processing completed',
      result
    });

  } catch (error) {
    console.error('Error in manual follow-up processing:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process follow-ups' },
      { status: 500 }
    );
  }
}

// GET - Get follow-up processing status and pending follow-ups
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { getAllFollowUps } = await import('@/lib/crmStorage');
    const allFollowups = await getAllFollowUps();
    const now = new Date();

    // Get statistics
    const stats = {
      total: allFollowups.length,
      pending: allFollowups.filter(f => f.status === 'pending').length,
      completed: allFollowups.filter(f => f.status === 'completed').length,
      failed: allFollowups.filter(f => f.status === 'failed').length,
      automated: allFollowups.filter(f => f.automationRule).length,
      due: allFollowups.filter(f => 
        f.status === 'pending' && 
        f.automationRule && 
        new Date(f.scheduledDate) <= now
      ).length
    };

    // Get next few pending automated follow-ups
    const upcomingFollowups = allFollowups
      .filter(f => f.status === 'pending' && f.automationRule)
      .sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime())
      .slice(0, 10)
      .map(f => ({
        id: f.id,
        title: f.title,
        type: f.type,
        scheduledDate: f.scheduledDate,
        customerId: f.customerId,
        leadId: f.leadId,
        priority: f.priority,
        isDue: new Date(f.scheduledDate) <= now
      }));

    return NextResponse.json({
      success: true,
      stats,
      upcomingFollowups,
      systemStatus: {
        automationEnabled: true,
        lastProcessed: new Date().toISOString(),
        nextProcessing: new Date(Date.now() + 60 * 60 * 1000).toISOString() // Next hour
      }
    });

  } catch (error) {
    console.error('Error fetching follow-up status:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch follow-up status' },
      { status: 500 }
    );
  }
}
