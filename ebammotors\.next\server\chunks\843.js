exports.id=843,exports.ids=[843,7990],exports.modules={6710:(e,r,t)=>{"use strict";t.d(r,{$z:()=>c,AE:()=>d,PY:()=>n,getAllCars:()=>s,iU:()=>u,jB:()=>l,pe:()=>_});var a=t(83376);let i=()=>!!(process.env.POSTGRES_URL||process.env.DATABASE_URL);function o(e){return{id:e.id,car_id:e.car_id,make:e.make,model:e.model,year:e.year,title:e.title,price:e.price,original_price:e.original_price,currency:e.currency||"JPY",mileage:e.mileage,fuel_type:e.fuel_type,transmission:e.transmission,engine_size:e.engine_size,drive_type:e.drive_type,seats:e.seats,doors:e.doors,body_type:e.body_type,body_condition:e.body_condition,interior_condition:e.interior_condition,exterior_color:e.exterior_color,interior_color:e.interior_color,main_image:e.main_image,images:e.images||[],image_folder:e.image_folder,specs:e.specs||[],features:e.features||[],status:e.status,stock_quantity:e.stock_quantity,location:e.location,slug:e.slug,description:e.description,meta_title:e.meta_title,meta_description:e.meta_description,view_count:e.view_count,popularity_score:e.popularity_score,is_featured:e.is_featured,is_recently_added:e.is_recently_added,is_price_reduced:e.is_price_reduced,import_batch_id:e.import_batch_id,import_source:e.import_source,import_notes:e.import_notes,added_date:e.added_date,updated_date:e.updated_date,sold_date:e.sold_date,created_at:e.created_at,updated_at:e.updated_at}}async function s(e={},r=1,t=50){if(!i())throw Error("Database not available");try{let i=[],s=[],n=1;e.make&&(i.push(`make ILIKE $${n}`),s.push(`%${e.make}%`),n++),e.model&&(i.push(`model ILIKE $${n}`),s.push(`%${e.model}%`),n++),e.year_min&&(i.push(`year >= $${n}`),s.push(e.year_min),n++),e.year_max&&(i.push(`year <= $${n}`),s.push(e.year_max),n++),e.price_min&&(i.push(`price >= $${n}`),s.push(e.price_min),n++),e.price_max&&(i.push(`price <= $${n}`),s.push(e.price_max),n++),e.mileage_max&&(i.push(`mileage <= $${n}`),s.push(e.mileage_max),n++),e.fuel_type&&(i.push(`fuel_type = $${n}`),s.push(e.fuel_type),n++),e.transmission&&(i.push(`transmission = $${n}`),s.push(e.transmission),n++),e.body_condition&&(i.push(`body_condition = $${n}`),s.push(e.body_condition),n++),e.status&&(i.push(`status = $${n}`),s.push(e.status),n++),void 0!==e.is_featured&&(i.push(`is_featured = $${n}`),s.push(e.is_featured),n++),void 0!==e.is_recently_added&&(i.push(`is_recently_added = $${n}`),s.push(e.is_recently_added),n++),e.search_query&&(i.push(`(
        title ILIKE $${n} OR 
        make ILIKE $${n} OR 
        model ILIKE $${n} OR 
        description ILIKE $${n}
      )`),s.push(`%${e.search_query}%`),n++);let l=i.length>0?`WHERE ${i.join(" AND ")}`:"",c=`SELECT COUNT(*) as total FROM cars ${l}`,d=await a.sql.query(c,s),u=parseInt(d.rows[0].total),_=`
      SELECT * FROM cars 
      ${l}
      ORDER BY added_date DESC, created_at DESC
      LIMIT $${n} OFFSET $${n+1}
    `;s.push(t,(r-1)*t);let{rows:p}=await a.sql.query(_,s);return{cars:p.map(o),total_count:u,page:r,per_page:t,total_pages:Math.ceil(u/t),filters_applied:e}}catch(e){throw console.error("Error fetching cars:",e),Error("Failed to fetch cars from database")}}async function n(e){if(!i())throw Error("Database not available");try{let{rows:r}=await (0,a.sql)`
      SELECT * FROM cars WHERE id = ${e}
    `;if(0===r.length)return null;return o(r[0])}catch(e){throw console.error("Error fetching car by ID:",e),Error("Failed to fetch car from database")}}async function l(e){if(!i())throw Error("Database not available");try{let{rows:r}=await (0,a.sql)`
      SELECT * FROM cars WHERE car_id = ${e}
    `;if(0===r.length)return null;return o(r[0])}catch(e){throw console.error("Error fetching car by car_id:",e),Error("Failed to fetch car from database")}}async function c(e){if(!i())throw Error("Database not available");try{let r=e.slug||function(e,r,t,a){let i=`${e}-${r}-${t}`.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,""),o=a.split("_").pop()||Math.random().toString(36).substr(2,5);return`${i}-${o}`}(e.make,e.model,e.year,e.car_id),{rows:t}=await (0,a.sql)`
      INSERT INTO cars (
        car_id, make, model, year, title, price, original_price, currency,
        mileage, fuel_type, transmission, engine_size, drive_type, seats, doors, body_type,
        body_condition, interior_condition, exterior_color, interior_color,
        main_image, images, image_folder, specs, features,
        status, stock_quantity, location, slug, description, meta_title, meta_description,
        is_featured, import_batch_id, import_source, import_notes
      ) VALUES (
        ${e.car_id}, ${e.make}, ${e.model}, ${e.year}, ${e.title},
        ${e.price}, ${e.original_price||null}, ${e.currency||"JPY"},
        ${e.mileage||null}, ${e.fuel_type||null}, ${e.transmission||null},
        ${e.engine_size||null}, ${e.drive_type||null}, ${e.seats||null},
        ${e.doors||null}, ${e.body_type||null},
        ${e.body_condition||"Good"}, ${e.interior_condition||"Good"},
        ${e.exterior_color||null}, ${e.interior_color||null},
        ${e.main_image||null}, ${JSON.stringify(e.images||[])}, ${e.image_folder||null},
        ${JSON.stringify(e.specs||[])}, ${JSON.stringify(e.features||[])},
        ${e.status||"Available"}, ${e.stock_quantity||1}, ${e.location||"Japan"},
        ${r}, ${e.description||null}, ${e.meta_title||null}, ${e.meta_description||null},
        ${e.is_featured||!1}, ${e.import_batch_id||null}, ${e.import_source||"Manual"},
        ${e.import_notes||null}
      )
      RETURNING *
    `;return o(t[0])}catch(r){if(console.error("Error creating car:",r),r instanceof Error&&r.message.includes("duplicate key"))throw Error(`Car with ID '${e.car_id}' already exists`);throw Error("Failed to create car in database")}}async function d(e,r){if(!i())throw Error("Database not available");try{let t=[],i=[],s=1;if(Object.entries(r).forEach(([e,r])=>{void 0!==r&&("images"===e||"specs"===e||"features"===e?(t.push(`${e} = $${s}`),i.push(JSON.stringify(r))):(t.push(`${e} = $${s}`),i.push(r)),s++)}),0===t.length)throw Error("No fields to update");t.push("updated_at = NOW()"),t.push("updated_date = NOW()"),i.push(e);let n=`
      UPDATE cars
      SET ${t.join(", ")}
      WHERE id = $${s}
      RETURNING *
    `,{rows:l}=await a.sql.query(n,i);if(0===l.length)return null;return o(l[0])}catch(e){throw console.error("Error updating car:",e),Error("Failed to update car in database")}}async function u(e){if(!i())throw Error("Database not available");try{return(await (0,a.sql)`
      DELETE FROM cars WHERE id = ${e}
    `).rowCount>0}catch(e){throw console.error("Error deleting car:",e),Error("Failed to delete car from database")}}async function _(e){if(!i())throw Error("Database not available");if(0===e.length)return 0;try{let r=e.map((e,r)=>`$${r+1}`).join(","),t=`DELETE FROM cars WHERE id IN (${r})`;return(await a.sql.query(t,e)).rowCount||0}catch(e){throw console.error("Error bulk deleting cars:",e),Error("Failed to bulk delete cars from database")}}},47990:()=>{}};