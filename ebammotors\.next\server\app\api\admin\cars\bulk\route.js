(()=>{var e={};e.id=5458,e.ids=[5458,7990],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,s)=>{"use strict";s.d(t,{Qq:()=>m,Tq:()=>f,bS:()=>d,fF:()=>l,mU:()=>c});var r=s(85663),a=s(43205),n=s.n(a);let i=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await r.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),s=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return n().sign(t,i,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,s=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:s,expiresAt:s+864e5,lastActivity:s}),function(){let e=Date.now();for(let[t,s]of o.entries())e>s.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function l(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=n().verify(e,i);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let s=Date.now();return s>t.expiresAt?(o.delete(e),null):(t.lastActivity=s,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),s=p.get(e);return!s||t-s.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):s.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-s.lastAttempt)}:(s.count++,s.lastAttempt=t,p.set(e,s),{allowed:!0,remainingAttempts:5-s.count})}function f(e){p.delete(e)}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},44981:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>q,routeModule:()=>h,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>$});var r={};s.r(r),s.d(r,{POST:()=>d,PUT:()=>y});var a=s(96559),n=s(48088),i=s(37719),o=s(32190),u=s(77268),c=s(83376);async function d(e){try{let t;if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{action:s,car_ids:r}=await e.json();if(!s||!r||!Array.isArray(r)||0===r.length)return o.NextResponse.json({success:!1,message:"Invalid request. Action and car_ids are required."},{status:400});let a="";switch(s){case"feature":t=await l(r,!0),a=`${t.count} cars marked as featured`;break;case"unfeature":t=await l(r,!1),a=`${t.count} cars unmarked as featured`;break;case"delete":t=await p(r),a=`${t.count} cars deleted`;break;case"update_status":let{status:n}=await e.json();if(!n)return o.NextResponse.json({success:!1,message:"Status is required for update_status action"},{status:400});t=await m(r,n),a=`${t.count} cars status updated to ${n}`;break;case"update_location":let{location:i}=await e.json();if(!i)return o.NextResponse.json({success:!1,message:"Location is required for update_location action"},{status:400});t=await f(r,i),a=`${t.count} cars location updated to ${i}`;break;case"export":let c=await g(r);return o.NextResponse.json({success:!0,message:"Cars exported successfully",data:c});default:return o.NextResponse.json({success:!1,message:"Invalid action"},{status:400})}return o.NextResponse.json({success:!0,message:a,affected_count:t.count})}catch(e){return console.error("Error performing bulk operation:",e),o.NextResponse.json({success:!1,message:"Failed to perform bulk operation"},{status:500})}}async function l(e,t){let s=e.map((e,t)=>`$${t+1}`).join(","),r=`
    UPDATE cars 
    SET is_featured = $${e.length+1}, updated_at = NOW() 
    WHERE id IN (${s})
  `;return{count:(await c.sql.query(r,[...e,t])).rowCount||0}}async function p(e){let t=e.map((e,t)=>`$${t+1}`).join(","),s=`DELETE FROM cars WHERE id IN (${t})`;return{count:(await c.sql.query(s,e)).rowCount||0}}async function m(e,t){let s=e.map((e,t)=>`$${t+1}`).join(","),r=`
    UPDATE cars 
    SET status = $${e.length+1}, updated_at = NOW() 
    WHERE id IN (${s})
  `;return{count:(await c.sql.query(r,[...e,t])).rowCount||0}}async function f(e,t){let s=e.map((e,t)=>`$${t+1}`).join(","),r=`
    UPDATE cars 
    SET location = $${e.length+1}, updated_at = NOW() 
    WHERE id IN (${s})
  `;return{count:(await c.sql.query(r,[...e,t])).rowCount||0}}async function g(e){let t=e.map((e,t)=>`$${t+1}`).join(","),s=`
    SELECT 
      car_id,
      make,
      model,
      year,
      title,
      price,
      original_price,
      currency,
      status,
      mileage,
      fuel_type,
      transmission,
      engine_size,
      drive_type,
      seats,
      doors,
      body_type,
      body_condition,
      interior_condition,
      exterior_color,
      interior_color,
      location,
      is_featured,
      stock_quantity,
      description,
      created_at,
      updated_at
    FROM cars 
    WHERE id IN (${t})
    ORDER BY created_at DESC
  `;return(await c.sql.query(s,e)).rows}async function y(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{car_ids:t,updates:s}=await e.json();if(!t||!Array.isArray(t)||0===t.length||!s)return o.NextResponse.json({success:!1,message:"Invalid request. car_ids and updates are required."},{status:400});let r=[],a=[],n=1;for(let[e,t]of Object.entries(s))["make","model","year","title","price","original_price","currency","status","mileage","fuel_type","transmission","body_condition","location","is_featured","stock_quantity","description"].includes(e)&&(r.push(`${e} = $${n}`),a.push(t),n++);if(0===r.length)return o.NextResponse.json({success:!1,message:"No valid fields to update"},{status:400});r.push("updated_at = NOW()");let i=t.map((e,t)=>`$${n+t}`).join(",");a.push(...t);let d=`
      UPDATE cars 
      SET ${r.join(", ")} 
      WHERE id IN (${i})
    `,l=await c.sql.query(d,a);return o.NextResponse.json({success:!0,message:`${l.rowCount||0} cars updated successfully`,affected_count:l.rowCount||0})}catch(e){return console.error("Error performing bulk update:",e),o.NextResponse.json({success:!1,message:"Failed to perform bulk update"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/cars/bulk/route",pathname:"/api/admin/cars/bulk",filename:"route",bundlePath:"app/api/admin/cars/bulk/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\cars\\bulk\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:$,serverHooks:w}=h;function q(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:$})}},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77268:(e,t,s)=>{"use strict";s.d(t,{iY:()=>a}),s(32190);var r=s(12909);function a(e,t){let s=e.headers.get("authorization"),a=e.cookies.get("admin_session")?.value,n=(0,r.fF)(s,a);if(n.isValid)return{isValid:!0,adminId:n.adminId,method:"token/session"};let i=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return i&&i===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,7696,3376],()=>s(44981));module.exports=r})();