(()=>{var e={};e.id=2520,e.ids=[2520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75474:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>g,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>w});var r={};t.r(r),t.d(r,{POST:()=>m});var i=t(96559),n=t(48088),a=t(37719),o=t(32190),u=t(29021),c=t(33873),p=t.n(c);let d=p().join(process.cwd(),"data","push-subscriptions.json");async function l(){try{let e=await u.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function x(e){let s=p().dirname(d);try{await u.promises.access(s)}catch{await u.promises.mkdir(s,{recursive:!0})}await u.promises.writeFile(d,JSON.stringify(e,null,2))}async function m(e){try{let{endpoint:s}=await e.json();if(!s)return o.NextResponse.json({success:!1,message:"Endpoint is required"},{status:400});let t=(await l()).filter(e=>e.endpoint!==s);return await x(t),o.NextResponse.json({success:!0,message:"Unsubscribed successfully"})}catch(e){return o.NextResponse.json({success:!1,message:"Failed to unsubscribe"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/notifications/unsubscribe/route",pathname:"/api/notifications/unsubscribe",filename:"route",bundlePath:"app/api/notifications/unsubscribe/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\notifications\\unsubscribe\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:b,workUnitAsyncStorage:w,serverHooks:g}=f;function v(){return(0,a.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:w})}},78335:()=>{},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,580],()=>t(75474));module.exports=r})();