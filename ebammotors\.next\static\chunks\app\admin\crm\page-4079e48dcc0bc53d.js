(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3417],{381:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1243:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1497:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},2318:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2846:(e,s,t)=>{"use strict";function a(e){try{let s="string"==typeof e?new Date(e):e,t=s.getFullYear(),a=String(s.getMonth()+1).padStart(2,"0"),l=String(s.getDate()).padStart(2,"0"),r=String(s.getHours()).padStart(2,"0"),i=String(s.getMinutes()).padStart(2,"0");return"".concat(l,"/").concat(a,"/").concat(t," ").concat(r,":").concat(i)}catch(e){return"Invalid Date"}}function l(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"\xa5";try{return"".concat(s).concat(function(e){try{return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}catch(e){return"0"}}(e))}catch(e){return"".concat(s,"0")}}t.d(s,{r6:()=>a,vv:()=>l})},3109:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5267:(e,s,t)=>{Promise.resolve().then(t.bind(t,7680))},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7680:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(5155),l=t(2115),r=t(3109),i=t(7580),n=t(2318),c=t(1497),d=t(9074),o=t(5868),x=t(2846),m=t(3904),h=t(4861),u=t(646),p=t(5339);function g(){let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)(""),o=async()=>{r(!0);try{let e=await fetch("/api/chatbot"),t=await e.json();s(t)}catch(e){s({status:"error",aiAvailable:!1,error:"Failed to check AI status"})}finally{r(!1)}},x=async()=>{n(!0),d("");try{let e=await fetch("/api/chatbot",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:"Hello, can you tell me about EBAM Motors?",context:"Testing AI functionality",locale:"en"})}),s=await e.json();s.success?d("✅ AI Response: ".concat(s.response.substring(0,200)).concat(s.response.length>200?"...":"")):d("❌ AI Test Failed: ".concat(s.error))}catch(e){d("❌ Test Error: ".concat(e))}finally{n(!1)}};return(0,l.useEffect)(()=>{o()},[]),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"AI Chatbot Status"}),(0,a.jsxs)("button",{onClick:o,disabled:t,className:"flex items-center space-x-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 ".concat(t?"animate-spin":"")}),(0,a.jsx)("span",{children:"Refresh"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[t?(0,a.jsx)(m.A,{className:"w-5 h-5 animate-spin text-blue-500"}):e&&e.aiAvailable?(0,a.jsx)(u.A,{className:"w-5 h-5 text-green-500"}):(0,a.jsx)(h.A,{className:"w-5 h-5 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsx)("p",{className:"font-medium ".concat(t?"text-blue-600":e&&e.aiAvailable?"text-green-600":"text-red-600"),children:t?"Checking...":e?e.aiAvailable?"AI Active":"AI Unavailable":"Unknown"})]})]}),(null==e?void 0:e.provider)&&(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-blue-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Provider"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.provider.provider}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.provider.model})]})]})]}),(null==e?void 0:e.error)&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-red-600",children:[(0,a.jsx)("strong",{children:"Error:"})," ",e.error]})}),(0,a.jsxs)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-blue-800 mb-2",children:(0,a.jsx)("strong",{children:"Configuration:"})}),(0,a.jsxs)("ul",{className:"text-xs text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Check your .env.local file for AI_PROVIDER setting"}),(0,a.jsx)("li",{children:"• Ensure API keys are configured correctly"}),(0,a.jsx)("li",{children:"• See AI_CHATBOT_SETUP.md for detailed instructions"})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Test AI Response"}),(0,a.jsx)("button",{onClick:x,disabled:i||!(null==e?void 0:e.aiAvailable),className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed",children:i?"Testing...":"Test AI"})]}),c&&(0,a.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:c})})]}),(0,a.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Quick Setup"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-2",children:[(0,a.jsx)("a",{href:"https://huggingface.co/settings/tokens",target:"_blank",rel:"noopener noreferrer",className:"text-xs px-3 py-2 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 text-center",children:"Get Hugging Face Key"}),(0,a.jsx)("a",{href:"https://console.groq.com/",target:"_blank",rel:"noopener noreferrer",className:"text-xs px-3 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200 text-center",children:"Get Groq Key"}),(0,a.jsx)("a",{href:"https://ollama.ai/",target:"_blank",rel:"noopener noreferrer",className:"text-xs px-3 py-2 bg-green-100 text-green-800 rounded hover:bg-green-200 text-center",children:"Install Ollama"})]})]})]})}var y=t(1243),b=t(2657),j=t(381);function v(){let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)({totalResponses:0,lowRisk:0,mediumRisk:0,highRisk:0,hallucinationRate:0}),[i,n]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s([{id:"1",timestamp:new Date().toISOString(),userMessage:"What is the cheapest car?",aiResponse:"The cheapest car is Toyota Voxy at \xa5300,000",riskLevel:"low",issues:[],cleanedResponse:"The cheapest car is Toyota Voxy at \xa5300,000"},{id:"2",timestamp:new Date(Date.now()-36e5).toISOString(),userMessage:"Do you have Honda Civic?",aiResponse:"Yes, we have Honda Civic for \xa5450,000 with 2 year warranty",riskLevel:"high",issues:["Unknown Honda model","Invalid price format","Warranty claim"],cleanedResponse:"I can only provide information about our current inventory: Toyota Voxy, Noah, Sienta, Yaris, and Vitz. For other models, please contact our team."}]),r({totalResponses:50,lowRisk:45,mediumRisk:3,highRisk:2,hallucinationRate:10})},[]);let c=e=>{switch(e){case"low":return"text-green-600 bg-green-100";case"medium":return"text-yellow-600 bg-yellow-100";case"high":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},d=e=>{switch(e){case"low":return(0,a.jsx)(u.A,{className:"w-4 h-4"});case"medium":return(0,a.jsx)(y.A,{className:"w-4 h-4"});case"high":return(0,a.jsx)(h.A,{className:"w-4 h-4"});default:return(0,a.jsx)(b.A,{className:"w-4 h-4"})}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"AI Hallucination Monitor"}),(0,a.jsxs)("button",{onClick:()=>n(!i),className:"flex items-center space-x-2 px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Settings"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-blue-600",children:"Total Responses"}),(0,a.jsx)("p",{className:"text-xl font-bold text-blue-900",children:t.totalResponses})]}),(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-green-600",children:"Low Risk"}),(0,a.jsx)("p",{className:"text-xl font-bold text-green-900",children:t.lowRisk})]}),(0,a.jsxs)("div",{className:"p-3 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-yellow-600",children:"Medium Risk"}),(0,a.jsx)("p",{className:"text-xl font-bold text-yellow-900",children:t.mediumRisk})]}),(0,a.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-red-600",children:"High Risk"}),(0,a.jsx)("p",{className:"text-xl font-bold text-red-900",children:t.highRisk})]})]}),(0,a.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Hallucination Rate"}),(0,a.jsxs)("span",{className:"text-sm font-bold ".concat(t.hallucinationRate>15?"text-red-600":t.hallucinationRate>5?"text-yellow-600":"text-green-600"),children:[t.hallucinationRate,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"h-2 rounded-full ".concat(t.hallucinationRate>15?"bg-red-500":t.hallucinationRate>5?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(Math.min(t.hallucinationRate,100),"%")}})}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Target: <5% | Warning: >10% | Critical: >15%"})]}),i&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-3",children:"Hallucination Control Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Temperature:"})," 0.3 (Lower = less creative)"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Top P:"})," 0.8 (Nucleus sampling)"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Max Tokens:"})," 200"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Frequency Penalty:"})," 0.5"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Presence Penalty:"})," 0.3"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Validation:"})," Enabled"]})]})]}),(0,a.jsxs)("div",{className:"mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800",children:["\uD83D\uDCA1 ",(0,a.jsx)("strong",{children:"Tip:"})," Lower temperature reduces creativity but increases accuracy. Adjust in ",(0,a.jsx)("code",{children:"src/config/chatbotConfig.ts"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Recent AI Responses"}),(0,a.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:e.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ".concat(c(e.riskLevel)),children:[d(e.riskLevel),(0,a.jsxs)("span",{children:[e.riskLevel.toUpperCase()," RISK"]})]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleString()})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-700",children:"User:"}),(0,a.jsx)("p",{className:"text-gray-600 bg-gray-50 p-2 rounded",children:e.userMessage})]}),"low"!==e.riskLevel&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-700",children:"Original AI Response:"}),(0,a.jsx)("p",{className:"text-red-600 bg-red-50 p-2 rounded",children:e.aiResponse})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-green-700",children:"low"===e.riskLevel?"AI Response:":"Cleaned Response:"}),(0,a.jsx)("p",{className:"text-green-600 bg-green-50 p-2 rounded",children:e.cleanedResponse})]}),e.issues.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-orange-700",children:"Issues Detected:"}),(0,a.jsx)("ul",{className:"text-orange-600 text-xs list-disc list-inside",children:e.issues.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("button",{className:"px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200",children:"Export Log"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200",children:"Clear Low Risk"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-yellow-100 text-yellow-700 rounded text-sm hover:bg-yellow-200",children:"Review Medium Risk"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200",children:"Flag High Risk"})]})]})]})}function N(){let[e,s]=(0,l.useState)(!1),[t,m]=(0,l.useState)(""),[h,u]=(0,l.useState)("overview"),[p,y]=(0,l.useState)(!1),[b,j]=(0,l.useState)([]),[N,f]=(0,l.useState)([]),[w,k]=(0,l.useState)([]),[A,R]=(0,l.useState)([]),S=async e=>{e.preventDefault(),y(!0);try{let e=await fetch("/api/admin/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:t})}),a=await e.json();a.success?(s(!0),await C(a.adminKey)):alert("Invalid admin password")}catch(e){console.error("Authentication error:",e),alert("Authentication failed")}finally{y(!1)}},C=async e=>{y(!0);try{let[s,t,a,l]=await Promise.all([fetch("/api/customers?adminKey=".concat(encodeURIComponent(e))),fetch("/api/leads?adminKey=".concat(encodeURIComponent(e))),fetch("/api/interactions?adminKey=".concat(encodeURIComponent(e))),fetch("/api/followups?adminKey=".concat(encodeURIComponent(e)))]),[r,i,n,c]=await Promise.all([s.json(),t.json(),a.json(),l.json()]);r.success&&j(r.customers||[]),i.success&&f(i.leads||[]),n.success&&k(n.interactions||[]),c.success&&R(c.followups||[])}catch(e){console.error("Error loading dashboard data:",e)}finally{y(!1)}};if(!e)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-md w-full max-w-md",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-center mb-6",children:"CRM Admin Access"}),(0,a.jsxs)("form",{onSubmit:S,children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Admin Password"}),(0,a.jsx)("input",{type:"password",value:t,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsx)("button",{type:"submit",disabled:p,className:"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50",children:p?"Authenticating...":"Access CRM"})]})]})});let M=(()=>{let e=N.filter(e=>"new"===e.status).length,s=b.filter(e=>"active"===e.status).length;return{newLeads:e,activeCustomers:s,pendingFollowUps:A.filter(e=>"pending"===e.status).length,totalRevenue:b.reduce((e,s)=>e+s.totalSpent,0)}})();return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"EBAM Motors CRM"}),(0,a.jsx)("button",{onClick:()=>s(!1),className:"text-gray-500 hover:text-gray-700",children:"Logout"})]})})}),(0,a.jsx)("div",{className:"bg-white border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:r.A},{id:"customers",label:"Customers",icon:i.A},{id:"leads",label:"Leads",icon:n.A},{id:"interactions",label:"Interactions",icon:c.A},{id:"followups",label:"Follow-ups",icon:d.A}].map(e=>{let{id:s,label:t,icon:l}=e;return(0,a.jsxs)("button",{onClick:()=>u(s),className:"flex items-center px-3 py-4 text-sm font-medium border-b-2 ".concat(h===s?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)(l,{className:"w-4 h-4 mr-2"}),t]},s)})})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["overview"===h&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{className:"w-8 h-8 text-blue-500"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"New Leads"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:M.newLeads})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"w-8 h-8 text-green-500"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Customers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:M.activeCustomers})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"w-8 h-8 text-orange-500"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Follow-ups"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:M.pendingFollowUps})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"w-8 h-8 text-purple-500"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,x.vv)(M.totalRevenue)})]})]})})]}),(0,a.jsx)(g,{}),(0,a.jsx)(v,{}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Activity"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"space-y-4",children:w.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 text-gray-400 mt-0.5"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900",children:e.content}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:(0,x.r6)(e.createdAt)})]})]},e.id))})})]})]}),"overview"!==h&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("p",{className:"text-gray-500",children:[h.charAt(0).toUpperCase()+h.slice(1)," management interface - Full implementation available in locale-based route: /en/admin/crm"]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("a",{href:"/en/admin/crm",className:"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 inline-block",children:"Go to Full CRM Dashboard"})})]})]})]})}},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var a=t(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=r(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:o="",children:x,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:s,...d,width:l,height:l,stroke:t,strokeWidth:i?24*Number(r)/Number(l):r,className:n("lucide",o),...!x&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(x)?x:[x]])}),x=(e,s)=>{let t=(0,a.forwardRef)((t,r)=>{let{className:c,...d}=t;return(0,a.createElement)(o,{ref:r,iconNode:s,className:n("lucide-".concat(l(i(e))),"lucide-".concat(e),c),...d})});return t.displayName=i(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(5267)),_N_E=e.O()}]);