(()=>{var e={};e.id=6947,e.ids=[843,6947,7990],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,r)=>{"use strict";r.d(t,{$z:()=>u,AE:()=>l,PY:()=>o,getAllCars:()=>n,iU:()=>d,jB:()=>c,pe:()=>p});var s=r(83376);let a=()=>!!(process.env.POSTGRES_URL||process.env.DATABASE_URL);function i(e){return{id:e.id,car_id:e.car_id,make:e.make,model:e.model,year:e.year,title:e.title,price:e.price,original_price:e.original_price,currency:e.currency||"JPY",mileage:e.mileage,fuel_type:e.fuel_type,transmission:e.transmission,engine_size:e.engine_size,drive_type:e.drive_type,seats:e.seats,doors:e.doors,body_type:e.body_type,body_condition:e.body_condition,interior_condition:e.interior_condition,exterior_color:e.exterior_color,interior_color:e.interior_color,main_image:e.main_image,images:e.images||[],image_folder:e.image_folder,specs:e.specs||[],features:e.features||[],status:e.status,stock_quantity:e.stock_quantity,location:e.location,slug:e.slug,description:e.description,meta_title:e.meta_title,meta_description:e.meta_description,view_count:e.view_count,popularity_score:e.popularity_score,is_featured:e.is_featured,is_recently_added:e.is_recently_added,is_price_reduced:e.is_price_reduced,import_batch_id:e.import_batch_id,import_source:e.import_source,import_notes:e.import_notes,added_date:e.added_date,updated_date:e.updated_date,sold_date:e.sold_date,created_at:e.created_at,updated_at:e.updated_at}}async function n(e={},t=1,r=50){if(!a())throw Error("Database not available");try{let a=[],n=[],o=1;e.make&&(a.push(`make ILIKE $${o}`),n.push(`%${e.make}%`),o++),e.model&&(a.push(`model ILIKE $${o}`),n.push(`%${e.model}%`),o++),e.year_min&&(a.push(`year >= $${o}`),n.push(e.year_min),o++),e.year_max&&(a.push(`year <= $${o}`),n.push(e.year_max),o++),e.price_min&&(a.push(`price >= $${o}`),n.push(e.price_min),o++),e.price_max&&(a.push(`price <= $${o}`),n.push(e.price_max),o++),e.mileage_max&&(a.push(`mileage <= $${o}`),n.push(e.mileage_max),o++),e.fuel_type&&(a.push(`fuel_type = $${o}`),n.push(e.fuel_type),o++),e.transmission&&(a.push(`transmission = $${o}`),n.push(e.transmission),o++),e.body_condition&&(a.push(`body_condition = $${o}`),n.push(e.body_condition),o++),e.status&&(a.push(`status = $${o}`),n.push(e.status),o++),void 0!==e.is_featured&&(a.push(`is_featured = $${o}`),n.push(e.is_featured),o++),void 0!==e.is_recently_added&&(a.push(`is_recently_added = $${o}`),n.push(e.is_recently_added),o++),e.search_query&&(a.push(`(
        title ILIKE $${o} OR 
        make ILIKE $${o} OR 
        model ILIKE $${o} OR 
        description ILIKE $${o}
      )`),n.push(`%${e.search_query}%`),o++);let c=a.length>0?`WHERE ${a.join(" AND ")}`:"",u=`SELECT COUNT(*) as total FROM cars ${c}`,l=await s.sql.query(u,n),d=parseInt(l.rows[0].total),p=`
      SELECT * FROM cars 
      ${c}
      ORDER BY added_date DESC, created_at DESC
      LIMIT $${o} OFFSET $${o+1}
    `;n.push(r,(t-1)*r);let{rows:m}=await s.sql.query(p,n);return{cars:m.map(i),total_count:d,page:t,per_page:r,total_pages:Math.ceil(d/r),filters_applied:e}}catch(e){throw console.error("Error fetching cars:",e),Error("Failed to fetch cars from database")}}async function o(e){if(!a())throw Error("Database not available");try{let{rows:t}=await (0,s.sql)`
      SELECT * FROM cars WHERE id = ${e}
    `;if(0===t.length)return null;return i(t[0])}catch(e){throw console.error("Error fetching car by ID:",e),Error("Failed to fetch car from database")}}async function c(e){if(!a())throw Error("Database not available");try{let{rows:t}=await (0,s.sql)`
      SELECT * FROM cars WHERE car_id = ${e}
    `;if(0===t.length)return null;return i(t[0])}catch(e){throw console.error("Error fetching car by car_id:",e),Error("Failed to fetch car from database")}}async function u(e){if(!a())throw Error("Database not available");try{let t=e.slug||function(e,t,r,s){let a=`${e}-${t}-${r}`.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,""),i=s.split("_").pop()||Math.random().toString(36).substr(2,5);return`${a}-${i}`}(e.make,e.model,e.year,e.car_id),{rows:r}=await (0,s.sql)`
      INSERT INTO cars (
        car_id, make, model, year, title, price, original_price, currency,
        mileage, fuel_type, transmission, engine_size, drive_type, seats, doors, body_type,
        body_condition, interior_condition, exterior_color, interior_color,
        main_image, images, image_folder, specs, features,
        status, stock_quantity, location, slug, description, meta_title, meta_description,
        is_featured, import_batch_id, import_source, import_notes
      ) VALUES (
        ${e.car_id}, ${e.make}, ${e.model}, ${e.year}, ${e.title},
        ${e.price}, ${e.original_price||null}, ${e.currency||"JPY"},
        ${e.mileage||null}, ${e.fuel_type||null}, ${e.transmission||null},
        ${e.engine_size||null}, ${e.drive_type||null}, ${e.seats||null},
        ${e.doors||null}, ${e.body_type||null},
        ${e.body_condition||"Good"}, ${e.interior_condition||"Good"},
        ${e.exterior_color||null}, ${e.interior_color||null},
        ${e.main_image||null}, ${JSON.stringify(e.images||[])}, ${e.image_folder||null},
        ${JSON.stringify(e.specs||[])}, ${JSON.stringify(e.features||[])},
        ${e.status||"Available"}, ${e.stock_quantity||1}, ${e.location||"Japan"},
        ${t}, ${e.description||null}, ${e.meta_title||null}, ${e.meta_description||null},
        ${e.is_featured||!1}, ${e.import_batch_id||null}, ${e.import_source||"Manual"},
        ${e.import_notes||null}
      )
      RETURNING *
    `;return i(r[0])}catch(t){if(console.error("Error creating car:",t),t instanceof Error&&t.message.includes("duplicate key"))throw Error(`Car with ID '${e.car_id}' already exists`);throw Error("Failed to create car in database")}}async function l(e,t){if(!a())throw Error("Database not available");try{let r=[],a=[],n=1;if(Object.entries(t).forEach(([e,t])=>{void 0!==t&&("images"===e||"specs"===e||"features"===e?(r.push(`${e} = $${n}`),a.push(JSON.stringify(t))):(r.push(`${e} = $${n}`),a.push(t)),n++)}),0===r.length)throw Error("No fields to update");r.push("updated_at = NOW()"),r.push("updated_date = NOW()"),a.push(e);let o=`
      UPDATE cars
      SET ${r.join(", ")}
      WHERE id = $${n}
      RETURNING *
    `,{rows:c}=await s.sql.query(o,a);if(0===c.length)return null;return i(c[0])}catch(e){throw console.error("Error updating car:",e),Error("Failed to update car in database")}}async function d(e){if(!a())throw Error("Database not available");try{return(await (0,s.sql)`
      DELETE FROM cars WHERE id = ${e}
    `).rowCount>0}catch(e){throw console.error("Error deleting car:",e),Error("Failed to delete car from database")}}async function p(e){if(!a())throw Error("Database not available");if(0===e.length)return 0;try{let t=e.map((e,t)=>`$${t+1}`).join(","),r=`DELETE FROM cars WHERE id IN (${t})`;return(await s.sql.query(r,e)).rowCount||0}catch(e){throw console.error("Error bulk deleting cars:",e),Error("Failed to bulk delete cars from database")}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>m,Tq:()=>_,bS:()=>l,fF:()=>d,mU:()=>u});var s=r(85663),a=r(43205),i=r.n(a);let n=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function c(e,t){try{return await s.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function u(e){return o.delete(e)}async function l(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),r=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await c(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(t,n,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let e=Date.now();for(let[t,r]of o.entries())e>r.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function d(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=i().verify(e,n);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let r=Date.now();return r>t.expiresAt?(o.delete(e),null):(t.lastActivity=r,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),r=p.get(e);return!r||t-r.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-r.lastAttempt)}:(r.count++,r.lastAttempt=t,p.set(e,r),{allowed:!0,remainingAttempts:5-r.count})}function _(e){p.delete(e)}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77268:(e,t,r)=>{"use strict";r.d(t,{iY:()=>a}),r(32190);var s=r(12909);function a(e,t){let r=e.headers.get("authorization"),a=e.cookies.get("admin_session")?.value,i=(0,s.fF)(r,a);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let n=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return n&&n===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90975:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>_,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>m,GET:()=>l,POST:()=>d,PUT:()=>p});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),c=r(6710),u=r(77268);async function l(e){try{let{searchParams:t}=new URL(e.url),r=(0,u.iY)(e).isValid,s=t.get("id"),a=t.get("car_id");if(s){let e=await (0,c.PY)(s);if(!e)return o.NextResponse.json({success:!1,message:"Car not found"},{status:404});return o.NextResponse.json({success:!0,car:e})}if(a){let e=await (0,c.jB)(a);if(!e)return o.NextResponse.json({success:!1,message:"Car not found"},{status:404});return o.NextResponse.json({success:!0,car:e})}let i={};t.get("make")&&(i.make=t.get("make")),t.get("model")&&(i.model=t.get("model")),t.get("year_min")&&(i.year_min=parseInt(t.get("year_min"))),t.get("year_max")&&(i.year_max=parseInt(t.get("year_max"))),t.get("price_min")&&(i.price_min=parseInt(t.get("price_min"))),t.get("price_max")&&(i.price_max=parseInt(t.get("price_max"))),t.get("mileage_max")&&(i.mileage_max=parseInt(t.get("mileage_max"))),t.get("fuel_type")&&(i.fuel_type=t.get("fuel_type")),t.get("transmission")&&(i.transmission=t.get("transmission")),t.get("body_condition")&&(i.body_condition=t.get("body_condition")),t.get("status")&&(i.status=t.get("status")),t.get("is_featured")&&(i.is_featured="true"===t.get("is_featured")),t.get("is_recently_added")&&(i.is_recently_added="true"===t.get("is_recently_added")),t.get("search")&&(i.search_query=t.get("search")),r||(i.status="Available");let n=parseInt(t.get("page")||"1"),l=parseInt(t.get("per_page")||"50"),d=await (0,c.getAllCars)(i,n,l);return o.NextResponse.json({success:!0,...d})}catch(e){return console.error("Error fetching cars:",e),o.NextResponse.json({success:!1,message:"Failed to fetch cars"},{status:500})}}async function d(e){try{let t=await e.json(),{car:r}=t;if(!(0,u.iY)(e,t).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!r)return o.NextResponse.json({success:!1,message:"Car data is required"},{status:400});let s=["car_id","make","model","year","price"].filter(e=>!r[e]);if(s.length>0)return o.NextResponse.json({success:!1,message:`Missing required fields: ${s.join(", ")}`},{status:400});let a={...r,import_source:"Manual"},i=await (0,c.$z)(a);return o.NextResponse.json({success:!0,message:"Car created successfully",car:i})}catch(e){if(console.error("Error creating car:",e),e instanceof Error&&e.message.includes("already exists"))return o.NextResponse.json({success:!1,message:e.message},{status:409});return o.NextResponse.json({success:!1,message:"Failed to create car"},{status:500})}}async function p(e){try{let{adminKey:t,id:r,updates:s}=await e.json(),a=process.env.ADMIN_PASSWORD||"admin123";if(t!==a)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!r||!s)return o.NextResponse.json({success:!1,message:"Car ID and updates are required"},{status:400});let i=await (0,c.AE)(r,s);if(!i)return o.NextResponse.json({success:!1,message:"Car not found"},{status:404});return o.NextResponse.json({success:!0,message:"Car updated successfully",car:i})}catch(e){return console.error("Error updating car:",e),o.NextResponse.json({success:!1,message:"Failed to update car"},{status:500})}}async function m(e){try{let{adminKey:t,id:r,ids:s}=await e.json(),a=process.env.ADMIN_PASSWORD||"admin123";if(t!==a)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(s&&Array.isArray(s)){let e=await (0,c.pe)(s);return o.NextResponse.json({success:!0,message:`Successfully deleted ${e} cars`,deleted_count:e})}if(!r)return o.NextResponse.json({success:!1,message:"Car ID or IDs are required"},{status:400});if(!await (0,c.iU)(r))return o.NextResponse.json({success:!1,message:"Car not found"},{status:404});return o.NextResponse.json({success:!0,message:"Car deleted successfully"})}catch(e){return console.error("Error deleting car(s):",e),o.NextResponse.json({success:!1,message:"Failed to delete car(s)"},{status:500})}}let _=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cars/route",pathname:"/api/cars",filename:"route",bundlePath:"app/api/cars/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\cars\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:y}=_;function h(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7696,3376],()=>r(90975));module.exports=s})();