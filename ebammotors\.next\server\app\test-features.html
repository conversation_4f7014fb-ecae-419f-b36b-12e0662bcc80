<!DOCTYPE html><html lang="en" class="__variable_e8ce0c __variable_d5a796"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/fe3acb13291c7c34.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/446713b8705d64a7.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-071536056086afa3.js"/><script src="/_next/static/chunks/4bd1b696-9ff9c0d733882faf.js" async=""></script><script src="/_next/static/chunks/1684-e57e5717133b5c95.js" async=""></script><script src="/_next/static/chunks/main-app-c6d3b598721f0d06.js" async=""></script><script src="/_next/static/chunks/8357-a6a9f0ccd9b659fa.js" async=""></script><script src="/_next/static/chunks/app/layout-13259b84ad90b9ac.js" async=""></script><script src="/_next/static/chunks/app/test-features/page-2be93ce31202fa18.js" async=""></script><link rel="preload" href="https://www.googletagmanager.com/gtag/js?id=your-google-analytics-4-id" as="script"/><title>EBAM MOTORS - Quality Used Goods from Japan to Ghana</title><meta name="description" content="EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa."/><meta name="author" content="EBAM MOTORS"/><link rel="manifest" href="/manifest.json"/><meta name="keywords" content="used cars Japan, export Ghana, EBAM Motors, Japanese cars, used electronics, furniture export, trading company, sustainable trade"/><meta name="creator" content="EBAM MOTORS"/><meta name="publisher" content="EBAM MOTORS"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="google-site-verification" content="your-google-verification-code"/><meta property="og:title" content="EBAM MOTORS - Quality Used Goods from Japan to Ghana"/><meta property="og:description" content="EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa."/><meta property="og:url" content="https://ebammotors.com"/><meta property="og:site_name" content="EBAM MOTORS"/><meta property="og:locale" content="en_US"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="EBAM MOTORS - Quality Used Goods from Japan to Ghana"/><meta name="twitter:description" content="EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa."/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="font-inter antialiased"><div class="min-h-screen bg-gray-100 p-4"><div class="max-w-7xl mx-auto"><div class="mb-8"><h1 class="text-3xl font-bold text-gray-900 mb-2">🧪 EBAM Motors - Feature Testing Dashboard</h1><p class="text-gray-600">Test all advanced features from this dashboard. Open browser DevTools to monitor network requests and console logs.</p></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-6"><div class="bg-white p-6 rounded-lg shadow border"><h3 class="text-lg font-semibold mb-4">🔍 Analytics Testing</h3><div class="space-y-2"><button class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">Test Car View Event</button><button class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">Test Search Event</button><button class="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700">Test Contact Form Event</button></div><div class="mt-4 text-xs text-gray-600">Open browser DevTools → Network tab to see analytics requests</div></div><div class="bg-white p-6 rounded-lg shadow border"><h3 class="text-lg font-semibold mb-4">📱 Social Share Testing</h3><div class="space-y-4"><div><h4 class="font-medium mb-2">Test Social Sharing:</h4><div class="flex space-x-2"><button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Facebook</button><button class="bg-blue-400 text-white px-4 py-2 rounded hover:bg-blue-500">Twitter</button><button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">WhatsApp</button></div></div><div class="text-sm text-gray-600"><p>To test full social sharing functionality:</p><ol class="list-decimal list-inside mt-2 space-y-1"><li>Import SocialShare component</li><li>Add social media platform configurations</li><li>Test on different devices and browsers</li></ol></div></div></div><div class="bg-white p-6 rounded-lg shadow border"><h3 class="text-lg font-semibold mb-4">🔔 Push Notification Testing</h3><div class="space-y-4"><div><button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Test Browser Notification</button></div><div class="text-sm"><strong>Status:</strong> <!-- -->Not supported</div><div class="text-sm text-gray-600"><p>To test full push notification functionality:</p><ol class="list-decimal list-inside mt-2 space-y-1"><li>Configure VAPID keys in environment variables</li><li>Import NotificationSettings component</li><li>Test subscription management</li><li>Test admin notification sender</li><li>Test on mobile devices with HTTPS</li></ol></div></div></div><div class="bg-white p-6 rounded-lg shadow border"><h3 class="text-lg font-semibold mb-4">📱 PWA Status</h3><div class="space-y-4"><button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Check PWA Status</button><button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-2">View Manifest</button><div class="text-sm text-gray-600"><p><strong>Test Steps:</strong></p><ol class="list-decimal list-inside space-y-1 mt-2"><li>Check if service worker is registered</li><li>Test offline functionality (DevTools → Network → Offline)</li><li>Look for installation prompt (wait 5 seconds)</li><li>Check manifest.json accessibility</li></ol></div></div></div><div class="bg-white p-6 rounded-lg shadow border"><h3 class="text-lg font-semibold mb-4">🔌 API Testing</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4"><button class="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm">Health Check</button><button class="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm">Analytics</button><button class="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm">Notification Stats</button><button class="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm">Forum Posts</button><button class="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm">Social Feed</button></div><div class="space-y-2 max-h-64 overflow-auto"></div></div><div class="bg-white p-6 rounded-lg shadow border"><h3 class="text-lg font-semibold mb-4">🔍 Advanced Search Testing</h3><div class="space-y-4"><div class="flex space-x-2"><input type="text" placeholder="Search for cars..." class="flex-1 border border-gray-300 rounded px-3 py-2" value=""/><button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Search</button></div><div class="text-sm text-gray-600"><p>To test full advanced search functionality:</p><ol class="list-decimal list-inside mt-2 space-y-1"><li>Import AdvancedSearch component</li><li>Configure search filters and sorting</li><li>Test autocomplete and suggestions</li><li>Test saved searches functionality</li></ol></div></div></div></div><div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4"><h3 class="font-semibold text-yellow-800 mb-2">⚠️ Testing Notes:</h3><ul class="text-sm text-yellow-700 space-y-1"><li>• Make sure your .env.local file has the required environment variables</li><li>• Some features require HTTPS (use ngrok for local testing if needed)</li><li>• Push notifications need VAPID keys to be configured</li><li>• Check browser console for any error messages</li><li>• Test on different browsers and devices</li></ul></div></div></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-071536056086afa3.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[283,[\"8357\",\"static/chunks/8357-a6a9f0ccd9b659fa.js\",\"7177\",\"static/chunks/app/layout-13259b84ad90b9ac.js\"],\"AuthProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[1340,[\"8357\",\"static/chunks/8357-a6a9f0ccd9b659fa.js\",\"7177\",\"static/chunks/app/layout-13259b84ad90b9ac.js\"],\"default\"]\n6:I[6259,[\"8357\",\"static/chunks/8357-a6a9f0ccd9b659fa.js\",\"7177\",\"static/chunks/app/layout-13259b84ad90b9ac.js\"],\"GoogleAnalytics\"]\n7:I[3646,[\"9261\",\"static/chunks/app/test-features/page-2be93ce31202fa18.js\"],\"default\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/fe3acb13291c7c34.css\",\"style\"]\n:HL[\"/_next/static/css/446713b8705d64a7.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"CfJKZLrmeyhwfVzVt1AnI\",\"p\":\"\",\"c\":[\"\",\"test-features\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"test-features\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/fe3acb13291c7c34.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/446713b8705d64a7.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"__variable_e8ce0c __variable_d5a796\",\"children\":[\"$\",\"body\",null,{\"className\":\"font-inter antialiased\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"$L2\",null,{\"children\":[[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L5\",null,{}]]}],[\"$\",\"$L6\",null,{\"gaId\":\"your-google-analytics-4-id\"}]]}]}]]}],{\"children\":[\"test-features\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"divdr4zmrMCkJ-QBrOnBo\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"EBAM MOTORS - Quality Used Goods from Japan to Ghana\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"EBAM MOTORS\"}],[\"$\",\"link\",\"3\",{\"rel\":\"manifest\",\"href\":\"/manifest.json\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"meta\",\"4\",{\"name\":\"keywords\",\"content\":\"used cars Japan, export Ghana, EBAM Motors, Japanese cars, used electronics, furniture export, trading company, sustainable trade\"}],[\"$\",\"meta\",\"5\",{\"name\":\"creator\",\"content\":\"EBAM MOTORS\"}],[\"$\",\"meta\",\"6\",{\"name\":\"publisher\",\"content\":\"EBAM MOTORS\"}],[\"$\",\"meta\",\"7\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"8\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"9\",{\"name\":\"google-site-verification\",\"content\":\"your-google-verification-code\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:title\",\"content\":\"EBAM MOTORS - Quality Used Goods from Japan to Ghana\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:description\",\"content\":\"EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa.\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:url\",\"content\":\"https://ebammotors.com\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:site_name\",\"content\":\"EBAM MOTORS\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:title\",\"content\":\"EBAM MOTORS - Quality Used Goods from Japan to Ghana\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:description\",\"content\":\"EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa.\"}],[\"$\",\"link\",\"19\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"e:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>