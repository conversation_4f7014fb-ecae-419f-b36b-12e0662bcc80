(()=>{var e={};e.id=5957,e.ids=[5957],e.modules={2813:(e,s,t)=>{Promise.resolve().then(t.bind(t,20366))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6925:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71031)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\dashboard\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6943:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},7549:(e,s,t)=>{Promise.resolve().then(t.bind(t,71031))},8819:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10022:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12640:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},12941:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16189:(e,s,t)=>{"use strict";var a=t(65773);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},19080:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20366:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eS});var a=t(60687),r=t(43210),l=t(16189),i=t(62688);let n=(0,i.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var c=t(94478),d=t(28561),o=t(41312),x=t(58887);let m=(0,i.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),h=(0,i.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var u=t(99891),g=t(84027),p=t(11860),y=t(12941),j=t(99270),b=t(97051),f=t(43649),v=t(40083),N=t(93613),w=t(12597),k=t(13861);function A({onAuthenticated:e}){let[s,t]=(0,r.useState)(""),[l,i]=(0,r.useState)(!1),[n,c]=(0,r.useState)(!1),[d,o]=(0,r.useState)(""),[x,m]=(0,r.useState)(0),h=async a=>{a.preventDefault(),c(!0),o("");try{let a=await fetch("/api/admin/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:s})}),r=await a.json();r.success?e():(o(r.message||"Invalid password"),m(e=>e+1),t(""))}catch(e){console.error("Auth error:",e),o("Authentication failed. Please try again."),m(e=>e+1)}finally{c(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(u.A,{className:"w-8 h-8 text-blue-600"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Admin Access"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Enter your password to access the admin dashboard"})]}),d&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-red-700 text-sm font-medium",children:d}),x>=3&&(0,a.jsx)("p",{className:"text-red-600 text-xs mt-1",children:"Multiple failed attempts detected. Please wait before trying again."})]})]}),(0,a.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Admin Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",type:l?"text":"password",value:s,onChange:e=>t(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"Enter admin password",required:!0,disabled:n}),(0,a.jsx)("button",{type:"button",onClick:()=>i(!l),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:n,children:l?(0,a.jsx)(w.A,{className:"w-5 h-5"}):(0,a.jsx)(k.A,{className:"w-5 h-5"})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:n||!s.trim(),className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:n?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Authenticating..."]}):"Access Dashboard"})]}),(0,a.jsx)("div",{className:"mt-8 p-4 bg-gray-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Security Notice"}),(0,a.jsx)("p",{children:"This is a secure admin area. All access attempts are logged and monitored."})]})]})})]}),(0,a.jsx)("div",{className:"text-center mt-6",children:(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"EBAM Motors Admin Dashboard v2.0"})})]})})}var C=t(5336),S=t(48730),M=t(23928),_=t(25541),I=t(12640);function P(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0);if(t)return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]},s))})});if(!e)return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsx)("p",{className:"text-gray-600",children:"Failed to load dashboard data."})});let i=[{title:"Total Cars",value:e.totalCars,icon:c.A,color:"blue",change:"+5 this month"},{title:"Customers",value:e.totalCustomers,icon:o.A,color:"green",change:"+12 this month"},{title:"Orders",value:e.totalOrders,icon:d.A,color:"purple",change:"+3 this week"},{title:"Pending Reviews",value:e.pendingReviews,icon:x.A,color:"orange",change:"Needs attention"}],n=e=>{switch(e){case"success":return(0,a.jsx)(C.A,{className:"w-4 h-4 text-green-500"});case"warning":return(0,a.jsx)(f.A,{className:"w-4 h-4 text-yellow-500"});case"error":return(0,a.jsx)(f.A,{className:"w-4 h-4 text-red-500"});default:return(0,a.jsx)(S.A,{className:"w-4 h-4 text-gray-500"})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:i.map((e,s)=>{let t=e.icon;return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.change})]}),(0,a.jsx)("div",{className:`p-3 rounded-full bg-${e.color}-100`,children:(0,a.jsx)(t,{className:`w-6 h-6 text-${e.color}-600`})})]})},s)})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Revenue Overview"}),(0,a.jsx)(M.A,{className:"w-5 h-5 text-green-600"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Revenue"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["\xa5",e.totalRevenue.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[e.monthlyGrowth>0?(0,a.jsx)(_.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,a.jsx)(I.A,{className:"w-4 h-4 text-red-500 mr-1"}),(0,a.jsxs)("span",{className:`text-sm font-medium ${e.monthlyGrowth>0?"text-green-600":"text-red-600"}`,children:[e.monthlyGrowth>0?"+":"",e.monthlyGrowth,"% from last month"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Health"}),(0,a.jsx)(h,{className:`w-5 h-5 ${"healthy"===e.systemHealth?"text-green-600":"warning"===e.systemHealth?"text-yellow-600":"text-red-600"}`})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:["healthy"===e.systemHealth&&(0,a.jsx)(C.A,{className:"w-5 h-5 text-green-500 mr-2"}),"warning"===e.systemHealth&&(0,a.jsx)(f.A,{className:"w-5 h-5 text-yellow-500 mr-2"}),"error"===e.systemHealth&&(0,a.jsx)(f.A,{className:"w-5 h-5 text-red-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:e.systemHealth})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"All systems operational. Last check: 2 minutes ago"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"space-y-4",children:e.recentActivity.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[n(e.status),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.timestamp})]})]},e.id))}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{className:"flex items-center text-sm text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-1"}),"View all activity"]})})]})]})]})}var L=t(78122);let D=(0,i.A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),E=(0,i.A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),R=(0,i.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),F=(0,i.A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),T=(0,i.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);function $(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)(!0),[c,d]=(0,r.useState)(30);(0,r.useRef)(null);let o=async()=>{try{let e=localStorage.getItem("admin_token"),t=await fetch("/api/admin/health",{headers:{Authorization:`Bearer ${e}`}});if(t.ok){let e=await t.json();s(e)}}catch(e){console.error("Failed to fetch health data:",e)}finally{l(!1)}};return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${(e=>{switch(e){case"healthy":return"text-green-600 bg-green-100";case"degraded":return"text-yellow-600 bg-yellow-100";case"unhealthy":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(e.status)}`,children:(e=>{switch(e){case"healthy":return(0,a.jsx)(C.A,{className:"w-5 h-5"});case"degraded":return(0,a.jsx)(S.A,{className:"w-5 h-5"});case"unhealthy":return(0,a.jsx)(f.A,{className:"w-5 h-5"});default:return(0,a.jsx)(h,{className:"w-5 h-5"})}})(e.status)}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 capitalize",children:["System ",e.status]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Last updated: ",new Date(e.timestamp).toLocaleTimeString()]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("label",{className:"text-sm text-gray-600 mr-2",children:"Auto-refresh:"}),(0,a.jsx)("button",{onClick:()=>{n(!i),i||o()},className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${i?"bg-blue-600":"bg-gray-200"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${i?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("button",{onClick:o,className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-1"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(D,{className:"w-5 h-5 text-gray-400 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Uptime"}),(0,a.jsx)("p",{className:"font-semibold",children:(e=>{let s=Math.floor(e/86400),t=Math.floor(e%86400/3600),a=Math.floor(e%3600/60);return s>0?`${s}d ${t}h ${a}m`:t>0?`${t}h ${a}m`:`${a}m`})(e.uptime)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(E,{className:"w-5 h-5 text-gray-400 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Requests (5m)"}),(0,a.jsx)("p",{className:"font-semibold",children:e.stats.requests})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(R,{className:"w-5 h-5 text-gray-400 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Avg Response"}),(0,a.jsxs)("p",{className:"font-semibold",children:[e.stats.averageResponseTime,"ms"]})]})]})]}),e.issues.length>0&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800 mb-2",children:"Active Issues"}),(0,a.jsx)("ul",{className:"text-sm text-yellow-700 space-y-1",children:e.issues.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 flex-shrink-0"}),e]},s))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Metrics"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Error Rate"}),(0,a.jsxs)("div",{className:"flex items-center",children:[e.stats.errorRate>5?(0,a.jsx)(_.A,{className:"w-4 h-4 text-red-500 mr-1"}):(0,a.jsx)(I.A,{className:"w-4 h-4 text-green-500 mr-1"}),(0,a.jsxs)("span",{className:`font-semibold ${e.stats.errorRate>5?"text-red-600":"text-green-600"}`,children:[e.stats.errorRate,"%"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Slow Requests"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:e.stats.slowRequests})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Total Requests"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:e.stats.requests})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Database Health"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsxs)("div",{className:"flex items-center",children:[e.database?.healthy?(0,a.jsx)(C.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,a.jsx)(f.A,{className:"w-4 h-4 text-red-500 mr-1"}),(0,a.jsx)("span",{className:`font-semibold ${e.database?.healthy?"text-green-600":"text-red-600"}`,children:e.database?.healthy?"Healthy":"Error"})]})]}),e.database?.latency&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Latency"}),(0,a.jsxs)("span",{className:"font-semibold text-gray-900",children:[e.database.latency,"ms"]})]}),e.database?.error&&(0,a.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:e.database.error})})]})]})]}),e.memory&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Memory Usage"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(D,{className:"w-6 h-6 text-blue-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"RSS"}),(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:[e.memory.rss," MB"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(F,{className:"w-6 h-6 text-green-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Heap Total"}),(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:[e.memory.heapTotal," MB"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(T,{className:"w-6 h-6 text-purple-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Heap Used"}),(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:[e.memory.heapUsed," MB"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(h,{className:"w-6 h-6 text-orange-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"External"}),(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:[e.memory.external," MB"]})]})]})]})]}):(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(f.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Health Data Unavailable"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Unable to fetch system health information."}),(0,a.jsx)("button",{onClick:o,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Retry"})]})})}let z=(0,i.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var q=t(19080),O=t(64398),U=t(80462),H=t(6943),V=t(25366);let B=(0,i.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var G=t(63143),J=t(88233),X=t(96882),W=t(31158),Y=t(10022),K=t(41862);function Q({isOpen:e,onClose:s,onImportComplete:t}){let[l,i]=(0,r.useState)(null),[n,c]=(0,r.useState)(!1),[d,o]=(0,r.useState)(null),[x,m]=(0,r.useState)(!1),h=(0,r.useRef)(null);if(!e)return null;let u=e=>{"text/csv"===e.type||e.name.endsWith(".csv")?(i(e),o(null)):alert("Please select a CSV file")},g=async()=>{if(l){c(!0);try{let e=new FormData;e.append("csvFile",l);let s=localStorage.getItem("admin_password")||"NDAAA5@sons&Daughters";e.append("adminKey",s),e.append("importedBy","admin");let a=await fetch("/api/cars/import",{method:"POST",body:e}),r=await a.json();o(r),r.success&&t()}catch(e){console.error("Import error:",e),o({success:!1,batch_id:"",total_rows:0,successful_imports:0,failed_imports:0,errors:["Failed to process import: "+(e instanceof Error?e.message:"Unknown error")],imported_cars:[]})}finally{c(!1)}}},y=()=>{i(null),o(null),c(!1),s()};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Bulk Import Cars from CSV"}),(0,a.jsx)("button",{onClick:y,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(p.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"p-6",children:d?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:`p-4 rounded-lg border ${d.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:(0,a.jsxs)("div",{className:"flex items-center",children:[d.success?(0,a.jsx)(C.A,{className:"w-6 h-6 text-green-600 mr-3"}):(0,a.jsx)(N.A,{className:"w-6 h-6 text-red-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:`font-medium ${d.success?"text-green-800":"text-red-800"}`,children:d.success?"Import Completed":"Import Failed"}),(0,a.jsxs)("p",{className:`text-sm ${d.success?"text-green-600":"text-red-600"}`,children:[d.successful_imports," of ",d.total_rows," cars imported successfully"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:d.total_rows}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Rows"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:d.successful_imports}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Successful"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:d.failed_imports}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Failed"})]})]}),d.errors.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Import Errors:"}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 max-h-40 overflow-y-auto",children:d.errors.map((e,s)=>(0,a.jsx)("p",{className:"text-sm text-red-700 mb-1",children:e},s))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:y,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Close"}),(0,a.jsx)("button",{onClick:()=>{i(null),o(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Import Another File"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(X.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-2",children:"How to import cars:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-1",children:[(0,a.jsx)("li",{children:"Download the CSV template below"}),(0,a.jsx)("li",{children:"Fill in your car data (required: car_id, make, model, year, price)"}),(0,a.jsx)("li",{children:"Upload the completed CSV file"}),(0,a.jsx)("li",{children:"Review the import results"})]})]})]})}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("button",{onClick:()=>{let e=new Blob([`car_id,make,model,year,title,price,original_price,mileage,fuel_type,transmission,engine_size,drive_type,seats,doors,body_type,body_condition,interior_condition,exterior_color,interior_color,main_image,images,image_folder,specs,features,status,stock_quantity,location,description,is_featured
toyota_voxy_2012_001,Toyota,Voxy,2012,Toyota Voxy 2012 - 8 Seater Family Van,300000,350000,85000,Gasoline,CVT,2.0L,2WD,8,5,Van,Good,Good,Silver,Black,/images/toyota/voxy/2012/main.jpg,"/images/toyota/voxy/2012/1.jpg,/images/toyota/voxy/2012/2.jpg",toyota/voxy/2012,"Air Conditioning,Power Steering,Electric Windows","Family Car,Spacious,Reliable",Available,1,Japan,Excellent condition Toyota Voxy with low mileage,false
honda_freed_2015_001,Honda,Freed,2015,Honda Freed 2015 - Compact Minivan,420000,450000,65000,Hybrid,CVT,1.5L,2WD,6,5,Minivan,Excellent,Excellent,White,Gray,/images/honda/freed/2015/main.jpg,"/images/honda/freed/2015/1.jpg,/images/honda/freed/2015/2.jpg",honda/freed/2015,"Hybrid Engine,Navigation System,Backup Camera","Fuel Efficient,Compact,Modern",Available,1,Japan,Low mileage Honda Freed hybrid in excellent condition,true`],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="car_import_template.csv",document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(s),document.body.removeChild(t)},className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)(W.A,{className:"w-4 h-4 mr-2"}),"Download CSV Template"]})}),(0,a.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${x?"border-blue-400 bg-blue-50":l?"border-green-400 bg-green-50":"border-gray-300 hover:border-gray-400"}`,onDrop:e=>{e.preventDefault(),m(!1);let s=Array.from(e.dataTransfer.files);s.length>0&&u(s[0])},onDragOver:e=>{e.preventDefault(),m(!0)},onDragLeave:e=>{e.preventDefault(),m(!1)},children:[l?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)(Y.A,{className:"w-8 h-8 text-green-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-800",children:l.name}),(0,a.jsxs)("p",{className:"text-xs text-green-600",children:[(l.size/1024).toFixed(1)," KB"]})]})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)(z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900 mb-2",children:"Drop your CSV file here"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"or click to browse files"}),(0,a.jsx)("button",{onClick:()=>h.current?.click(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Select CSV File"})]}),(0,a.jsx)("input",{ref:h,type:"file",accept:".csv",onChange:e=>{let s=e.target.files?.[0];s&&u(s)},className:"hidden"})]}),l&&(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)("button",{onClick:g,disabled:n,className:"flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Importing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(z,{className:"w-4 h-4 mr-2"}),"Import Cars"]})})})]})})]})})}function Z(){let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)(""),[d,o]=(0,r.useState)({make:"",model:"",status:"",year_min:"",year_max:"",price_min:"",price_max:"",is_featured:"",location:""}),[x,m]=(0,r.useState)("grid"),[h,u]=(0,r.useState)([]),[g,p]=(0,r.useState)(!1),[y,b]=(0,r.useState)(1),[v,N]=(0,r.useState)(0),[w,A]=(0,r.useState)("created_at"),[S,M]=(0,r.useState)("desc"),[_,I]=(0,r.useState)(!1),[P,L]=(0,r.useState)({total:0,available:0,sold:0,featured:0,lowStock:0}),D=async()=>{l(!0);try{let e=localStorage.getItem("admin_token"),t=new URLSearchParams({page:y.toString(),per_page:"20",search:i,sort_by:w,sort_order:S,...Object.fromEntries(Object.entries(d).filter(([e,s])=>""!==s))}),a=await fetch(`/api/admin/cars?${t}`,{headers:{Authorization:`Bearer ${e}`}});if(a.ok){let e=await a.json();s(e.cars||[]),N(e.total_count||0)}}catch(e){console.error("Error loading cars:",e)}finally{l(!1)}},E=async()=>{try{let e=localStorage.getItem("admin_token"),s=await fetch("/api/admin/cars/stats",{headers:{Authorization:`Bearer ${e}`}});if(s.ok){let e=await s.json();L(e)}}catch(e){console.error("Error loading stats:",e)}},R=e=>{n(e),b(1)},F=(e,s)=>{o(t=>({...t,[e]:s})),b(1)},T=e=>{u(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},$=async e=>{if(0!==h.length)try{let s=localStorage.getItem("admin_token");(await fetch("/api/admin/cars/bulk",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({action:e,car_ids:h})})).ok&&(await D(),await E(),u([]))}catch(e){console.error("Error performing bulk action:",e)}},X=e=>{switch(e.toLowerCase()){case"available":return"text-green-600 bg-green-100";case"sold":default:return"text-gray-600 bg-gray-100";case"reserved":return"text-yellow-600 bg-yellow-100";case"pending":return"text-blue-600 bg-blue-100"}},W=(e,s="JPY")=>new Intl.NumberFormat("ja-JP",{style:"currency",currency:s,minimumFractionDigits:0}).format(e);return t&&0===e.length?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Car Inventory"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage your vehicle inventory and listings"})]}),(0,a.jsxs)("button",{onClick:()=>I(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,a.jsx)(z,{className:"w-4 h-4 mr-2"}),"Bulk Import CSV"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(c.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.total}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Cars"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(C.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.available}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Available"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(q.A,{className:"w-6 h-6 text-gray-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.sold}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Sold"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(O.A,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.featured}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Featured"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(f.A,{className:"w-6 h-6 text-red-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.lowStock}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Low Stock"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search cars by make, model, or ID...",value:i,onChange:e=>R(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>p(!g),className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(U.A,{className:"w-4 h-4 mr-2"}),"Filters"]}),(0,a.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[(0,a.jsx)("button",{onClick:()=>m("grid"),className:`p-2 ${"grid"===x?"bg-blue-50 text-blue-600":"text-gray-400"}`,children:(0,a.jsx)(H.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>m("list"),className:`p-2 ${"list"===x?"bg-blue-50 text-blue-600":"text-gray-400"}`,children:(0,a.jsx)(V.A,{className:"w-4 h-4"})})]})]})]}),g&&(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("select",{value:d.make,onChange:e=>F("make",e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Makes"}),(0,a.jsx)("option",{value:"Toyota",children:"Toyota"}),(0,a.jsx)("option",{value:"Honda",children:"Honda"}),(0,a.jsx)("option",{value:"Nissan",children:"Nissan"}),(0,a.jsx)("option",{value:"Mazda",children:"Mazda"})]}),(0,a.jsxs)("select",{value:d.status,onChange:e=>F("status",e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"Available",children:"Available"}),(0,a.jsx)("option",{value:"Sold",children:"Sold"}),(0,a.jsx)("option",{value:"Reserved",children:"Reserved"}),(0,a.jsx)("option",{value:"Pending",children:"Pending"})]}),(0,a.jsx)("input",{type:"number",placeholder:"Min Year",value:d.year_min,onChange:e=>F("year_min",e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)("input",{type:"number",placeholder:"Max Year",value:d.year_max,onChange:e=>F("year_max",e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("button",{onClick:()=>{o({make:"",model:"",status:"",year_min:"",year_max:"",price_min:"",price_max:"",is_featured:"",location:""}),b(1)},className:"text-sm text-gray-600 hover:text-gray-900",children:"Clear all filters"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",e.length," of ",v," cars"]})]})]})]}),h.length>0&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[h.length," car",h.length>1?"s":""," selected"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>$("feature"),className:"px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 transition-colors",children:"Feature"}),(0,a.jsx)("button",{onClick:()=>$("unfeature"),className:"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors",children:"Unfeature"}),(0,a.jsx)("button",{onClick:()=>$("delete"),className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors",children:"Delete"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===e.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(c.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No cars found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters"})]}):"grid"===x?(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow",children:[(0,a.jsxs)("div",{className:"relative",children:[e.main_image?(0,a.jsx)("img",{src:e.main_image,alt:e.title,className:"w-full h-48 object-cover"}):(0,a.jsx)("div",{className:"w-full h-48 bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(B,{className:"w-12 h-12 text-gray-400"})}),(0,a.jsx)("div",{className:"absolute top-2 left-2",children:(0,a.jsx)("input",{type:"checkbox",checked:h.includes(e.id),onChange:()=>T(e.id),className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"})}),(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${X(e.status)}`,children:e.status})}),e.is_featured&&(0,a.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,a.jsx)(O.A,{className:"w-5 h-5 text-yellow-500 fill-current"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:[e.make," ",e.model," ",e.year]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("span",{className:"text-lg font-bold text-blue-600",children:W(e.price,e.currency)}),e.original_price&&e.original_price>e.price&&(0,a.jsx)("span",{className:"text-sm text-gray-500 line-through",children:W(e.original_price,e.currency)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 mb-3",children:[(0,a.jsx)("span",{children:e.mileage?`${e.mileage.toLocaleString()} km`:"N/A"}),(0,a.jsx)("span",{children:e.fuel_type||"N/A"}),(0,a.jsx)("span",{children:e.transmission||"N/A"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["Stock: ",e.stock_quantity]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(k.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"p-1 text-gray-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(G.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"p-1 text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(J.A,{className:"w-4 h-4"})})]})]})]})]},e.id))})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left",children:(0,a.jsx)("input",{type:"checkbox",checked:h.length===e.length&&e.length>0,onChange:()=>{h.length===e.length?u([]):u(e.map(e=>e.id))},className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("input",{type:"checkbox",checked:h.includes(e.id),onChange:()=>T(e.id),className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.main_image?(0,a.jsx)("img",{src:e.main_image,alt:e.title,className:"w-12 h-12 object-cover rounded-lg mr-4"}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-4",children:(0,a.jsx)(B,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.title}),e.is_featured&&(0,a.jsx)(O.A,{className:"w-4 h-4 text-yellow-500 fill-current ml-2"})]}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.make," ",e.model," ",e.year]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:W(e.price,e.currency)}),e.original_price&&e.original_price>e.price&&(0,a.jsx)("div",{className:"text-sm text-gray-500 line-through",children:W(e.original_price,e.currency)})]}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${X(e.status)}`,children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.stock_quantity}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(k.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(G.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(J.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})}),v>20&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Showing ",(y-1)*20+1," to ",Math.min(20*y,v)," of ",v," results"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>b(e=>Math.max(1,e-1)),disabled:1===y,className:"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("span",{className:"px-3 py-1 bg-blue-600 text-white rounded text-sm",children:y}),(0,a.jsx)("button",{onClick:()=>b(e=>e+1),disabled:20*y>=v,className:"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})}),(0,a.jsx)(Q,{isOpen:_,onClose:()=>I(!1),onImportComplete:()=>{I(!1),D(),E()}})]})}let ee=(0,i.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var es=t(97992),et=t(41550),ea=t(40228);function er(){let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)(""),[c,d]=(0,r.useState)("all"),[o,m]=(0,r.useState)(null),[h,u]=(0,r.useState)([]),[g,y]=(0,r.useState)({total:0,pending:0,approved:0,rejected:0,averageRating:0,recentSubmissions:0}),b=async()=>{l(!0);try{let e=localStorage.getItem("admin_token"),t=await fetch("/api/reviews",{headers:{Authorization:`Bearer ${e}`}});if(t.ok){let e=await t.json();s(e.reviews||[]),f(e.reviews||[])}}catch(e){console.error("Error fetching reviews:",e)}finally{l(!1)}},f=e=>{let s=e.length,t=e.filter(e=>"pending"===e.status).length,a=e.filter(e=>"approved"===e.status).length,r=e.filter(e=>"rejected"===e.status).length,l=e.filter(e=>"approved"===e.status),i=l.length>0?l.reduce((e,s)=>e+s.rating,0)/l.length:0,n=new Date;n.setDate(n.getDate()-7),y({total:s,pending:t,approved:a,rejected:r,averageRating:Math.round(10*i)/10,recentSubmissions:e.filter(e=>new Date(e.submittedAt)>n).length})},v=async(e,s)=>{try{let t=localStorage.getItem("admin_token");(await fetch("/api/admin/reviews/moderate",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({reviewId:e,status:s})})).ok?await b():alert("Failed to moderate review")}catch(e){console.error("Error moderating review:",e),alert("Error moderating review")}},N=async e=>{if(0!==h.length)try{let s=localStorage.getItem("admin_token");(await fetch("/api/admin/reviews/bulk",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({action:e,reviewIds:h})})).ok&&(await b(),u([]))}catch(e){console.error("Error performing bulk action:",e)}},w=e=>{u(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},k=e.filter(e=>{let s=!i||e.name.toLowerCase().includes(i.toLowerCase())||e.location.toLowerCase().includes(i.toLowerCase())||e.review.toLowerCase().includes(i.toLowerCase()),t="all"===c||e.status===c,a=!o||e.rating===o;return s&&t&&a}),A=e=>{switch(e){case"approved":return"text-green-600 bg-green-100";case"rejected":return"text-red-600 bg-red-100";case"pending":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},C=e=>Array.from({length:5},(s,t)=>(0,a.jsx)(O.A,{className:`w-4 h-4 ${t<e?"text-yellow-400 fill-current":"text-gray-300"}`},t));return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Review Moderation"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage customer reviews and feedback"})]}),(0,a.jsxs)("button",{onClick:b,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-6 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(x.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.total}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Reviews"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(S.A,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.pending}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Pending"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(ee,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.approved}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Approved"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(p.A,{className:"w-6 h-6 text-red-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.rejected}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Rejected"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(O.A,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.averageRating}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Avg Rating"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(_.A,{className:"w-6 h-6 text-indigo-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.recentSubmissions}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"This Week"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search reviews by name, location, or content...",value:i,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"approved",children:"Approved"}),(0,a.jsx)("option",{value:"rejected",children:"Rejected"})]}),(0,a.jsxs)("select",{value:o||"",onChange:e=>m(e.target.value?parseInt(e.target.value):null),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Ratings"}),(0,a.jsx)("option",{value:"5",children:"5 Stars"}),(0,a.jsx)("option",{value:"4",children:"4 Stars"}),(0,a.jsx)("option",{value:"3",children:"3 Stars"}),(0,a.jsx)("option",{value:"2",children:"2 Stars"}),(0,a.jsx)("option",{value:"1",children:"1 Star"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["Showing ",k.length," of ",e.length," reviews"]}),i&&(0,a.jsx)("button",{onClick:()=>n(""),className:"text-blue-600 hover:text-blue-700",children:"Clear search"})]})]}),h.length>0&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[h.length," review",h.length>1?"s":""," selected"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>N("approve"),className:"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors",children:"Approve"}),(0,a.jsx)("button",{onClick:()=>N("reject"),className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors",children:"Reject"}),(0,a.jsx)("button",{onClick:()=>N("delete"),className:"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors",children:"Delete"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===k.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(x.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No reviews found"}),(0,a.jsx)("p",{className:"text-gray-600",children:i?"Try adjusting your search or filters":"No reviews have been submitted yet"})]}):(0,a.jsxs)("div",{className:"divide-y divide-gray-200",children:[(0,a.jsx)("div",{className:"p-4 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:h.length===k.length&&k.length>0,onChange:()=>{h.length===k.length?u([]):u(k.map(e=>e.id))},className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 mr-4"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Select All"})]})}),k.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("input",{type:"checkbox",checked:h.includes(e.id),onChange:()=>w(e.id),className:"w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 mt-1"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mt-1",children:[(0,a.jsx)(es.A,{className:"w-4 h-4 mr-1"}),e.location,(0,a.jsx)(et.A,{className:"w-4 h-4 ml-3 mr-1"}),e.email]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("div",{className:"flex items-center",children:[C(e.rating),(0,a.jsxs)("span",{className:"ml-2 text-sm font-medium text-gray-700",children:[e.rating,"/5"]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${A(e.status)}`,children:e.status}),"pending"===e.status&&(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>v(e.id,"approved"),className:"p-1 text-green-600 hover:bg-green-100 rounded transition-colors",title:"Approve",children:(0,a.jsx)(ee,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>v(e.id,"rejected"),className:"p-1 text-red-600 hover:bg-red-100 rounded transition-colors",title:"Reject",children:(0,a.jsx)(p.A,{className:"w-4 h-4"})})]})]})]}),e.title&&(0,a.jsx)("h5",{className:"font-medium text-gray-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-700 mb-3 leading-relaxed",children:e.review}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(ea.A,{className:"w-4 h-4 mr-1"}),new Date(e.submittedAt).toLocaleDateString()]}),e.vehiclePurchased&&(0,a.jsxs)("span",{children:["Vehicle: ",e.vehiclePurchased]})]}),(0,a.jsxs)("div",{className:"text-xs",children:["Words: ",e.review.trim().split(/\s+/).length]})]})]})]})},e.id))]})})]})}var el=t(23026);let ei=(0,i.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var en=t(48340),ec=t(58869),ed=t(8819);function eo({isOpen:e,onClose:s,onCustomerAdded:t}){let[l,i]=(0,r.useState)({personalInfo:{name:"",email:"",phone:""},address:{street:"",city:"",state:"",country:"Ghana",postalCode:""},preferences:{communicationPreference:"email",currency:"JPY",notifications:{email:!0,sms:!1,marketing:!1}},tags:[],notes:""}),[n,c]=(0,r.useState)(!1),[d,o]=(0,r.useState)("");if(!e)return null;let x=(e,s,t)=>{i(a=>({...a,[e]:{...a[e],[s]:t}}))},m=(e,s,t,a)=>{i(r=>({...r,[e]:{...r[e],[s]:{...r[e][s],[t]:a}}}))},h=()=>{d.trim()&&!l.tags.includes(d.trim())&&(i(e=>({...e,tags:[...e.tags,d.trim()]})),o(""))},u=e=>{i(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},g=async e=>{e.preventDefault(),c(!0);try{let e=await fetch("/api/customers",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...l,adminKey:"NDAAA5@sons&Daughters"})});if(e.ok)t(),s(),i({personalInfo:{name:"",email:"",phone:""},address:{street:"",city:"",state:"",country:"Ghana",postalCode:""},preferences:{communicationPreference:"email",currency:"JPY",notifications:{email:!0,sms:!1,marketing:!1}},tags:[],notes:""});else{let s=await e.json();alert("Error creating customer: "+s.message)}}catch(e){console.error("Error creating customer:",e),alert("Error creating customer. Please try again.")}finally{c(!1)}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Add New Customer"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(p.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("form",{onSubmit:g,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ec.A,{className:"w-5 h-5 mr-2"}),"Personal Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:l.personalInfo.name,onChange:e=>x("personalInfo","name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",required:!0,value:l.personalInfo.email,onChange:e=>x("personalInfo","email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter email address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",value:l.personalInfo.phone,onChange:e=>x("personalInfo","phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"+233 XX XXX XXXX"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(es.A,{className:"w-5 h-5 mr-2"}),"Address"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Street Address"}),(0,a.jsx)("input",{type:"text",value:l.address.street,onChange:e=>x("address","street",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter street address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City"}),(0,a.jsx)("input",{type:"text",value:l.address.city,onChange:e=>x("address","city",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter city"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Country"}),(0,a.jsxs)("select",{value:l.address.country,onChange:e=>x("address","country",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"Ghana",children:"Ghana"}),(0,a.jsx)("option",{value:"Nigeria",children:"Nigeria"}),(0,a.jsx)("option",{value:"Kenya",children:"Kenya"}),(0,a.jsx)("option",{value:"South Africa",children:"South Africa"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Preferences"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Communication"}),(0,a.jsxs)("select",{value:l.preferences.communicationPreference,onChange:e=>x("preferences","communicationPreference",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"email",children:"Email"}),(0,a.jsx)("option",{value:"phone",children:"Phone"}),(0,a.jsx)("option",{value:"sms",children:"SMS"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Currency"}),(0,a.jsxs)("select",{value:l.preferences.currency,onChange:e=>x("preferences","currency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"JPY",children:"Japanese Yen (\xa5)"}),(0,a.jsx)("option",{value:"USD",children:"US Dollar ($)"}),(0,a.jsx)("option",{value:"GHS",children:"Ghana Cedi (₵)"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notification Preferences"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:l.preferences.notifications.email,onChange:e=>m("preferences","notifications","email",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Email notifications"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:l.preferences.notifications.sms,onChange:e=>m("preferences","notifications","sms",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"SMS notifications"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:l.preferences.notifications.marketing,onChange:e=>m("preferences","notifications","marketing",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Marketing communications"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tags"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("input",{type:"text",value:d,onChange:e=>o(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),h()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add a tag and press Enter"}),(0,a.jsx)("button",{type:"button",onClick:h,className:"px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200",children:"Add"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:l.tags.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>u(e),className:"ml-1 text-blue-600 hover:text-blue-800",children:(0,a.jsx)(p.A,{className:"w-3 h-3"})})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes"}),(0,a.jsx)("textarea",{value:l.notes,onChange:e=>i(s=>({...s,notes:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add any additional notes about this customer..."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[(0,a.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:n,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed.A,{className:"w-4 h-4 mr-2"}),"Create Customer"]})})]})]})]})})}function ex({isOpen:e,onClose:s,onLeadCreated:t}){let[l,i]=(0,r.useState)({personalInfo:{name:"",email:"",phone:""},leadInfo:{source:"website",status:"new",priority:"medium",interestedVehicle:"",budget:"",timeline:"",notes:""},tags:[]}),[n,c]=(0,r.useState)(!1),[d,o]=(0,r.useState)("");if(!e)return null;let x=(e,s,t)=>{i(a=>({...a,[e]:{...a[e],[s]:t}}))},m=()=>{d.trim()&&!l.tags.includes(d.trim())&&(i(e=>({...e,tags:[...e.tags,d.trim()]})),o(""))},h=e=>{i(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},u=async e=>{e.preventDefault(),c(!0);try{let e=await fetch("/api/leads",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...l,adminKey:"NDAAA5@sons&Daughters"})});if(e.ok)t(),s(),i({personalInfo:{name:"",email:"",phone:""},leadInfo:{source:"website",status:"new",priority:"medium",interestedVehicle:"",budget:"",timeline:"",notes:""},tags:[]});else{let s=await e.json();alert("Error creating lead: "+s.message)}}catch(e){console.error("Error creating lead:",e),alert("Error creating lead. Please try again.")}finally{c(!1)}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Create New Lead"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(p.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("form",{onSubmit:u,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ec.A,{className:"w-5 h-5 mr-2"}),"Contact Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:l.personalInfo.name,onChange:e=>x("personalInfo","name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",required:!0,value:l.personalInfo.email,onChange:e=>x("personalInfo","email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter email address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",value:l.personalInfo.phone,onChange:e=>x("personalInfo","phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"+233 XX XXX XXXX"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ei,{className:"w-5 h-5 mr-2"}),"Lead Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Lead Source"}),(0,a.jsxs)("select",{value:l.leadInfo.source,onChange:e=>x("leadInfo","source",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"website",children:"Website"}),(0,a.jsx)("option",{value:"social_media",children:"Social Media"}),(0,a.jsx)("option",{value:"referral",children:"Referral"}),(0,a.jsx)("option",{value:"phone_call",children:"Phone Call"}),(0,a.jsx)("option",{value:"email",children:"Email"}),(0,a.jsx)("option",{value:"walk_in",children:"Walk-in"}),(0,a.jsx)("option",{value:"advertisement",children:"Advertisement"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,a.jsxs)("select",{value:l.leadInfo.priority,onChange:e=>x("leadInfo","priority",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"low",children:"Low"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"urgent",children:"Urgent"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:l.leadInfo.status,onChange:e=>x("leadInfo","status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"new",children:"New"}),(0,a.jsx)("option",{value:"contacted",children:"Contacted"}),(0,a.jsx)("option",{value:"qualified",children:"Qualified"}),(0,a.jsx)("option",{value:"proposal",children:"Proposal Sent"}),(0,a.jsx)("option",{value:"negotiation",children:"In Negotiation"}),(0,a.jsx)("option",{value:"closed_won",children:"Closed Won"}),(0,a.jsx)("option",{value:"closed_lost",children:"Closed Lost"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Timeline"}),(0,a.jsxs)("select",{value:l.leadInfo.timeline,onChange:e=>x("leadInfo","timeline",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select timeline"}),(0,a.jsx)("option",{value:"immediate",children:"Immediate (within 1 week)"}),(0,a.jsx)("option",{value:"short_term",children:"Short term (1-4 weeks)"}),(0,a.jsx)("option",{value:"medium_term",children:"Medium term (1-3 months)"}),(0,a.jsx)("option",{value:"long_term",children:"Long term (3+ months)"}),(0,a.jsx)("option",{value:"just_browsing",children:"Just browsing"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Interested Vehicle"}),(0,a.jsx)("input",{type:"text",value:l.leadInfo.interestedVehicle,onChange:e=>x("leadInfo","interestedVehicle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Toyota Voxy 2012"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Budget Range"}),(0,a.jsxs)("select",{value:l.leadInfo.budget,onChange:e=>x("leadInfo","budget",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select budget range"}),(0,a.jsx)("option",{value:"under_200k",children:"Under \xa5200,000"}),(0,a.jsx)("option",{value:"200k_400k",children:"\xa5200,000 - \xa5400,000"}),(0,a.jsx)("option",{value:"400k_600k",children:"\xa5400,000 - \xa5600,000"}),(0,a.jsx)("option",{value:"600k_800k",children:"\xa5600,000 - \xa5800,000"}),(0,a.jsx)("option",{value:"800k_1m",children:"\xa5800,000 - \xa51,000,000"}),(0,a.jsx)("option",{value:"over_1m",children:"Over \xa51,000,000"}),(0,a.jsx)("option",{value:"flexible",children:"Flexible"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tags"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("input",{type:"text",value:d,onChange:e=>o(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),m()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add a tag and press Enter"}),(0,a.jsx)("button",{type:"button",onClick:m,className:"px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200",children:"Add"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:l.tags.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 text-sm rounded-full",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>h(e),className:"ml-1 text-purple-600 hover:text-purple-800",children:(0,a.jsx)(p.A,{className:"w-3 h-3"})})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes"}),(0,a.jsx)("textarea",{value:l.leadInfo.notes,onChange:e=>x("leadInfo","notes",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add any additional notes about this lead, their requirements, conversation details, etc."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[(0,a.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:n,className:"flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed.A,{className:"w-4 h-4 mr-2"}),"Create Lead"]})})]})]})]})})}function em({isOpen:e,onClose:s,onFollowupScheduled:t}){let[l,i]=(0,r.useState)([]),[n,c]=(0,r.useState)({customerId:"",type:"email",priority:"medium",title:"",description:"",scheduledDate:"",scheduledTime:"",notes:""}),[d,o]=(0,r.useState)(!1),[x,m]=(0,r.useState)(!1);if(!e)return null;let h=(e,s)=>{c(t=>({...t,[e]:s}))},u=async e=>{e.preventDefault(),o(!0);try{let e=new Date(`${n.scheduledDate}T${n.scheduledTime}`).toISOString(),a=await fetch("/api/followups",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({customerId:n.customerId,type:n.type,status:"pending",priority:n.priority,title:n.title,description:n.description,scheduledDate:e,notes:n.notes,createdBy:"admin",adminKey:"NDAAA5@sons&Daughters"})});if(a.ok)t(),s(),c({customerId:"",type:"email",priority:"medium",title:"",description:"",scheduledDate:"",scheduledTime:"",notes:""});else{let e=await a.json();alert("Error scheduling follow-up: "+e.message)}}catch(e){console.error("Error scheduling follow-up:",e),alert("Error scheduling follow-up. Please try again.")}finally{o(!1)}},g=l.find(e=>e.id===n.customerId);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Schedule Follow-up"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(p.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("form",{onSubmit:u,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ec.A,{className:"w-5 h-5 mr-2"}),"Customer"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Customer *"}),x?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(K.A,{className:"w-5 h-5 animate-spin text-gray-400"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading customers..."})]}):(0,a.jsxs)("select",{required:!0,value:n.customerId,onChange:e=>h("customerId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Choose a customer"}),l.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.personalInfo.name," (",e.personalInfo.email,")"]},e.id))]}),g&&(0,a.jsxs)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Selected:"})," ",g.personalInfo.name]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"Email:"})," ",g.personalInfo.email]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(ea.A,{className:"w-5 h-5 mr-2"}),"Follow-up Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Follow-up Type *"}),(0,a.jsxs)("select",{required:!0,value:n.type,onChange:e=>h("type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"email",children:"Email"}),(0,a.jsx)("option",{value:"phone",children:"Phone Call"}),(0,a.jsx)("option",{value:"meeting",children:"Meeting"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,a.jsxs)("select",{value:n.priority,onChange:e=>h("priority",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"low",children:"Low"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"urgent",children:"Urgent"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scheduled Date *"}),(0,a.jsx)("input",{type:"date",required:!0,value:n.scheduledDate,onChange:e=>h("scheduledDate",e.target.value),min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scheduled Time *"}),(0,a.jsx)("input",{type:"time",required:!0,value:n.scheduledTime,onChange:e=>h("scheduledTime",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),(0,a.jsx)("input",{type:"text",required:!0,value:n.title,onChange:e=>h("title",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Follow up on Toyota Voxy inquiry"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),(0,a.jsx)("textarea",{required:!0,value:n.description,onChange:e=>h("description",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe the purpose of this follow-up..."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Additional Notes"}),(0,a.jsx)("textarea",{value:n.notes,onChange:e=>h("notes",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Any additional notes or reminders for this follow-up..."})]}),n.type&&(0,a.jsx)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:["email"===n.type&&(0,a.jsx)(et.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),"phone"===n.type&&(0,a.jsx)(en.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),"meeting"===n.type&&(0,a.jsx)(ea.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsxs)("p",{className:"font-medium mb-1",children:["email"===n.type&&"Email Follow-up","phone"===n.type&&"Phone Call Follow-up","meeting"===n.type&&"Meeting Follow-up"]}),(0,a.jsxs)("p",{children:["email"===n.type&&"An email reminder will be sent to you at the scheduled time.","phone"===n.type&&"A phone call reminder will be added to your tasks.","meeting"===n.type&&"A meeting reminder will be scheduled in your calendar."]})]})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[(0,a.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:d||!n.customerId,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Scheduling..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed.A,{className:"w-4 h-4 mr-2"}),"Schedule Follow-up"]})})]})]})]})})}function eh(){let[e,s]=(0,r.useState)("overview"),[t,l]=(0,r.useState)([]),[i,n]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[h,u]=(0,r.useState)(""),[g,p]=(0,r.useState)("all"),[y,b]=(0,r.useState)({customers:{total:0,active:0,new:0,vip:0},leads:{total:0,new:0,qualified:0,converted:0},revenue:{total:0,thisMonth:0,averageOrderValue:0},interactions:{total:0,thisWeek:0,responseRate:0}}),[f,v]=(0,r.useState)(!1),[N,w]=(0,r.useState)(!1),[A,C]=(0,r.useState)(!1),_=async()=>{d(!0);try{let e=localStorage.getItem("admin_token"),s=await fetch("/api/customers",{headers:{Authorization:`Bearer ${e}`}});if(s.ok){let e=await s.json();l(e.customers||[])}let t=await fetch("/api/leads",{headers:{Authorization:`Bearer ${e}`}});if(t.ok){let e=await t.json();n(e.leads||[])}let a=await fetch("/api/admin/crm/stats",{headers:{Authorization:`Bearer ${e}`}});if(a.ok){let e=await a.json();b(e)}}catch(e){console.error("Error fetching CRM data:",e)}finally{d(!1)}},I=e=>{switch(e.toLowerCase()){case"active":case"converted":return"text-green-600 bg-green-100";case"new":return"text-blue-600 bg-blue-100";case"qualified":return"text-purple-600 bg-purple-100";case"lost":return"text-red-600 bg-red-100";case"nurturing":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},P=e=>{switch(e.toLowerCase()){case"urgent":return"text-red-600 bg-red-100";case"high":return"text-orange-600 bg-orange-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},D=e=>new Intl.NumberFormat("ja-JP",{style:"currency",currency:"JPY",minimumFractionDigits:0}).format(e),E=t.filter(e=>{let s=!h||e.personalInfo.name.toLowerCase().includes(h.toLowerCase())||e.personalInfo.email.toLowerCase().includes(h.toLowerCase()),t="all"===g||e.status===g;return s&&t}),R=i.filter(e=>{let s=!h||e.customerInfo.name.toLowerCase().includes(h.toLowerCase())||e.customerInfo.email&&e.customerInfo.email.toLowerCase().includes(h.toLowerCase()),t="all"===g||e.status===g;return s&&t});return c?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"CRM Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage customers, leads, and relationships"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{onClick:_,className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-2"}),"Refresh"]}),(0,a.jsxs)("button",{onClick:()=>v(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(el.A,{className:"w-4 h-4 mr-2"}),"Add Customer"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[{id:"overview",label:"Overview",icon:m},{id:"customers",label:"Customers",icon:o.A},{id:"leads",label:"Leads",icon:ei},{id:"interactions",label:"Interactions",icon:x.A}].map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${e===t.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[(0,a.jsx)(r,{className:"w-4 h-4 mr-2"}),t.label]},t.id)})})]}),"overview"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y.customers.total}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["+",y.customers.new," new this month"]})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-blue-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Leads"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y.leads.total}),(0,a.jsxs)("p",{className:"text-sm text-purple-600",children:[y.leads.qualified," qualified"]})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(ei,{className:"w-6 h-6 text-purple-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:D(y.revenue.total)}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["Avg: ",D(y.revenue.averageOrderValue)]})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(M.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Interactions"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y.interactions.total}),(0,a.jsxs)("p",{className:"text-sm text-blue-600",children:[y.interactions.thisWeek," this week"]})]}),(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(x.A,{className:"w-6 h-6 text-orange-600"})})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>v(!0),className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(el.A,{className:"w-5 h-5 text-blue-600 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Add New Customer"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Create a new customer profile"})]})]}),(0,a.jsxs)("button",{onClick:()=>w(!0),className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(ei,{className:"w-5 h-5 text-purple-600 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Create Lead"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Add a new sales lead"})]})]}),(0,a.jsxs)("button",{onClick:()=>C(!0),className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(ea.A,{className:"w-5 h-5 text-green-600 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Schedule Follow-up"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Set reminder for customer contact"})]})]})]})]})]}),"customers"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search customers...",value:h,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:g,onChange:e=>p(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"new",children:"New"}),(0,a.jsx)("option",{value:"vip",children:"VIP"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===E.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(o.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No customers found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Orders"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Spent"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tier"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:E.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.personalInfo.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.personalInfo.email}),e.address.city&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,a.jsx)(es.A,{className:"w-3 h-3 mr-1"}),e.address.city,", ",e.address.country]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${I(e.status)}`,children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.totalOrders}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:D(e.totalSpent)}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(O.A,{className:"w-4 h-4 text-yellow-400 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:e.membershipTier})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(k.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(G.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-purple-600 transition-colors",children:(0,a.jsx)(et.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]}),"leads"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search leads...",value:h,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:g,onChange:e=>p(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"new",children:"New"}),(0,a.jsx)("option",{value:"contacted",children:"Contacted"}),(0,a.jsx)("option",{value:"qualified",children:"Qualified"}),(0,a.jsx)("option",{value:"converted",children:"Converted"}),(0,a.jsx)("option",{value:"lost",children:"Lost"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===R.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(ei,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No leads found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:R.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:e.customerInfo.name}),(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${I(e.status)}`,children:e.status}),(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${P(e.priority)}`,children:e.priority})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mb-3",children:[e.customerInfo.email&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(et.A,{className:"w-4 h-4 mr-1"}),e.customerInfo.email]}),e.customerInfo.phone&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(en.A,{className:"w-4 h-4 mr-1"}),e.customerInfo.phone]}),e.customerInfo.location&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(es.A,{className:"w-4 h-4 mr-1"}),e.customerInfo.location]})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-3",children:e.inquiry.message}),e.inquiry.productInterest&&(0,a.jsxs)("div",{className:"text-sm text-blue-600 mb-2",children:["Interest: ",e.inquiry.productInterest]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,a.jsx)(S.A,{className:"w-4 h-4 mr-1"}),"Created ",new Date(e.createdAt).toLocaleDateString(),(0,a.jsx)("span",{className:"mx-2",children:"•"}),"Source: ",e.source]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(k.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(G.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-purple-600 transition-colors",children:(0,a.jsx)(et.A,{className:"w-4 h-4"})})]})]})},e.id))})})]}),"interactions"===e&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(x.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Interactions Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Interaction tracking and communication history coming soon"})]})}),(0,a.jsx)(eo,{isOpen:f,onClose:()=>v(!1),onCustomerAdded:_}),(0,a.jsx)(ex,{isOpen:N,onClose:()=>w(!1),onLeadCreated:_}),(0,a.jsx)(em,{isOpen:A,onClose:()=>C(!1),onFollowupScheduled:_})]})}function eu(){let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)(""),[c,o]=(0,r.useState)("all"),[x,m]=(0,r.useState)("all"),[h,u]=(0,r.useState)([]),[g,p]=(0,r.useState)({total:0,pending:0,processing:0,completed:0,cancelled:0,totalRevenue:0,averageOrderValue:0,recentOrders:0}),y=async()=>{l(!0);try{let e=localStorage.getItem("admin_token"),t=await fetch("/api/orders",{headers:{Authorization:`Bearer ${e}`}});if(t.ok){let e=await t.json();s(e.orders||[]),b(e.orders||[])}}catch(e){console.error("Error fetching orders:",e)}finally{l(!1)}},b=e=>{let s=e.length,t=e.filter(e=>"pending_payment"===e.status||"pending"===e.status).length,a=e.filter(e=>"processing"===e.status||"confirmed"===e.status).length,r=e.filter(e=>"completed"===e.status||"delivered"===e.status).length,l=e.filter(e=>"cancelled"===e.status).length,i=e.filter(e=>"completed"===e.payment.status).reduce((e,s)=>e+s.totalAmount,0),n=new Date;n.setDate(n.getDate()-7),p({total:s,pending:t,processing:a,completed:r,cancelled:l,totalRevenue:i,averageOrderValue:s>0?i/s:0,recentOrders:e.filter(e=>new Date(e.createdAt)>n).length})},v=async e=>{try{let s=localStorage.getItem("admin_token"),t=await fetch(`/api/admin/orders/${e}/invoice`,{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(t.ok){let s=await t.blob(),a=window.URL.createObjectURL(s),r=document.createElement("a");r.href=a,r.download=`invoice-${e}.pdf`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}}catch(e){console.error("Error generating invoice:",e)}},N=e=>{switch(e.toLowerCase()){case"completed":case"delivered":case"paid":return"text-green-600 bg-green-100";case"processing":case"confirmed":case"shipped":return"text-blue-600 bg-blue-100";case"pending":case"pending_payment":return"text-yellow-600 bg-yellow-100";case"cancelled":case"failed":case"refunded":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},w=(e,s="JPY")=>new Intl.NumberFormat("ja-JP",{style:"currency",currency:s,minimumFractionDigits:0}).format(e),A=e.filter(e=>{let s=!i||e.orderNumber.toLowerCase().includes(i.toLowerCase())||e.customerInfo.name.toLowerCase().includes(i.toLowerCase())||e.customerInfo.email.toLowerCase().includes(i.toLowerCase())||e.vehicle.title.toLowerCase().includes(i.toLowerCase()),t="all"===c||e.status===c,a="all"===x||e.payment.status===x;return s&&t&&a});return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Order Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track and manage customer orders"})]}),(0,a.jsxs)("button",{onClick:y,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(d.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.total}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Orders"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(S.A,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.pending}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Pending"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(q.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.processing}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Processing"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(C.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.completed}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Completed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(f.A,{className:"w-6 h-6 text-red-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.cancelled}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Cancelled"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(M.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w(g.totalRevenue)}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Revenue"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-2",children:(0,a.jsx)(_.A,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.recentOrders}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"This Week"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search orders by number, customer, or vehicle...",value:i,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:c,onChange:e=>o(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"pending_payment",children:"Pending Payment"}),(0,a.jsx)("option",{value:"processing",children:"Processing"}),(0,a.jsx)("option",{value:"confirmed",children:"Confirmed"}),(0,a.jsx)("option",{value:"shipped",children:"Shipped"}),(0,a.jsx)("option",{value:"delivered",children:"Delivered"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,a.jsxs)("select",{value:x,onChange:e=>m(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Payments"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"processing",children:"Processing"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"refunded",children:"Refunded"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-4 text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["Showing ",A.length," of ",e.length," orders"]}),i&&(0,a.jsx)("button",{onClick:()=>n(""),className:"text-blue-600 hover:text-blue-700",children:"Clear search"})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:0===A.length?(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(d.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No orders found"}),(0,a.jsx)("p",{className:"text-gray-600",children:i?"Try adjusting your search or filters":"No orders have been placed yet"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Vehicle"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:A.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["#",e.orderNumber]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.id.slice(0,8),"..."]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.customerInfo.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.customerInfo.email}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,a.jsx)(es.A,{className:"w-3 h-3 mr-1"}),e.customerInfo.address.city,", ",e.customerInfo.address.country]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.vehicle.images[0]&&(0,a.jsx)("img",{src:e.vehicle.images[0],alt:e.vehicle.title,className:"w-12 h-12 object-cover rounded-lg mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.vehicle.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:w(e.vehicle.price,e.vehicle.currency)})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:w(e.totalAmount,e.currency)}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Shipping: ",w(e.shipping.cost,e.currency)]})]}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${N(e.payment.status)}`,children:e.payment.status}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:e.payment.method.name})]}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${N(e.status)}`,children:e.status}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:e.shipping.status})]}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>v(e.id),className:"text-gray-400 hover:text-blue-600 transition-colors",title:"Download Invoice",children:(0,a.jsx)(W.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",title:"View Details",children:(0,a.jsx)(k.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-purple-600 transition-colors",title:"Edit Order",children:(0,a.jsx)(G.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-orange-600 transition-colors",title:"Contact Customer",children:(0,a.jsx)(et.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]})}let eg=(0,i.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),ep=(0,i.A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]);function ey(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)("30d"),[x,u]=(0,r.useState)("overview"),g=async()=>{l(!0);try{let e=localStorage.getItem("admin_token"),t=await fetch(`/api/admin/analytics?range=${i}`,{headers:{Authorization:`Bearer ${e}`}});if(t.ok){let e=await t.json();s(e)}}catch(e){console.error("Error fetching analytics:",e)}finally{l(!1)}},p=async e=>{try{let s=localStorage.getItem("admin_token"),t=await fetch(`/api/admin/analytics/export?type=${e}&range=${i}`,{headers:{Authorization:`Bearer ${s}`}});if(t.ok){let s=await t.blob(),a=window.URL.createObjectURL(s),r=document.createElement("a");r.href=a,r.download=`analytics-${e}-${i}.csv`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}}catch(e){console.error("Error exporting report:",e)}},y=e=>new Intl.NumberFormat("ja-JP",{style:"currency",currency:"JPY",minimumFractionDigits:0}).format(e),j=e=>`${e>0?"+":""}${e.toFixed(1)}%`,b=e=>e>0?(0,a.jsx)(eg,{className:"w-4 h-4 text-green-500"}):e<0?(0,a.jsx)(ep,{className:"w-4 h-4 text-red-500"}):null,f=e=>e>0?"text-green-600":e<0?"text-red-600":"text-gray-600";return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"System Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Comprehensive business insights and reports"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("select",{value:i,onChange:e=>n(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"7d",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30d",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90d",children:"Last 90 days"}),(0,a.jsx)("option",{value:"1y",children:"Last year"})]}),(0,a.jsxs)("button",{onClick:g,className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-2"}),"Refresh"]}),(0,a.jsxs)("button",{onClick:()=>p("overview"),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(W.A,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[{id:"overview",label:"Overview",icon:m},{id:"sales",label:"Sales",icon:M.A},{id:"customers",label:"Customers",icon:o.A},{id:"products",label:"Products",icon:c.A},{id:"performance",label:"Performance",icon:ei}].map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>u(e.id),className:`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${x===e.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[(0,a.jsx)(s,{className:"w-4 h-4 mr-2"}),e.label]},e.id)})})]}),"overview"===x&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y(e.overview.totalRevenue)}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[b(e.overview.revenueGrowth),(0,a.jsx)("span",{className:`text-sm font-medium ${f(e.overview.revenueGrowth)}`,children:j(e.overview.revenueGrowth)})]})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(M.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.overview.totalOrders}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[b(e.overview.orderGrowth),(0,a.jsx)("span",{className:`text-sm font-medium ${f(e.overview.orderGrowth)}`,children:j(e.overview.orderGrowth)})]})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(d.A,{className:"w-6 h-6 text-blue-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.overview.totalCustomers}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[b(e.overview.customerGrowth),(0,a.jsx)("span",{className:`text-sm font-medium ${f(e.overview.customerGrowth)}`,children:j(e.overview.customerGrowth)})]})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-purple-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Conversion Rate"}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:[e.overview.conversionRate.toFixed(1),"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Visitors to customers"})]}),(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(ei,{className:"w-6 h-6 text-orange-600"})})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Sales Trends"}),(0,a.jsx)("div",{className:"space-y-4",children:e.salesTrends.slice(-6).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e.month}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:y(e.revenue)}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.orders," orders"]})]})]},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Top Products"}),(0,a.jsx)("div",{className:"space-y-4",children:e.topProducts.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-500 w-6",children:["#",s+1]}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.sales," sales"]})]})]}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:y(e.revenue)})]},e.id))})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Geographic Distribution"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:e.geographicData.slice(0,6).map((e,s)=>(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.customers}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.country}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.orders," orders • ",y(e.revenue)]})]},s))})]})]}),"performance"===x&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Average Order Value"}),(0,a.jsx)(M.A,{className:"w-5 h-5 text-green-600"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y(e.performanceMetrics.averageOrderValue)}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Per transaction"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Customer Lifetime Value"}),(0,a.jsx)(o.A,{className:"w-5 h-5 text-purple-600"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y(e.performanceMetrics.customerLifetimeValue)}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Average per customer"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Repeat Customer Rate"}),(0,a.jsx)(h,{className:"w-5 h-5 text-blue-600"})]}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:[e.performanceMetrics.repeatCustomerRate.toFixed(1),"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Return customers"})]})]})}),"overview"!==x&&"performance"!==x&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:[x.charAt(0).toUpperCase()+x.slice(1)," Analytics"]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Detailed ",x," analytics coming soon"]})]})})]}):(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Analytics Unavailable"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Unable to load analytics data."}),(0,a.jsx)("button",{onClick:g,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Retry"})]})})}let ej=(0,i.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var eb=t(64021),ef=t(35071);function ev(){let[e,s]=(0,r.useState)("overview"),[t,l]=(0,r.useState)([]),[i,n]=(0,r.useState)([]),[c,d]=(0,r.useState)(null),[x,m]=(0,r.useState)(!0),[p,y]=(0,r.useState)(""),[b,v]=(0,r.useState)("all"),N=async()=>{m(!0);try{let e=localStorage.getItem("admin_token"),s=await fetch("/api/admin/security/logs",{headers:{Authorization:`Bearer ${e}`}});if(s.ok){let e=await s.json();l(e.logs||[])}let t=await fetch("/api/admin/security/users",{headers:{Authorization:`Bearer ${e}`}});if(t.ok){let e=await t.json();n(e.users||[])}let a=await fetch("/api/admin/security/settings",{headers:{Authorization:`Bearer ${e}`}});if(a.ok){let e=await a.json();d(e.settings)}}catch(e){console.error("Error fetching security data:",e)}finally{m(!1)}},w=e=>{switch(e){case"success":return"text-green-600 bg-green-100";case"failed":return"text-red-600 bg-red-100";case"warning":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},k=e=>{switch(e){case"super_admin":return"text-purple-600 bg-purple-100";case"admin":return"text-blue-600 bg-blue-100";case"moderator":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},A=e=>{switch(e.toLowerCase()){case"login":return(0,a.jsx)(ej,{className:"w-4 h-4"});case"logout":return(0,a.jsx)(eb.A,{className:"w-4 h-4"});case"failed_login":return(0,a.jsx)(ef.A,{className:"w-4 h-4"});case"password_change":return(0,a.jsx)(u.A,{className:"w-4 h-4"});case"user_created":return(0,a.jsx)(el.A,{className:"w-4 h-4"});case"user_deleted":return(0,a.jsx)(J.A,{className:"w-4 h-4"});case"settings_changed":return(0,a.jsx)(g.A,{className:"w-4 h-4"});default:return(0,a.jsx)(h,{className:"w-4 h-4"})}},S=t.filter(e=>{let s=!p||e.user.toLowerCase().includes(p.toLowerCase())||e.action.toLowerCase().includes(p.toLowerCase())||e.ipAddress.includes(p),t="all"===b||e.status===b;return s&&t}),M={totalLogs:t.length,failedLogins:t.filter(e=>"failed_login"===e.action).length,activeUsers:i.filter(e=>"active"===e.status).length,suspendedUsers:i.filter(e=>"suspended"===e.status).length,recentAlerts:t.filter(e=>{let s=new Date(e.timestamp),t=new Date;return t.setDate(t.getDate()-1),s>t&&"failed"===e.status}).length};return x?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Security & Access Control"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage system security, users, and access permissions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{onClick:N,className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-2"}),"Refresh"]}),(0,a.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(W.A,{className:"w-4 h-4 mr-2"}),"Export Logs"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[{id:"overview",label:"Overview",icon:u.A},{id:"users",label:"Admin Users",icon:o.A},{id:"logs",label:"Security Logs",icon:h},{id:"settings",label:"Settings",icon:g.A}].map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${e===t.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[(0,a.jsx)(r,{className:"w-4 h-4 mr-2"}),t.label]},t.id)})})]}),"overview"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Logs"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:M.totalLogs})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(h,{className:"w-6 h-6 text-blue-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Failed Logins"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:M.failedLogins})]}),(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-full",children:(0,a.jsx)(ef.A,{className:"w-6 h-6 text-red-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Users"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:M.activeUsers})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(C.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Suspended"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:M.suspendedUsers})]}),(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-full",children:(0,a.jsx)(f.A,{className:"w-6 h-6 text-yellow-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Recent Alerts"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:M.recentAlerts})]}),(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(f.A,{className:"w-6 h-6 text-orange-600"})})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Security Events"}),(0,a.jsx)("div",{className:"space-y-4",children:t.slice(0,10).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:`p-2 rounded-full ${w(e.status)}`,children:A(e.action)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.action.replace("_"," ")}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.user," • ",e.ipAddress]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900",children:new Date(e.timestamp).toLocaleString()}),(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${w(e.status)}`,children:e.status})]})]},e.id))})]})]}),"users"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Admin Users"}),(0,a.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(el.A,{className:"w-4 h-4 mr-2"}),"Add User"]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Login"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.username}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${k(e.role)}`,children:e.role.replace("_"," ")})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"active"===e.status?"text-green-600 bg-green-100":"suspended"===e.status?"text-red-600 bg-red-100":"text-gray-600 bg-gray-100"}`,children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.lastLogin?new Date(e.lastLogin).toLocaleDateString():"Never"}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(G.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(J.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})]})}),"logs"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search logs by user, action, or IP address...",value:p,onChange:e=>y(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:b,onChange:e=>v(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"success",children:"Success"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"warning",children:"Warning"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"IP Address"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Details"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:S.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:new Date(e.timestamp).toLocaleString()}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[A(e.action),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-900",children:e.action.replace("_"," ")})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.user}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.ipAddress}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${w(e.status)}`,children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.details})]},e.id))})]})})})]}),"settings"===e&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Security Settings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Advanced security configuration coming soon"})]})})]})}var eN=t(98971);function ew(){let[e,s]=(0,r.useState)("general"),[t,l]=(0,r.useState)({general:{siteName:"EBAM Motors",siteDescription:"Premium Japanese Cars for Ghana and Africa",contactEmail:"<EMAIL>",contactPhone:"+233245375692",businessAddress:"Kumasi, Ghana",currency:"JPY",timezone:"Africa/Accra"},email:{provider:"resend",smtpHost:"smtp.resend.com",smtpPort:587,smtpUser:"resend",smtpPassword:"",fromEmail:"<EMAIL>",fromName:"EBAM Motors"},notifications:{emailNotifications:!0,orderNotifications:!0,reviewNotifications:!0,systemAlerts:!0,dailySummary:!1},security:{sessionTimeout:60,maxLoginAttempts:5,passwordMinLength:8,requireTwoFactor:!1,allowedIPs:[]},appearance:{theme:"light",primaryColor:"#2563eb",logoUrl:"/logo.png",faviconUrl:"/favicon.ico"},automation:{followupEnabled:!0,followupDelayDays:3,emailFollowups:!0,smsFollowups:!0,leadFollowups:!0,customerFollowups:!0}}),[i,n]=(0,r.useState)(!1),[c,d]=(0,r.useState)("idle"),o=[{id:"general",label:"General",icon:g.A},{id:"email",label:"Email",icon:et.A},{id:"notifications",label:"Notifications",icon:b.A},{id:"security",label:"Security",icon:u.A},{id:"appearance",label:"Appearance",icon:eN.A},{id:"automation",label:"Automation",icon:L.A}],x=async()=>{d("saving");try{let e=localStorage.getItem("admin_token");(await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify(t)})).ok?d("success"):d("error"),setTimeout(()=>d("idle"),3e3)}catch(e){console.error("Error saving settings:",e),d("error"),setTimeout(()=>d("idle"),3e3)}},m=(e,s,t)=>{l(a=>({...a,[e]:{...a[e],[s]:t}}))};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"System Settings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Configure your admin dashboard and system preferences"})]}),(0,a.jsx)("button",{onClick:x,disabled:"saving"===c,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${"success"===c?"bg-green-600 text-white":"error"===c?"bg-red-600 text-white":"bg-blue-600 text-white hover:bg-blue-700"} disabled:opacity-50`,children:"saving"===c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Saving..."]}):"success"===c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(C.A,{className:"w-4 h-4 mr-2"}),"Saved"]}):"error"===c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Error"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed.A,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"flex space-x-8 px-6",children:o.map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${e===t.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,a.jsx)(r,{className:"w-4 h-4 mr-2"}),t.label]},t.id)})})}),(0,a.jsxs)("div",{className:"p-6",children:["general"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Site Name"}),(0,a.jsx)("input",{type:"text",value:t.general.siteName,onChange:e=>m("general","siteName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Email"}),(0,a.jsx)("input",{type:"email",value:t.general.contactEmail,onChange:e=>m("general","contactEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Phone"}),(0,a.jsx)("input",{type:"text",value:t.general.contactPhone,onChange:e=>m("general","contactPhone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Currency"}),(0,a.jsxs)("select",{value:t.general.currency,onChange:e=>m("general","currency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"JPY",children:"Japanese Yen (\xa5)"}),(0,a.jsx)("option",{value:"USD",children:"US Dollar ($)"}),(0,a.jsx)("option",{value:"GHS",children:"Ghana Cedi (₵)"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Site Description"}),(0,a.jsx)("textarea",{value:t.general.siteDescription,onChange:e=>m("general","siteDescription",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Business Address"}),(0,a.jsx)("textarea",{value:t.general.businessAddress,onChange:e=>m("general","businessAddress",e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),"email"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(X.A,{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Email Configuration"}),(0,a.jsx)("p",{children:"Configure your email settings for sending notifications, order confirmations, and system alerts."})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Provider"}),(0,a.jsxs)("select",{value:t.email.provider,onChange:e=>m("email","provider",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"resend",children:"Resend"}),(0,a.jsx)("option",{value:"smtp",children:"Custom SMTP"}),(0,a.jsx)("option",{value:"gmail",children:"Gmail"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"From Email"}),(0,a.jsx)("input",{type:"email",value:t.email.fromEmail,onChange:e=>m("email","fromEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"From Name"}),(0,a.jsx)("input",{type:"text",value:t.email.fromName,onChange:e=>m("email","fromName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"SMTP Host"}),(0,a.jsx)("input",{type:"text",value:t.email.smtpHost,onChange:e=>m("email","smtpHost",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),"notifications"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"space-y-4",children:Object.entries(t.notifications).map(([e,s])=>(0,a.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 capitalize",children:e.replace(/([A-Z])/g," $1").trim()}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["emailNotifications"===e&&"Enable email notifications for all events","orderNotifications"===e&&"Get notified when new orders are placed","reviewNotifications"===e&&"Get notified when new reviews are submitted","systemAlerts"===e&&"Receive system health and security alerts","dailySummary"===e&&"Receive daily summary reports"]})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:s,onChange:s=>m("notifications",e,s.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]},e))})}),"security"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(f.A,{className:"w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Security Settings"}),(0,a.jsx)("p",{children:"These settings affect the security of your admin panel. Change with caution."})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Session Timeout (minutes)"}),(0,a.jsx)("input",{type:"number",value:t.security.sessionTimeout,onChange:e=>m("security","sessionTimeout",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Login Attempts"}),(0,a.jsx)("input",{type:"number",value:t.security.maxLoginAttempts,onChange:e=>m("security","maxLoginAttempts",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minimum Password Length"}),(0,a.jsx)("input",{type:"number",value:t.security.passwordMinLength,onChange:e=>m("security","passwordMinLength",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("label",{className:"flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.security.requireTwoFactor,onChange:e=>m("security","requireTwoFactor",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-700",children:"Require Two-Factor Authentication"})]})})]})]}),"appearance"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Theme"}),(0,a.jsxs)("select",{value:t.appearance.theme,onChange:e=>m("appearance","theme",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"light",children:"Light"}),(0,a.jsx)("option",{value:"dark",children:"Dark"}),(0,a.jsx)("option",{value:"auto",children:"Auto"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Primary Color"}),(0,a.jsx)("input",{type:"color",value:t.appearance.primaryColor,onChange:e=>m("appearance","primaryColor",e.target.value),className:"w-full h-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logo URL"}),(0,a.jsx)("input",{type:"url",value:t.appearance.logoUrl,onChange:e=>m("appearance","logoUrl",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Favicon URL"}),(0,a.jsx)("input",{type:"url",value:t.appearance.faviconUrl,onChange:e=>m("appearance","faviconUrl",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})}),"automation"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(L.A,{className:"w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-green-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Automated Follow-up System"}),(0,a.jsx)("p",{children:"Automatically send follow-up emails and SMS to customers and leads after a specified delay. This helps improve customer engagement and conversion rates."})]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Enable Automated Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Master switch for all automated follow-up features"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.followupEnabled,onChange:e=>m("automation","followupEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Follow-up Delay (Days)"}),(0,a.jsx)("input",{type:"number",min:"1",max:"30",value:t.automation.followupDelayDays,onChange:e=>m("automation","followupDelayDays",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:!t.automation.followupEnabled}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Number of days to wait before sending follow-up"})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:"Follow-up Methods"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:"Email Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Send automated follow-up emails"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.emailFollowups,onChange:e=>m("automation","emailFollowups",e.target.checked),disabled:!t.automation.followupEnabled,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:"SMS Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Send automated SMS messages"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.smsFollowups,onChange:e=>m("automation","smsFollowups",e.target.checked),disabled:!t.automation.followupEnabled,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:"Target Audiences"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:"Customer Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Follow up with new customers"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.customerFollowups,onChange:e=>m("automation","customerFollowups",e.target.checked),disabled:!t.automation.followupEnabled,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:"Lead Follow-ups"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Follow up with new leads"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.automation.leadFollowups,onChange:e=>m("automation","leadFollowups",e.target.checked),disabled:!t.automation.followupEnabled,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4 pt-6 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:"System Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("button",{type:"button",onClick:async()=>{try{(await fetch("/api/admin/followups/process",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}})).ok?alert("Follow-up processing triggered successfully!"):alert("Failed to trigger follow-up processing")}catch(e){alert("Error triggering follow-up processing")}},className:"flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-2"}),"Process Pending Follow-ups"]}),(0,a.jsxs)("button",{type:"button",onClick:async()=>{try{(await fetch("/api/admin/system/init",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}})).ok?alert("System services initialized successfully!"):alert("Failed to initialize system services")}catch(e){alert("Error initializing system services")}},className:"flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)(D,{className:"w-4 h-4 mr-2"}),"Initialize System"]})]})]})]})]})]})]})]})}let ek=[{id:"overview",label:"Overview",icon:n,path:"/admin/dashboard"},{id:"cars",label:"Car Management",icon:c.A,path:"/admin/cars"},{id:"orders",label:"Orders",icon:d.A,path:"/admin/orders"},{id:"customers",label:"CRM",icon:o.A,path:"/admin/customers"},{id:"reviews",label:"Reviews",icon:x.A,path:"/admin/reviews"},{id:"analytics",label:"Analytics",icon:m,path:"/admin/analytics"},{id:"health",label:"System Health",icon:h,path:"/admin/health"},{id:"security",label:"Security",icon:u.A,path:"/admin/security"},{id:"settings",label:"Settings",icon:g.A,path:"/admin/settings"}];function eA(){let[e,s]=(0,r.useState)(!1),[t,i]=(0,r.useState)(!1),[n,c]=(0,r.useState)(!1),[o,m]=(0,r.useState)("overview"),[h,u]=(0,r.useState)(3),[g,N]=(0,r.useState)(!1),w=(0,l.useRouter)();(0,l.useSearchParams)();let k=(0,r.useRef)(null),C=(e,s)=>{m(e),c(!1);let t=`/admin/dashboard?section=${e}`;w.push(t)},S=(e,s)=>{switch(N(!1),e){case"order":C("orders","/admin/orders");break;case"review":C("reviews","/admin/reviews");break;case"system":C("health","/admin/health");break;case"car":C("cars","/admin/cars");break;default:C("overview","/admin/dashboard")}h>0&&u(e=>e-1)};return e?t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[(0,a.jsxs)("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${n?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"EBAM Admin"}),(0,a.jsx)("button",{onClick:()=>c(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,a.jsx)(p.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("nav",{className:"mt-6 px-3",children:ek.map(e=>{let s=e.icon,t=o===e.id;return(0,a.jsxs)("button",{onClick:()=>C(e.id,e.path),className:`w-full flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${t?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,a.jsx)(s,{className:"w-5 h-5 mr-3"}),e.label,e.badge&&(0,a.jsx)("span",{className:"ml-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full",children:e.badge})]},e.id)})})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col lg:ml-0",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{onClick:()=>c(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,a.jsx)(y.A,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:"ml-4 lg:ml-0",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:ek.find(e=>e.id===o)?.label||"Dashboard"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"hidden md:block relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"relative",ref:k,children:[(0,a.jsxs)("button",{onClick:()=>N(!g),className:"relative p-2 text-gray-400 hover:text-gray-600",children:[(0,a.jsx)(b.A,{className:"w-5 h-5"}),h>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:h})]}),g&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Notifications"})}),(0,a.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[(0,a.jsx)("button",{onClick:()=>S("order","ORD-001"),className:"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"w-4 h-4 text-blue-600"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"New order received"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Order #ORD-001 from John Doe"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"2 minutes ago"})]})]})}),(0,a.jsx)("button",{onClick:()=>S("review","REV-001"),className:"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"w-4 h-4 text-green-600"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"New review submitted"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"5-star review for Toyota Voxy"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"15 minutes ago"})]})]})}),(0,a.jsx)("button",{onClick:()=>S("system","SYS-001"),className:"w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(f.A,{className:"w-4 h-4 text-yellow-600"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"System alert"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Low inventory warning for Honda Freed"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"1 hour ago"})]})]})})]}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsx)("button",{onClick:()=>N(!1),className:"w-full text-center text-sm text-blue-600 hover:text-blue-800",children:"View all notifications"})})]})]}),(0,a.jsxs)("button",{onClick:()=>{i(!1)},className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})}),(0,a.jsxs)("main",{className:"flex-1 p-6 overflow-auto",children:["overview"===o&&(0,a.jsx)(P,{}),"health"===o&&(0,a.jsx)($,{}),"cars"===o&&(0,a.jsx)(Z,{}),"reviews"===o&&(0,a.jsx)(er,{}),"customers"===o&&(0,a.jsx)(eh,{}),"orders"===o&&(0,a.jsx)(eu,{}),"analytics"===o&&(0,a.jsx)(ey,{}),"security"===o&&(0,a.jsx)(ev,{}),"settings"===o&&(0,a.jsx)(ew,{}),"overview"!==o&&"health"!==o&&"cars"!==o&&"reviews"!==o&&"customers"!==o&&"orders"!==o&&"analytics"!==o&&"security"!==o&&"settings"!==o&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:ek.find(e=>e.id===o)?.label}),(0,a.jsx)("p",{className:"text-gray-600",children:"This section is under development. Please check back soon."})]})]})]}),n&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>c(!1)})]}):(0,a.jsx)(A,{onAuthenticated:()=>i(!0)}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading dashboard..."})]})})}function eC(){return(0,a.jsx)(eA,{})}function eS(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading dashboard..."})]})}),children:(0,a.jsx)(eC,{})})}},23026:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25366:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},25541:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},28561:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37652:(e,s,t)=>{Promise.resolve().then(t.bind(t,99111))},40083:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43649:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44263:()=>{},48340:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},61193:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(60687),r=t(85814),l=t.n(r),i=t(43210);function n({children:e}){let[s,t]=(0,i.useState)(!1);return s?(0,a.jsxs)("div",{className:"min-h-screen bg-neutral-50 admin-layout",children:[(0,a.jsx)("header",{className:"bg-white/95 backdrop-blur-sm shadow-sm border-b border-neutral-200 sticky top-0 z-50 transition-all duration-300",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"A"})}),(0,a.jsx)("h1",{className:"text-xl font-bold text-neutral-800",children:"EBAM Motors Admin"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-sm text-neutral-600",children:"Admin Panel"}),(0,a.jsx)(l(),{href:"/",className:"text-sm text-primary-600 hover:text-primary-700 font-medium",children:"← Back to Website"})]})]})})}),(0,a.jsx)("main",{className:"py-8",children:e}),(0,a.jsx)("footer",{className:"bg-white border-t border-neutral-200 mt-auto",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsx)("div",{className:"text-center text-sm text-neutral-500",children:"EBAM Motors Admin Panel - Handle with care"})})})]}):(0,a.jsx)("div",{className:"min-h-screen bg-neutral-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading admin panel..."})]})})}t(44263)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64021:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},64398:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71031:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\dashboard\\page.tsx","default")},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},84027:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},90388:(e,s,t)=>{Promise.resolve().then(t.bind(t,61193))},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94478:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},96882:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97051:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97992:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},98971:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},99111:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\layout.tsx","default")},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,7445,1658,5814,5839],()=>t(6925));module.exports=a})();