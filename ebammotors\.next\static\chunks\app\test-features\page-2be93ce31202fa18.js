(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9261],{429:(e,s,t)=>{Promise.resolve().then(t.bind(t,3646))},3646:(e,s,t)=>{"use strict";t.d(s,{default:()=>p});var i=t(5155),a=t(2115);let n=()=>"function"==typeof window.gtag,l=e=>{n()&&window.gtag("event",e.action,{event_category:e.category,event_label:e.label,value:e.value,...e.custom_parameters})},r=(e,s,t)=>{l({action:"view_item",category:"ecommerce",label:s,value:t,custom_parameters:{item_id:e,item_name:s,item_category:"used_cars",currency:"JPY",value:t}})},o=(e,s)=>{l({action:"search",category:"engagement",label:e,value:s,custom_parameters:{search_term:e,results_count:s}})},c=e=>{l({action:"form_submit",category:"engagement",label:e,custom_parameters:{form_type:e}})};function d(){let[e,s]=(0,a.useState)(""),t=async e=>{try{switch(e){case"car_view":r("test-car-123","Toyota Voxy 2020",25e5),s("Car view event tracked");break;case"search":o("Toyota Voxy",5),s("Search event tracked");break;case"contact":c("test_form"),s("Contact form event tracked")}}catch(e){s("Error: ".concat(e))}};return(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDD0D Analytics Testing"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("button",{onClick:()=>t("car_view"),className:"w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700",children:"Test Car View Event"}),(0,i.jsx)("button",{onClick:()=>t("search"),className:"w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700",children:"Test Search Event"}),(0,i.jsx)("button",{onClick:()=>t("contact"),className:"w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700",children:"Test Contact Form Event"})]}),e&&(0,i.jsx)("div",{className:"mt-4 p-3 bg-gray-100 rounded text-sm",children:e}),(0,i.jsx)("div",{className:"mt-4 text-xs text-gray-600",children:"Open browser DevTools → Network tab to see analytics requests"})]})}function m(){let e=e=>{alert("Would share on ".concat(e," - check console for details"))};return(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDCF1 Social Share Testing"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Test Social Sharing:"}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)("button",{onClick:()=>e("Facebook"),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Facebook"}),(0,i.jsx)("button",{onClick:()=>e("Twitter"),className:"bg-blue-400 text-white px-4 py-2 rounded hover:bg-blue-500",children:"Twitter"}),(0,i.jsx)("button",{onClick:()=>e("WhatsApp"),className:"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700",children:"WhatsApp"})]})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)("p",{children:"To test full social sharing functionality:"}),(0,i.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1",children:[(0,i.jsx)("li",{children:"Import SocialShare component"}),(0,i.jsx)("li",{children:"Add social media platform configurations"}),(0,i.jsx)("li",{children:"Test on different devices and browsers"})]})]})]})]})}function h(){let[e,s]=(0,a.useState)(""),[t,n]=(0,a.useState)(null);return(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDD0D Advanced Search Testing"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)("input",{type:"text",value:e,onChange:e=>s(e.target.value),placeholder:"Search for cars...",className:"flex-1 border border-gray-300 rounded px-3 py-2"}),(0,i.jsx)("button",{onClick:()=>{n({query:e,results:[{id:1,name:"Toyota Voxy 2020",price:25e5},{id:2,name:"Honda Freed 2019",price:22e5}],timestamp:new Date().toISOString()})},className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Search"})]}),t&&(0,i.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Mock Search Results:"}),(0,i.jsx)("pre",{className:"text-xs overflow-auto",children:JSON.stringify(t,null,2)})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)("p",{children:"To test full advanced search functionality:"}),(0,i.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1",children:[(0,i.jsx)("li",{children:"Import AdvancedSearch component"}),(0,i.jsx)("li",{children:"Configure search filters and sorting"}),(0,i.jsx)("li",{children:"Test autocomplete and suggestions"}),(0,i.jsx)("li",{children:"Test saved searches functionality"})]})]})]})]})}function u(){let[e,s]=(0,a.useState)({}),[t,n]=(0,a.useState)(null),l=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",i=arguments.length>2?arguments[2]:void 0;n(e);try{let a={method:t,headers:{"Content-Type":"application/json"}};i&&(a.body=JSON.stringify(i));let n=await fetch(e,a),l=await n.json();s(s=>({...s,[e]:{status:n.status,data:l,timestamp:new Date().toISOString()}}))}catch(t){s(s=>({...s,[e]:{error:t instanceof Error?t.message:"Unknown error",timestamp:new Date().toISOString()}}))}finally{n(null)}};return(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDD0C API Testing"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 mb-4",children:[{name:"Health Check",endpoint:"/api/admin/health",method:"GET"},{name:"Analytics",endpoint:"/api/admin/analytics",method:"GET"},{name:"Notification Stats",endpoint:"/api/notifications/send",method:"GET"},{name:"Forum Posts",endpoint:"/api/community/forum/posts",method:"GET"},{name:"Social Feed",endpoint:"/api/social/feed",method:"GET"}].map(e=>(0,i.jsx)("button",{onClick:()=>l(e.endpoint,e.method),disabled:t===e.endpoint,className:"bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm",children:t===e.endpoint?"Testing...":e.name},e.endpoint))}),(0,i.jsx)("div",{className:"space-y-2 max-h-64 overflow-auto",children:Object.entries(e).map(e=>{let[s,t]=e;return(0,i.jsxs)("div",{className:"p-3 bg-gray-100 rounded",children:[(0,i.jsx)("div",{className:"font-medium text-sm",children:s}),(0,i.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:t.error?(0,i.jsxs)("span",{className:"text-red-600",children:["Error: ",t.error]}):(0,i.jsxs)("span",{className:"text-green-600",children:["Status: ",t.status]})}),(0,i.jsx)("pre",{className:"text-xs mt-2 overflow-auto max-h-32",children:JSON.stringify(t.data||t.error,null,2)})]},s)})})]})}function x(){let[e,s]=(0,a.useState)("Not supported"),t=async()=>{if("Notification"in window){let e=await Notification.requestPermission();s("Permission: ".concat(e)),"granted"===e&&new Notification("Test Notification",{body:"This is a test notification from EBAM Motors",icon:"/favicon.ico"})}else s("Notifications not supported")};return(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDD14 Push Notification Testing"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("div",{children:(0,i.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Test Browser Notification"})}),(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("strong",{children:"Status:"})," ",e]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)("p",{children:"To test full push notification functionality:"}),(0,i.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1",children:[(0,i.jsx)("li",{children:"Configure VAPID keys in environment variables"}),(0,i.jsx)("li",{children:"Import NotificationSettings component"}),(0,i.jsx)("li",{children:"Test subscription management"}),(0,i.jsx)("li",{children:"Test admin notification sender"}),(0,i.jsx)("li",{children:"Test on mobile devices with HTTPS"})]})]})]})]})}function g(){let[e,s]=(0,a.useState)({});return(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDCF1 PWA Status"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("button",{onClick:()=>{s({serviceWorker:"serviceWorker"in navigator,manifest:null!==document.querySelector('link[rel="manifest"]'),isStandalone:window.matchMedia("(display-mode: standalone)").matches,isInstallable:"BeforeInstallPromptEvent"in window})},className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Check PWA Status"}),(0,i.jsx)("button",{onClick:()=>{window.open("/manifest.json","_blank")},className:"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-2",children:"View Manifest"}),Object.keys(e).length>0&&(0,i.jsxs)("div",{className:"p-3 bg-gray-100 rounded",children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"PWA Status:"}),(0,i.jsx)("pre",{className:"text-xs",children:JSON.stringify(e,null,2)})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)("p",{children:(0,i.jsx)("strong",{children:"Test Steps:"})}),(0,i.jsxs)("ol",{className:"list-decimal list-inside space-y-1 mt-2",children:[(0,i.jsx)("li",{children:"Check if service worker is registered"}),(0,i.jsx)("li",{children:"Test offline functionality (DevTools → Network → Offline)"}),(0,i.jsx)("li",{children:"Look for installation prompt (wait 5 seconds)"}),(0,i.jsx)("li",{children:"Check manifest.json accessibility"})]})]})]})]})}function p(){return(0,i.jsx)("div",{className:"min-h-screen bg-gray-100 p-4",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\uD83E\uDDEA EBAM Motors - Feature Testing Dashboard"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Test all advanced features from this dashboard. Open browser DevTools to monitor network requests and console logs."})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsx)(d,{}),(0,i.jsx)(m,{}),(0,i.jsx)(x,{}),(0,i.jsx)(g,{}),(0,i.jsx)(u,{}),(0,i.jsx)(h,{})]}),(0,i.jsxs)("div",{className:"mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,i.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"⚠️ Testing Notes:"}),(0,i.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,i.jsx)("li",{children:"• Make sure your .env.local file has the required environment variables"}),(0,i.jsx)("li",{children:"• Some features require HTTPS (use ngrok for local testing if needed)"}),(0,i.jsx)("li",{children:"• Push notifications need VAPID keys to be configured"}),(0,i.jsx)("li",{children:"• Check browser console for any error messages"}),(0,i.jsx)("li",{children:"• Test on different browsers and devices"})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(429)),_N_E=e.O()}]);