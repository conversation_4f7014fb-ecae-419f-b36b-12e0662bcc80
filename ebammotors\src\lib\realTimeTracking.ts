// Real-time tracking system for EBAM Motors
// Enhanced tracking with GPS, notifications, and customer communication

export interface TrackingLocation {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  country: string;
  timestamp: string;
}

export interface TrackingEvent {
  id: string;
  orderId: string;
  status: string;
  title: string;
  description: string;
  location?: TrackingLocation;
  timestamp: string;
  estimatedDelivery?: string;
  actualDelivery?: string;
  images?: string[];
  documents?: string[];
  notes?: string;
  isPublic: boolean;
}

export interface ShippingRoute {
  id: string;
  orderId: string;
  origin: TrackingLocation;
  destination: TrackingLocation;
  waypoints: TrackingLocation[];
  currentLocation?: TrackingLocation;
  estimatedDistance: number;
  estimatedDuration: number;
  actualDistance?: number;
  actualDuration?: number;
  transportMode: 'sea' | 'air' | 'land';
  carrier: string;
  trackingNumber: string;
}

export interface DeliveryNotification {
  id: string;
  orderId: string;
  type: 'sms' | 'email' | 'push' | 'whatsapp';
  recipient: string;
  message: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed';
  sentAt?: string;
  deliveredAt?: string;
  errorMessage?: string;
}

export interface CustomerCommunication {
  id: string;
  orderId: string;
  type: 'message' | 'call' | 'email' | 'video_call';
  direction: 'inbound' | 'outbound';
  subject?: string;
  content: string;
  attachments?: string[];
  timestamp: string;
  status: 'pending' | 'sent' | 'read' | 'replied';
  agentId?: string;
  customerId: string;
}

// Tracking status definitions
export const TRACKING_STATUSES = {
  ORDER_PLACED: {
    id: 'order_placed',
    title: 'Order Placed',
    description: 'Your order has been received and is being processed',
    icon: '📋',
    color: 'blue',
  },
  PAYMENT_CONFIRMED: {
    id: 'payment_confirmed',
    title: 'Payment Confirmed',
    description: 'Payment has been verified and order is confirmed',
    icon: '💳',
    color: 'green',
  },
  VEHICLE_INSPECTION: {
    id: 'vehicle_inspection',
    title: 'Vehicle Inspection',
    description: 'Vehicle is being inspected and prepared for export',
    icon: '🔍',
    color: 'yellow',
  },
  EXPORT_DOCUMENTATION: {
    id: 'export_documentation',
    title: 'Export Documentation',
    description: 'Preparing export documents and customs paperwork',
    icon: '📄',
    color: 'orange',
  },
  SHIPPED_FROM_JAPAN: {
    id: 'shipped_from_japan',
    title: 'Shipped from Japan',
    description: 'Vehicle has departed from Japan port',
    icon: '🚢',
    color: 'blue',
  },
  IN_TRANSIT: {
    id: 'in_transit',
    title: 'In Transit',
    description: 'Vehicle is on its way to destination',
    icon: '🌊',
    color: 'blue',
  },
  ARRIVED_AT_PORT: {
    id: 'arrived_at_port',
    title: 'Arrived at Port',
    description: 'Vehicle has arrived at destination port',
    icon: '⚓',
    color: 'purple',
  },
  CUSTOMS_CLEARANCE: {
    id: 'customs_clearance',
    title: 'Customs Clearance',
    description: 'Vehicle is going through customs clearance',
    icon: '🛃',
    color: 'orange',
  },
  READY_FOR_PICKUP: {
    id: 'ready_for_pickup',
    title: 'Ready for Pickup',
    description: 'Vehicle is ready for pickup or final delivery',
    icon: '✅',
    color: 'green',
  },
  DELIVERED: {
    id: 'delivered',
    title: 'Delivered',
    description: 'Vehicle has been successfully delivered',
    icon: '🎉',
    color: 'green',
  },
};

// Get real-time tracking information
export const getTrackingInfo = async (orderId: string): Promise<{
  events: TrackingEvent[];
  route?: ShippingRoute;
  currentStatus: string;
  estimatedDelivery?: string;
  notifications: DeliveryNotification[];
  communications: CustomerCommunication[];
}> => {
  try {
    const response = await fetch(`/api/tracking/realtime/${orderId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch tracking information');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
};

// Add tracking event
export const addTrackingEvent = async (
  orderId: string,
  status: string,
  title: string,
  description: string,
  location?: Partial<TrackingLocation>,
  options?: {
    estimatedDelivery?: string;
    images?: string[];
    documents?: string[];
    notes?: string;
    isPublic?: boolean;
    sendNotification?: boolean;
  }
): Promise<TrackingEvent> => {
  try {
    const response = await fetch('/api/tracking/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orderId,
        status,
        title,
        description,
        location,
        ...options,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to add tracking event');
    }

    const data = await response.json();
    return data.event;
  } catch (error) {
    throw error;
  }
};

// Update GPS location
export const updateGPSLocation = async (
  orderId: string,
  location: TrackingLocation
): Promise<void> => {
  try {
    await fetch('/api/tracking/gps', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orderId,
        location,
      }),
    });
  } catch (error) {
    throw error;
  }
};

// Send delivery notification
export const sendDeliveryNotification = async (
  orderId: string,
  type: 'sms' | 'email' | 'push' | 'whatsapp',
  recipient: string,
  message: string,
  templateData?: any
): Promise<DeliveryNotification> => {
  try {
    const response = await fetch('/api/tracking/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orderId,
        type,
        recipient,
        message,
        templateData,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send notification');
    }

    const data = await response.json();
    return data.notification;
  } catch (error) {
    throw error;
  }
};

// Add customer communication
export const addCustomerCommunication = async (
  orderId: string,
  type: 'message' | 'call' | 'email' | 'video_call',
  direction: 'inbound' | 'outbound',
  content: string,
  options?: {
    subject?: string;
    attachments?: string[];
    agentId?: string;
  }
): Promise<CustomerCommunication> => {
  try {
    const response = await fetch('/api/tracking/communications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orderId,
        type,
        direction,
        content,
        ...options,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to add communication');
    }

    const data = await response.json();
    return data.communication;
  } catch (error) {
    throw error;
  }
};

// Calculate estimated delivery time
export const calculateEstimatedDelivery = (
  shippingMethod: 'sea' | 'air',
  origin: string,
  destination: string
): Date => {
  const baseDeliveryTimes = {
    sea: {
      'Japan-Ghana': 45, // days
      'Japan-Nigeria': 42,
      'Japan-Kenya': 35,
      'Japan-South Africa': 40,
    },
    air: {
      'Japan-Ghana': 7, // days
      'Japan-Nigeria': 6,
      'Japan-Kenya': 5,
      'Japan-South Africa': 6,
    },
  };

  const route = `${origin}-${destination}`;
  const baseDays = baseDeliveryTimes[shippingMethod][route as keyof typeof baseDeliveryTimes.sea] || 
                   (shippingMethod === 'sea' ? 45 : 7);

  // Add some buffer time
  const bufferDays = shippingMethod === 'sea' ? 7 : 2;
  const totalDays = baseDays + bufferDays;

  const estimatedDate = new Date();
  estimatedDate.setDate(estimatedDate.getDate() + totalDays);
  
  return estimatedDate;
};

// Get shipping route information
export const getShippingRoute = async (orderId: string): Promise<ShippingRoute | null> => {
  try {
    const response = await fetch(`/api/tracking/route/${orderId}`);
    
    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data.route;
  } catch (error) {
    return null;
  }
};

// Subscribe to real-time updates
export const subscribeToTrackingUpdates = (
  orderId: string,
  callback: (event: TrackingEvent) => void
): () => void => {
  // In a real implementation, this would use WebSockets or Server-Sent Events
  const eventSource = new EventSource(`/api/tracking/stream/${orderId}`);
  
  eventSource.onmessage = (event) => {
    try {
      const trackingEvent = JSON.parse(event.data);
      callback(trackingEvent);
    } catch (error) {
      // Error parsing tracking event
    }
  };

  eventSource.onerror = (error) => {
    // Tracking stream error
  };

  // Return cleanup function
  return () => {
    eventSource.close();
  };
};

// Format tracking status for display
export const formatTrackingStatus = (status: string): {
  title: string;
  description: string;
  icon: string;
  color: string;
} => {
  const statusInfo = Object.values(TRACKING_STATUSES).find(s => s.id === status);
  return statusInfo || {
    title: 'Unknown Status',
    description: 'Status information not available',
    icon: '❓',
    color: 'gray',
  };
};

// Get tracking timeline
export const getTrackingTimeline = (events: TrackingEvent[]): {
  completed: TrackingEvent[];
  current?: TrackingEvent;
  upcoming: string[];
} => {
  const sortedEvents = events.sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  const completed = sortedEvents.slice(0, -1);
  const current = sortedEvents[sortedEvents.length - 1];
  
  // Determine upcoming statuses based on current status
  const allStatuses = Object.keys(TRACKING_STATUSES);
  const currentIndex = allStatuses.indexOf(current?.status || '');
  const upcoming = currentIndex >= 0 ? allStatuses.slice(currentIndex + 1) : [];

  return { completed, current, upcoming };
};

// Generate tracking QR code data
export const generateTrackingQR = (orderId: string): string => {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://ebammotors.com';
  return `${baseUrl}/tracking?order=${orderId}`;
};

// Validate tracking number format
export const validateTrackingNumber = (trackingNumber: string): boolean => {
  // Basic validation - can be enhanced based on carrier requirements
  return /^[A-Z0-9]{8,20}$/.test(trackingNumber.toUpperCase());
};
