(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1093],{387:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var r=a(5155),t=a(2115),n=a(5695);function u(){let e=(0,n.useRouter)();return(0,t.useEffect)(()=>{e.replace("/admin/dashboard?section=analytics")},[e]),(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Redirecting to analytics dashboard..."})]})})}},3107:(e,s,a)=>{Promise.resolve().then(a.bind(a,387))},5695:(e,s,a)=>{"use strict";var r=a(8999);a.o(r,"usePathname")&&a.d(s,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(3107)),_N_E=e.O()}]);