import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { sql } from '@vercel/postgres';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = parseInt(searchParams.get('per_page') || '20');
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sort_by') || 'created_at';
    const sortOrder = searchParams.get('sort_order') || 'desc';
    
    // Filters
    const make = searchParams.get('make') || '';
    const model = searchParams.get('model') || '';
    const status = searchParams.get('status') || '';
    const yearMin = searchParams.get('year_min') || '';
    const yearMax = searchParams.get('year_max') || '';
    const priceMin = searchParams.get('price_min') || '';
    const priceMax = searchParams.get('price_max') || '';
    const isFeatured = searchParams.get('is_featured') || '';
    const location = searchParams.get('location') || '';

    // Build WHERE clause
    const conditions = ['1=1'];
    const values: any[] = [];
    let paramIndex = 1;

    if (search) {
      conditions.push(`(
        LOWER(title) LIKE $${paramIndex} OR 
        LOWER(make) LIKE $${paramIndex} OR 
        LOWER(model) LIKE $${paramIndex} OR 
        car_id LIKE $${paramIndex}
      )`);
      values.push(`%${search.toLowerCase()}%`);
      paramIndex++;
    }

    if (make) {
      conditions.push(`LOWER(make) = $${paramIndex}`);
      values.push(make.toLowerCase());
      paramIndex++;
    }

    if (model) {
      conditions.push(`LOWER(model) = $${paramIndex}`);
      values.push(model.toLowerCase());
      paramIndex++;
    }

    if (status) {
      conditions.push(`LOWER(status) = $${paramIndex}`);
      values.push(status.toLowerCase());
      paramIndex++;
    }

    if (yearMin) {
      conditions.push(`year >= $${paramIndex}`);
      values.push(parseInt(yearMin));
      paramIndex++;
    }

    if (yearMax) {
      conditions.push(`year <= $${paramIndex}`);
      values.push(parseInt(yearMax));
      paramIndex++;
    }

    if (priceMin) {
      conditions.push(`price >= $${paramIndex}`);
      values.push(parseFloat(priceMin));
      paramIndex++;
    }

    if (priceMax) {
      conditions.push(`price <= $${paramIndex}`);
      values.push(parseFloat(priceMax));
      paramIndex++;
    }

    if (isFeatured) {
      conditions.push(`is_featured = $${paramIndex}`);
      values.push(isFeatured === 'true');
      paramIndex++;
    }

    if (location) {
      conditions.push(`LOWER(location) = $${paramIndex}`);
      values.push(location.toLowerCase());
      paramIndex++;
    }

    const whereClause = conditions.join(' AND ');
    
    // Validate sort column
    const validSortColumns = ['created_at', 'updated_at', 'title', 'make', 'model', 'year', 'price', 'status'];
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const sortDirection = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM cars WHERE ${whereClause}`;
    const countResult = await sql.query(countQuery, values);
    const totalCount = parseInt(countResult.rows[0]?.total || '0');

    // Get cars with pagination
    const offset = (page - 1) * perPage;
    const carsQuery = `
      SELECT 
        id,
        car_id,
        make,
        model,
        year,
        title,
        price,
        original_price,
        currency,
        status,
        mileage,
        fuel_type,
        transmission,
        body_condition,
        location,
        is_featured,
        stock_quantity,
        main_image,
        images,
        created_at,
        updated_at
      FROM cars 
      WHERE ${whereClause}
      ORDER BY ${sortColumn} ${sortDirection}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    values.push(perPage, offset);
    const carsResult = await sql.query(carsQuery, values);

    return NextResponse.json({
      success: true,
      cars: carsResult.rows,
      total_count: totalCount,
      page,
      per_page: perPage,
      total_pages: Math.ceil(totalCount / perPage)
    });

  } catch (error) {
    console.error('Error fetching cars:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch cars' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const carData = await request.json();

    // Validate required fields
    const requiredFields = ['car_id', 'make', 'model', 'year', 'title', 'price'];
    for (const field of requiredFields) {
      if (!carData[field]) {
        return NextResponse.json(
          { success: false, message: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Insert new car
    const result = await sql`
      INSERT INTO cars (
        car_id, make, model, year, title, price, original_price, currency,
        mileage, fuel_type, transmission, engine_size, drive_type, seats, doors, body_type,
        body_condition, interior_condition, exterior_color, interior_color,
        main_image, images, image_folder, specs, features,
        status, stock_quantity, location, slug, description, meta_title, meta_description,
        is_featured, import_source, import_notes
      ) VALUES (
        ${carData.car_id}, ${carData.make}, ${carData.model}, ${carData.year}, ${carData.title},
        ${carData.price}, ${carData.original_price || null}, ${carData.currency || 'JPY'},
        ${carData.mileage || null}, ${carData.fuel_type || null}, ${carData.transmission || null},
        ${carData.engine_size || null}, ${carData.drive_type || null}, ${carData.seats || null},
        ${carData.doors || null}, ${carData.body_type || null}, ${carData.body_condition || 'Good'},
        ${carData.interior_condition || 'Good'}, ${carData.exterior_color || null}, ${carData.interior_color || null},
        ${carData.main_image || null}, ${carData.images || []}, ${carData.image_folder || null},
        ${carData.specs || []}, ${carData.features || []}, ${carData.status || 'Available'},
        ${carData.stock_quantity || 1}, ${carData.location || 'Japan'}, ${carData.slug || null},
        ${carData.description || null}, ${carData.meta_title || null}, ${carData.meta_description || null},
        ${carData.is_featured || false}, 'admin', 'Added via admin panel'
      )
      RETURNING id, car_id
    `;

    return NextResponse.json({
      success: true,
      message: 'Car added successfully',
      car: result.rows[0]
    });

  } catch (error) {
    console.error('Error adding car:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to add car' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const carId = searchParams.get('id');
    
    if (!carId) {
      return NextResponse.json(
        { success: false, message: 'Car ID is required' },
        { status: 400 }
      );
    }

    const carData = await request.json();

    // Update car
    const result = await sql`
      UPDATE cars SET
        make = ${carData.make},
        model = ${carData.model},
        year = ${carData.year},
        title = ${carData.title},
        price = ${carData.price},
        original_price = ${carData.original_price || null},
        currency = ${carData.currency || 'JPY'},
        mileage = ${carData.mileage || null},
        fuel_type = ${carData.fuel_type || null},
        transmission = ${carData.transmission || null},
        body_condition = ${carData.body_condition || 'Good'},
        status = ${carData.status || 'Available'},
        stock_quantity = ${carData.stock_quantity || 1},
        location = ${carData.location || 'Japan'},
        is_featured = ${carData.is_featured || false},
        description = ${carData.description || null},
        updated_at = NOW()
      WHERE id = ${carId}
      RETURNING id, car_id
    `;

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Car not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Car updated successfully',
      car: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating car:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update car' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const carId = searchParams.get('id');
    
    if (!carId) {
      return NextResponse.json(
        { success: false, message: 'Car ID is required' },
        { status: 400 }
      );
    }

    // Delete car
    const result = await sql`
      DELETE FROM cars WHERE id = ${carId}
      RETURNING id, car_id
    `;

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Car not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Car deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting car:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete car' },
      { status: 500 }
    );
  }
}
