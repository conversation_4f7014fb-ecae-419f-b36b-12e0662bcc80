(()=>{var e={};e.id=2851,e.ids=[2851],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32529:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>A,routeModule:()=>m,serverHooks:()=>v,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>f});var r={};a.r(r),a.d(r,{GET:()=>g,POST:()=>d});var s=a(96559),n=a(48088),o=a(37719),i=a(32190);let c={antiHallucination:{temperature:.3,topP:.8,maxTokens:200,frequencyPenalty:.5,presencePenalty:.3,stopSequences:["User:","Human:","Customer:","\n\nUser:","\n\nHuman:"],suspiciousPatterns:[/¥[0-9,]+(?!,000)/g,/Toyota [A-Z][a-z]+(?!Voxy|Noah|Sienta|Yaris|Vitz)/g,/Honda [A-Z][a-z]+/g,/Nissan [A-Z][a-z]+/g,/\$[0-9,]+/g,/[0-9]+ years? warranty/gi,/guaranteed|promise|100%/gi,/free shipping|no cost/gi,/[0-9]+ days? delivery/gi]},company:{name:"EBAM Motors",location:"Kumasi, Ghana",whatsapp:"+************",whatsappChannel:"https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G",services:["Used Cars (Toyota, Honda, Nissan)","Electronics (appliances, phones, laptops)","Furniture (dining sets, office furniture)","Bicycles (city bikes, mountain bikes)","Heavy Equipment","Household Items"]},responseGuidelines:{uncertaintyPhrases:["I don't have that specific information in our current inventory","Let me connect you with our team for detailed information about that","For the most accurate details, please contact us directly","I'd recommend speaking with our specialists for specific technical details"],fallbackResponses:{unknownCar:"I can only provide information about our current inventory: Toyota Voxy, Noah, Sienta, Yaris, and Vitz. For other models, please contact our team.",unknownPrice:"For the most current pricing and availability, please contact us via WhatsApp at +************.",technicalSpecs:"For detailed technical specifications, I'd recommend speaking directly with our team who can provide comprehensive information.",shipping:"Shipping costs and timeframes vary by destination. Please contact us for a personalized quote."},maxResponseLength:300,disclaimers:{pricing:"*Prices are FOB Japan and subject to change. Contact us for current rates.*",shipping:"*Shipping costs and times vary by destination.*",availability:"*Availability subject to change. Please confirm before ordering.*"}},monitoring:{logSuspiciousResponses:!0,flagPotentialHallucinations:!0,requireHumanReview:!1}},l=()=>{let e=c.responseGuidelines.uncertaintyPhrases;return e[Math.floor(Math.random()*e.length)]},u=e=>{let t=[];return c.antiHallucination.suspiciousPatterns.forEach((a,r)=>{a.test(e)&&t.push(`Suspicious pattern detected: ${a.source}`)}),e.length>c.responseGuidelines.maxResponseLength&&t.push("Response too long"),{isValid:0===t.length,issues:t}};class p{constructor(){this.provider=process.env.AI_PROVIDER||"fallback",this.huggingfaceApiKey=process.env.HUGGINGFACE_API_KEY,this.huggingfaceModel=process.env.HUGGINGFACE_MODEL||"meta-llama/Llama-2-7b-chat-hf",this.ollamaBaseUrl=process.env.OLLAMA_BASE_URL||"http://localhost:11434",this.ollamaModel=process.env.OLLAMA_MODEL||"llama3.2:3b",this.groqApiKey=process.env.GROQ_API_KEY,this.groqModel=process.env.GROQ_MODEL||"llama-3.1-8b-instant"}async generateResponse(e,t="",a="en"){let r=this.buildSystemPrompt(t,a);try{switch(this.provider){case"huggingface":return await this.callHuggingFace(r,e);case"ollama":return await this.callOllama(r,e);case"groq":return await this.callGroq(r,e);default:return{text:"",error:"AI provider not configured. Using fallback system."}}}catch(e){return console.error("AI Service Error:",e),{text:"",error:"AI service temporarily unavailable"}}}buildSystemPrompt(e,t){let a=`
EXACT Car Inventory & Pricing (FOB Japan) - DO NOT MAKE UP OTHER PRICES:
- Toyota Voxy (8 seats): \xa5300,000 (CHEAPEST)
- Toyota Noah (8 seats): \xa5350,000
- Toyota Sienta (7 seats): \xa5320,000
- Toyota Yaris (5 seats): \xa5550,000
- Toyota Vitz (5 seats): \xa5325,000

ONLY these 5 cars are available. DO NOT mention other models or prices.
`,r=`
CRITICAL RULES - NEVER VIOLATE:
1. ONLY use the exact car prices listed above
2. NEVER make up car models, prices, or availability
3. If asked about cars not listed, say "I don't have that information in our current inventory"
4. NEVER invent shipping costs, dates, or technical specifications
5. For unknown information, say "Let me connect you with our team for specific details"
6. ONLY use the contact information provided: WhatsApp +************
7. NEVER create fake customer testimonials or reviews
8. If unsure about anything, be honest and offer to connect them with a human agent
`,s="ja"===t?`あなたはEBAM Motorsの親切で正確なカスタマーサービス担当者です。日本からガーナ・アフリカへの中古品輸出を専門としています。

${a}

${r}

会社情報:
- 中古車（トヨタ、ホンダ、日産）
- 電子機器（家電、携帯電話、ノートパソコン）
- 家具（ダイニングセット、オフィス家具）
- 自転車（シティバイク、マウンテンバイク）
- 重機
- 家庭用品

連絡先:
- WhatsApp: +************
- 場所: クマシ、ガーナ
- WhatsAppチャンネル: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G

正確な情報のみを提供し、不明な場合は素直に「詳細については担当者にお繋ぎします」と答えてください。`:`You are a helpful and ACCURATE customer service representative for EBAM Motors, specializing in exporting used goods from Japan to Ghana and Africa.

${a}

${r}

Company Services:
- Used Cars (Toyota, Honda, Nissan)
- Electronics (appliances, phones, laptops)
- Furniture (dining sets, office furniture)
- Bicycles (city bikes, mountain bikes)
- Heavy Equipment
- Household Items

Contact Information:
- WhatsApp: +************
- Location: Kumasi, Ghana
- WhatsApp Channel: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G

Be accurate and honest. Only provide information you're certain about. When in doubt, offer to connect them with a human agent.`;return e?`${s}

Page Context: ${e}`:s}async callHuggingFace(e,t){if(!this.huggingfaceApiKey||this.huggingfaceApiKey.includes("your_"))return{text:"",error:"Hugging Face API key not configured"};let a=`${e}

User: ${t}
Assistant:`;try{let e=await fetch(`https://api-inference.huggingface.co/models/${this.huggingfaceModel}`,{method:"POST",headers:{Authorization:`Bearer ${this.huggingfaceApiKey}`,"Content-Type":"application/json"},body:JSON.stringify({inputs:a,parameters:{max_new_tokens:200,temperature:.7,do_sample:!0,return_full_text:!1,stop:["User:","Human:"]}})}),t=await e.json();if(t.error)return{text:"",error:t.error};return{text:(t[0]?.generated_text||"").trim()}}catch(e){return{text:"",error:`Hugging Face API error: ${e}`}}}async callOllama(e,t){let a=await fetch(`${this.ollamaBaseUrl}/api/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({model:this.ollamaModel,messages:[{role:"system",content:e},{role:"user",content:t}],stream:!1,options:{temperature:.7,top_p:.9,max_tokens:150}})});if(!a.ok)return{text:"",error:"Ollama service unavailable"};let r=await a.json();return{text:r.message?.content||""}}async callGroq(e,t){if(!this.groqApiKey||this.groqApiKey.includes("your_"))return{text:"",error:"Groq API key not configured"};try{let a=await fetch("https://api.groq.com/openai/v1/chat/completions",{method:"POST",headers:{Authorization:`Bearer ${this.groqApiKey}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.groqModel,messages:[{role:"system",content:e},{role:"user",content:t}],temperature:c.antiHallucination.temperature,max_tokens:c.antiHallucination.maxTokens,top_p:c.antiHallucination.topP,frequency_penalty:c.antiHallucination.frequencyPenalty,presence_penalty:c.antiHallucination.presencePenalty,stream:!1,stop:c.antiHallucination.stopSequences})});if(!a.ok){let e=await a.json();return{text:"",error:`Groq API error: ${e.error?.message||"Unknown error"}`}}let r=await a.json();if(r.error)return{text:"",error:r.error.message};let s=r.choices?.[0]?.message?.content||"",n=this.validateAndCleanResponse(s);return{text:n.cleanedResponse,hallucinationRisk:n.risk,validationIssues:n.issues}}catch(e){return{text:"",error:`Groq API request failed: ${e}`}}}validateAndCleanResponse(e){let t=u(e).issues,a=e.trim(),r="low";return"high"==(r=0===t.length?"low":t.length<=2?"medium":"high")?(a=l()+` Please contact us via WhatsApp at ${c.company.whatsapp} for accurate information.`,c.monitoring.logSuspiciousResponses&&console.warn("High hallucination risk detected:",{originalResponse:e,issues:t,timestamp:new Date().toISOString()})):"medium"===r&&(a+=`

*${c.responseGuidelines.disclaimers.pricing}*`),{cleanedResponse:a=this.applySafetyFilters(a),risk:r,issues:t}}applySafetyFilters(e){let t=e;return(t=(t=(t=t.replace(/Toyota [A-Z][a-z]+(?!Voxy|Noah|Sienta|Yaris|Vitz)/g,"one of our available Toyota models")).replace(/¥[0-9,]+(?!,000)/g,"competitive pricing")).replace(/guaranteed|promise|100%/gi,"typically")).length>c.responseGuidelines.maxResponseLength&&(t=t.substring(0,c.responseGuidelines.maxResponseLength-50)+"... Please contact us for complete details."),t}async isAvailable(){try{switch(this.provider){case"huggingface":return!!this.huggingfaceApiKey;case"ollama":return(await fetch(`${this.ollamaBaseUrl}/api/tags`,{method:"GET",signal:AbortSignal.timeout(5e3)})).ok;case"groq":return!!this.groqApiKey;default:return!1}}catch(e){return!1}}getProviderInfo(){switch(this.provider){case"huggingface":return{provider:"Hugging Face",model:this.huggingfaceModel};case"ollama":return{provider:"Ollama",model:this.ollamaModel};case"groq":return{provider:"Groq",model:this.groqModel};default:return{provider:"Fallback",model:"Rule-based system"}}}}let h=new p;async function d(e){try{let{message:t,context:a,locale:r}=await e.json();if(!t||"string"!=typeof t)return i.NextResponse.json({error:"Message is required and must be a string"},{status:400});if(!await h.isAvailable())return i.NextResponse.json({success:!1,error:"AI service not available",useAI:!1});let s=await h.generateResponse(t,a||"",r||"en");if(s.error)return i.NextResponse.json({success:!1,error:s.error,useAI:!1});return i.NextResponse.json({success:!0,response:s.text,useAI:!0,provider:h.getProviderInfo()})}catch(e){return console.error("Chatbot API error:",e),i.NextResponse.json({success:!1,error:"Internal server error",useAI:!1},{status:500})}}async function g(){try{let e=await h.isAvailable(),t=h.getProviderInfo();return i.NextResponse.json({status:"ok",aiAvailable:e,provider:t})}catch(e){return console.error("Error in chatbot API:",e),i.NextResponse.json({status:"error",aiAvailable:!1,error:"Service check failed"})}}let m=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/chatbot/route",pathname:"/api/chatbot",filename:"route",bundlePath:"app/api/chatbot/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\chatbot\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:y,workUnitAsyncStorage:f,serverHooks:v}=m;function A(){return(0,o.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:f})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580],()=>a(32529));module.exports=r})();