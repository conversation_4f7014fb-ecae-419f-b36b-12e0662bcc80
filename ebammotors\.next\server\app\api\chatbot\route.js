/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chatbot/route";
exports.ids = ["app/api/chatbot/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chatbot/route.ts */ \"(rsc)/./src/app/api/chatbot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chatbot/route\",\n        pathname: \"/api/chatbot\",\n        filename: \"route\",\n        bundlePath: \"app/api/chatbot/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\chatbot\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chatbot/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/chatbot/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_AIService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../services/AIService */ \"(rsc)/./src/services/AIService.ts\");\n\n\nasync function POST(request) {\n    try {\n        const { message, context, locale } = await request.json();\n        if (!message || typeof message !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // Check if AI service is available\n        const isAIAvailable = await _services_AIService__WEBPACK_IMPORTED_MODULE_1__.aiService.isAvailable();\n        if (!isAIAvailable) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'AI service not available',\n                useAI: false\n            });\n        }\n        // Generate AI response\n        const aiResponse = await _services_AIService__WEBPACK_IMPORTED_MODULE_1__.aiService.generateResponse(message, context || '', locale || 'en');\n        if (aiResponse.error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: aiResponse.error,\n                useAI: false\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            response: aiResponse.text,\n            useAI: true,\n            provider: _services_AIService__WEBPACK_IMPORTED_MODULE_1__.aiService.getProviderInfo()\n        });\n    } catch (error) {\n        console.error('Chatbot API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error',\n            useAI: false\n        }, {\n            status: 500\n        });\n    }\n}\n// Health check endpoint\nasync function GET() {\n    try {\n        const isAvailable = await _services_AIService__WEBPACK_IMPORTED_MODULE_1__.aiService.isAvailable();\n        const providerInfo = _services_AIService__WEBPACK_IMPORTED_MODULE_1__.aiService.getProviderInfo();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'ok',\n            aiAvailable: isAvailable,\n            provider: providerInfo\n        });\n    } catch (err) {\n        console.error('Error in chatbot API:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            aiAvailable: false,\n            error: 'Service check failed'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chatbot/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/chatbotConfig.ts":
/*!*************************************!*\
  !*** ./src/config/chatbotConfig.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CHATBOT_CONFIG: () => (/* binding */ CHATBOT_CONFIG),\n/* harmony export */   getCheapestCar: () => (/* binding */ getCheapestCar),\n/* harmony export */   getUncertaintyResponse: () => (/* binding */ getUncertaintyResponse),\n/* harmony export */   isValidCarModel: () => (/* binding */ isValidCarModel),\n/* harmony export */   isValidPrice: () => (/* binding */ isValidPrice),\n/* harmony export */   validateResponse: () => (/* binding */ validateResponse)\n/* harmony export */ });\n// Chatbot Configuration for Controlling Hallucination\nconst CHATBOT_CONFIG = {\n    // Anti-hallucination settings\n    antiHallucination: {\n        // Model parameters to reduce hallucination\n        temperature: 0.3,\n        topP: 0.8,\n        maxTokens: 200,\n        frequencyPenalty: 0.5,\n        presencePenalty: 0.3,\n        // Stop sequences to prevent rambling\n        stopSequences: [\n            \"User:\",\n            \"Human:\",\n            \"Customer:\",\n            \"\\n\\nUser:\",\n            \"\\n\\nHuman:\"\n        ],\n        // Validation patterns\n        suspiciousPatterns: [\n            /¥[0-9,]+(?!,000)/g,\n            /Toyota [A-Z][a-z]+(?!Voxy|Noah|Sienta|Yaris|Vitz)/g,\n            /Honda [A-Z][a-z]+/g,\n            /Nissan [A-Z][a-z]+/g,\n            /\\$[0-9,]+/g,\n            /[0-9]+ years? warranty/gi,\n            /guaranteed|promise|100%/gi,\n            /free shipping|no cost/gi,\n            /[0-9]+ days? delivery/gi\n        ]\n    },\n    // Exact inventory data - NEVER change these without updating actual inventory\n    inventory: {\n        cars: [\n            {\n                model: 'Toyota Voxy',\n                seats: 8,\n                price: 300000,\n                status: 'available'\n            },\n            {\n                model: 'Toyota Noah',\n                seats: 8,\n                price: 350000,\n                status: 'available'\n            },\n            {\n                model: 'Toyota Sienta',\n                seats: 7,\n                price: 320000,\n                status: 'available'\n            },\n            {\n                model: 'Toyota Yaris',\n                seats: 5,\n                price: 550000,\n                status: 'available'\n            },\n            {\n                model: 'Toyota Vitz',\n                seats: 5,\n                price: 325000,\n                status: 'available'\n            }\n        ],\n        // Valid price formats\n        validPrices: [\n            '¥300,000',\n            '¥350,000',\n            '¥320,000',\n            '¥550,000',\n            '¥325,000'\n        ],\n        // Valid model names\n        validModels: [\n            'Voxy',\n            'Noah',\n            'Sienta',\n            'Yaris',\n            'Vitz'\n        ],\n        // Cheapest car\n        cheapestCar: {\n            model: 'Toyota Voxy',\n            price: '¥300,000'\n        }\n    },\n    // Company information - EXACT details only\n    company: {\n        name: 'EBAM Motors',\n        location: 'Kumasi, Ghana',\n        whatsapp: '+************',\n        whatsappChannel: 'https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G',\n        services: [\n            'Used Cars (Toyota, Honda, Nissan)',\n            'Electronics (appliances, phones, laptops)',\n            'Furniture (dining sets, office furniture)',\n            'Bicycles (city bikes, mountain bikes)',\n            'Heavy Equipment',\n            'Household Items'\n        ]\n    },\n    // Response guidelines\n    responseGuidelines: {\n        // When to admit uncertainty\n        uncertaintyPhrases: [\n            \"I don't have that specific information in our current inventory\",\n            \"Let me connect you with our team for detailed information about that\",\n            \"For the most accurate details, please contact us directly\",\n            \"I'd recommend speaking with our specialists for specific technical details\"\n        ],\n        // Safe fallback responses\n        fallbackResponses: {\n            unknownCar: \"I can only provide information about our current inventory: Toyota Voxy, Noah, Sienta, Yaris, and Vitz. For other models, please contact our team.\",\n            unknownPrice: \"For the most current pricing and availability, please contact us via WhatsApp at +************.\",\n            technicalSpecs: \"For detailed technical specifications, I'd recommend speaking directly with our team who can provide comprehensive information.\",\n            shipping: \"Shipping costs and timeframes vary by destination. Please contact us for a personalized quote.\"\n        },\n        // Maximum response length\n        maxResponseLength: 300,\n        // Required disclaimers for certain topics\n        disclaimers: {\n            pricing: \"*Prices are FOB Japan and subject to change. Contact us for current rates.*\",\n            shipping: \"*Shipping costs and times vary by destination.*\",\n            availability: \"*Availability subject to change. Please confirm before ordering.*\"\n        }\n    },\n    // Monitoring and logging\n    monitoring: {\n        logSuspiciousResponses: true,\n        flagPotentialHallucinations: true,\n        requireHumanReview: false // Set to true for extra safety\n    }\n};\n// Helper functions\nconst getCheapestCar = ()=>CHATBOT_CONFIG.inventory.cheapestCar;\nconst isValidCarModel = (model)=>{\n    return CHATBOT_CONFIG.inventory.validModels.some((validModel)=>model.toLowerCase().includes(validModel.toLowerCase()));\n};\nconst isValidPrice = (price)=>{\n    return CHATBOT_CONFIG.inventory.validPrices.includes(price);\n};\nconst getUncertaintyResponse = ()=>{\n    const phrases = CHATBOT_CONFIG.responseGuidelines.uncertaintyPhrases;\n    return phrases[Math.floor(Math.random() * phrases.length)];\n};\nconst validateResponse = (response)=>{\n    const issues = [];\n    const config = CHATBOT_CONFIG.antiHallucination;\n    // Check for suspicious patterns\n    config.suspiciousPatterns.forEach((pattern, index)=>{\n        if (pattern.test(response)) {\n            issues.push(`Suspicious pattern detected: ${pattern.source}`);\n        }\n    });\n    // Check response length\n    if (response.length > CHATBOT_CONFIG.responseGuidelines.maxResponseLength) {\n        issues.push('Response too long');\n    }\n    return {\n        isValid: issues.length === 0,\n        issues\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/chatbotConfig.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/AIService.ts":
/*!***********************************!*\
  !*** ./src/services/AIService.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIService: () => (/* binding */ AIService),\n/* harmony export */   aiService: () => (/* binding */ aiService)\n/* harmony export */ });\n/* harmony import */ var _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/chatbotConfig */ \"(rsc)/./src/config/chatbotConfig.ts\");\n\nclass AIService {\n    constructor(){\n        this.provider = process.env.AI_PROVIDER || 'fallback';\n        this.huggingfaceApiKey = process.env.HUGGINGFACE_API_KEY;\n        this.huggingfaceModel = process.env.HUGGINGFACE_MODEL || 'meta-llama/Llama-2-7b-chat-hf';\n        this.ollamaBaseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';\n        this.ollamaModel = process.env.OLLAMA_MODEL || 'llama3.2:3b';\n        this.groqApiKey = process.env.GROQ_API_KEY;\n        this.groqModel = process.env.GROQ_MODEL || 'llama-3.1-8b-instant';\n    }\n    async generateResponse(userMessage, context = '', locale = 'en') {\n        const systemPrompt = this.buildSystemPrompt(context, locale);\n        try {\n            switch(this.provider){\n                case 'huggingface':\n                    return await this.callHuggingFace(systemPrompt, userMessage);\n                case 'ollama':\n                    return await this.callOllama(systemPrompt, userMessage);\n                case 'groq':\n                    return await this.callGroq(systemPrompt, userMessage);\n                default:\n                    return {\n                        text: '',\n                        error: 'AI provider not configured. Using fallback system.'\n                    };\n            }\n        } catch (error) {\n            console.error('AI Service Error:', error);\n            return {\n                text: '',\n                error: 'AI service temporarily unavailable'\n            };\n        }\n    }\n    buildSystemPrompt(context, locale) {\n        const carPricing = `\nEXACT Car Inventory & Pricing (FOB Japan) - DO NOT MAKE UP OTHER PRICES:\n- Toyota Voxy (8 seats): ¥300,000 (CHEAPEST)\n- Toyota Noah (8 seats): ¥350,000\n- Toyota Sienta (7 seats): ¥320,000\n- Toyota Yaris (5 seats): ¥550,000\n- Toyota Vitz (5 seats): ¥325,000\n\nONLY these 5 cars are available. DO NOT mention other models or prices.\n`;\n        const antiHallucinationRules = `\nCRITICAL RULES - NEVER VIOLATE:\n1. ONLY use the exact car prices listed above\n2. NEVER make up car models, prices, or availability\n3. If asked about cars not listed, say \"I don't have that information in our current inventory\"\n4. NEVER invent shipping costs, dates, or technical specifications\n5. For unknown information, say \"Let me connect you with our team for specific details\"\n6. ONLY use the contact information provided: WhatsApp +************\n7. NEVER create fake customer testimonials or reviews\n8. If unsure about anything, be honest and offer to connect them with a human agent\n`;\n        const basePrompt = locale === 'ja' ? `あなたはEBAM Motorsの親切で正確なカスタマーサービス担当者です。日本からガーナ・アフリカへの中古品輸出を専門としています。\n\n${carPricing}\n\n${antiHallucinationRules}\n\n会社情報:\n- 中古車（トヨタ、ホンダ、日産）\n- 電子機器（家電、携帯電話、ノートパソコン）\n- 家具（ダイニングセット、オフィス家具）\n- 自転車（シティバイク、マウンテンバイク）\n- 重機\n- 家庭用品\n\n連絡先:\n- WhatsApp: +************\n- 場所: クマシ、ガーナ\n- WhatsAppチャンネル: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G\n\n正確な情報のみを提供し、不明な場合は素直に「詳細については担当者にお繋ぎします」と答えてください。` : `You are a helpful and ACCURATE customer service representative for EBAM Motors, specializing in exporting used goods from Japan to Ghana and Africa.\n\n${carPricing}\n\n${antiHallucinationRules}\n\nCompany Services:\n- Used Cars (Toyota, Honda, Nissan)\n- Electronics (appliances, phones, laptops)\n- Furniture (dining sets, office furniture)\n- Bicycles (city bikes, mountain bikes)\n- Heavy Equipment\n- Household Items\n\nContact Information:\n- WhatsApp: +************\n- Location: Kumasi, Ghana\n- WhatsApp Channel: https://whatsapp.com/channel/0029Vb5sDlFDTkK191T6Ut1G\n\nBe accurate and honest. Only provide information you're certain about. When in doubt, offer to connect them with a human agent.`;\n        return context ? `${basePrompt}\\n\\nPage Context: ${context}` : basePrompt;\n    }\n    async callHuggingFace(systemPrompt, userMessage) {\n        if (!this.huggingfaceApiKey || this.huggingfaceApiKey.includes('your_')) {\n            return {\n                text: '',\n                error: 'Hugging Face API key not configured'\n            };\n        }\n        // Use text generation with proper formatting\n        const prompt = `${systemPrompt}\n\nUser: ${userMessage}\nAssistant:`;\n        try {\n            const response = await fetch(`https://api-inference.huggingface.co/models/${this.huggingfaceModel}`, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.huggingfaceApiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    inputs: prompt,\n                    parameters: {\n                        max_new_tokens: 200,\n                        temperature: 0.7,\n                        do_sample: true,\n                        return_full_text: false,\n                        stop: [\n                            \"User:\",\n                            \"Human:\"\n                        ]\n                    }\n                })\n            });\n            const data = await response.json();\n            if (data.error) {\n                return {\n                    text: '',\n                    error: data.error\n                };\n            }\n            const generatedText = data[0]?.generated_text || '';\n            return {\n                text: generatedText.trim()\n            };\n        } catch (error) {\n            return {\n                text: '',\n                error: `Hugging Face API error: ${error}`\n            };\n        }\n    }\n    async callOllama(systemPrompt, userMessage) {\n        const messages = [\n            {\n                role: 'system',\n                content: systemPrompt\n            },\n            {\n                role: 'user',\n                content: userMessage\n            }\n        ];\n        const response = await fetch(`${this.ollamaBaseUrl}/api/chat`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: this.ollamaModel,\n                messages: messages,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    top_p: 0.9,\n                    max_tokens: 150\n                }\n            })\n        });\n        if (!response.ok) {\n            return {\n                text: '',\n                error: 'Ollama service unavailable'\n            };\n        }\n        const data = await response.json();\n        return {\n            text: data.message?.content || ''\n        };\n    }\n    async callGroq(systemPrompt, userMessage) {\n        if (!this.groqApiKey || this.groqApiKey.includes('your_')) {\n            return {\n                text: '',\n                error: 'Groq API key not configured'\n            };\n        }\n        const messages = [\n            {\n                role: 'system',\n                content: systemPrompt\n            },\n            {\n                role: 'user',\n                content: userMessage\n            }\n        ];\n        try {\n            const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.groqApiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.groqModel,\n                    messages: messages,\n                    // Anti-hallucination parameters from config\n                    temperature: _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.antiHallucination.temperature,\n                    max_tokens: _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.antiHallucination.maxTokens,\n                    top_p: _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.antiHallucination.topP,\n                    frequency_penalty: _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.antiHallucination.frequencyPenalty,\n                    presence_penalty: _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.antiHallucination.presencePenalty,\n                    stream: false,\n                    stop: _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.antiHallucination.stopSequences\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                return {\n                    text: '',\n                    error: `Groq API error: ${errorData.error?.message || 'Unknown error'}`\n                };\n            }\n            const data = await response.json();\n            if (data.error) {\n                return {\n                    text: '',\n                    error: data.error.message\n                };\n            }\n            const aiResponse = data.choices?.[0]?.message?.content || '';\n            // Comprehensive hallucination detection and response validation\n            const validationResult = this.validateAndCleanResponse(aiResponse);\n            return {\n                text: validationResult.cleanedResponse,\n                hallucinationRisk: validationResult.risk,\n                validationIssues: validationResult.issues\n            };\n        } catch (error) {\n            return {\n                text: '',\n                error: `Groq API request failed: ${error}`\n            };\n        }\n    }\n    // Comprehensive validation and hallucination detection\n    validateAndCleanResponse(response) {\n        const validation = (0,_config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.validateResponse)(response);\n        const issues = validation.issues;\n        let cleanedResponse = response.trim();\n        let risk = 'low';\n        // Determine risk level based on issues found\n        if (issues.length === 0) {\n            risk = 'low';\n        } else if (issues.length <= 2) {\n            risk = 'medium';\n        } else {\n            risk = 'high';\n        }\n        // Handle high-risk responses\n        if (risk === 'high') {\n            // Replace with safe fallback\n            cleanedResponse = (0,_config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.getUncertaintyResponse)() + ` Please contact us via WhatsApp at ${_config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.company.whatsapp} for accurate information.`;\n            // Log for monitoring\n            if (_config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.monitoring.logSuspiciousResponses) {\n                console.warn('High hallucination risk detected:', {\n                    originalResponse: response,\n                    issues: issues,\n                    timestamp: new Date().toISOString()\n                });\n            }\n        } else if (risk === 'medium') {\n            // Add disclaimer for medium risk\n            cleanedResponse += `\\n\\n*${_config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.responseGuidelines.disclaimers.pricing}*`;\n        }\n        // Additional safety checks\n        cleanedResponse = this.applySafetyFilters(cleanedResponse);\n        return {\n            cleanedResponse,\n            risk,\n            issues\n        };\n    }\n    // Apply additional safety filters\n    applySafetyFilters(response) {\n        let filtered = response;\n        // Replace any invalid car models with safe alternatives\n        const invalidModelPattern = /Toyota [A-Z][a-z]+(?!Voxy|Noah|Sienta|Yaris|Vitz)/g;\n        filtered = filtered.replace(invalidModelPattern, 'one of our available Toyota models');\n        // Replace invalid prices with safe language\n        const invalidPricePattern = /¥[0-9,]+(?!,000)/g;\n        filtered = filtered.replace(invalidPricePattern, 'competitive pricing');\n        // Remove absolute guarantees\n        filtered = filtered.replace(/guaranteed|promise|100%/gi, 'typically');\n        // Ensure response isn't too long\n        if (filtered.length > _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.responseGuidelines.maxResponseLength) {\n            filtered = filtered.substring(0, _config_chatbotConfig__WEBPACK_IMPORTED_MODULE_0__.CHATBOT_CONFIG.responseGuidelines.maxResponseLength - 50) + '... Please contact us for complete details.';\n        }\n        return filtered;\n    }\n    // Check if AI service is available\n    async isAvailable() {\n        try {\n            switch(this.provider){\n                case 'huggingface':\n                    return !!this.huggingfaceApiKey;\n                case 'ollama':\n                    const ollamaResponse = await fetch(`${this.ollamaBaseUrl}/api/tags`, {\n                        method: 'GET',\n                        signal: AbortSignal.timeout(5000) // 5 second timeout\n                    });\n                    return ollamaResponse.ok;\n                case 'groq':\n                    return !!this.groqApiKey;\n                default:\n                    return false;\n            }\n        } catch (error) {\n            return false;\n        }\n    }\n    // Get current provider info\n    getProviderInfo() {\n        switch(this.provider){\n            case 'huggingface':\n                return {\n                    provider: 'Hugging Face',\n                    model: this.huggingfaceModel\n                };\n            case 'ollama':\n                return {\n                    provider: 'Ollama',\n                    model: this.ollamaModel\n                };\n            case 'groq':\n                return {\n                    provider: 'Groq',\n                    model: this.groqModel\n                };\n            default:\n                return {\n                    provider: 'Fallback',\n                    model: 'Rule-based system'\n                };\n        }\n    }\n}\nconst aiService = new AIService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/AIService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();