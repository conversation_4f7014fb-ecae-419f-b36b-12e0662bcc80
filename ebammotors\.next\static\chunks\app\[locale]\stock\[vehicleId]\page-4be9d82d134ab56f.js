(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9218],{1897:(e,s,t)=>{Promise.resolve().then(t.bind(t,3171))},3171:(e,s,t)=>{"use strict";t.d(s,{default:()=>en});var l=t(5155),a=t(2115),n=t(6874),r=t.n(n),i=t(2657),c=t(381),o=t(7434),d=t(133),x=t(4202),m=t(7550),h=t(1976),u=t(6516),p=t(9074),b=t(8611),j=t(4460),g=t(4516),N=t(5525),v=t(7909),f=t(6766),y=t(2355),w=t(3052),k=t(4481),C=t(4416),A=t(1788);function S(e){let{vehicle:s,locale:t}=e,[n,r]=(0,a.useState)(0),[i,c]=(0,a.useState)(!1),[o,d]=(0,a.useState)(0),x=s.images||[s.image],m=e=>{d(e),c(!0)},h=async e=>{try{let t=await fetch(e),l=await t.blob(),a=window.URL.createObjectURL(l),n=document.createElement("a");n.href=a,n.download="".concat(s.title,"-").concat(o+1,".jpg"),document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(a),document.body.removeChild(n)}catch(e){console.error("Error downloading image:",e)}};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"relative bg-white rounded-lg overflow-hidden border group",children:(0,l.jsxs)("div",{className:"aspect-[4/3] relative",children:[(0,l.jsx)(f.default,{src:x[n],alt:"".concat(s.title," - Image ").concat(n+1),fill:!0,className:"object-cover cursor-pointer",onClick:()=>m(n)}),x.length>1&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{onClick:()=>{r(e=>(e-1+x.length)%x.length)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70",children:(0,l.jsx)(y.A,{className:"w-5 h-5"})}),(0,l.jsx)("button",{onClick:()=>{r(e=>(e+1)%x.length)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70",children:(0,l.jsx)(w.A,{className:"w-5 h-5"})})]}),(0,l.jsx)("div",{className:"absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity",children:(0,l.jsx)(k.A,{className:"w-4 h-4"})}),(0,l.jsxs)("div",{className:"absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[n+1," / ",x.length]})]})}),x.length>1&&(0,l.jsx)("div",{className:"grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2",children:x.map((e,t)=>(0,l.jsx)("button",{onClick:()=>r(t),className:"relative aspect-square rounded-lg overflow-hidden border-2 transition-all ".concat(t===n?"border-primary-600 ring-2 ring-primary-200":"border-neutral-200 hover:border-neutral-300"),children:(0,l.jsx)(f.default,{src:e,alt:"".concat(s.title," - Thumbnail ").concat(t+1),fill:!0,className:"object-cover"})},t))}),i&&(0,l.jsx)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"relative w-full h-full flex items-center justify-center p-4",children:[(0,l.jsx)("button",{onClick:()=>c(!1),className:"absolute top-4 right-4 text-white p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors z-10",children:(0,l.jsx)(C.A,{className:"w-6 h-6"})}),(0,l.jsx)("button",{onClick:()=>h(x[o]),className:"absolute top-4 right-16 text-white p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors z-10",children:(0,l.jsx)(A.A,{className:"w-6 h-6"})}),x.length>1&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{onClick:()=>{d(e=>(e-1+x.length)%x.length)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full bg-black/50 hover:bg-black/70 transition-colors",children:(0,l.jsx)(y.A,{className:"w-8 h-8"})}),(0,l.jsx)("button",{onClick:()=>{d(e=>(e+1)%x.length)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full bg-black/50 hover:bg-black/70 transition-colors",children:(0,l.jsx)(w.A,{className:"w-8 h-8"})})]}),(0,l.jsx)("div",{className:"relative max-w-full max-h-full",children:(0,l.jsx)(f.default,{src:x[o],alt:"".concat(s.title," - Image ").concat(o+1),width:1200,height:900,className:"object-contain max-w-full max-h-full"})}),(0,l.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-center",children:(0,l.jsxs)("div",{className:"bg-black/50 px-4 py-2 rounded-lg",children:[(0,l.jsx)("div",{className:"font-medium",children:s.title}),(0,l.jsxs)("div",{className:"text-sm opacity-75",children:["en"===t?"Image":"画像"," ",o+1," ","en"===t?"of":"/"," ",x.length]})]})})]})})]})}var I=t(7930),T=t(7580),R=t(8313),E=t(3127);function P(e){var s;let{vehicle:t,locale:a}=e,n={make:t.title.split(" ")[0]||"Toyota",model:t.title.split(" ").slice(1,-1).join(" ")||"Unknown",year:t.year||2010,mileage:t.mileage||5e4,engine:"Hybrid"===t.fuelType?"1.8L Hybrid":"Diesel"===t.fuelType?"2.0L Diesel":"1.5L Gasoline",fuelType:t.fuelType||"Gasoline",transmission:t.transmission||"Automatic",drivetrain:"Front-Wheel Drive",seating:(null==(s=t.specs.find(e=>e.includes("seater")))?void 0:s.replace("-seater"," seats"))||"5 seats",doors:"4 doors",bodyType:"Sedan",bodyCondition:t.bodyCondition||"Good",interiorCondition:t.bodyCondition||"Good",airConditioning:"Yes",powerSteering:"Yes",registrationStatus:"Valid",inspectionStatus:"Passed",exportReady:"Yes",color:["White","Silver","Black","Blue","Red"][t.id%5],fuelTankCapacity:"50L",groundClearance:"150mm"},r=[{title:"en"===a?"Basic Information":"基本情報",icon:I.A,items:[{label:"en"===a?"Make":"メーカー",value:n.make},{label:"en"===a?"Model":"モデル",value:n.model},{label:"en"===a?"Year":"年式",value:n.year.toString()},{label:"en"===a?"Body Type":"ボディタイプ",value:n.bodyType},{label:"en"===a?"Color":"色",value:n.color},{label:"en"===a?"Doors":"ドア数",value:n.doors}]},{title:"en"===a?"Engine & Performance":"エンジン・性能",icon:c.A,items:[{label:"en"===a?"Engine":"エンジン",value:n.engine},{label:"en"===a?"Fuel Type":"燃料タイプ",value:n.fuelType},{label:"en"===a?"Transmission":"トランスミッション",value:n.transmission},{label:"en"===a?"Drivetrain":"駆動方式",value:n.drivetrain},{label:"en"===a?"Fuel Tank":"燃料タンク",value:n.fuelTankCapacity}]},{title:"en"===a?"Dimensions & Capacity":"寸法・容量",icon:T.A,items:[{label:"en"===a?"Seating Capacity":"乗車定員",value:n.seating},{label:"en"===a?"Mileage":"走行距離",value:"".concat(Math.floor(n.mileage/1e3),"k km")},{label:"en"===a?"Ground Clearance":"最低地上高",value:n.groundClearance}]},{title:"en"===a?"Condition & Features":"状態・装備",icon:N.A,items:[{label:"en"===a?"Body Condition":"ボディ状態",value:n.bodyCondition},{label:"en"===a?"Interior Condition":"内装状態",value:n.interiorCondition},{label:"en"===a?"Air Conditioning":"エアコン",value:n.airConditioning},{label:"en"===a?"Power Steering":"パワーステアリング",value:n.powerSteering}]},{title:"en"===a?"Documentation & Export":"書類・輸出",icon:R.A,items:[{label:"en"===a?"Registration":"登録",value:n.registrationStatus},{label:"en"===a?"Inspection":"車検",value:n.inspectionStatus},{label:"en"===a?"Export Ready":"輸出準備",value:n.exportReady},{label:"en"===a?"Location":"所在地",value:t.location}]}];return(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-neutral-800 mb-2",children:"en"===a?"Detailed Specifications":"詳細仕様"}),(0,l.jsx)("p",{className:"text-neutral-600",children:"en"===a?"Complete technical specifications and features for this vehicle.":"この車両の完全な技術仕様と機能。"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:r.map((e,s)=>{let t=e.icon;return(0,l.jsxs)("div",{className:"bg-white rounded-lg border overflow-hidden",children:[(0,l.jsx)("div",{className:"bg-neutral-50 px-6 py-4 border-b",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"p-2 bg-primary-100 rounded-lg",children:(0,l.jsx)(t,{className:"w-5 h-5 text-primary-600"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:e.title})]})}),(0,l.jsx)("div",{className:"p-6",children:(0,l.jsx)("div",{className:"space-y-4",children:e.items.map((e,s)=>(0,l.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-neutral-100 last:border-b-0",children:[(0,l.jsx)("span",{className:"text-neutral-600 font-medium",children:e.label}),(0,l.jsx)("span",{className:"text-neutral-800 font-semibold",children:e.value})]},s))})})]},s)})}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,l.jsx)(E.A,{className:"w-5 h-5 text-primary-600"}),(0,l.jsx)("span",{children:"en"===a?"Standard Features":"標準装備"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:["en"===a?"Power Windows":"パワーウィンドウ","en"===a?"Central Locking":"セントラルロック","en"===a?"ABS Brakes":"ABSブレーキ","en"===a?"Airbags":"エアバッグ","en"===a?"Radio/CD Player":"ラジオ/CDプレーヤー","en"===a?"Electric Mirrors":"電動ミラー"].map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,l.jsx)("span",{className:"text-neutral-700",children:e})]},s))})]}),(0,l.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,l.jsx)(j.A,{className:"w-5 h-5 text-green-600"}),(0,l.jsx)("span",{children:"en"===a?"Fuel Economy":"燃費"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"Hybrid"===n.fuelType?"25":"Diesel"===n.fuelType?"18":"15"}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===a?"km/L City":"km/L 市街地"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"Hybrid"===n.fuelType?"28":"Diesel"===n.fuelType?"22":"18"}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===a?"km/L Highway":"km/L 高速道路"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"Hybrid"===n.fuelType?"26":"Diesel"===n.fuelType?"20":"16"}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===a?"km/L Combined":"km/L 総合"})]})]})]})]})}var V=t(646),D=t(1007);function O(e){var s;let{vehicle:t,locale:n}=e,[r,c]=(0,a.useState)(null),d=(()=>{let e=new Date().getFullYear()-(t.year||2010);return{previousOwners:Math.min(Math.floor(e/3)+1,3),serviceRecords:Math.floor(1.5*e)+2,accidentHistory:"Excellent"===t.bodyCondition?"None":"Needs Work"===t.bodyCondition?"Minor":"None",lastInspection:"2024-01-15",registrationHistory:[{date:"2024-01-15",type:"Inspection",status:"Passed",location:"Tokyo"},{date:"2023-06-20",type:"Registration Renewal",status:"Completed",location:"Tokyo"},{date:"2023-01-10",type:"Inspection",status:"Passed",location:"Tokyo"}]}})(),x=[{id:"inspection",title:"en"===n?"Vehicle Inspection Report":"車両検査報告書",date:"2024-01-15",status:"passed",description:"en"===n?"Comprehensive 150-point inspection":"包括的な150項目検査",icon:V.A},{id:"history",title:"en"===n?"Ownership History":"所有者履歴",date:"2024-01-10",status:"available",description:"en"===n?"Complete ownership and registration history":"完全な所有者・登録履歴",icon:D.A},{id:"maintenance",title:"en"===n?"Maintenance Records":"メンテナンス記録",date:"2024-01-05",status:"available",description:"en"===n?"Service and maintenance history":"サービス・メンテナンス履歴",icon:R.A},{id:"export",title:"en"===n?"Export Certification":"輸出証明書",date:"2024-01-20",status:"ready",description:"en"===n?"Export readiness and documentation":"輸出準備と書類",icon:N.A}],m=e=>{var s;let t=(null==(s=x.find(s=>s.id===e))?void 0:s.title)||"Report";alert("".concat("en"===n?"Downloading":"ダウンロード中",": ").concat(t))},h=e=>{c(e)};return(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-neutral-800 mb-2",children:"en"===n?"Vehicle History & Reports":"車両履歴・レポート"}),(0,l.jsx)("p",{className:"text-neutral-600",children:"en"===n?"Complete history, inspection reports, and documentation for this vehicle.":"この車両の完全な履歴、検査報告書、書類。"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border text-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:d.previousOwners}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===n?"Previous Owners":"前所有者数"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border text-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-green-600",children:d.serviceRecords}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===n?"Service Records":"サービス記録"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border text-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"None"===d.accidentHistory?"0":"1"}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===n?"Accidents":"事故歴"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border text-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"✓"}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===n?"Export Ready":"輸出準備完了"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg border",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b bg-neutral-50",children:(0,l.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 flex items-center space-x-2",children:[(0,l.jsx)(o.A,{className:"w-5 h-5 text-primary-600"}),(0,l.jsx)("span",{children:"en"===n?"Available Reports":"利用可能なレポート"})]})}),(0,l.jsx)("div",{className:"p-6",children:(0,l.jsx)("div",{className:"space-y-4",children:x.map(e=>{let s=e.icon;return(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-neutral-50 transition-colors",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"p-2 rounded-lg ".concat("passed"===e.status?"bg-green-100":"ready"===e.status?"bg-blue-100":"bg-neutral-100"),children:(0,l.jsx)(s,{className:"w-5 h-5 ".concat("passed"===e.status?"text-green-600":"ready"===e.status?"text-blue-600":"text-neutral-600")})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold text-neutral-800",children:e.title}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:e.description}),(0,l.jsxs)("p",{className:"text-xs text-neutral-500 mt-1",children:["en"===n?"Updated":"更新日",": ",e.date]})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("button",{onClick:()=>h(e.id),className:"p-2 text-neutral-600 hover:text-primary-600 transition-colors",title:"en"===n?"View Report":"レポートを表示",children:(0,l.jsx)(i.A,{className:"w-4 h-4"})}),(0,l.jsx)("button",{onClick:()=>m(e.id),className:"p-2 text-neutral-600 hover:text-primary-600 transition-colors",title:"en"===n?"Download Report":"レポートをダウンロード",children:(0,l.jsx)(A.A,{className:"w-4 h-4"})})]})]},e.id)})})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg border",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b bg-neutral-50",children:(0,l.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 flex items-center space-x-2",children:[(0,l.jsx)(p.A,{className:"w-5 h-5 text-primary-600"}),(0,l.jsx)("span",{children:"en"===n?"Registration Timeline":"登録履歴タイムライン"})]})}),(0,l.jsx)("div",{className:"p-6",children:(0,l.jsx)("div",{className:"space-y-6",children:d.registrationHistory.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,l.jsxs)("div",{className:"flex-shrink-0",children:[(0,l.jsx)("div",{className:"w-3 h-3 bg-primary-600 rounded-full mt-2"}),s<d.registrationHistory.length-1&&(0,l.jsx)("div",{className:"w-px h-12 bg-neutral-200 ml-1 mt-1"})]}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h4",{className:"font-semibold text-neutral-800",children:e.type}),(0,l.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("Passed"===e.status||"Completed"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.status})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-sm text-neutral-600",children:[(0,l.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,l.jsx)(p.A,{className:"w-3 h-3"}),(0,l.jsx)("span",{children:e.date})]}),(0,l.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,l.jsx)(g.A,{className:"w-3 h-3"}),(0,l.jsx)("span",{children:e.location})]})]})]})]},s))})})]}),r&&(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-neutral-800",children:null==(s=x.find(e=>e.id===r))?void 0:s.title}),(0,l.jsx)("button",{onClick:()=>c(null),className:"text-neutral-600 hover:text-neutral-800",children:"\xd7"})]}),(0,l.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)(o.A,{className:"w-16 h-16 text-neutral-400 mx-auto mb-4"}),(0,l.jsx)("h4",{className:"text-lg font-semibold text-neutral-800 mb-2",children:"en"===n?"Report Preview":"レポートプレビュー"}),(0,l.jsx)("p",{className:"text-neutral-600 mb-6",children:"en"===n?"This is a preview of the report. The full report contains detailed information about the vehicle.":"これはレポートのプレビューです。完全なレポートには車両の詳細情報が含まれています。"}),(0,l.jsx)("button",{onClick:()=>m(r),className:"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors",children:"en"===n?"Download Full Report":"完全なレポートをダウンロード"})]})})]})})]})}var M=t(2103),L=t(2178),F=t(5690),B=t(2568),H=t(1284),G=t(7918);function Y(e){let{vehicle:s,locale:t}=e,[n,r]=(0,a.useState)(0),[i,c]=(0,a.useState)(!1),[o,x]=(0,a.useState)(!1),[m,h]=(0,a.useState)(!1),[u,p]=(0,a.useState)(!0),b=(0,a.useRef)(null),j=(0,a.useRef)(0),g=(0,a.useRef)(0),N=s.images||[s.image],v=N.length;(0,a.useEffect)(()=>{if(i&&!o){let e=setInterval(()=>{r(e=>(e+1)%v)},150);return()=>clearInterval(e)}},[i,o,v]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{p(!1)},5e3);return()=>clearTimeout(e)},[]);let y=()=>{x(!1)};return(0,a.useEffect)(()=>{let e=()=>{h(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",e),()=>document.removeEventListener("fullscreenchange",e)},[]),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-neutral-800 mb-2",children:"en"===t?"360\xb0 Vehicle View":"360\xb0車両ビュー"}),(0,l.jsx)("p",{className:"text-neutral-600",children:"en"===t?"Drag to rotate, click play for auto-rotation, or use the controls below.":"ドラッグして回転、再生をクリックして自動回転、または下のコントロールを使用してください。"})]}),(0,l.jsxs)("div",{ref:b,className:"relative bg-white rounded-lg border overflow-hidden ".concat(m?"fixed inset-0 z-50 rounded-none":""),children:[u&&(0,l.jsxs)("div",{className:"absolute top-4 left-4 right-4 bg-black/75 text-white p-4 rounded-lg z-20 animate-fadeIn",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,l.jsx)(M.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"font-semibold",children:"en"===t?"How to use 360\xb0 View":"360\xb0ビューの使い方"})]}),(0,l.jsx)("p",{className:"text-sm",children:"en"===t?"Drag left or right to rotate the vehicle. Use controls below for auto-rotation.":"左右にドラッグして車両を回転させます。自動回転には下のコントロールを使用してください。"})]}),(0,l.jsxs)("div",{className:"relative ".concat(m?"h-screen":"aspect-[16/10]"," cursor-grab active:cursor-grabbing select-none"),onMouseDown:e=>{x(!0),c(!1),j.current=e.clientX,g.current=n,p(!1)},onMouseMove:e=>{if(!o)return;let s=Math.floor((e.clientX-j.current)/3);r((g.current+s+v)%v)},onMouseUp:y,onMouseLeave:y,onTouchStart:e=>{x(!0),c(!1),j.current=e.touches[0].clientX,g.current=n,p(!1)},onTouchMove:e=>{if(!o)return;let s=Math.floor((e.touches[0].clientX-j.current)/3);r((g.current+s+v)%v)},onTouchEnd:()=>{x(!1)},children:[(0,l.jsx)(f.default,{src:N[n],alt:"".concat(s.title," - 360\xb0 View Frame ").concat(n+1),fill:!0,className:"object-contain bg-neutral-100",priority:!0,draggable:!1}),(0,l.jsxs)("div",{className:"absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[n+1," / ",v]}),o&&(0,l.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:"en"===t?"Rotating...":"回転中..."})]}),(0,l.jsxs)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/75 backdrop-blur-sm rounded-full p-2 flex items-center space-x-2",children:[(0,l.jsx)("button",{onClick:()=>{r(e=>(e-1+v)%v),c(!1),p(!1)},className:"p-2 text-white hover:text-primary-400 transition-colors",title:"en"===t?"Rotate Left":"左回転",children:(0,l.jsx)(d.A,{className:"w-5 h-5"})}),(0,l.jsx)("button",{onClick:()=>{c(!i),p(!1)},className:"p-2 text-white hover:text-primary-400 transition-colors",title:i?"en"===t?"Pause":"一時停止":"en"===t?"Play":"再生",children:i?(0,l.jsx)(L.A,{className:"w-5 h-5"}):(0,l.jsx)(F.A,{className:"w-5 h-5"})}),(0,l.jsx)("button",{onClick:()=>{r(e=>(e+1)%v),c(!1),p(!1)},className:"p-2 text-white hover:text-primary-400 transition-colors",title:"en"===t?"Rotate Right":"右回転",children:(0,l.jsx)(B.A,{className:"w-5 h-5"})}),(0,l.jsx)("div",{className:"w-px h-6 bg-white/30 mx-1"}),(0,l.jsx)("button",{onClick:()=>p(!u),className:"p-2 text-white hover:text-primary-400 transition-colors",title:"en"===t?"Show Instructions":"使い方を表示",children:(0,l.jsx)(H.A,{className:"w-5 h-5"})}),(0,l.jsx)("button",{onClick:()=>{if(document.fullscreenElement)document.exitFullscreen(),h(!1);else{var e;null==(e=b.current)||e.requestFullscreen(),h(!0)}},className:"p-2 text-white hover:text-primary-400 transition-colors",title:"en"===t?"Fullscreen":"フルスクリーン",children:(0,l.jsx)(G.A,{className:"w-5 h-5"})})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4",children:"en"===t?"Exterior Highlights":"外装のハイライト"}),(0,l.jsx)("div",{className:"space-y-3",children:["en"===t?"Body condition: "+(s.bodyCondition||"Good"):"ボディ状態: "+(s.bodyCondition||"良好"),"en"===t?"Paint quality: Excellent":"塗装品質: 優秀","en"===t?"Rust inspection: Passed":"錆検査: 合格","en"===t?"Panel alignment: Good":"パネル整列: 良好"].map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,l.jsx)("span",{className:"text-neutral-700",children:e})]},s))})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4",children:"en"===t?"View Options":"表示オプション"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors",children:[(0,l.jsx)("div",{className:"font-medium text-neutral-800",children:"en"===t?"Interior 360\xb0 View":"内装360\xb0ビュー"}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Coming soon":"近日公開"})]}),(0,l.jsxs)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors",children:[(0,l.jsx)("div",{className:"font-medium text-neutral-800",children:"en"===t?"Engine Bay View":"エンジンルームビュー"}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Coming soon":"近日公開"})]}),(0,l.jsxs)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-neutral-50 transition-colors",children:[(0,l.jsx)("div",{className:"font-medium text-neutral-800",children:"en"===t?"Undercarriage View":"車体下部ビュー"}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===t?"Coming soon":"近日公開"})]})]})]})]})]})}var z=t(3109),U=t(8500),_=t(4616),J=t(7712),W=t(2138),q=t(8564);function X(e){let{vehicle:s,locale:t,onClose:n,isModal:i=!1}=e,[c,o]=(0,a.useState)([s]),[d,x]=(0,a.useState)([]),[m,h]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/stock");if(!e.ok)throw Error("Failed to fetch stock data");let t=(await e.json()).filter(e=>"cars"===e.category&&e.carId!==s.carId).slice(0,10);x(t)}catch(e){console.error("Error loading vehicles for comparison:",e),x([])}})()},[s.carId]);let u=e=>{c.length<3&&!c.find(s=>s.carId===e.carId)&&(o([...c,e]),h(!1))},p=e=>{c.length>1&&o(c.filter(s=>s.carId!==e))},b=e=>parseInt(e.replace(/[¥,]/g,""))||0,j=(e,s)=>{let t=s.reduce((e,s)=>e+s,0)/s.length;return e>1.1*t?(0,l.jsx)(z.A,{className:"w-4 h-4 text-green-600"}):e<.9*t?(0,l.jsx)(U.A,{className:"w-4 h-4 text-red-600"}):(0,l.jsx)("div",{className:"w-4 h-4"})},g=[{label:"en"===t?"Price":"価格",getValue:e=>e.price,getNumericValue:e=>b(e.price),isNumeric:!0,lowerIsBetter:!0},{label:"en"===t?"Year":"年式",getValue:e=>{var s;return(null==(s=e.year)?void 0:s.toString())||"N/A"},getNumericValue:e=>e.year||0,isNumeric:!0,lowerIsBetter:!1},{label:"en"===t?"Mileage":"走行距離",getValue:e=>e.mileage?"".concat(Math.floor(e.mileage/1e3),"k km"):"N/A",getNumericValue:e=>e.mileage||0,isNumeric:!0,lowerIsBetter:!0},{label:"en"===t?"Fuel Type":"燃料タイプ",getValue:e=>e.fuelType||"N/A",getNumericValue:()=>0,isNumeric:!1,lowerIsBetter:!1},{label:"en"===t?"Transmission":"トランスミッション",getValue:e=>e.transmission||"N/A",getNumericValue:()=>0,isNumeric:!1,lowerIsBetter:!1},{label:"en"===t?"Condition":"状態",getValue:e=>e.bodyCondition||"N/A",getNumericValue:()=>0,isNumeric:!1,lowerIsBetter:!1}],N=(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-neutral-800",children:"en"===t?"Vehicle Comparison":"車両比較"}),i&&n&&(0,l.jsx)("button",{onClick:n,className:"p-2 text-neutral-600 hover:text-neutral-800 transition-colors",children:(0,l.jsx)(C.A,{className:"w-6 h-6"})})]}),c.length<3&&(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsxs)("button",{onClick:()=>h(!m),className:"flex items-center space-x-2 px-3 sm:px-4 py-2 border-2 border-dashed border-neutral-300 rounded-lg text-neutral-600 hover:border-primary-500 hover:text-primary-600 transition-colors text-sm sm:text-base","aria-expanded":m,"aria-controls":"vehicle-selection",children:[(0,l.jsx)(_.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"en"===t?"Add Vehicle to Compare":"比較する車両を追加"}),(0,l.jsx)("span",{className:"sm:hidden",children:"en"===t?"Add Vehicle":"車両追加"})]})}),m&&(0,l.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-4 border",children:[(0,l.jsx)("h3",{className:"font-semibold text-neutral-800 mb-3",children:"en"===t?"Select Vehicle to Compare":"比較する車両を選択"}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto",children:d.map(e=>(0,l.jsxs)("button",{onClick:()=>u(e),className:"flex items-center space-x-3 p-3 bg-white rounded-lg border hover:border-primary-500 transition-colors text-left",children:[(0,l.jsx)("div",{className:"relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0",children:(0,l.jsx)(f.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover"})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsx)("div",{className:"font-medium text-neutral-800 truncate",children:e.title}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:e.price})]})]},e.carId))})]}),(0,l.jsx)("div",{className:"bg-white rounded-lg border overflow-hidden",children:(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{children:(0,l.jsxs)("tr",{className:"bg-neutral-50 border-b",children:[(0,l.jsx)("th",{className:"text-left p-2 sm:p-4 font-semibold text-neutral-800 min-w-[100px] sm:min-w-[120px] text-sm sm:text-base",children:"en"===t?"Specification":"仕様"}),c.map(e=>(0,l.jsx)("th",{className:"text-center p-2 sm:p-4 min-w-[150px] sm:min-w-[200px]",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("div",{className:"relative w-12 h-12 sm:w-16 sm:h-16 mx-auto rounded-lg overflow-hidden",children:(0,l.jsx)(f.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover"})}),(0,l.jsx)("div",{className:"font-semibold text-neutral-800 text-xs sm:text-sm",children:e.title}),c.length>1&&e.carId!==s.carId&&(0,l.jsx)("button",{onClick:()=>p(e.carId),className:"text-red-600 hover:text-red-800 transition-colors p-1","aria-label":"en"===t?"Remove from comparison":"比較から削除",children:(0,l.jsx)(J.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mx-auto"})})]})},e.carId))]})}),(0,l.jsx)("tbody",{children:g.map((e,s)=>{let t=c.map(s=>e.getNumericValue(s)).filter(e=>e>0);return(0,l.jsxs)("tr",{className:"border-b hover:bg-neutral-50",children:[(0,l.jsx)("td",{className:"p-2 sm:p-4 font-medium text-neutral-700 text-sm sm:text-base",children:e.label}),c.map(s=>{let a=e.getValue(s),n=e.getNumericValue(s),r=t.filter(e=>e!==n);return(0,l.jsx)("td",{className:"p-2 sm:p-4 text-center",children:(0,l.jsxs)("div",{className:"flex items-center justify-center space-x-1 sm:space-x-2",children:[(0,l.jsx)("span",{className:"font-semibold text-neutral-800 text-sm sm:text-base",children:a}),e.isNumeric&&r.length>0&&(0,l.jsx)("div",{className:"flex-shrink-0",children:j(n,r)})]})},s.carId)})]},s)})})]})})}),(0,l.jsx)("div",{className:"flex flex-wrap gap-3 justify-center",children:c.map(e=>(0,l.jsxs)(r(),{href:"/".concat(t,"/stock/").concat(e.carId),className:"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:[(0,l.jsxs)("span",{children:["en"===t?"View":"表示"," ",e.title.split(" ")[0]]}),(0,l.jsx)(W.A,{className:"w-4 h-4"})]},e.carId))}),(0,l.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:[(0,l.jsxs)("h3",{className:"font-semibold text-blue-800 mb-2 flex items-center space-x-2",children:[(0,l.jsx)(q.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"en"===t?"Comparison Tips":"比較のヒント"})]}),(0,l.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,l.jsxs)("li",{children:["• ","en"===t?"Green arrows indicate better values":"緑の矢印はより良い値を示します"]}),(0,l.jsxs)("li",{children:["• ","en"===t?"Red arrows indicate areas for consideration":"赤の矢印は検討すべき領域を示します"]}),(0,l.jsxs)("li",{children:["• ","en"===t?"Consider total cost including shipping":"送料を含む総費用を考慮してください"]})]})]})]});return i?(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,l.jsx)("div",{className:"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden",children:(0,l.jsx)("div",{className:"p-6 overflow-y-auto max-h-[90vh]",children:N})})}):(0,l.jsx)("div",{className:"bg-white rounded-lg p-6 border",children:N})}var K=t(7809),Q=t(9799),Z=t(1586),$=t(6740),ee=t(4186),es=t(283),et=t(351);function el(e){let{isOpen:s,onClose:t,vehicle:n,locale:r}=e,{user:i,addPurchase:c}=(0,es.A)(),[o,d]=(0,a.useState)("details"),[x,m]=(0,a.useState)(!1),[h,u]=(0,a.useState)(!1),[p,b]=(0,a.useState)([]),[j,v]=(0,a.useState)(null),[y,w]=(0,a.useState)([]),[k,A]=(0,a.useState)(null),[S,I]=(0,a.useState)({street:"",city:"",state:"",country:"Ghana",postalCode:""}),[T,R]=(0,a.useState)(!1),[E,P]=(0,a.useState)(null);if((0,a.useEffect)(()=>{if(s){let e=(0,et.u0)();b(e),e.length>0&&v(e[0])}},[s]),!s)return null;let D=async()=>{if(S.city&&S.country){R(!0);try{let e=await fetch("/api/shipping/calculate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({vehicle:{category:n.category||"sedan",year:n.year||2020,price:parseInt(n.price.replace(/[¥,]/g,""))||5e5},destination:{country:S.country,city:S.city},preference:"balanced"})}),s=await e.json();s.success&&(w(s.data.options),s.data.recommended&&A(s.data.recommended))}catch(e){console.error("Error calculating shipping:",e)}finally{R(!1)}}},O=async()=>{if(i&&j&&k){m(!0);try{let e=parseInt(n.price.replace(/[¥,]/g,""))||0,s=e+k.costs.total,t={customerId:i.id,customerInfo:{name:i.name,email:i.email,phone:i.phone||"",address:S},vehicle:{id:n.carId,title:n.title,price:e,currency:"JPY",images:n.images,specs:n.specs},payment:{method:j,amount:s,currency:"JPY",dueDate:new Date(Date.now()+2592e6).toISOString().split("T")[0]},shipping:{method:k.method,cost:k.costs.total,address:S,estimatedDelivery:k.estimatedDelivery},totalAmount:s,currency:"JPY"},l=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),a=await l.json();if(a.success)P(a.order.id),c({vehicleId:n.carId,vehicleTitle:n.title,price:n.price,status:"pending",trackingNumber:a.order.id}),u(!0),d("confirmation");else throw Error(a.message)}catch(e){console.error("Error creating order:",e),alert("Failed to create order. Please try again.")}finally{m(!1)}}},M=()=>{d("details"),u(!1),m(!1),v(null),A(null),w([]),P(null),t()};return(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-bold text-neutral-800 flex items-center space-x-2",children:[(0,l.jsx)(K.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"en"===r?"Purchase Vehicle":"車両購入"})]}),(0,l.jsx)("button",{onClick:M,className:"text-neutral-600 hover:text-neutral-800 transition-colors",children:(0,l.jsx)(C.A,{className:"w-6 h-6"})})]}),(0,l.jsxs)("div",{className:"p-6",children:["details"===o&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"font-semibold text-neutral-800 mb-3",children:"en"===r?"Vehicle Details":"車両詳細"}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)(f.default,{src:n.image,alt:n.title,width:80,height:80,className:"w-20 h-20 object-cover rounded-lg"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-neutral-800",children:n.title}),(0,l.jsx)("p",{className:"text-2xl font-bold text-primary-600",children:n.price}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:n.location})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"font-semibold text-neutral-800",children:"en"===r?"Purchase Options":"購入オプション"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,l.jsx)(N.A,{className:"w-8 h-8 text-blue-600 mx-auto mb-2"}),(0,l.jsx)("h4",{className:"font-medium text-neutral-800 mb-1",children:"en"===r?"Inspection":"検査"}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:"en"===r?"Professional inspection included":"プロの検査が含まれています"})]}),(0,l.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,l.jsx)(Q.A,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,l.jsx)("h4",{className:"font-medium text-neutral-800 mb-1",children:"en"===r?"Shipping":"配送"}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:"en"===r?"Door-to-door delivery":"ドア・ツー・ドア配送"})]}),(0,l.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,l.jsx)(Z.A,{className:"w-8 h-8 text-purple-600 mx-auto mb-2"}),(0,l.jsx)("h4",{className:"font-medium text-neutral-800 mb-1",children:"en"===r?"Payment":"支払い"}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:"en"===r?"Secure payment processing":"安全な決済処理"})]})]})]}),(0,l.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,l.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"en"===r?"Important Information":"重要な情報"}),(0,l.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,l.jsxs)("li",{children:["• ","en"===r?"Final price includes all fees and taxes":"最終価格にはすべての手数料と税金が含まれています"]}),(0,l.jsxs)("li",{children:["• ","en"===r?"Delivery time: 4-6 weeks to Ghana":"配送時間：ガーナまで4-6週間"]}),(0,l.jsxs)("li",{children:["• ","en"===r?"Full refund if vehicle condition differs from description":"車両の状態が説明と異なる場合は全額返金"]})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,l.jsx)("button",{onClick:M,className:"px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors",children:"en"===r?"Cancel":"キャンセル"}),(0,l.jsx)("button",{onClick:()=>d("shipping"),className:"px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:"en"===r?"Configure Shipping":"配送設定"})]})]}),"shipping"===o&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,l.jsx)(Q.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"en"===r?"Shipping Configuration":"配送設定"})]}),(0,l.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-4",children:[(0,l.jsxs)("h4",{className:"font-medium text-neutral-800 mb-3 flex items-center space-x-2",children:[(0,l.jsx)(g.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"en"===r?"Delivery Address":"配送先住所"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"en"===r?"Street Address":"住所"}),(0,l.jsx)("input",{type:"text",value:S.street,onChange:e=>I(s=>({...s,street:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===r?"Enter street address":"住所を入力"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"en"===r?"City":"市区町村"}),(0,l.jsx)("input",{type:"text",value:S.city,onChange:e=>I(s=>({...s,city:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===r?"Enter city":"市区町村を入力"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"en"===r?"State/Region":"州/地域"}),(0,l.jsx)("input",{type:"text",value:S.state,onChange:e=>I(s=>({...s,state:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"en"===r?"Enter state/region":"州/地域を入力"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"en"===r?"Country":"国"}),(0,l.jsxs)("select",{value:S.country,onChange:e=>I(s=>({...s,country:e.target.value})),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,l.jsx)("option",{value:"Ghana",children:"Ghana"}),(0,l.jsx)("option",{value:"Nigeria",children:"Nigeria"}),(0,l.jsx)("option",{value:"Kenya",children:"Kenya"}),(0,l.jsx)("option",{value:"South Africa",children:"South Africa"}),(0,l.jsx)("option",{value:"Ivory Coast",children:"Ivory Coast"})]})]})]}),(0,l.jsxs)("button",{onClick:D,disabled:!S.city||!S.country||T,className:"mt-4 flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,l.jsx)($.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:T?"en"===r?"Calculating...":"計算中...":"en"===r?"Calculate Shipping":"配送料計算"})]})]}),y.length>0&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("h4",{className:"font-medium text-neutral-800 flex items-center space-x-2",children:[(0,l.jsx)(ee.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"en"===r?"Shipping Options":"配送オプション"})]}),y.map((e,s)=>(0,l.jsxs)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null==k?void 0:k.method.id)===e.method.id?"border-primary-500 bg-primary-50":"border-neutral-300 hover:border-neutral-400"),onClick:()=>A(e),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h5",{className:"font-medium text-neutral-800",children:e.method.name}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:e.method.description}),(0,l.jsxs)("p",{className:"text-sm text-neutral-500 mt-1",children:["en"===r?"Estimated delivery:":"配送予定:"," ",e.estimatedDelivery]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("div",{className:"text-lg font-bold text-primary-600",children:["\xa5",e.costs.total.toLocaleString()]}),(0,l.jsxs)("div",{className:"text-sm text-neutral-500",children:[e.method.estimatedDays," ","en"===r?"days":"日"]})]})]}),e.isRecommended&&(0,l.jsx)("div",{className:"mt-2 inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"en"===r?"Recommended":"推奨"})]},s))]}),(0,l.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,l.jsx)("button",{onClick:()=>d("details"),className:"px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors",children:"en"===r?"Back":"戻る"}),(0,l.jsx)("button",{onClick:()=>d("payment"),disabled:!k,className:"px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"en"===r?"Continue to Payment":"支払いに進む"})]})]}),"payment"===o&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,l.jsx)(Z.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"en"===r?"Payment Method":"支払い方法"})]}),(0,l.jsxs)("div",{className:"bg-neutral-50 rounded-lg p-4",children:[(0,l.jsx)("h4",{className:"font-medium text-neutral-800 mb-3",children:"en"===r?"Order Summary":"注文概要"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{children:n.title}),(0,l.jsx)("span",{children:n.price})]}),k&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsxs)("span",{children:["en"===r?"Shipping":"配送料"," (",k.method.name,")"]}),(0,l.jsxs)("span",{children:["\xa5",k.costs.total.toLocaleString()]})]}),(0,l.jsxs)("div",{className:"border-t pt-2 flex justify-between font-bold",children:[(0,l.jsx)("span",{children:"en"===r?"Total":"合計"}),(0,l.jsxs)("span",{children:["\xa5",(parseInt(n.price.replace(/[¥,]/g,""))+k.costs.total).toLocaleString()]})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h4",{className:"font-medium text-neutral-800",children:"en"===r?"Select Payment Method":"支払い方法を選択"}),p.map(e=>(0,l.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null==j?void 0:j.id)===e.id?"border-primary-500 bg-primary-50":"border-neutral-300 hover:border-neutral-400"),onClick:()=>v(e),children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("span",{className:"text-2xl",children:e.icon}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h5",{className:"font-medium text-neutral-800",children:e.name}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:e.description})]})]})},e.id))]}),(0,l.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,l.jsx)("p",{className:"text-sm text-blue-700 mb-3",children:"en"===r?"By proceeding, you agree to create an order. Payment instructions will be provided after order creation.":"続行することで、注文の作成に同意したことになります。注文作成後に支払い指示が提供されます。"}),(0,l.jsx)("button",{onClick:O,disabled:x||!j,className:"w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2",children:x?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,l.jsx)("span",{children:"en"===r?"Creating Order...":"注文作成中..."})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(K.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"en"===r?"Create Order":"注文作成"})]})})]}),(0,l.jsx)("div",{className:"flex items-center justify-between pt-4",children:(0,l.jsx)("button",{onClick:()=>d("shipping"),disabled:x,className:"px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors disabled:opacity-50",children:"en"===r?"Back":"戻る"})})]}),"confirmation"===o&&h&&(0,l.jsxs)("div",{className:"space-y-6 text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto",children:(0,l.jsx)(V.A,{className:"w-8 h-8 text-green-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold text-neutral-800 mb-2",children:"en"===r?"Order Created Successfully!":"注文作成完了！"}),(0,l.jsx)("p",{className:"text-neutral-600 mb-4",children:"en"===r?"Your order has been created and an invoice has been generated. Please complete payment to proceed.":"注文が作成され、請求書が生成されました。続行するには支払いを完了してください。"}),E&&(0,l.jsx)("div",{className:"bg-neutral-100 rounded-lg p-3 mb-4",children:(0,l.jsxs)("p",{className:"text-sm text-neutral-600",children:["en"===r?"Order ID:":"注文ID:"," ",(0,l.jsx)("span",{className:"font-mono font-medium",children:E})]})})]}),j&&(0,l.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-left",children:[(0,l.jsxs)("h4",{className:"font-medium text-blue-800 mb-2 flex items-center space-x-2",children:[(0,l.jsx)(Z.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"en"===r?"Payment Instructions":"支払い指示"})]}),(0,l.jsx)("div",{className:"text-sm text-blue-700 whitespace-pre-line",children:j&&k&&E&&(()=>{let e=parseInt(n.price.replace(/[¥,]/g,""))+k.costs.total;return"Payment Method: ".concat(j.name,"\nAmount: \xa5").concat(e.toLocaleString(),"\nOrder Reference: ").concat(E,"\n\nPlease contact us for detailed payment instructions.")})()})]}),(0,l.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,l.jsx)("h4",{className:"font-medium text-green-800 mb-2",children:"en"===r?"What happens next?":"次に何が起こりますか？"}),(0,l.jsxs)("ul",{className:"text-sm text-green-700 space-y-1 text-left",children:[(0,l.jsxs)("li",{children:["• ","en"===r?"Complete payment using the instructions above":"上記の指示に従って支払いを完了"]}),(0,l.jsxs)("li",{children:["• ","en"===r?"Vehicle inspection and preparation":"車両検査と準備"]}),(0,l.jsxs)("li",{children:["• ","en"===r?"Export documentation processing":"輸出書類処理"]}),(0,l.jsxs)("li",{children:["• ","en"===r?"Shipping arrangement":"配送手配"]}),(0,l.jsxs)("li",{children:["• ","en"===r?"Regular updates on delivery status":"配送状況の定期更新"]})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{onClick:M,className:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors",children:"en"===r?"Close":"閉じる"}),E&&(0,l.jsx)("button",{onClick:()=>window.open("/tracking?orderId=".concat(E),"_blank"),className:"w-full bg-neutral-600 text-white py-2 px-6 rounded-lg hover:bg-neutral-700 transition-colors",children:"en"===r?"Track Order":"注文追跡"})]})]})]})]})})}var ea=t(8350);function en(e){var s;let{vehicle:t,locale:n}=e,[f,y]=(0,a.useState)("overview"),[w,k]=(0,a.useState)(!1),[C,A]=(0,a.useState)(!1),[I,T]=(0,a.useState)(!1),[R,E]=(0,a.useState)(!1),V=[{id:"overview",label:"en"===n?"Overview":"概要",icon:i.A},{id:"specs",label:"en"===n?"Specifications":"仕様",icon:c.A},{id:"history",label:"en"===n?"History & Reports":"履歴・レポート",icon:o.A},{id:"360view",label:"en"===n?"360\xb0 View":"360\xb0ビュー",icon:d.A},{id:"compare",label:"en"===n?"Compare":"比較",icon:x.A}],D=async()=>{if(navigator.share)try{await navigator.share({title:t.title,text:"Check out this ".concat(t.title," at EBAM Motors"),url:window.location.href})}catch(e){}else navigator.clipboard.writeText(window.location.href),alert("en"===n?"Link copied to clipboard!":"リンクがクリップボードにコピーされました！")};return(0,l.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,l.jsx)(v.default,{}),(0,l.jsx)("div",{className:"bg-white border-b sticky top-16 z-40",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)(r(),{href:"/".concat(n,"/stock"),className:"flex items-center space-x-2 text-neutral-600 hover:text-primary-600 transition-colors",children:[(0,l.jsx)(m.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"en"===n?"Back to Stock":"在庫に戻る"})]}),(0,l.jsx)("div",{className:"hidden sm:block w-px h-6 bg-neutral-300"}),(0,l.jsx)("h1",{className:"text-xl font-bold text-neutral-800",children:t.title})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("button",{onClick:()=>k(!w),className:"p-2 rounded-lg border transition-colors ".concat(w?"bg-red-50 border-red-200 text-red-600":"bg-white border-neutral-300 text-neutral-600 hover:text-red-600"),children:(0,l.jsx)(h.A,{className:"w-5 h-5 ".concat(w?"fill-current":"")})}),(0,l.jsx)("button",{onClick:D,className:"p-2 rounded-lg border border-neutral-300 text-neutral-600 hover:text-primary-600 transition-colors",children:(0,l.jsx)(u.A,{className:"w-5 h-5"})}),(0,l.jsxs)("button",{onClick:()=>A(!0),className:"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2",children:[(0,l.jsx)(x.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"en"===n?"Compare":"比較"})]})]})]})})}),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,l.jsx)(S,{vehicle:t,locale:n}),(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 text-neutral-600 mb-1",children:[(0,l.jsx)(p.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"text-sm",children:"en"===n?"Year":"年式"})]}),(0,l.jsx)("div",{className:"text-lg font-semibold text-neutral-800",children:t.year||"N/A"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 text-neutral-600 mb-1",children:[(0,l.jsx)(b.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"text-sm",children:"en"===n?"Mileage":"走行距離"})]}),(0,l.jsx)("div",{className:"text-lg font-semibold text-neutral-800",children:t.mileage?"".concat(Math.floor(t.mileage/1e3),"k km"):"N/A"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 text-neutral-600 mb-1",children:[(0,l.jsx)(j.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"text-sm",children:"en"===n?"Fuel":"燃料"})]}),(0,l.jsx)("div",{className:"text-lg font-semibold text-neutral-800",children:t.fuelType||"N/A"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 text-neutral-600 mb-1",children:[(0,l.jsx)(c.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"text-sm",children:"en"===n?"Transmission":"トランスミッション"})]}),(0,l.jsx)("div",{className:"text-lg font-semibold text-neutral-800",children:t.transmission||"N/A"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("div",{className:"bg-white rounded-lg p-6 border shadow-sm",children:(0,l.jsxs)("div",{className:"text-center space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-primary-600",children:t.price}),(0,l.jsx)("div",{className:"text-sm text-neutral-600",children:"en"===n?"FOB Price":"FOB価格"})]}),(0,l.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat("Available"===t.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:[(0,l.jsx)("div",{className:"w-2 h-2 rounded-full mr-2 ".concat("Available"===t.status?"bg-green-500":"bg-yellow-500")}),t.status]}),(0,l.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-neutral-600",children:[(0,l.jsx)(g.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"text-sm",children:t.location})]}),(0,l.jsxs)("div",{className:"space-y-3 pt-4",children:[(0,l.jsx)("button",{onClick:()=>T(!0),className:"w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors",children:"en"===n?"Purchase Now":"今すぐ購入"}),(0,l.jsx)("button",{onClick:()=>E(!0),className:"w-full border border-primary-600 text-primary-600 py-3 px-4 rounded-lg font-semibold hover:bg-primary-50 transition-colors",children:"en"===n?"Request Quote":"見積もり依頼"})]})]})}),(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,l.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-4 flex items-center space-x-2",children:[(0,l.jsx)(N.A,{className:"w-5 h-5 text-primary-600"}),(0,l.jsx)("span",{children:"en"===n?"Trust & Safety":"信頼・安全"})]}),(0,l.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,l.jsx)("span",{children:"en"===n?"Verified Seller":"認証済み販売者"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,l.jsx)("span",{children:"en"===n?"Inspection Available":"検査利用可能"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,l.jsx)("span",{children:"en"===n?"Secure Payment":"安全な支払い"})]})]})]})]})]}),(0,l.jsxs)("div",{className:"mt-12",children:[(0,l.jsx)("div",{className:"border-b border-neutral-200",children:(0,l.jsx)("nav",{className:"flex space-x-8 overflow-x-auto",children:V.map(e=>{let s=e.icon;return(0,l.jsxs)("button",{onClick:()=>y(e.id),className:"flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ".concat(f===e.id?"border-primary-600 text-primary-600":"border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300"),children:[(0,l.jsx)(s,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:e.label})]},e.id)})})}),(0,l.jsxs)("div",{className:"mt-8",children:["overview"===f&&(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 mb-4",children:"en"===n?"Vehicle Overview":"車両概要"}),(0,l.jsx)("div",{className:"prose max-w-none",children:(0,l.jsx)("p",{className:"text-neutral-600",children:"en"===n?"This ".concat(t.title," is in ").concat(null==(s=t.bodyCondition)?void 0:s.toLowerCase()," condition and ready for export. The vehicle has been thoroughly inspected and comes with all necessary documentation for international shipping."):"この".concat(t.title,"は").concat(t.bodyCondition,"の状態で、輸出準備が整っています。車両は徹底的に検査され、国際輸送に必要なすべての書類が揃っています。")})})]}),"specs"===f&&(0,l.jsx)(P,{vehicle:t,locale:n}),"history"===f&&(0,l.jsx)(O,{vehicle:t,locale:n}),"360view"===f&&(0,l.jsx)(Y,{vehicle:t,locale:n}),"compare"===f&&(0,l.jsx)(X,{vehicle:t,locale:n})]})]})]}),C&&(0,l.jsx)(X,{vehicle:t,locale:n,onClose:()=>A(!1),isModal:!0}),I&&(0,l.jsx)(el,{isOpen:I,onClose:()=>T(!1),vehicle:t,locale:n}),R&&(0,l.jsx)(ea.A,{locale:n,isOpen:R,onClose:()=>E(!1),productInterest:"".concat(t.title," - ").concat(t.price)})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,6580,9544,365,7909,9712,8441,1684,7358],()=>s(1897)),_N_E=e.O()}]);