(()=>{var e={};e.id=4131,e.ids=[4131],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7263:(e,t,n)=>{"use strict";n.d(t,{LP:()=>i,U1:()=>c,de:()=>u,g5:()=>r.g5});var a=n(80137),r=n(30358);async function s(e){let t=function(e){let t=[{id:"1",description:e.vehicle.title,quantity:1,unitPrice:e.vehicle.price,total:e.vehicle.price}];return e.shipping.cost>0&&t.push({id:"2",description:`Shipping (${e.shipping.method.name})`,quantity:1,unitPrice:e.shipping.cost,total:e.shipping.cost}),t}(e),{subtotal:n,tax:a,total:s}=function(e){let t=e.reduce((e,t)=>e+t.total,0);return{subtotal:t,tax:0,total:t+0}}(t),i={orderId:e.id,issuedDate:new Date().toISOString().split("T")[0],dueDate:e.payment.dueDate||new Date(Date.now()+2592e6).toISOString().split("T")[0],status:"sent",items:t,subtotal:n,tax:a,shipping:e.shipping.cost,total:s,currency:e.currency,paymentTerms:"Payment due within 30 days of invoice date",notes:`Order: ${e.orderNumber}
Vehicle: ${e.vehicle.title}
Shipping to: ${e.shipping.address.city}, ${e.shipping.address.country}`};return await (0,r.iO)(i)}function i(e,t){let n=new a.Ay,r=n.internal.pageSize.width;n.setFontSize(24),n.setFont("helvetica","bold"),n.text("EBAM MOTORS",20,30),n.setFontSize(12),n.setFont("helvetica","normal"),n.text("Premium Japanese Vehicles Export",20,40),n.text("Japan Office: Tokyo, Japan",20,50),n.text("Ghana Office: Kumasi, Ghana",20,60),n.text("Phone: +************",20,70),n.text("Email: <EMAIL>",20,80),n.setFontSize(20),n.setFont("helvetica","bold"),n.text("INVOICE",r-20-40,30),n.setFontSize(12),n.setFont("helvetica","normal"),n.text(`Invoice #: ${e.invoiceNumber}`,r-20-60,45),n.text(`Date: ${e.issuedDate}`,r-20-60,55),n.text(`Due Date: ${e.dueDate}`,r-20-60,65),n.text(`Order #: ${t.orderNumber}`,r-20-60,75),n.setFontSize(14),n.setFont("helvetica","bold"),n.text("Bill To:",20,110),n.setFontSize(12),n.setFont("helvetica","normal"),n.text(t.customerInfo.name,20,125),n.text(t.customerInfo.email,20,135),n.text(t.customerInfo.phone,20,145),n.text(t.customerInfo.address.street,20,155),n.text(`${t.customerInfo.address.city}, ${t.customerInfo.address.state}`,20,165),n.text(`${t.customerInfo.address.country} ${t.customerInfo.address.postalCode}`,20,175),n.setFontSize(14),n.setFont("helvetica","bold"),n.text("Ship To:",r-20-80,110),n.setFontSize(12),n.setFont("helvetica","normal"),n.text(t.shipping.address.street,r-20-80,125),n.text(`${t.shipping.address.city}, ${t.shipping.address.state}`,r-20-80,135),n.text(`${t.shipping.address.country} ${t.shipping.address.postalCode}`,r-20-80,145),n.setFontSize(12),n.setFont("helvetica","bold"),n.text("Description",20,200),n.text("Qty",r-120,200),n.text("Unit Price",r-80,200),n.text("Total",r-40,200),n.line(20,205,r-20,205),n.setFont("helvetica","normal");let s=215;if(e.items.forEach((e,t)=>{n.text(e.description,20,s),n.text(e.quantity.toString(),r-120,s),n.text(`\xa5${e.unitPrice.toLocaleString()}`,r-80,s),n.text(`\xa5${e.total.toLocaleString()}`,r-40,s),s+=20}),n.line(20,s,r-20,s),s+=10,n.setFont("helvetica","normal"),n.text("Subtotal:",r-80,s),n.text(`\xa5${e.subtotal.toLocaleString()}`,r-40,s),s+=15,e.tax>0&&(n.text("Tax:",r-80,s),n.text(`\xa5${e.tax.toLocaleString()}`,r-40,s),s+=15),n.setFont("helvetica","bold"),n.text("Total:",r-80,s),n.text(`\xa5${e.total.toLocaleString()}`,r-40,s),s+=30,n.setFontSize(10),n.setFont("helvetica","normal"),n.text("Payment Terms:",20,s),s+=10,n.text(e.paymentTerms,20,s),e.notes){s+=20,n.text("Notes:",20,s),s+=10;let t=n.splitTextToSize(e.notes,r-40);n.text(t,20,s)}let i=n.internal.pageSize.height-30;return n.setFontSize(8),n.text("Thank you for your business!",20,i),n.text("For questions about this invoice, please contact <NAME_EMAIL>",20,i+10),n}async function o(e,t){i(e,t).output("blob");let n=`/invoices/${e.invoiceNumber}.pdf`;return await (0,r.Dq)(e.id,{pdfUrl:n}),n}async function c(e){let t=await s(e);return await o(t,e),t}async function u(e){return await (0,r.Dq)(e,{status:"paid"})}},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},24407:(e,t,n)=>{"use strict";n.d(t,{N1:()=>u,U:()=>r,UQ:()=>d,Uv:()=>o,ZK:()=>s,_4:()=>c,gY:()=>i});let a=[{id:"bank_transfer_ghana",type:"bank_transfer",name:"Bank Transfer (Ghana)",description:"Direct bank transfer to our Ghana account",icon:"\uD83C\uDFE6",enabled:!0,config:{bankName:"Ghana Commercial Bank",accountNumber:"*************",accountName:"EBAM Motors Ghana Ltd",swiftCode:"GCBLGHAC"}},{id:"bank_transfer_japan",type:"bank_transfer",name:"Bank Transfer (Japan)",description:"Direct bank transfer to our Japan account",icon:"\uD83C\uDFE6",enabled:!0,config:{bankName:"Mizuho Bank",accountNumber:"*************",accountName:"EBAM Motors Japan KK",swiftCode:"MHCBJPJT"}},{id:"mtn_mobile_money",type:"mobile_money",name:"MTN Mobile Money",description:"Pay using MTN Mobile Money",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"mtn",number:"+************"}},{id:"vodafone_cash",type:"mobile_money",name:"Vodafone Cash",description:"Pay using Vodafone Cash",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"vodafone",number:"+************"}},{id:"airtel_money",type:"mobile_money",name:"AirtelTigo Money",description:"Pay using AirtelTigo Money",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"airtel",number:"+************"}},{id:"stripe_card",type:"stripe",name:"Credit/Debit Card",description:"Pay securely with your credit or debit card",icon:"\uD83D\uDCB3",enabled:!0,config:{publishableKey:process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}},{id:"cash_agent_kumasi",type:"cash_agent",name:"Cash Payment (Kumasi)",description:"Pay cash through our agent in Kumasi",icon:"\uD83D\uDCB5",enabled:!0,config:{agentLocations:["Kumasi Central Market","Adum Shopping Center","KNUST Campus"]}},{id:"cash_agent_accra",type:"cash_agent",name:"Cash Payment (Accra)",description:"Pay cash through our agent in Accra",icon:"\uD83D\uDCB5",enabled:!0,config:{agentLocations:["Makola Market","Osu Oxford Street","East Legon"]}}],r=[{id:"sea_freight_standard",name:"Sea Freight (Standard)",description:"Most economical option, 4-6 weeks delivery",estimatedDays:35,basePrice:15e4,pricePerKg:50,maxWeight:2e3,trackingIncluded:!0,insuranceIncluded:!0},{id:"sea_freight_express",name:"Sea Freight (Express)",description:"Faster sea shipping, 3-4 weeks delivery",estimatedDays:25,basePrice:2e5,pricePerKg:75,maxWeight:2e3,trackingIncluded:!0,insuranceIncluded:!0},{id:"air_freight",name:"Air Freight",description:"Fastest option, 1-2 weeks delivery",estimatedDays:10,basePrice:5e5,pricePerKg:200,maxWeight:500,trackingIncluded:!0,insuranceIncluded:!0}],s={compact:1200,sedan:1400,suv:1800,van:1600,truck:2500,motorcycle:200,default:1500};function i(e){return a.find(t=>t.id===e)||null}function o(e){return r.find(t=>t.id===e)||null}function c(e,t,n="ghana"){let a=o(e);if(!a)return 0;let r=s[t.toLowerCase()]||s.default,i=a.pricePerKg?r*a.pricePerKg:0,u=1;return"ghana"!==n.toLowerCase()&&(u=1.2),Math.round((a.basePrice+i)*u)}function u(e){let t=o(e);if(!t)return"";let n=new Date;return n.setDate(n.getDate()+t.estimatedDays),n.toISOString().split("T")[0]}function d(e,t,n){let a=i(e);if(!a)return"";switch(a.type){case"bank_transfer":return`Transfer \xa5${t.toLocaleString()} to:
Bank: ${a.config?.bankName}
Account: ${a.config?.accountNumber}
Name: ${a.config?.accountName}
Reference: ${n}`;case"mobile_money":return`Send \xa5${t.toLocaleString()} to ${a.config?.number}
Reference: ${n}
Network: ${a.name}`;case"cash_agent":return`Visit one of our agent locations with \xa5${t.toLocaleString()}:
${a.config?.agentLocations?.join(", ")}
Reference: ${n}`;case"stripe":return`You will be redirected to secure payment page to complete your payment of \xa5${t.toLocaleString()}.`;default:return`Payment amount: \xa5${t.toLocaleString()}
Order reference: ${n}`}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30358:(e,t,n)=>{"use strict";n.d(t,{Dq:()=>D,HQ:()=>N,Q4:()=>b,Vd:()=>w,ee:()=>I,fS:()=>x,g5:()=>_,getOrderById:()=>S,iO:()=>$,iY:()=>v});var a=n(29021),r=n(33873),s=n.n(r),i=n(23870);let o=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,c=s().join(process.cwd(),"data"),u=s().join(c,"orders.json"),d=s().join(c,"invoices.json"),p=[],l=[];async function m(){if(!o)try{await a.promises.access(c)}catch{await a.promises.mkdir(c,{recursive:!0})}}async function g(){if(o)return p;try{await m();let e=await a.promises.readFile(u,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function y(e){if(o){p=e;return}await m(),await a.promises.writeFile(u,JSON.stringify(e,null,2))}async function f(){if(o)return l;try{await m();let e=await a.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function h(e){if(o){l=e;return}await m(),await a.promises.writeFile(d,JSON.stringify(e,null,2))}async function x(e){let t=await g(),n={...e,id:(0,i.A)(),orderNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`EB${e.slice(-6)}${t}`}(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(n),await y(t),n}async function w(){return await g()}async function S(e){return(await g()).find(t=>t.id===e)||null}async function b(e){return(await g()).filter(t=>t.customerId===e)}async function v(e,t){let n=await g(),a=n.findIndex(t=>t.id===e);return -1!==a&&(n[a]={...n[a],...t,updatedAt:new Date().toISOString()},await y(n),!0)}async function I(e,t){let n=await S(e);if(!n)return!1;let a={...n.payment,...t};return await v(e,{payment:a})}async function N(e,t){let n=await S(e);if(!n)return!1;let a={...t,id:(0,i.A)()},r={...n.shipping,updates:[...n.shipping.updates,a]};return await v(e,{shipping:r})}async function $(e){let t=await f(),n={...e,id:(0,i.A)(),invoiceNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,3).toUpperCase();return`INV-${e.slice(-6)}-${t}`}()};return t.push(n),await h(t),n}async function _(e){return(await f()).find(t=>t.orderId===e)||null}async function D(e,t){let n=await f(),a=n.findIndex(t=>t.id===e);return -1!==a&&(n[a]={...n[a],...t},await h(n),!0)}},33873:e=>{"use strict";e.exports=require("path")},43227:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>I,routeModule:()=>w,serverHooks:()=>v,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>b});var a={};n.r(a),n.d(a,{GET:()=>x,PATCH:()=>h,POST:()=>g});var r=n(96559),s=n(48088),i=n(37719),o=n(32190),c=n(30358),u=n(24407),d=n(7263),p=n(16967),l=n(97877);let m=process.env.STRIPE_SECRET_KEY?new l.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia"}):null;async function g(e){try{let{orderId:t,paymentMethodId:n,amount:a,currency:r="JPY"}=await e.json();if(!t||!n||!a)return o.NextResponse.json({success:!1,message:"Order ID, payment method, and amount are required"},{status:400});if(!await (0,c.getOrderById)(t))return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});let s=(0,u.gY)(n);if(!s)return o.NextResponse.json({success:!1,message:"Invalid payment method"},{status:400});switch(s.type){case"stripe":return await y(t,a,r);case"bank_transfer":case"mobile_money":case"cash_agent":return await f(t,n,a,r);default:return o.NextResponse.json({success:!1,message:"Unsupported payment method"},{status:400})}}catch(e){return console.error("Error processing payment:",e),o.NextResponse.json({success:!1,message:"Failed to process payment"},{status:500})}}async function y(e,t,n){if(!m)return o.NextResponse.json({success:!1,message:"Stripe not configured"},{status:500});try{let a=await m.paymentIntents.create({amount:Math.round(100*t),currency:n.toLowerCase(),metadata:{orderId:e}});return await (0,c.ee)(e,{status:"processing",transactionId:a.id}),o.NextResponse.json({success:!0,data:{clientSecret:a.client_secret,paymentIntentId:a.id}})}catch(e){return console.error("Stripe payment error:",e),o.NextResponse.json({success:!1,message:"Failed to create payment intent"},{status:500})}}async function f(e,t,n,a){try{let r=(0,u.UQ)(t,n,e);return await (0,c.ee)(e,{status:"pending"}),o.NextResponse.json({success:!0,data:{instructions:r,paymentMethod:t,amount:n,currency:a,orderId:e}})}catch(e){return console.error("Manual payment error:",e),o.NextResponse.json({success:!1,message:"Failed to process manual payment"},{status:500})}}async function h(e){try{let{orderId:t,status:n,transactionId:a,adminKey:r}=await e.json(),s=process.env.ADMIN_PASSWORD||"admin123";if(r!==s)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!t||!n)return o.NextResponse.json({success:!1,message:"Order ID and status are required"},{status:400});let i={status:n};if(a&&(i.transactionId=a),"completed"===n&&(i.paidAt=new Date().toISOString()),!await (0,c.ee)(t,i))return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});if("completed"===n){let e=await (0,d.g5)(t);e&&await (0,d.de)(e.id);let n=await (0,c.getOrderById)(t);if(n){await (0,c.ee)(t,{status:"completed"}),await (0,c.HQ)(t,{timestamp:new Date().toISOString(),status:"Payment Confirmed",location:"EBAM Motors Office",description:"Payment received and confirmed. Vehicle preparation will begin shortly."});try{let e={customerName:n.customer.name,orderNumber:n.id,orderDate:new Date(n.createdAt).toLocaleDateString(),vehicle:{title:n.vehicle.title,price:`\xa5${n.vehicle.price.toLocaleString()}`,image:n.vehicle.image},total:`\xa5${n.payment.amount.toLocaleString()}`,shippingAddress:n.shipping.address,estimatedDelivery:n.shipping.estimatedDelivery||"To be determined"};await p.gm.sendOrderConfirmation(n.customer.email,e);let t={type:"new_order",title:"New Order Received",message:`Order ${n.id} has been paid and confirmed. Customer: ${n.customer.name} (${n.customer.email}). Vehicle: ${n.vehicle.title}. Amount: \xa5${n.payment.amount.toLocaleString()}`,data:{orderId:n.id,customerName:n.customer.name,customerEmail:n.customer.email,vehicleTitle:n.vehicle.title,amount:n.payment.amount,paymentMethod:n.payment.method},timestamp:new Date().toISOString()};await p.gm.sendAdminNotification(t)}catch(e){console.error("Failed to send notification emails:",e)}}}return o.NextResponse.json({success:!0,message:`Payment status updated to ${n}`})}catch(e){return console.error("Error updating payment status:",e),o.NextResponse.json({success:!1,message:"Failed to update payment status"},{status:500})}}async function x(e){try{let{searchParams:t}=new URL(e.url),n=t.get("orderId");if(!n)return o.NextResponse.json({success:!1,message:"Order ID is required"},{status:400});let a=await (0,c.getOrderById)(n);if(!a)return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});return o.NextResponse.json({success:!0,data:{orderId:a.id,status:a.payment.status,amount:a.payment.amount,currency:a.payment.currency,method:a.payment.method.name,transactionId:a.payment.transactionId,paidAt:a.payment.paidAt,dueDate:a.payment.dueDate}})}catch(e){return console.error("Error getting payment status:",e),o.NextResponse.json({success:!1,message:"Failed to get payment status"},{status:500})}}let w=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/payments/route",pathname:"/api/payments",filename:"route",bundlePath:"app/api/payments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\payments\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:S,workUnitAsyncStorage:b,serverHooks:v}=w;function I(){return(0,i.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:b})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[4447,580,4406,7877,6967],()=>n(43227));module.exports=a})();