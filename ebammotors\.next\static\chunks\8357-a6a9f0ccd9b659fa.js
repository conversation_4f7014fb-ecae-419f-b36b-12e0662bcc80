(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8357],{1788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return l}});let n={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},r=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function a(e){return["async","defer","noModule"].includes(e)}function l(e,t){for(let[l,o]of Object.entries(t)){if(!t.hasOwnProperty(l)||r.includes(l)||void 0===o)continue;let i=n[l]||l.toLowerCase();"SCRIPT"===e.tagName&&a(i)?e[i]=!!o:e.setAttribute(i,String(o)),(!1===o||"SCRIPT"===e.tagName&&a(i)&&(!o||"false"===o))&&(e.setAttribute(i,""),e.removeAttribute(i))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3554:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a.a});var r=n(9243),a=n.n(r),l={};for(let e in r)"default"!==e&&(l[e]=()=>r[e]);n.d(t,l)},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5302:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},6063:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sendGTMEvent=void 0,t.GoogleTagManager=function(e){let{gtmId:t,gtmScriptUrl:n="https://www.googletagmanager.com/gtm.js",dataLayerName:i="dataLayer",auth:s,preview:c,dataLayer:u,nonce:d}=e;o=i;let f="dataLayer"!==i?"&l=".concat(i):"";return(0,a.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-gtm"}})},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.default,{id:"_next-gtm-init",dangerouslySetInnerHTML:{__html:"\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ".concat(u?"w[l].push(".concat(JSON.stringify(u),")"):"","\n      })(window,'").concat(i,"');")},nonce:d}),(0,r.jsx)(l.default,{id:"_next-gtm","data-ntpc":"GTM",src:"".concat(n,"?id=").concat(t).concat(f).concat(s?"&gtm_auth=".concat(s):"").concat(c?"&gtm_preview=".concat(c,"&gtm_cookies_win=x"):""),nonce:d})]})};let r=n(5155),a=n(2115),l=function(e){return e&&e.__esModule?e:{default:e}}(n(3554)),o="dataLayer";t.sendGTMEvent=(e,t)=>{let n=t||o;window[n]=window[n]||[],window[n].push(e)}},6259:(e,t,n)=>{"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleAnalytics=function(e){let{gaId:t,debugMode:n,dataLayerName:i="dataLayer",nonce:s}=e;return void 0===r&&(r=i),(0,l.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-ga"}})},[]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.default,{id:"_next-ga-init",dangerouslySetInnerHTML:{__html:"\n          window['".concat(i,"'] = window['").concat(i,"'] || [];\n          function gtag(){window['").concat(i,"'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '").concat(t,"' ").concat(n?",{ 'debug_mode': true }":"",");")},nonce:s}),(0,a.jsx)(o.default,{id:"_next-ga",src:"https://www.googletagmanager.com/gtag/js?id=".concat(t),nonce:s})]})},t.sendGAEvent=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(void 0===r)return void console.warn("@next/third-parties: GA has not been initialized");window[r]?window[r].push(arguments):console.warn("@next/third-parties: GA dataLayer ".concat(r," does not exist"))};let a=n(5155),l=n(2115),o=function(e){return e&&e.__esModule?e:{default:e}}(n(3554))},6767:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},7495:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_d5a796",variable:"__variable_d5a796"}},8930:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{html:t,height:n=null,width:l=null,children:o,dataNtpc:i=""}=e;return(0,a.useEffect)(()=>{i&&performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-".concat(i)}})},[i]),(0,r.jsxs)(r.Fragment,{children:[o,t?(0,r.jsx)("div",{style:{height:null!=n?"".concat(n,"px"):"auto",width:null!=l?"".concat(l,"px"):"auto"},"data-ntpc":i,dangerouslySetInnerHTML:{__html:t}}):null]})};let r=n(5155),a=n(2115)},9243:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return m},handleClientScriptLoad:function(){return _},initScriptLoader:function(){return y}});let r=n(8229),a=n(6966),l=n(5155),o=r._(n(7650)),i=a._(n(2115)),s=n(2830),c=n(2714),u=n(2374),d=new Map,f=new Set,p=e=>{if(o.default.preinit)return void e.forEach(e=>{o.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},g=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:a=null,dangerouslySetInnerHTML:l,children:o="",strategy:i="afterInteractive",onError:s,stylesheets:u}=e,g=n||t;if(g&&f.has(g))return;if(d.has(t)){f.add(g),d.get(t).then(r,s);return}let _=()=>{a&&a(),f.add(g)},y=document.createElement("script"),h=new Promise((e,t)=>{y.addEventListener("load",function(t){e(),r&&r.call(this,t),_()}),y.addEventListener("error",function(e){t(e)})}).catch(function(e){s&&s(e)});l?(y.innerHTML=l.__html||"",_()):o?(y.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):"",_()):t&&(y.src=t,d.set(t,h)),(0,c.setAttributesFromProps)(y,e),"worker"===i&&y.setAttribute("type","text/partytown"),y.setAttribute("data-nscript",i),u&&p(u),document.body.appendChild(y)};function _(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>g(e))}):g(e)}function y(e){e.forEach(_),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function h(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:a=null,strategy:c="afterInteractive",onError:d,stylesheets:p,..._}=e,{updateScripts:y,scripts:h,getIsSsr:m,appDir:w,nonce:b}=(0,i.useContext)(s.HeadManagerContext),v=(0,i.useRef)(!1);(0,i.useEffect)(()=>{let e=t||n;v.current||(a&&e&&f.has(e)&&a(),v.current=!0)},[a,t,n]);let x=(0,i.useRef)(!1);if((0,i.useEffect)(()=>{if(!x.current){if("afterInteractive"===c)g(e);else"lazyOnload"===c&&("complete"===document.readyState?(0,u.requestIdleCallback)(()=>g(e)):window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>g(e))}));x.current=!0}},[e,c]),("beforeInteractive"===c||"worker"===c)&&(y?(h[c]=(h[c]||[]).concat([{id:t,src:n,onLoad:r,onReady:a,onError:d,..._}]),y(h)):m&&m()?f.add(t||n):m&&!m()&&g(e)),w){if(p&&p.forEach(e=>{o.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)if(!n)return _.dangerouslySetInnerHTML&&(_.children=_.dangerouslySetInnerHTML.__html,delete _.dangerouslySetInnerHTML),(0,l.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{..._,id:t}])+")"}});else return o.default.preload(n,_.integrity?{as:"script",integrity:_.integrity,nonce:b,crossOrigin:_.crossOrigin}:{as:"script",nonce:b,crossOrigin:_.crossOrigin}),(0,l.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{..._,id:t}])+")"}});"afterInteractive"===c&&n&&o.default.preload(n,_.integrity?{as:"script",integrity:_.integrity,nonce:b,crossOrigin:_.crossOrigin}:{as:"script",nonce:b,crossOrigin:_.crossOrigin})}return null}Object.defineProperty(h,"__nextScript",{value:!0});let m=h;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9946:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),o=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:u="",children:d,iconNode:f,...p}=e;return(0,r.createElement)("svg",{ref:t,...c,width:a,height:a,stroke:n,strokeWidth:o?24*Number(l)/Number(a):l,className:i("lucide",u),...!d&&!s(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:s,...c}=n;return(0,r.createElement)(u,{ref:l,iconNode:t,className:i("lucide-".concat(a(o(e))),"lucide-".concat(e),s),...c})});return n.displayName=o(e),n}}}]);