(()=>{var e={};e.id=3417,e.ids=[3417],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6189:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["admin",{children:["crm",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,14383)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\crm\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\crm\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/crm/page",pathname:"/admin/crm",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14383:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\admin\\\\crm\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\crm\\page.tsx","default")},15256:(e,s,t)=>{"use strict";function a(e){try{let s="string"==typeof e?new Date(e):e,t=s.getFullYear(),a=String(s.getMonth()+1).padStart(2,"0"),r=String(s.getDate()).padStart(2,"0"),l=String(s.getHours()).padStart(2,"0"),i=String(s.getMinutes()).padStart(2,"0");return`${r}/${a}/${t} ${l}:${i}`}catch(e){return"Invalid Date"}}function r(e,s="\xa5"){try{return`${s}${function(e){try{return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}catch(e){return"0"}}(e)}`}catch(e){return`${s}0`}}t.d(s,{r6:()=>a,vv:()=>r})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23026:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},23253:(e,s,t)=>{Promise.resolve().then(t.bind(t,14383))},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37652:(e,s,t)=>{Promise.resolve().then(t.bind(t,99111))},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43649:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44263:()=>{},57410:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(60687),r=t(43210),l=t(25541),i=t(41312),n=t(23026),d=t(58887),c=t(40228),o=t(23928),x=t(15256),m=t(78122),h=t(35071),p=t(5336),u=t(93613);function g(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)(!1),[d,c]=(0,r.useState)(""),o=async()=>{l(!0);try{let e=await fetch("/api/chatbot"),t=await e.json();s(t)}catch(e){s({status:"error",aiAvailable:!1,error:"Failed to check AI status"})}finally{l(!1)}},x=async()=>{n(!0),c("");try{let e=await fetch("/api/chatbot",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:"Hello, can you tell me about EBAM Motors?",context:"Testing AI functionality",locale:"en"})}),s=await e.json();s.success?c(`✅ AI Response: ${s.response.substring(0,200)}${s.response.length>200?"...":""}`):c(`❌ AI Test Failed: ${s.error}`)}catch(e){c(`❌ Test Error: ${e}`)}finally{n(!1)}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"AI Chatbot Status"}),(0,a.jsxs)("button",{onClick:o,disabled:t,className:"flex items-center space-x-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:[(0,a.jsx)(m.A,{className:`w-4 h-4 ${t?"animate-spin":""}`}),(0,a.jsx)("span",{children:"Refresh"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[t?(0,a.jsx)(m.A,{className:"w-5 h-5 animate-spin text-blue-500"}):e&&e.aiAvailable?(0,a.jsx)(p.A,{className:"w-5 h-5 text-green-500"}):(0,a.jsx)(h.A,{className:"w-5 h-5 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsx)("p",{className:`font-medium ${t?"text-blue-600":!e||!e.aiAvailable?"text-red-600":"text-green-600"}`,children:t?"Checking...":e?e.aiAvailable?"AI Active":"AI Unavailable":"Unknown"})]})]}),e?.provider&&(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-blue-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Provider"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.provider.provider}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.provider.model})]})]})]}),e?.error&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-red-600",children:[(0,a.jsx)("strong",{children:"Error:"})," ",e.error]})}),(0,a.jsxs)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-blue-800 mb-2",children:(0,a.jsx)("strong",{children:"Configuration:"})}),(0,a.jsxs)("ul",{className:"text-xs text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Check your .env.local file for AI_PROVIDER setting"}),(0,a.jsx)("li",{children:"• Ensure API keys are configured correctly"}),(0,a.jsx)("li",{children:"• See AI_CHATBOT_SETUP.md for detailed instructions"})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Test AI Response"}),(0,a.jsx)("button",{onClick:x,disabled:i||!e?.aiAvailable,className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed",children:i?"Testing...":"Test AI"})]}),d&&(0,a.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:d})})]}),(0,a.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Quick Setup"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-2",children:[(0,a.jsx)("a",{href:"https://huggingface.co/settings/tokens",target:"_blank",rel:"noopener noreferrer",className:"text-xs px-3 py-2 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 text-center",children:"Get Hugging Face Key"}),(0,a.jsx)("a",{href:"https://console.groq.com/",target:"_blank",rel:"noopener noreferrer",className:"text-xs px-3 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200 text-center",children:"Get Groq Key"}),(0,a.jsx)("a",{href:"https://ollama.ai/",target:"_blank",rel:"noopener noreferrer",className:"text-xs px-3 py-2 bg-green-100 text-green-800 rounded hover:bg-green-200 text-center",children:"Install Ollama"})]})]})]})}var b=t(43649),y=t(13861),j=t(84027);function v(){let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)({totalResponses:0,lowRisk:0,mediumRisk:0,highRisk:0,hallucinationRate:0}),[i,n]=(0,r.useState)(!1),d=e=>{switch(e){case"low":return"text-green-600 bg-green-100";case"medium":return"text-yellow-600 bg-yellow-100";case"high":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},c=e=>{switch(e){case"low":return(0,a.jsx)(p.A,{className:"w-4 h-4"});case"medium":return(0,a.jsx)(b.A,{className:"w-4 h-4"});case"high":return(0,a.jsx)(h.A,{className:"w-4 h-4"});default:return(0,a.jsx)(y.A,{className:"w-4 h-4"})}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"AI Hallucination Monitor"}),(0,a.jsxs)("button",{onClick:()=>n(!i),className:"flex items-center space-x-2 px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Settings"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-blue-600",children:"Total Responses"}),(0,a.jsx)("p",{className:"text-xl font-bold text-blue-900",children:t.totalResponses})]}),(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-green-600",children:"Low Risk"}),(0,a.jsx)("p",{className:"text-xl font-bold text-green-900",children:t.lowRisk})]}),(0,a.jsxs)("div",{className:"p-3 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-yellow-600",children:"Medium Risk"}),(0,a.jsx)("p",{className:"text-xl font-bold text-yellow-900",children:t.mediumRisk})]}),(0,a.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-red-600",children:"High Risk"}),(0,a.jsx)("p",{className:"text-xl font-bold text-red-900",children:t.highRisk})]})]}),(0,a.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Hallucination Rate"}),(0,a.jsxs)("span",{className:`text-sm font-bold ${t.hallucinationRate>15?"text-red-600":t.hallucinationRate>5?"text-yellow-600":"text-green-600"}`,children:[t.hallucinationRate,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:`h-2 rounded-full ${t.hallucinationRate>15?"bg-red-500":t.hallucinationRate>5?"bg-yellow-500":"bg-green-500"}`,style:{width:`${Math.min(t.hallucinationRate,100)}%`}})}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Target: <5% | Warning: >10% | Critical: >15%"})]}),i&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-3",children:"Hallucination Control Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Temperature:"})," 0.3 (Lower = less creative)"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Top P:"})," 0.8 (Nucleus sampling)"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Max Tokens:"})," 200"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Frequency Penalty:"})," 0.5"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Presence Penalty:"})," 0.3"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Validation:"})," Enabled"]})]})]}),(0,a.jsxs)("div",{className:"mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800",children:["\uD83D\uDCA1 ",(0,a.jsx)("strong",{children:"Tip:"})," Lower temperature reduces creativity but increases accuracy. Adjust in ",(0,a.jsx)("code",{children:"src/config/chatbotConfig.ts"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Recent AI Responses"}),(0,a.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:e.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:`flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${d(e.riskLevel)}`,children:[c(e.riskLevel),(0,a.jsxs)("span",{children:[e.riskLevel.toUpperCase()," RISK"]})]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleString()})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-700",children:"User:"}),(0,a.jsx)("p",{className:"text-gray-600 bg-gray-50 p-2 rounded",children:e.userMessage})]}),"low"!==e.riskLevel&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-700",children:"Original AI Response:"}),(0,a.jsx)("p",{className:"text-red-600 bg-red-50 p-2 rounded",children:e.aiResponse})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-green-700",children:"low"===e.riskLevel?"AI Response:":"Cleaned Response:"}),(0,a.jsx)("p",{className:"text-green-600 bg-green-50 p-2 rounded",children:e.cleanedResponse})]}),e.issues.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-orange-700",children:"Issues Detected:"}),(0,a.jsx)("ul",{className:"text-orange-600 text-xs list-disc list-inside",children:e.issues.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("button",{className:"px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200",children:"Export Log"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200",children:"Clear Low Risk"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-yellow-100 text-yellow-700 rounded text-sm hover:bg-yellow-200",children:"Review Medium Risk"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200",children:"Flag High Risk"})]})]})]})}function f(){let[e,s]=(0,r.useState)(!1),[t,m]=(0,r.useState)(""),[h,p]=(0,r.useState)("overview"),[u,b]=(0,r.useState)(!1),[y,j]=(0,r.useState)([]),[f,N]=(0,r.useState)([]),[w,A]=(0,r.useState)([]),[k,R]=(0,r.useState)([]),C=async e=>{e.preventDefault(),b(!0);try{let e=await fetch("/api/admin/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:t})}),a=await e.json();a.success?(s(!0),await M(a.adminKey)):alert("Invalid admin password")}catch(e){console.error("Authentication error:",e),alert("Authentication failed")}finally{b(!1)}},M=async e=>{b(!0);try{let[s,t,a,r]=await Promise.all([fetch(`/api/customers?adminKey=${encodeURIComponent(e)}`),fetch(`/api/leads?adminKey=${encodeURIComponent(e)}`),fetch(`/api/interactions?adminKey=${encodeURIComponent(e)}`),fetch(`/api/followups?adminKey=${encodeURIComponent(e)}`)]),[l,i,n,d]=await Promise.all([s.json(),t.json(),a.json(),r.json()]);l.success&&j(l.customers||[]),i.success&&N(i.leads||[]),n.success&&A(n.interactions||[]),d.success&&R(d.followups||[])}catch(e){console.error("Error loading dashboard data:",e)}finally{b(!1)}};if(!e)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-md w-full max-w-md",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-center mb-6",children:"CRM Admin Access"}),(0,a.jsxs)("form",{onSubmit:C,children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Admin Password"}),(0,a.jsx)("input",{type:"password",value:t,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsx)("button",{type:"submit",disabled:u,className:"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50",children:u?"Authenticating...":"Access CRM"})]})]})});let S=(()=>{let e=f.filter(e=>"new"===e.status).length,s=y.filter(e=>"active"===e.status).length;return{newLeads:e,activeCustomers:s,pendingFollowUps:k.filter(e=>"pending"===e.status).length,totalRevenue:y.reduce((e,s)=>e+s.totalSpent,0)}})();return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"EBAM Motors CRM"}),(0,a.jsx)("button",{onClick:()=>s(!1),className:"text-gray-500 hover:text-gray-700",children:"Logout"})]})})}),(0,a.jsx)("div",{className:"bg-white border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:l.A},{id:"customers",label:"Customers",icon:i.A},{id:"leads",label:"Leads",icon:n.A},{id:"interactions",label:"Interactions",icon:d.A},{id:"followups",label:"Follow-ups",icon:c.A}].map(({id:e,label:s,icon:t})=>(0,a.jsxs)("button",{onClick:()=>p(e),className:`flex items-center px-3 py-4 text-sm font-medium border-b-2 ${h===e?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:[(0,a.jsx)(t,{className:"w-4 h-4 mr-2"}),s]},e))})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["overview"===h&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{className:"w-8 h-8 text-blue-500"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"New Leads"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:S.newLeads})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"w-8 h-8 text-green-500"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Customers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:S.activeCustomers})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"w-8 h-8 text-orange-500"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Follow-ups"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:S.pendingFollowUps})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"w-8 h-8 text-purple-500"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,x.vv)(S.totalRevenue)})]})]})})]}),(0,a.jsx)(g,{}),(0,a.jsx)(v,{}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Activity"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"space-y-4",children:w.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-gray-400 mt-0.5"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900",children:e.content}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:(0,x.r6)(e.createdAt)})]})]},e.id))})})]})]}),"overview"!==h&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("p",{className:"text-gray-500",children:[h.charAt(0).toUpperCase()+h.slice(1)," management interface - Full implementation available in locale-based route: /en/admin/crm"]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("a",{href:"/en/admin/crm",className:"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 inline-block",children:"Go to Full CRM Dashboard"})})]})]})]})}},58887:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},61193:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(60687),r=t(85814),l=t.n(r),i=t(43210);function n({children:e}){let[s,t]=(0,i.useState)(!1);return s?(0,a.jsxs)("div",{className:"min-h-screen bg-neutral-50 admin-layout",children:[(0,a.jsx)("header",{className:"bg-white/95 backdrop-blur-sm shadow-sm border-b border-neutral-200 sticky top-0 z-50 transition-all duration-300",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"A"})}),(0,a.jsx)("h1",{className:"text-xl font-bold text-neutral-800",children:"EBAM Motors Admin"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-sm text-neutral-600",children:"Admin Panel"}),(0,a.jsx)(l(),{href:"/",className:"text-sm text-primary-600 hover:text-primary-700 font-medium",children:"← Back to Website"})]})]})})}),(0,a.jsx)("main",{className:"py-8",children:e}),(0,a.jsx)("footer",{className:"bg-white border-t border-neutral-200 mt-auto",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsx)("div",{className:"text-center text-sm text-neutral-500",children:"EBAM Motors Admin Panel - Handle with care"})})})]}):(0,a.jsx)("div",{className:"min-h-screen bg-neutral-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading admin panel..."})]})})}t(44263)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65109:(e,s,t)=>{Promise.resolve().then(t.bind(t,57410))},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},84027:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},90388:(e,s,t)=>{Promise.resolve().then(t.bind(t,61193))},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},99111:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\admin\\layout.tsx","default")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,7445,1658,5814,5839],()=>t(6189));module.exports=a})();