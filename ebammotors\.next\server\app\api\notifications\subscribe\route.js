(()=>{var e={};e.id=4027,e.ids=[4027],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61139:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>v,routeModule:()=>b,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var r={};t.r(r),t.d(r,{GET:()=>m,POST:()=>w});var i=t(96559),n=t(48088),a=t(37719),o=t(32190),u=t(29021),c=t(33873),p=t.n(c);let d=p().join(process.cwd(),"data","push-subscriptions.json");async function l(){let e=p().dirname(d);try{await u.promises.access(e)}catch{await u.promises.mkdir(e,{recursive:!0})}}async function x(){try{await l();let e=await u.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function g(e){await l(),await u.promises.writeFile(d,JSON.stringify(e,null,2))}async function w(e){try{let s=await e.json();if(!s.endpoint||!s.keys?.p256dh||!s.keys?.auth)return o.NextResponse.json({success:!1,message:"Invalid subscription data"},{status:400});let t=await x(),r=t.findIndex(e=>e.endpoint===s.endpoint);return r>=0?t[r]={...t[r],...s,lastUsed:new Date().toISOString()}:t.push({...s,id:Date.now().toString(),createdAt:new Date().toISOString(),lastUsed:new Date().toISOString()}),await g(t),o.NextResponse.json({success:!0,message:"Subscription saved successfully"})}catch(e){return console.error("Error saving push subscription:",e),o.NextResponse.json({success:!1,message:"Failed to save subscription"},{status:500})}}async function m(e){try{let{searchParams:s}=new URL(e.url),t=s.get("endpoint");if(!t)return o.NextResponse.json({success:!1,message:"Endpoint parameter required"},{status:400});let r=(await x()).find(e=>e.endpoint===t);return o.NextResponse.json({success:!0,subscription:r||null,isSubscribed:!!r})}catch(e){return console.error("Error getting subscription status:",e),o.NextResponse.json({success:!1,message:"Failed to get subscription status"},{status:500})}}let b=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/notifications/subscribe/route",pathname:"/api/notifications/subscribe",filename:"route",bundlePath:"app/api/notifications/subscribe/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\notifications\\subscribe\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:h}=b;function v(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,580],()=>t(61139));module.exports=r})();