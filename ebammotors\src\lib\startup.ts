/**
 * Application startup initialization
 * This file contains functions that should run when the application starts
 */

import { initializeAutomatedFollowups } from './automatedFollowup';

/**
 * Initialize all startup services
 */
export function initializeServices(): void {
  console.log('🚀 Initializing EBAM Motors services...');

  try {
    // Initialize automated follow-up system
    initializeAutomatedFollowups();

    console.log('✅ All services initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing services:', error);
  }
}

/**
 * Initialize services only in production or when explicitly enabled
 */
export function initializeServicesIfEnabled(): void {
  const isProduction = process.env.NODE_ENV === 'production';
  const forceEnable = process.env.ENABLE_AUTOMATION === 'true';
  
  if (isProduction || forceEnable) {
    initializeServices();
  } else {
    console.log('🔧 Automated services disabled in development mode');
    console.log('   Set ENABLE_AUTOMATION=true in .env.local to enable');
  }
}
