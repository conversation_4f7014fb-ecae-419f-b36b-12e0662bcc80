import { promises as fs } from 'fs';
import path from 'path';

// Check if we're in a serverless environment (like Vercel)
const isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;

/**
 * Get storage type for logging/debugging
 */
export function getStorageType(): string {
  return isServerless ? 'memory' : 'file';
}

export interface Review {
  id: string;
  name: string;
  location: string;
  email: string;
  rating: number;
  title?: string;
  review: string;
  vehiclePurchased?: string;
  purchaseDate?: string;
  locale: string;
  submittedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  images?: string[];
  // Translation fields
  titleEn?: string;
  titleJa?: string;
  reviewEn?: string;
  reviewJa?: string;
}

// Path to store reviews data (for local development)
const REVIEWS_FILE_PATH = path.join(process.cwd(), 'data', 'reviews.json');
const DATA_DIR = path.join(process.cwd(), 'data');

// In-memory storage for serverless environments
let memoryReviews: Review[] = [];

// Default sample reviews for initial setup
const DEFAULT_REVIEWS: Review[] = [
  {
    id: 'sample-1',
    name: 'Kwame Mensah',
    location: 'Accra, Ghana',
    email: '<EMAIL>',
    rating: 5,
    title: 'Excellent Service and Quality Vehicle',
    review: 'I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!',
    vehiclePurchased: 'Toyota Voxy 2015',
    purchaseDate: '2024-01',
    locale: 'en',
    submittedAt: '2024-01-15T10:30:00.000Z',
    status: 'approved',
    images: [],
    titleEn: 'Excellent Service and Quality Vehicle',
    titleJa: '優れたサービスと高品質な車両',
    reviewEn: 'I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!',
    reviewJa: 'EBAM Motorsを通じてトヨタ ヴォクシーを購入しましたが、全プロセスがスムーズでプロフェッショナルでした。車は説明通りの優れた状態で到着しました。チームは私のすべての質問に迅速に対応し、配送プロセスを手間なく進めてくれました。強くお勧めします！'
  },
  {
    id: 'sample-2',
    name: 'Akosua Boateng',
    location: 'Kumasi, Ghana',
    email: '<EMAIL>',
    rating: 5,
    title: 'Professional and Trustworthy',
    review: 'EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!',
    vehiclePurchased: 'Honda Fit 2016',
    purchaseDate: '2024-02',
    locale: 'en',
    submittedAt: '2024-02-10T14:20:00.000Z',
    status: 'approved',
    images: [],
    titleEn: 'Professional and Trustworthy',
    titleJa: 'プロフェッショナルで信頼できる',
    reviewEn: 'EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!',
    reviewJa: 'EBAM Motorsは私の日常通勤に最適なホンダ フィットを見つけるのを手伝ってくれました。彼らのチームは車両について非常に知識が豊富で、各車について詳細な情報を提供してくれました。配送は迅速で、車は完璧な状態で到着しました。全体的に素晴らしい体験でした！'
  }
];

/**
 * Ensure the data directory exists
 */
async function ensureDataDirectory(): Promise<void> {
  try {
    await fs.access(DATA_DIR);
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true });
  }
}

/**
 * Initialize reviews file with default data if it doesn't exist
 */
async function initializeReviewsFile(): Promise<void> {
  try {
    await fs.access(REVIEWS_FILE_PATH);
  } catch {
    // File doesn't exist, create it with default reviews
    await ensureDataDirectory();
    await fs.writeFile(REVIEWS_FILE_PATH, JSON.stringify(DEFAULT_REVIEWS, null, 2), 'utf8');
    console.log('✅ Reviews file initialized with sample data');
  }
}

/**
 * Read all reviews from storage (file system or memory)
 */
export async function getAllReviews(): Promise<Review[]> {
  // In serverless environments, use memory storage
  if (isServerless) {
    // Initialize with default reviews if memory is empty
    if (memoryReviews.length === 0) {
      memoryReviews = [...DEFAULT_REVIEWS];
      console.log('✅ Memory storage initialized with default reviews');
    }
    return memoryReviews;
  }

  // In local development, use file system
  try {
    await initializeReviewsFile();
    const data = await fs.readFile(REVIEWS_FILE_PATH, 'utf8');
    return JSON.parse(data) as Review[];
  } catch (error) {
    console.error('Error reading reviews:', error);
    // Return default reviews if file is corrupted
    return DEFAULT_REVIEWS;
  }
}

/**
 * Save all reviews to storage (file system or memory)
 */
export async function saveAllReviews(reviews: Review[]): Promise<void> {
  // In serverless environments, use memory storage
  if (isServerless) {
    memoryReviews = [...reviews];
    console.log('✅ Reviews saved to memory storage');
    return;
  }

  // In local development, use file system
  try {
    await ensureDataDirectory();
    await fs.writeFile(REVIEWS_FILE_PATH, JSON.stringify(reviews, null, 2), 'utf8');
    console.log('✅ Reviews saved to file system');
  } catch (error) {
    console.error('Error saving reviews:', error);
    throw new Error('Failed to save reviews');
  }
}

/**
 * Add a new review
 */
export async function addReview(reviewData: Omit<Review, 'id'>): Promise<Review> {
  const reviews = await getAllReviews();
  
  const newReview: Review = {
    ...reviewData,
    id: Date.now().toString() + '-' + Math.random().toString(36).substr(2, 9)
  };
  
  reviews.push(newReview);
  await saveAllReviews(reviews);
  
  console.log('✅ New review added:', {
    id: newReview.id,
    name: newReview.name,
    rating: newReview.rating,
    status: newReview.status
  });
  
  return newReview;
}

/**
 * Update review status (approve/reject)
 */
export async function updateReviewStatus(reviewId: string, status: 'approved' | 'rejected'): Promise<boolean> {
  const reviews = await getAllReviews();
  const reviewIndex = reviews.findIndex(review => review.id === reviewId);
  
  if (reviewIndex === -1) {
    return false;
  }
  
  reviews[reviewIndex].status = status;
  await saveAllReviews(reviews);
  
  console.log('✅ Review status updated:', {
    id: reviewId,
    status: status
  });
  
  return true;
}

/**
 * Get reviews by status
 */
export async function getReviewsByStatus(status: 'pending' | 'approved' | 'rejected'): Promise<Review[]> {
  const reviews = await getAllReviews();
  return reviews.filter(review => review.status === status);
}

/**
 * Get approved reviews for public display
 */
export async function getApprovedReviews(): Promise<Review[]> {
  return getReviewsByStatus('approved');
}

/**
 * Get a specific review by ID
 */
export async function getReviewById(reviewId: string): Promise<Review | null> {
  const allReviews = await getAllReviews();
  return allReviews.find(review => review.id === reviewId) || null;
}

/**
 * Get pending reviews for admin review
 */
export async function getPendingReviews(): Promise<Review[]> {
  return getReviewsByStatus('pending');
}

/**
 * Delete a review (optional - for admin use)
 */
export async function deleteReview(reviewId: string): Promise<boolean> {
  const reviews = await getAllReviews();
  const initialLength = reviews.length;
  const filteredReviews = reviews.filter(review => review.id !== reviewId);
  
  if (filteredReviews.length === initialLength) {
    return false; // Review not found
  }
  
  await saveAllReviews(filteredReviews);
  
  console.log('✅ Review deleted:', { id: reviewId });
  
  return true;
}
