import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Configure experimental features
  experimental: {
    turbo: undefined, // Disable Turbopack
  },
  // Configure server external packages
  serverExternalPackages: [],
  // Ensure proper image optimization
  images: {
    unoptimized: false,
    domains: [],
  },
  // Help with hydration issues
  reactStrictMode: false, // Disable strict mode to prevent hydration issues
  // Compiler options to help with hydration
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },
  // Disable static optimization for admin routes
  async rewrites() {
    return [];
  },
};

export default nextConfig;
