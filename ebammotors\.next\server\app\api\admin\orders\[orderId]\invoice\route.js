(()=>{var e={};e.id=5631,e.ids=[5631],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7263:(e,t,n)=>{"use strict";n.d(t,{LP:()=>a,U1:()=>u,de:()=>c,g5:()=>i.g5});var r=n(80137),i=n(30358);async function s(e){let t=function(e){let t=[{id:"1",description:e.vehicle.title,quantity:1,unitPrice:e.vehicle.price,total:e.vehicle.price}];return e.shipping.cost>0&&t.push({id:"2",description:`Shipping (${e.shipping.method.name})`,quantity:1,unitPrice:e.shipping.cost,total:e.shipping.cost}),t}(e),{subtotal:n,tax:r,total:s}=function(e){let t=e.reduce((e,t)=>e+t.total,0);return{subtotal:t,tax:0,total:t+0}}(t),a={orderId:e.id,issuedDate:new Date().toISOString().split("T")[0],dueDate:e.payment.dueDate||new Date(Date.now()+2592e6).toISOString().split("T")[0],status:"sent",items:t,subtotal:n,tax:r,shipping:e.shipping.cost,total:s,currency:e.currency,paymentTerms:"Payment due within 30 days of invoice date",notes:`Order: ${e.orderNumber}
Vehicle: ${e.vehicle.title}
Shipping to: ${e.shipping.address.city}, ${e.shipping.address.country}`};return await (0,i.iO)(a)}function a(e,t){let n=new r.Ay,i=n.internal.pageSize.width;n.setFontSize(24),n.setFont("helvetica","bold"),n.text("EBAM MOTORS",20,30),n.setFontSize(12),n.setFont("helvetica","normal"),n.text("Premium Japanese Vehicles Export",20,40),n.text("Japan Office: Tokyo, Japan",20,50),n.text("Ghana Office: Kumasi, Ghana",20,60),n.text("Phone: +233245375692",20,70),n.text("Email: <EMAIL>",20,80),n.setFontSize(20),n.setFont("helvetica","bold"),n.text("INVOICE",i-20-40,30),n.setFontSize(12),n.setFont("helvetica","normal"),n.text(`Invoice #: ${e.invoiceNumber}`,i-20-60,45),n.text(`Date: ${e.issuedDate}`,i-20-60,55),n.text(`Due Date: ${e.dueDate}`,i-20-60,65),n.text(`Order #: ${t.orderNumber}`,i-20-60,75),n.setFontSize(14),n.setFont("helvetica","bold"),n.text("Bill To:",20,110),n.setFontSize(12),n.setFont("helvetica","normal"),n.text(t.customerInfo.name,20,125),n.text(t.customerInfo.email,20,135),n.text(t.customerInfo.phone,20,145),n.text(t.customerInfo.address.street,20,155),n.text(`${t.customerInfo.address.city}, ${t.customerInfo.address.state}`,20,165),n.text(`${t.customerInfo.address.country} ${t.customerInfo.address.postalCode}`,20,175),n.setFontSize(14),n.setFont("helvetica","bold"),n.text("Ship To:",i-20-80,110),n.setFontSize(12),n.setFont("helvetica","normal"),n.text(t.shipping.address.street,i-20-80,125),n.text(`${t.shipping.address.city}, ${t.shipping.address.state}`,i-20-80,135),n.text(`${t.shipping.address.country} ${t.shipping.address.postalCode}`,i-20-80,145),n.setFontSize(12),n.setFont("helvetica","bold"),n.text("Description",20,200),n.text("Qty",i-120,200),n.text("Unit Price",i-80,200),n.text("Total",i-40,200),n.line(20,205,i-20,205),n.setFont("helvetica","normal");let s=215;if(e.items.forEach((e,t)=>{n.text(e.description,20,s),n.text(e.quantity.toString(),i-120,s),n.text(`\xa5${e.unitPrice.toLocaleString()}`,i-80,s),n.text(`\xa5${e.total.toLocaleString()}`,i-40,s),s+=20}),n.line(20,s,i-20,s),s+=10,n.setFont("helvetica","normal"),n.text("Subtotal:",i-80,s),n.text(`\xa5${e.subtotal.toLocaleString()}`,i-40,s),s+=15,e.tax>0&&(n.text("Tax:",i-80,s),n.text(`\xa5${e.tax.toLocaleString()}`,i-40,s),s+=15),n.setFont("helvetica","bold"),n.text("Total:",i-80,s),n.text(`\xa5${e.total.toLocaleString()}`,i-40,s),s+=30,n.setFontSize(10),n.setFont("helvetica","normal"),n.text("Payment Terms:",20,s),s+=10,n.text(e.paymentTerms,20,s),e.notes){s+=20,n.text("Notes:",20,s),s+=10;let t=n.splitTextToSize(e.notes,i-40);n.text(t,20,s)}let a=n.internal.pageSize.height-30;return n.setFontSize(8),n.text("Thank you for your business!",20,a),n.text("For questions about this invoice, please contact <NAME_EMAIL>",20,a+10),n}async function o(e,t){a(e,t).output("blob");let n=`/invoices/${e.invoiceNumber}.pdf`;return await (0,i.Dq)(e.id,{pdfUrl:n}),n}async function u(e){let t=await s(e);return await o(t,e),t}async function c(e){return await (0,i.Dq)(e,{status:"paid"})}},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,n)=>{"use strict";n.d(t,{Qq:()=>m,Tq:()=>f,bS:()=>d,fF:()=>l,mU:()=>c});var r=n(85663),i=n(43205),s=n.n(i);let a=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await r.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),n=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return s().sign(t,a,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,n=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:n,expiresAt:n+864e5,lastActivity:n}),function(){let e=Date.now();for(let[t,n]of o.entries())e>n.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function l(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=s().verify(e,a);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let n=Date.now();return n>t.expiresAt?(o.delete(e),null):(t.lastActivity=n,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),n=p.get(e);return!n||t-n.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):n.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-n.lastAttempt)}:(n.count++,n.lastAttempt=t,p.set(e,n),{allowed:!0,remainingAttempts:5-n.count})}function f(e){p.delete(e)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30358:(e,t,n)=>{"use strict";n.d(t,{Dq:()=>D,HQ:()=>$,Q4:()=>S,Vd:()=>w,ee:()=>I,fS:()=>y,g5:()=>b,getOrderById:()=>v,iO:()=>F,iY:()=>A});var r=n(29021),i=n(33873),s=n.n(i),a=n(23870);let o=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,u=s().join(process.cwd(),"data"),c=s().join(u,"orders.json"),d=s().join(u,"invoices.json"),l=[],p=[];async function m(){if(!o)try{await r.promises.access(u)}catch{await r.promises.mkdir(u,{recursive:!0})}}async function f(){if(o)return l;try{await m();let e=await r.promises.readFile(c,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function h(e){if(o){l=e;return}await m(),await r.promises.writeFile(c,JSON.stringify(e,null,2))}async function g(){if(o)return p;try{await m();let e=await r.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function x(e){if(o){p=e;return}await m(),await r.promises.writeFile(d,JSON.stringify(e,null,2))}async function y(e){let t=await f(),n={...e,id:(0,a.A)(),orderNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`EB${e.slice(-6)}${t}`}(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(n),await h(t),n}async function w(){return await f()}async function v(e){return(await f()).find(t=>t.id===e)||null}async function S(e){return(await f()).filter(t=>t.customerId===e)}async function A(e,t){let n=await f(),r=n.findIndex(t=>t.id===e);return -1!==r&&(n[r]={...n[r],...t,updatedAt:new Date().toISOString()},await h(n),!0)}async function I(e,t){let n=await v(e);if(!n)return!1;let r={...n.payment,...t};return await A(e,{payment:r})}async function $(e,t){let n=await v(e);if(!n)return!1;let r={...t,id:(0,a.A)()},i={...n.shipping,updates:[...n.shipping.updates,r]};return await A(e,{shipping:i})}async function F(e){let t=await g(),n={...e,id:(0,a.A)(),invoiceNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,3).toUpperCase();return`INV-${e.slice(-6)}-${t}`}()};return t.push(n),await x(t),n}async function b(e){return(await g()).find(t=>t.orderId===e)||null}async function D(e,t){let n=await g(),r=n.findIndex(t=>t.id===e);return -1!==r&&(n[r]={...n[r],...t},await x(n),!0)}},33873:e=>{"use strict";e.exports=require("path")},36378:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>x,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>h});var r={};n.r(r),n.d(r,{GET:()=>p,POST:()=>l});var i=n(96559),s=n(48088),a=n(37719),o=n(32190),u=n(77268),c=n(30358),d=n(7263);async function l(e,{params:t}){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{orderId:n}=t;if(!n)return o.NextResponse.json({success:!1,message:"Order ID is required"},{status:400});let r=await (0,c.getOrderById)(n);if(!r)return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});let i=await (0,d.LP)(r);return new o.NextResponse(i,{status:200,headers:{"Content-Type":"application/pdf","Content-Disposition":`attachment; filename="invoice-${r.orderNumber}.pdf"`,"Content-Length":i.length.toString()}})}catch(e){return console.error("Error generating invoice:",e),o.NextResponse.json({success:!1,message:"Failed to generate invoice"},{status:500})}}async function p(e,{params:t}){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{orderId:n}=t;if(!n)return o.NextResponse.json({success:!1,message:"Order ID is required"},{status:400});let r=await (0,c.getOrderById)(n);if(!r)return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});return o.NextResponse.json({success:!0,invoice:{orderId:r.id,orderNumber:r.orderNumber,customerInfo:r.customerInfo,vehicle:r.vehicle,payment:r.payment,shipping:r.shipping,totalAmount:r.totalAmount,currency:r.currency,createdAt:r.createdAt,invoiceGenerated:r.invoiceGenerated}})}catch(e){return console.error("Error fetching invoice data:",e),o.NextResponse.json({success:!1,message:"Failed to fetch invoice data"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/orders/[orderId]/invoice/route",pathname:"/api/admin/orders/[orderId]/invoice",filename:"route",bundlePath:"app/api/admin/orders/[orderId]/invoice/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\orders\\[orderId]\\invoice\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:f,workUnitAsyncStorage:h,serverHooks:g}=m;function x(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:h})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77268:(e,t,n)=>{"use strict";n.d(t,{iY:()=>i}),n(32190);var r=n(12909);function i(e,t){let n=e.headers.get("authorization"),i=e.cookies.get("admin_session")?.value,s=(0,r.fF)(n,i);if(s.isValid)return{isValid:!0,adminId:s.adminId,method:"token/session"};let a=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return a&&a===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[4447,580,7696,4406],()=>n(36378));module.exports=r})();