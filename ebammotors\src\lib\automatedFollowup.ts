import { createFollowUp, getAllCustomers, getAllLeads, createInteraction } from './crmStorage';
import { emailService } from './resendService';

/**
 * Automated Follow-up System
 * Automatically schedules and sends follow-ups 3 days after customer/lead creation
 */

interface AutoFollowupConfig {
  delayDays: number;
  enableEmail: boolean;
  enableSMS: boolean;
  emailTemplate: string;
  smsTemplate: string;
}

const DEFAULT_CONFIG: AutoFollowupConfig = {
  delayDays: 3,
  enableEmail: true,
  enableSMS: true,
  emailTemplate: 'customer_followup',
  smsTemplate: 'customer_followup_sms'
};

/**
 * Schedule automatic follow-up for new customer
 */
export async function scheduleAutoFollowupForCustomer(customerId: string, customerData: any): Promise<void> {
  try {
    const followupDate = new Date();
    followupDate.setDate(followupDate.getDate() + DEFAULT_CONFIG.delayDays);

    // Schedule email follow-up
    if (DEFAULT_CONFIG.enableEmail) {
      await createFollowUp({
        type: 'email',
        status: 'pending',
        priority: 'medium',
        customerId,
        title: '3-Day Customer Follow-up (Email)',
        description: `Automated follow-up email to check customer satisfaction and offer assistance`,
        scheduledDate: followupDate.toISOString(),
        automationRule: {
          trigger: 'customer_created',
          delay: DEFAULT_CONFIG.delayDays * 24, // hours
          conditions: {
            type: 'email',
            template: DEFAULT_CONFIG.emailTemplate
          }
        },
        createdBy: 'system'
      });
    }

    // Schedule SMS follow-up
    if (DEFAULT_CONFIG.enableSMS && customerData.personalInfo?.phone) {
      await createFollowUp({
        type: 'sms',
        status: 'pending',
        priority: 'medium',
        customerId,
        title: '3-Day Customer Follow-up (SMS)',
        description: `Automated SMS follow-up to check customer satisfaction`,
        scheduledDate: followupDate.toISOString(),
        automationRule: {
          trigger: 'customer_created',
          delay: DEFAULT_CONFIG.delayDays * 24, // hours
          conditions: {
            type: 'sms',
            template: DEFAULT_CONFIG.smsTemplate,
            phone: customerData.personalInfo.phone
          }
        },
        createdBy: 'system'
      });
    }

    console.log(`📅 Scheduled automated follow-ups for customer ${customerId} in ${DEFAULT_CONFIG.delayDays} days`);
  } catch (error) {
    console.error('Error scheduling auto follow-up for customer:', error);
  }
}

/**
 * Schedule automatic follow-up for new lead
 */
export async function scheduleAutoFollowupForLead(leadId: string, leadData: any): Promise<void> {
  try {
    const followupDate = new Date();
    followupDate.setDate(followupDate.getDate() + DEFAULT_CONFIG.delayDays);

    // Schedule email follow-up
    if (DEFAULT_CONFIG.enableEmail && leadData.customerInfo?.email) {
      await createFollowUp({
        type: 'email',
        status: 'pending',
        priority: 'high',
        leadId,
        title: '3-Day Lead Follow-up (Email)',
        description: `Automated follow-up email for lead nurturing and conversion`,
        scheduledDate: followupDate.toISOString(),
        automationRule: {
          trigger: 'lead_created',
          delay: DEFAULT_CONFIG.delayDays * 24, // hours
          conditions: {
            type: 'email',
            template: 'lead_followup',
            leadSource: leadData.source,
            productInterest: leadData.inquiry?.productInterest
          }
        },
        createdBy: 'system'
      });
    }

    // Schedule SMS follow-up
    if (DEFAULT_CONFIG.enableSMS && leadData.customerInfo?.phone) {
      await createFollowUp({
        type: 'sms',
        status: 'pending',
        priority: 'high',
        leadId,
        title: '3-Day Lead Follow-up (SMS)',
        description: `Automated SMS follow-up for lead conversion`,
        scheduledDate: followupDate.toISOString(),
        automationRule: {
          trigger: 'lead_created',
          delay: DEFAULT_CONFIG.delayDays * 24, // hours
          conditions: {
            type: 'sms',
            template: 'lead_followup_sms',
            phone: leadData.customerInfo.phone,
            productInterest: leadData.inquiry?.productInterest
          }
        },
        createdBy: 'system'
      });
    }

    console.log(`📅 Scheduled automated follow-ups for lead ${leadId} in ${DEFAULT_CONFIG.delayDays} days`);
  } catch (error) {
    console.error('Error scheduling auto follow-up for lead:', error);
  }
}

/**
 * Process pending automated follow-ups
 * This should be called periodically (e.g., every hour) to send due follow-ups
 */
export async function processAutomatedFollowups(): Promise<void> {
  try {
    const { getAllFollowUps, updateFollowUp, getCustomerById, getLeadById } = await import('./crmStorage');
    const followups = await getAllFollowUps();
    const now = new Date();

    // Filter for pending automated follow-ups that are due
    const dueFollowups = followups.filter(followup => 
      followup.status === 'pending' &&
      followup.automationRule &&
      new Date(followup.scheduledDate) <= now
    );

    console.log(`🔄 Processing ${dueFollowups.length} due automated follow-ups`);

    for (const followup of dueFollowups) {
      try {
        await processIndividualFollowup(followup);
        
        // Mark as completed
        await updateFollowUp(followup.id, {
          status: 'completed',
          completedAt: new Date().toISOString(),
          notes: `${followup.notes || ''}\n\nAutomatically processed on ${new Date().toLocaleString()}`
        });

      } catch (error) {
        console.error(`Error processing followup ${followup.id}:`, error);
        
        // Mark as failed
        await updateFollowUp(followup.id, {
          status: 'failed',
          notes: `${followup.notes || ''}\n\nFailed to process: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
    }
  } catch (error) {
    console.error('Error processing automated follow-ups:', error);
  }
}

/**
 * Process individual follow-up (send email/SMS)
 */
async function processIndividualFollowup(followup: any): Promise<void> {
  const { getCustomerById, getLeadById } = await import('./crmStorage');
  
  let recipient: any = null;
  let recipientType = '';

  // Get recipient data
  if (followup.customerId) {
    recipient = await getCustomerById(followup.customerId);
    recipientType = 'customer';
  } else if (followup.leadId) {
    recipient = await getLeadById(followup.leadId);
    recipientType = 'lead';
  }

  if (!recipient) {
    throw new Error(`Recipient not found for followup ${followup.id}`);
  }

  const conditions = followup.automationRule?.conditions || {};

  if (followup.type === 'email') {
    await sendAutomatedEmail(recipient, recipientType, conditions, followup);
  } else if (followup.type === 'sms') {
    await sendAutomatedSMS(recipient, recipientType, conditions, followup);
  }

  // Log interaction
  await createInteraction({
    customerId: followup.customerId,
    leadId: followup.leadId,
    type: followup.type === 'email' ? 'email' : 'sms',
    direction: 'outbound',
    channel: 'automation',
    content: `Automated ${followup.type} follow-up sent: ${followup.title}`,
    subject: followup.title,
    tags: ['automated', 'follow_up', followup.type],
    createdBy: 'system',
  });
}

/**
 * Send automated email
 */
async function sendAutomatedEmail(recipient: any, recipientType: string, conditions: any, followup: any): Promise<void> {
  const email = recipientType === 'customer' 
    ? recipient.personalInfo?.email 
    : recipient.customerInfo?.email;

  if (!email) {
    throw new Error('No email address found for recipient');
  }

  const name = recipientType === 'customer'
    ? recipient.personalInfo?.name
    : recipient.customerInfo?.name;

  // Prepare email data based on template
  let emailData: any = {
    to: email,
    customerName: name,
    followupType: followup.title,
    description: followup.description
  };

  if (conditions.template === 'lead_followup') {
    emailData = {
      ...emailData,
      productInterest: conditions.productInterest || 'our vehicles',
      leadSource: conditions.leadSource || 'website',
      inquiryDetails: recipient.inquiry?.message || ''
    };
  }

  // Send email using the email service
  await emailService.sendFollowUpEmail(emailData);
  console.log(`📧 Automated email sent to ${email}`);
}

/**
 * Send automated SMS
 */
async function sendAutomatedSMS(recipient: any, recipientType: string, conditions: any, followup: any): Promise<void> {
  const phone = conditions.phone;

  if (!phone) {
    throw new Error('No phone number found for recipient');
  }

  const name = recipientType === 'customer'
    ? recipient.personalInfo?.name
    : recipient.customerInfo?.name;

  // Prepare SMS message
  let message = '';
  
  if (conditions.template === 'lead_followup_sms') {
    const productInterest = conditions.productInterest || 'vehicles';
    message = `Hi ${name}! Following up on your interest in ${productInterest}. We have great options available. Any questions? Reply or call +233245375692. - EBAM Motors`;
  } else {
    message = `Hi ${name}! Hope you're satisfied with our service. Need any assistance with your vehicle or have questions? We're here to help! Call +233245375692. - EBAM Motors`;
  }

  // For now, we'll log the SMS (you can integrate with SMS service like Twilio)
  console.log(`📱 Automated SMS would be sent to ${phone}: ${message}`);
  
  // TODO: Integrate with actual SMS service
  // await smsService.send(phone, message);
}

/**
 * Initialize automated follow-up system
 * Call this when the application starts
 */
export function initializeAutomatedFollowups(): void {
  console.log('🚀 Initializing automated follow-up system...');

  // Process pending follow-ups every hour
  setInterval(processAutomatedFollowups, 60 * 60 * 1000);

  // Also process immediately on startup
  setTimeout(processAutomatedFollowups, 5000); // Wait 5 seconds after startup

  console.log('✅ Automated follow-up system initialized');
}

/**
 * Manual trigger for processing follow-ups (for testing)
 */
export async function triggerFollowupProcessing(): Promise<{ processed: number; errors: number }> {
  console.log('🔄 Manually triggering follow-up processing...');
  
  try {
    await processAutomatedFollowups();
    return { processed: 1, errors: 0 };
  } catch (error) {
    console.error('Error in manual follow-up processing:', error);
    return { processed: 0, errors: 1 };
  }
}
