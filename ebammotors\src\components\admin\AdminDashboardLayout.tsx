'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  LayoutDashboard, 
  Car, 
  Users, 
  ShoppingCart, 
  MessageSquare, 
  BarChart3, 
  Settings, 
  Shield, 
  Activity,
  Menu,
  X,
  LogOut,
  Bell,
  Search,
  AlertTriangle
} from 'lucide-react';
import AdminAuth from './AdminAuth';
import DashboardOverview from './DashboardOverview';
import SystemHealthMonitoring from './SystemHealthMonitoring';
import CarManagement from './CarManagement';
import ReviewModeration from './ReviewModeration';
import CRMManagement from './CRMManagement';
import OrderManagement from './OrderManagement';
import SystemAnalytics from './SystemAnalytics';
import SecurityManagement from './SecurityManagement';
import AdminSettings from './AdminSettings';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  path: string;
  badge?: number;
}

const sidebarItems: SidebarItem[] = [
  { id: 'overview', label: 'Overview', icon: LayoutDashboard, path: '/admin/dashboard' },
  { id: 'cars', label: 'Car Management', icon: Car, path: '/admin/cars' },
  { id: 'orders', label: 'Orders', icon: ShoppingCart, path: '/admin/orders' },
  { id: 'customers', label: 'CRM', icon: Users, path: '/admin/customers' },
  { id: 'reviews', label: 'Reviews', icon: MessageSquare, path: '/admin/reviews' },
  { id: 'analytics', label: 'Analytics', icon: BarChart3, path: '/admin/analytics' },
  { id: 'health', label: 'System Health', icon: Activity, path: '/admin/health' },
  { id: 'security', label: 'Security', icon: Shield, path: '/admin/security' },
  { id: 'settings', label: 'Settings', icon: Settings, path: '/admin/settings' },
];

export default function AdminDashboardLayout() {
  const [mounted, setMounted] = useState(false);
  const [authenticated, setAuthenticated] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('overview');
  const [notifications, setNotifications] = useState(3);
  const [showNotifications, setShowNotifications] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const notificationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setMounted(true);
    // Check if user is already authenticated
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('admin_token');
      if (token) {
        setAuthenticated(true);
      }
    }

    // Check for section parameter in URL
    const section = searchParams.get('section');
    if (section && sidebarItems.find(item => item.id === section)) {
      setActiveSection(section);
    }
  }, [searchParams]);

  // Close notifications when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
    }

    if (showNotifications) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showNotifications]);

  const handleLogout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('admin_token');
    }
    setAuthenticated(false);
  };

  const handleSectionChange = (sectionId: string, path: string) => {
    setActiveSection(sectionId);
    setSidebarOpen(false);
    // Update URL with section parameter
    const newUrl = `/admin/dashboard?section=${sectionId}`;
    router.push(newUrl);
  };

  const handleNotificationClick = (notificationType: string, notificationId?: string) => {
    // Close the notifications dropdown
    setShowNotifications(false);

    // Navigate based on notification type
    switch (notificationType) {
      case 'order':
        handleSectionChange('orders', '/admin/orders');
        break;
      case 'review':
        handleSectionChange('reviews', '/admin/reviews');
        break;
      case 'system':
        handleSectionChange('health', '/admin/health');
        break;
      case 'car':
        handleSectionChange('cars', '/admin/cars');
        break;
      default:
        // For general notifications, stay on overview
        handleSectionChange('overview', '/admin/dashboard');
    }

    // Optionally decrease notification count
    if (notifications > 0) {
      setNotifications(prev => prev - 1);
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!authenticated) {
    return <AdminAuth onAuthenticated={() => setAuthenticated(true)} />;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-900">EBAM Admin</h1>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <nav className="mt-6 px-3">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeSection === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => handleSectionChange(item.id, item.path)}
                className={`w-full flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${
                  isActive
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <Icon className="w-5 h-5 mr-3" />
                {item.label}
                {item.badge && (
                  <span className="ml-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
                    {item.badge}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col lg:ml-0">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
              >
                <Menu className="w-5 h-5" />
              </button>
              
              <div className="ml-4 lg:ml-0">
                <h2 className="text-lg font-semibold text-gray-900">
                  {sidebarItems.find(item => item.id === activeSection)?.label || 'Dashboard'}
                </h2>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="hidden md:block relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Notifications */}
              <div className="relative" ref={notificationRef}>
                <button
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="relative p-2 text-gray-400 hover:text-gray-600"
                >
                  <Bell className="w-5 h-5" />
                  {notifications > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {notifications}
                    </span>
                  )}
                </button>

                {/* Notifications Dropdown */}
                {showNotifications && (
                  <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                    <div className="p-4 border-b border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
                    </div>
                    <div className="max-h-96 overflow-y-auto">
                      {/* Sample notifications */}
                      <button
                        onClick={() => handleNotificationClick('order', 'ORD-001')}
                        className="w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors"
                      >
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <ShoppingCart className="w-4 h-4 text-blue-600" />
                            </div>
                          </div>
                          <div className="ml-3 flex-1">
                            <p className="text-sm font-medium text-gray-900">New order received</p>
                            <p className="text-sm text-gray-600">Order #ORD-001 from John Doe</p>
                            <p className="text-xs text-gray-500 mt-1">2 minutes ago</p>
                          </div>
                        </div>
                      </button>

                      <button
                        onClick={() => handleNotificationClick('review', 'REV-001')}
                        className="w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors"
                      >
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                              <MessageSquare className="w-4 h-4 text-green-600" />
                            </div>
                          </div>
                          <div className="ml-3 flex-1">
                            <p className="text-sm font-medium text-gray-900">New review submitted</p>
                            <p className="text-sm text-gray-600">5-star review for Toyota Voxy</p>
                            <p className="text-xs text-gray-500 mt-1">15 minutes ago</p>
                          </div>
                        </div>
                      </button>

                      <button
                        onClick={() => handleNotificationClick('system', 'SYS-001')}
                        className="w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-left transition-colors"
                      >
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                              <AlertTriangle className="w-4 h-4 text-yellow-600" />
                            </div>
                          </div>
                          <div className="ml-3 flex-1">
                            <p className="text-sm font-medium text-gray-900">System alert</p>
                            <p className="text-sm text-gray-600">Low inventory warning for Honda Freed</p>
                            <p className="text-xs text-gray-500 mt-1">1 hour ago</p>
                          </div>
                        </div>
                      </button>
                    </div>
                    <div className="p-4 border-t border-gray-200">
                      <button
                        onClick={() => setShowNotifications(false)}
                        className="w-full text-center text-sm text-blue-600 hover:text-blue-800"
                      >
                        View all notifications
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Logout */}
              <button
                onClick={handleLogout}
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </button>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 p-6 overflow-auto">
          {activeSection === 'overview' && <DashboardOverview />}
          {activeSection === 'health' && <SystemHealthMonitoring />}
          {activeSection === 'cars' && <CarManagement />}
          {activeSection === 'reviews' && <ReviewModeration />}
          {activeSection === 'customers' && <CRMManagement />}
          {activeSection === 'orders' && <OrderManagement />}
          {activeSection === 'analytics' && <SystemAnalytics />}
          {activeSection === 'security' && <SecurityManagement />}
          {activeSection === 'settings' && <AdminSettings />}
          {activeSection !== 'overview' && activeSection !== 'health' && activeSection !== 'cars' && activeSection !== 'reviews' && activeSection !== 'customers' && activeSection !== 'orders' && activeSection !== 'analytics' && activeSection !== 'security' && activeSection !== 'settings' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {sidebarItems.find(item => item.id === activeSection)?.label}
              </h3>
              <p className="text-gray-600">
                This section is under development. Please check back soon.
              </p>
            </div>
          )}
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
