'use client';

import { useState, useEffect } from 'react';
import { 
  Car, 
  Users, 
  ShoppingCart, 
  MessageSquare, 
  TrendingUp, 
  TrendingDown,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Eye
} from 'lucide-react';

interface DashboardStats {
  totalCars: number;
  totalCustomers: number;
  totalOrders: number;
  pendingReviews: number;
  totalRevenue: number;
  monthlyGrowth: number;
  systemHealth: 'healthy' | 'warning' | 'error';
  recentActivity: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    status: 'success' | 'warning' | 'error';
  }>;
}

export default function DashboardOverview() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/admin/dashboard-stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        // Fallback to mock data if API not available
        setStats({
          totalCars: 45,
          totalCustomers: 128,
          totalOrders: 23,
          pendingReviews: 7,
          totalRevenue: 2450000,
          monthlyGrowth: 12.5,
          systemHealth: 'healthy',
          recentActivity: [
            {
              id: '1',
              type: 'order',
              message: 'New order #ORD-001 received',
              timestamp: '2 minutes ago',
              status: 'success'
            },
            {
              id: '2',
              type: 'review',
              message: 'Review pending approval',
              timestamp: '15 minutes ago',
              status: 'warning'
            },
            {
              id: '3',
              type: 'car',
              message: 'Toyota Voxy added to inventory',
              timestamp: '1 hour ago',
              status: 'success'
            }
          ]
        });
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Failed to load dashboard data.</p>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Cars',
      value: stats.totalCars,
      icon: Car,
      color: 'blue',
      change: '+5 this month'
    },
    {
      title: 'Customers',
      value: stats.totalCustomers,
      icon: Users,
      color: 'green',
      change: '+12 this month'
    },
    {
      title: 'Orders',
      value: stats.totalOrders,
      icon: ShoppingCart,
      color: 'purple',
      change: '+3 this week'
    },
    {
      title: 'Pending Reviews',
      value: stats.pendingReviews,
      icon: MessageSquare,
      color: 'orange',
      change: 'Needs attention'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div key={index} className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{card.value}</p>
                  <p className="text-xs text-gray-500 mt-1">{card.change}</p>
                </div>
                <div className={`p-3 rounded-full bg-${card.color}-100`}>
                  <Icon className={`w-6 h-6 text-${card.color}-600`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Revenue and Growth */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Revenue Overview</h3>
            <DollarSign className="w-5 h-5 text-green-600" />
          </div>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">¥{stats.totalRevenue.toLocaleString()}</p>
            </div>
            <div className="flex items-center">
              {stats.monthlyGrowth > 0 ? (
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${
                stats.monthlyGrowth > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.monthlyGrowth > 0 ? '+' : ''}{stats.monthlyGrowth}% from last month
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
            <Activity className={`w-5 h-5 ${
              stats.systemHealth === 'healthy' ? 'text-green-600' : 
              stats.systemHealth === 'warning' ? 'text-yellow-600' : 'text-red-600'
            }`} />
          </div>
          <div className="space-y-4">
            <div className="flex items-center">
              {stats.systemHealth === 'healthy' && <CheckCircle className="w-5 h-5 text-green-500 mr-2" />}
              {stats.systemHealth === 'warning' && <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2" />}
              {stats.systemHealth === 'error' && <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />}
              <span className="text-sm font-medium capitalize">{stats.systemHealth}</span>
            </div>
            <p className="text-sm text-gray-600">
              All systems operational. Last check: 2 minutes ago
            </p>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {stats.recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                {getStatusIcon(activity.status)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">{activity.timestamp}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6">
            <button className="flex items-center text-sm text-blue-600 hover:text-blue-700">
              <Eye className="w-4 h-4 mr-1" />
              View all activity
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
