"use strict";(()=>{var e={};e.id=9789,e.ids=[9789],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7263:(e,t,r)=>{r.d(t,{LP:()=>o,U1:()=>c,de:()=>u,g5:()=>s.g5});var n=r(80137),s=r(30358);async function a(e){let t=function(e){let t=[{id:"1",description:e.vehicle.title,quantity:1,unitPrice:e.vehicle.price,total:e.vehicle.price}];return e.shipping.cost>0&&t.push({id:"2",description:`Shipping (${e.shipping.method.name})`,quantity:1,unitPrice:e.shipping.cost,total:e.shipping.cost}),t}(e),{subtotal:r,tax:n,total:a}=function(e){let t=e.reduce((e,t)=>e+t.total,0);return{subtotal:t,tax:0,total:t+0}}(t),o={orderId:e.id,issuedDate:new Date().toISOString().split("T")[0],dueDate:e.payment.dueDate||new Date(Date.now()+2592e6).toISOString().split("T")[0],status:"sent",items:t,subtotal:r,tax:n,shipping:e.shipping.cost,total:a,currency:e.currency,paymentTerms:"Payment due within 30 days of invoice date",notes:`Order: ${e.orderNumber}
Vehicle: ${e.vehicle.title}
Shipping to: ${e.shipping.address.city}, ${e.shipping.address.country}`};return await (0,s.iO)(o)}function o(e,t){let r=new n.Ay,s=r.internal.pageSize.width;r.setFontSize(24),r.setFont("helvetica","bold"),r.text("EBAM MOTORS",20,30),r.setFontSize(12),r.setFont("helvetica","normal"),r.text("Premium Japanese Vehicles Export",20,40),r.text("Japan Office: Tokyo, Japan",20,50),r.text("Ghana Office: Kumasi, Ghana",20,60),r.text("Phone: +************",20,70),r.text("Email: <EMAIL>",20,80),r.setFontSize(20),r.setFont("helvetica","bold"),r.text("INVOICE",s-20-40,30),r.setFontSize(12),r.setFont("helvetica","normal"),r.text(`Invoice #: ${e.invoiceNumber}`,s-20-60,45),r.text(`Date: ${e.issuedDate}`,s-20-60,55),r.text(`Due Date: ${e.dueDate}`,s-20-60,65),r.text(`Order #: ${t.orderNumber}`,s-20-60,75),r.setFontSize(14),r.setFont("helvetica","bold"),r.text("Bill To:",20,110),r.setFontSize(12),r.setFont("helvetica","normal"),r.text(t.customerInfo.name,20,125),r.text(t.customerInfo.email,20,135),r.text(t.customerInfo.phone,20,145),r.text(t.customerInfo.address.street,20,155),r.text(`${t.customerInfo.address.city}, ${t.customerInfo.address.state}`,20,165),r.text(`${t.customerInfo.address.country} ${t.customerInfo.address.postalCode}`,20,175),r.setFontSize(14),r.setFont("helvetica","bold"),r.text("Ship To:",s-20-80,110),r.setFontSize(12),r.setFont("helvetica","normal"),r.text(t.shipping.address.street,s-20-80,125),r.text(`${t.shipping.address.city}, ${t.shipping.address.state}`,s-20-80,135),r.text(`${t.shipping.address.country} ${t.shipping.address.postalCode}`,s-20-80,145),r.setFontSize(12),r.setFont("helvetica","bold"),r.text("Description",20,200),r.text("Qty",s-120,200),r.text("Unit Price",s-80,200),r.text("Total",s-40,200),r.line(20,205,s-20,205),r.setFont("helvetica","normal");let a=215;if(e.items.forEach((e,t)=>{r.text(e.description,20,a),r.text(e.quantity.toString(),s-120,a),r.text(`\xa5${e.unitPrice.toLocaleString()}`,s-80,a),r.text(`\xa5${e.total.toLocaleString()}`,s-40,a),a+=20}),r.line(20,a,s-20,a),a+=10,r.setFont("helvetica","normal"),r.text("Subtotal:",s-80,a),r.text(`\xa5${e.subtotal.toLocaleString()}`,s-40,a),a+=15,e.tax>0&&(r.text("Tax:",s-80,a),r.text(`\xa5${e.tax.toLocaleString()}`,s-40,a),a+=15),r.setFont("helvetica","bold"),r.text("Total:",s-80,a),r.text(`\xa5${e.total.toLocaleString()}`,s-40,a),a+=30,r.setFontSize(10),r.setFont("helvetica","normal"),r.text("Payment Terms:",20,a),a+=10,r.text(e.paymentTerms,20,a),e.notes){a+=20,r.text("Notes:",20,a),a+=10;let t=r.splitTextToSize(e.notes,s-40);r.text(t,20,a)}let o=r.internal.pageSize.height-30;return r.setFontSize(8),r.text("Thank you for your business!",20,o),r.text("For questions about this invoice, please contact <NAME_EMAIL>",20,o+10),r}async function i(e,t){o(e,t).output("blob");let r=`/invoices/${e.invoiceNumber}.pdf`;return await (0,s.Dq)(e.id,{pdfUrl:r}),r}async function c(e){let t=await a(e);return await i(t,e),t}async function u(e){return await (0,s.Dq)(e,{status:"paid"})}},8086:e=>{e.exports=require("module")},10442:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>y,serverHooks:()=>b,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var n={};r.r(n),r.d(n,{GET:()=>f,PATCH:()=>h,POST:()=>g});var s=r(96559),a=r(48088),o=r(37719),i=r(32190),c=r(30358),u=r(7263),d=r(24407),l=r(12955),p=r(53190),m=r(6234);async function g(e){try{let t=await e.json();if(!t.customerId||!t.vehicle||!t.payment||!t.shipping)return i.NextResponse.json({success:!1,message:"Missing required order data"},{status:400});let r=(0,d.gY)(t.payment.method.id);if(!r)return i.NextResponse.json({success:!1,message:"Invalid payment method"},{status:400});let n=(0,d.Uv)(t.shipping.method.id);if(!n)return i.NextResponse.json({success:!1,message:"Invalid shipping method"},{status:400});let s=await (0,c.fS)({customerId:t.customerId,customerInfo:t.customerInfo,vehicle:t.vehicle,payment:{method:r,amount:t.payment.amount,currency:t.payment.currency||"JPY",status:"pending",dueDate:t.payment.dueDate},shipping:{method:n,cost:t.shipping.cost,address:t.shipping.address,estimatedDelivery:t.shipping.estimatedDelivery,status:"pending",updates:[]},status:"pending_payment",totalAmount:t.totalAmount,currency:t.currency||"JPY",notes:t.notes,invoiceGenerated:!1});try{let e=await (0,u.U1)(s);await (0,c.iY)(s.id,{invoiceGenerated:!0,invoiceUrl:e.pdfUrl})}catch(e){console.error("Failed to generate invoice:",e)}try{let e=await (0,l.syncCustomerFromOrder)(t);e&&await (0,p.createInteraction)({customerId:e.id,type:"order",direction:"inbound",channel:"website",content:`Order created: ${s.vehicle.title} for \xa5${s.totalAmount.toLocaleString()}`,subject:"New Order",tags:["order_creation","purchase"],relatedOrderId:s.id,relatedProductId:s.vehicle.id,createdBy:e.id})}catch(e){console.error("CRM integration error:",e)}return i.NextResponse.json({success:!0,message:"Order created successfully",order:s})}catch(e){return console.error("Error creating order:",e),i.NextResponse.json({success:!1,message:"Failed to create order"},{status:500})}}async function f(e){try{let{searchParams:t}=new URL(e.url),r=t.get("customerId"),n=t.get("adminKey"),s=process.env.ADMIN_PASSWORD||"admin123";if(n===s){let e=await (0,c.Vd)();return i.NextResponse.json({orders:e})}if(r){let e=await (0,c.Q4)(r);return i.NextResponse.json({orders:e})}return i.NextResponse.json({success:!1,message:"Customer ID required"},{status:400})}catch(e){return console.error("Error fetching orders:",e),i.NextResponse.json({success:!1,message:"Failed to fetch orders"},{status:500})}}async function h(e){try{let{orderId:t,updates:n,adminKey:s}=await e.json(),a=process.env.ADMIN_PASSWORD||"admin123";if(s!==a)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!await (0,c.iY)(t,n))return i.NextResponse.json({success:!1,message:"Order not found"},{status:404});try{let{getOrderById:e}=await Promise.resolve().then(r.bind(r,30358)),s=await e(t);if(s){let{getCustomerByEmail:e}=await Promise.resolve().then(r.bind(r,53190)),t=await e(s.customerInfo.email);t&&(await (0,p.createInteraction)({customerId:t.id,type:"order",direction:"outbound",channel:"website",content:`Order status updated: ${Object.keys(n).map(e=>`${e}: ${n[e]}`).join(", ")}`,subject:"Order Status Update",tags:["order_update","status_change"],relatedOrderId:s.id,createdBy:"admin"}),("delivered"===n.status||n.shipping?.status==="delivered")&&await (0,m._S)(t.id,s.id,{vehicleTitle:s.vehicle.title,orderNumber:s.orderNumber}))}}catch(e){console.error("CRM integration error during order update:",e)}return i.NextResponse.json({success:!0,message:"Order updated successfully"})}catch(e){return console.error("Error updating order:",e),i.NextResponse.json({success:!1,message:"Failed to update order"},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\orders\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:b}=y;function v(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12955:(e,t,r)=>{r.r(t),r.d(t,{GET:()=>a,PATCH:()=>i,POST:()=>o,syncCustomerFromOrder:()=>c});var n=r(32190),s=r(53190);async function a(e){try{let{searchParams:t}=new URL(e.url),r=t.get("adminKey"),a=t.get("id"),o=t.get("email"),i="true"===t.get("overview"),c=process.env.ADMIN_PASSWORD||"admin123";if(r!==c)return n.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(a&&i){let e=await (0,s.oP)(a);if(!e)return n.NextResponse.json({success:!1,message:"Customer not found"},{status:404});return n.NextResponse.json({success:!0,data:e})}if(a){let e=await (0,s.getCustomerById)(a);if(!e)return n.NextResponse.json({success:!1,message:"Customer not found"},{status:404});return n.NextResponse.json({success:!0,customer:e})}if(o){let e=await (0,s.getCustomerByEmail)(o);if(!e)return n.NextResponse.json({success:!1,message:"Customer not found"},{status:404});return n.NextResponse.json({success:!0,customer:e})}let u=await (0,s.Rf)();return u.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()),n.NextResponse.json({success:!0,customers:u})}catch(e){return console.error("Error fetching customers:",e),n.NextResponse.json({success:!1,message:"Failed to fetch customers"},{status:500})}}async function o(e){try{let t=await e.json();if(!t.personalInfo?.name||!t.personalInfo?.email)return n.NextResponse.json({success:!1,message:"Name and email are required"},{status:400});let a={status:"active",segment:"new",loyaltyPoints:0,membershipTier:"Bronze",totalSpent:0,totalOrders:0,averageOrderValue:0,acquisitionSource:"website",tags:[],preferences:{notifications:{email:!0,sms:!1,push:!0,marketing:!1},currency:"JPY",communicationPreference:"email"},address:{},...t},o=await (0,s.Y2)(a),{createInteraction:i}=await Promise.resolve().then(r.bind(r,53190));await i({customerId:o.id,type:"support",direction:"inbound",channel:"website",content:"Customer profile created/updated",subject:"Customer Registration",tags:["customer_registration","profile_update"],createdBy:"system"});let{scheduleAutoFollowupForCustomer:c}=await Promise.all([r.e(6967),r.e(2273)]).then(r.bind(r,62273));return await c(o.id,a),n.NextResponse.json({success:!0,message:"Customer saved successfully",customer:o})}catch(e){return console.error("Error creating/updating customer:",e),n.NextResponse.json({success:!1,message:"Failed to save customer"},{status:500})}}async function i(e){try{let{customerId:t,adminKey:a,...o}=await e.json(),i=process.env.ADMIN_PASSWORD||"admin123";if(a!==i)return n.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!t)return n.NextResponse.json({success:!1,message:"Customer ID is required"},{status:400});if(!await (0,s.Gk)(t,o))return n.NextResponse.json({success:!1,message:"Customer not found"},{status:404});let{createInteraction:c}=await Promise.resolve().then(r.bind(r,53190));return await c({customerId:t,type:"support",direction:"outbound",channel:"website",content:`Customer profile updated: ${Object.keys(o).join(", ")}`,subject:"Profile Update",tags:["profile_update","admin_action"],createdBy:"admin"}),n.NextResponse.json({success:!0,message:"Customer updated successfully"})}catch(e){return console.error("Error updating customer:",e),n.NextResponse.json({success:!1,message:"Failed to update customer"},{status:500})}}async function c(e){try{let t={personalInfo:{name:e.customerInfo.name,email:e.customerInfo.email,phone:e.customerInfo.phone},address:e.customerInfo.address,preferences:{notifications:{email:!0,sms:!1,push:!0,marketing:!1},currency:e.currency||"JPY",communicationPreference:"email"},status:"active",segment:"regular",loyaltyPoints:0,membershipTier:"Bronze",totalSpent:e.totalAmount,totalOrders:1,averageOrderValue:e.totalAmount,acquisitionSource:"order",tags:["customer"]},r=await (0,s.Y2)(t);if(r){let t=await (0,s.getCustomerById)(r.id);t&&t.totalOrders>0&&await (0,s.Gk)(r.id,{totalSpent:t.totalSpent+e.totalAmount,totalOrders:t.totalOrders+1,averageOrderValue:(t.totalSpent+e.totalAmount)/(t.totalOrders+1),lastOrderDate:new Date().toISOString()})}return r}catch(e){return console.error("Error syncing customer from order:",e),null}}},24407:(e,t,r)=>{r.d(t,{N1:()=>u,U:()=>s,UQ:()=>d,Uv:()=>i,ZK:()=>a,_4:()=>c,gY:()=>o});let n=[{id:"bank_transfer_ghana",type:"bank_transfer",name:"Bank Transfer (Ghana)",description:"Direct bank transfer to our Ghana account",icon:"\uD83C\uDFE6",enabled:!0,config:{bankName:"Ghana Commercial Bank",accountNumber:"*************",accountName:"EBAM Motors Ghana Ltd",swiftCode:"GCBLGHAC"}},{id:"bank_transfer_japan",type:"bank_transfer",name:"Bank Transfer (Japan)",description:"Direct bank transfer to our Japan account",icon:"\uD83C\uDFE6",enabled:!0,config:{bankName:"Mizuho Bank",accountNumber:"*************",accountName:"EBAM Motors Japan KK",swiftCode:"MHCBJPJT"}},{id:"mtn_mobile_money",type:"mobile_money",name:"MTN Mobile Money",description:"Pay using MTN Mobile Money",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"mtn",number:"+************"}},{id:"vodafone_cash",type:"mobile_money",name:"Vodafone Cash",description:"Pay using Vodafone Cash",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"vodafone",number:"+************"}},{id:"airtel_money",type:"mobile_money",name:"AirtelTigo Money",description:"Pay using AirtelTigo Money",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"airtel",number:"+************"}},{id:"stripe_card",type:"stripe",name:"Credit/Debit Card",description:"Pay securely with your credit or debit card",icon:"\uD83D\uDCB3",enabled:!0,config:{publishableKey:process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}},{id:"cash_agent_kumasi",type:"cash_agent",name:"Cash Payment (Kumasi)",description:"Pay cash through our agent in Kumasi",icon:"\uD83D\uDCB5",enabled:!0,config:{agentLocations:["Kumasi Central Market","Adum Shopping Center","KNUST Campus"]}},{id:"cash_agent_accra",type:"cash_agent",name:"Cash Payment (Accra)",description:"Pay cash through our agent in Accra",icon:"\uD83D\uDCB5",enabled:!0,config:{agentLocations:["Makola Market","Osu Oxford Street","East Legon"]}}],s=[{id:"sea_freight_standard",name:"Sea Freight (Standard)",description:"Most economical option, 4-6 weeks delivery",estimatedDays:35,basePrice:15e4,pricePerKg:50,maxWeight:2e3,trackingIncluded:!0,insuranceIncluded:!0},{id:"sea_freight_express",name:"Sea Freight (Express)",description:"Faster sea shipping, 3-4 weeks delivery",estimatedDays:25,basePrice:2e5,pricePerKg:75,maxWeight:2e3,trackingIncluded:!0,insuranceIncluded:!0},{id:"air_freight",name:"Air Freight",description:"Fastest option, 1-2 weeks delivery",estimatedDays:10,basePrice:5e5,pricePerKg:200,maxWeight:500,trackingIncluded:!0,insuranceIncluded:!0}],a={compact:1200,sedan:1400,suv:1800,van:1600,truck:2500,motorcycle:200,default:1500};function o(e){return n.find(t=>t.id===e)||null}function i(e){return s.find(t=>t.id===e)||null}function c(e,t,r="ghana"){let n=i(e);if(!n)return 0;let s=a[t.toLowerCase()]||a.default,o=n.pricePerKg?s*n.pricePerKg:0,u=1;return"ghana"!==r.toLowerCase()&&(u=1.2),Math.round((n.basePrice+o)*u)}function u(e){let t=i(e);if(!t)return"";let r=new Date;return r.setDate(r.getDate()+t.estimatedDays),r.toISOString().split("T")[0]}function d(e,t,r){let n=o(e);if(!n)return"";switch(n.type){case"bank_transfer":return`Transfer \xa5${t.toLocaleString()} to:
Bank: ${n.config?.bankName}
Account: ${n.config?.accountNumber}
Name: ${n.config?.accountName}
Reference: ${r}`;case"mobile_money":return`Send \xa5${t.toLocaleString()} to ${n.config?.number}
Reference: ${r}
Network: ${n.name}`;case"cash_agent":return`Visit one of our agent locations with \xa5${t.toLocaleString()}:
${n.config?.agentLocations?.join(", ")}
Reference: ${r}`;case"stripe":return`You will be redirected to secure payment page to complete your payment of \xa5${t.toLocaleString()}.`;default:return`Payment amount: \xa5${t.toLocaleString()}
Order reference: ${r}`}}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30358:(e,t,r)=>{r.d(t,{Dq:()=>j,HQ:()=>I,Q4:()=>v,Vd:()=>w,ee:()=>N,fS:()=>x,g5:()=>O,getOrderById:()=>b,iO:()=>P,iY:()=>S});var n=r(29021),s=r(33873),a=r.n(s),o=r(23870);let i=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,c=a().join(process.cwd(),"data"),u=a().join(c,"orders.json"),d=a().join(c,"invoices.json"),l=[],p=[];async function m(){if(!i)try{await n.promises.access(c)}catch{await n.promises.mkdir(c,{recursive:!0})}}async function g(){if(i)return l;try{await m();let e=await n.promises.readFile(u,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function f(e){if(i){l=e;return}await m(),await n.promises.writeFile(u,JSON.stringify(e,null,2))}async function h(){if(i)return p;try{await m();let e=await n.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function y(e){if(i){p=e;return}await m(),await n.promises.writeFile(d,JSON.stringify(e,null,2))}async function x(e){let t=await g(),r={...e,id:(0,o.A)(),orderNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`EB${e.slice(-6)}${t}`}(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await f(t),r}async function w(){return await g()}async function b(e){return(await g()).find(t=>t.id===e)||null}async function v(e){return(await g()).filter(t=>t.customerId===e)}async function S(e,t){let r=await g(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},await f(r),!0)}async function N(e,t){let r=await b(e);if(!r)return!1;let n={...r.payment,...t};return await S(e,{payment:n})}async function I(e,t){let r=await b(e);if(!r)return!1;let n={...t,id:(0,o.A)()},s={...r.shipping,updates:[...r.shipping.updates,n]};return await S(e,{shipping:s})}async function P(e){let t=await h(),r={...e,id:(0,o.A)(),invoiceNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,3).toUpperCase();return`INV-${e.slice(-6)}-${t}`}()};return t.push(r),await y(t),r}async function O(e){return(await h()).find(t=>t.orderId===e)||null}async function j(e,t){let r=await h(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t},await y(r),!0)}},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,580,4406,6967,1542],()=>r(10442));module.exports=n})();