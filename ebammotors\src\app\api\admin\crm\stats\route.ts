import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { 
  getAllCustomers, 
  getAllLeads, 
  getAllInteractions 
} from '@/lib/crmStorage';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get CRM statistics
    const stats = await getCRMStats();

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching CRM stats:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch CRM statistics' },
      { status: 500 }
    );
  }
}

async function getCRMStats() {
  try {
    // Get all data
    const [customers, leads, interactions] = await Promise.all([
      getAllCustomers(),
      getAllLeads(),
      getAllInteractions()
    ]);

    // Calculate customer stats
    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(c => c.status === 'active').length;
    const newCustomers = customers.filter(c => {
      const createdDate = new Date(c.createdAt);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return createdDate > thirtyDaysAgo;
    }).length;
    const vipCustomers = customers.filter(c => 
      c.membershipTier === 'Gold' || c.membershipTier === 'Platinum' || c.membershipTier === 'VIP'
    ).length;

    // Calculate lead stats
    const totalLeads = leads.length;
    const newLeads = leads.filter(l => l.status === 'new').length;
    const qualifiedLeads = leads.filter(l => l.status === 'qualified').length;
    const convertedLeads = leads.filter(l => l.status === 'won' || l.status === 'converted').length;

    // Calculate revenue stats
    const totalRevenue = customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0);
    const thisMonthRevenue = customers.reduce((sum, c) => {
      // This is a simplified calculation - in a real system you'd check actual order dates
      const lastOrderDate = c.lastOrderDate ? new Date(c.lastOrderDate) : null;
      const thisMonth = new Date();
      thisMonth.setDate(1);
      
      if (lastOrderDate && lastOrderDate >= thisMonth) {
        return sum + (c.totalSpent || 0);
      }
      return sum;
    }, 0);
    
    const customersWithOrders = customers.filter(c => c.totalOrders > 0);
    const averageOrderValue = customersWithOrders.length > 0 
      ? customersWithOrders.reduce((sum, c) => sum + c.averageOrderValue, 0) / customersWithOrders.length 
      : 0;

    // Calculate interaction stats
    const totalInteractions = interactions.length;
    const thisWeekInteractions = interactions.filter(i => {
      const interactionDate = new Date(i.createdAt);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return interactionDate > sevenDaysAgo;
    }).length;

    // Calculate response rate (simplified - percentage of interactions that are outbound)
    const outboundInteractions = interactions.filter(i => i.direction === 'outbound').length;
    const responseRate = totalInteractions > 0 ? (outboundInteractions / totalInteractions) * 100 : 0;

    // Additional analytics
    const leadConversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;
    const customerRetentionRate = totalCustomers > 0 ? (activeCustomers / totalCustomers) * 100 : 0;

    // Lead sources breakdown
    const leadSources = leads.reduce((acc, lead) => {
      acc[lead.source] = (acc[lead.source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Customer segments breakdown
    const customerSegments = customers.reduce((acc, customer) => {
      acc[customer.segment] = (acc[customer.segment] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Monthly trends (last 6 months)
    const monthlyTrends = getMonthlyTrends(customers, leads, interactions);

    // Top performing metrics
    const topCustomers = customers
      .sort((a, b) => (b.totalSpent || 0) - (a.totalSpent || 0))
      .slice(0, 5)
      .map(c => ({
        id: c.id,
        name: c.personalInfo.name,
        email: c.personalInfo.email,
        totalSpent: c.totalSpent || 0,
        totalOrders: c.totalOrders || 0,
        membershipTier: c.membershipTier
      }));

    return {
      customers: {
        total: totalCustomers,
        active: activeCustomers,
        new: newCustomers,
        vip: vipCustomers,
        retentionRate: Math.round(customerRetentionRate * 10) / 10
      },
      leads: {
        total: totalLeads,
        new: newLeads,
        qualified: qualifiedLeads,
        converted: convertedLeads,
        conversionRate: Math.round(leadConversionRate * 10) / 10
      },
      revenue: {
        total: Math.round(totalRevenue),
        thisMonth: Math.round(thisMonthRevenue),
        averageOrderValue: Math.round(averageOrderValue)
      },
      interactions: {
        total: totalInteractions,
        thisWeek: thisWeekInteractions,
        responseRate: Math.round(responseRate * 10) / 10
      },
      analytics: {
        leadSources,
        customerSegments,
        monthlyTrends,
        topCustomers
      }
    };
  } catch (error) {
    console.error('Error calculating CRM stats:', error);
    return {
      customers: { total: 0, active: 0, new: 0, vip: 0, retentionRate: 0 },
      leads: { total: 0, new: 0, qualified: 0, converted: 0, conversionRate: 0 },
      revenue: { total: 0, thisMonth: 0, averageOrderValue: 0 },
      interactions: { total: 0, thisWeek: 0, responseRate: 0 },
      analytics: {
        leadSources: {},
        customerSegments: {},
        monthlyTrends: [],
        topCustomers: []
      }
    };
  }
}

function getMonthlyTrends(customers: any[], leads: any[], interactions: any[]) {
  const trends = [];
  const now = new Date();
  
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const nextDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
    
    const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
    
    const customersThisMonth = customers.filter(c => {
      const createdDate = new Date(c.createdAt);
      return createdDate >= date && createdDate < nextDate;
    }).length;
    
    const leadsThisMonth = leads.filter(l => {
      const createdDate = new Date(l.createdAt);
      return createdDate >= date && createdDate < nextDate;
    }).length;
    
    const interactionsThisMonth = interactions.filter(i => {
      const createdDate = new Date(i.createdAt);
      return createdDate >= date && createdDate < nextDate;
    }).length;
    
    const revenueThisMonth = customers.reduce((sum, c) => {
      if (c.lastOrderDate) {
        const orderDate = new Date(c.lastOrderDate);
        if (orderDate >= date && orderDate < nextDate) {
          return sum + (c.totalSpent || 0);
        }
      }
      return sum;
    }, 0);
    
    trends.push({
      month: monthKey,
      customers: customersThisMonth,
      leads: leadsThisMonth,
      interactions: interactionsThisMonth,
      revenue: Math.round(revenueThisMonth)
    });
  }
  
  return trends;
}
