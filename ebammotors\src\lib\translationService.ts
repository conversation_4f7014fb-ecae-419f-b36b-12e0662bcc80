/**
 * Translation Service for EBAM Motors
 * Handles automatic translation of reviews between English and Japanese
 */

interface TranslationResponse {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
}

/**
 * Simple translation mappings for common review phrases
 * This provides basic translation without external APIs
 */
const translationMappings = {
  // English to Japanese
  'en-ja': {
    'excellent': '素晴らしい',
    'great': '素晴らしい',
    'good': '良い',
    'amazing': '驚くべき',
    'perfect': '完璧',
    'wonderful': '素晴らしい',
    'fantastic': '素晴らしい',
    'outstanding': '優秀',
    'professional': 'プロフェッショナル',
    'service': 'サービス',
    'quality': '品質',
    'car': '車',
    'vehicle': '車両',
    'delivery': '配送',
    'shipping': '配送',
    'fast': '速い',
    'quick': '迅速',
    'reliable': '信頼できる',
    'trustworthy': '信頼できる',
    'recommend': 'おすすめ',
    'satisfied': '満足',
    'happy': '満足',
    'experience': '体験',
    'customer': '顧客',
    'team': 'チーム',
    'staff': 'スタッフ',
    'price': '価格',
    'value': '価値',
    'condition': '状態',
    'thank you': 'ありがとうございます',
    'thanks': 'ありがとう'
  },
  // Japanese to English
  'ja-en': {
    '素晴らしい': 'excellent',
    '良い': 'good',
    '驚くべき': 'amazing',
    '完璧': 'perfect',
    '優秀': 'outstanding',
    'プロフェッショナル': 'professional',
    'サービス': 'service',
    '品質': 'quality',
    '車': 'car',
    '車両': 'vehicle',
    '配送': 'delivery',
    '速い': 'fast',
    '迅速': 'quick',
    '信頼できる': 'reliable',
    'おすすめ': 'recommend',
    '満足': 'satisfied',
    '体験': 'experience',
    '顧客': 'customer',
    'チーム': 'team',
    'スタッフ': 'staff',
    '価格': 'price',
    '価値': 'value',
    '状態': 'condition',
    'ありがとうございます': 'thank you',
    'ありがとう': 'thanks'
  }
};

/**
 * Template-based translations for common review patterns
 */
const reviewTemplates = {
  'en-ja': {
    'excellent service': '優れたサービス',
    'great experience': '素晴らしい体験',
    'highly recommend': '強くお勧めします',
    'professional team': 'プロフェッショナルなチーム',
    'quality vehicle': '高品質な車両',
    'fast delivery': '迅速な配送',
    'good condition': '良い状態',
    'satisfied customer': '満足した顧客',
    'will use again': 'また利用します',
    'thank you': 'ありがとうございます'
  },
  'ja-en': {
    '優れたサービス': 'excellent service',
    '素晴らしい体験': 'great experience',
    '強くお勧めします': 'highly recommend',
    'プロフェッショナルなチーム': 'professional team',
    '高品質な車両': 'quality vehicle',
    '迅速な配送': 'fast delivery',
    '良い状態': 'good condition',
    '満足した顧客': 'satisfied customer',
    'また利用します': 'will use again',
    'ありがとうございます': 'thank you'
  }
};

/**
 * Detect the language of a text
 */
export function detectLanguage(text: string): 'en' | 'ja' {
  // Simple detection based on character patterns
  const japanesePattern = /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/;
  return japanesePattern.test(text) ? 'ja' : 'en';
}

/**
 * Translate text using simple word/phrase mapping
 */
export async function translateText(
  text: string, 
  targetLanguage: 'en' | 'ja',
  sourceLanguage?: 'en' | 'ja'
): Promise<TranslationResponse> {
  const detectedLanguage = sourceLanguage || detectLanguage(text);
  
  // If source and target are the same, return original text
  if (detectedLanguage === targetLanguage) {
    return {
      translatedText: text,
      sourceLanguage: detectedLanguage,
      targetLanguage
    };
  }

  const translationKey = `${detectedLanguage}-${targetLanguage}` as keyof typeof translationMappings;
  const mappings = translationMappings[translationKey] || {};
  const templates = reviewTemplates[translationKey] || {};

  let translatedText = text.toLowerCase();

  // First, try template matching for common phrases
  for (const [source, target] of Object.entries(templates)) {
    const regex = new RegExp(source.toLowerCase(), 'gi');
    translatedText = translatedText.replace(regex, target);
  }

  // Then, word-by-word translation
  for (const [source, target] of Object.entries(mappings)) {
    const regex = new RegExp(`\\b${source.toLowerCase()}\\b`, 'gi');
    translatedText = translatedText.replace(regex, target);
  }

  // Capitalize first letter
  translatedText = translatedText.charAt(0).toUpperCase() + translatedText.slice(1);

  return {
    translatedText,
    sourceLanguage: detectedLanguage,
    targetLanguage
  };
}

/**
 * Generate a basic translation for a review
 * This is a fallback when no specific mappings are found
 */
export function generateBasicTranslation(text: string, targetLanguage: 'en' | 'ja'): string {
  const sourceLanguage = detectLanguage(text);
  
  if (sourceLanguage === targetLanguage) {
    return text;
  }

  // Basic fallback translations
  if (targetLanguage === 'ja') {
    return 'EBAM Motorsでの素晴らしい体験でした。高品質なサービスと車両をありがとうございました。';
  } else {
    return 'Great experience with EBAM Motors. Thank you for the quality service and vehicle.';
  }
}

/**
 * Translate review content including name and location if needed
 */
export async function translateReviewContent(
  review: {
    name: string;
    location: string;
    review: string;
    title?: string;
  },
  targetLanguage: 'en' | 'ja'
): Promise<{
  name: string;
  location: string;
  review: string;
  title?: string;
}> {
  const translatedReview = await translateText(review.review, targetLanguage);
  let translatedTitle = review.title;
  
  if (review.title) {
    const titleTranslation = await translateText(review.title, targetLanguage);
    translatedTitle = titleTranslation.translatedText;
  }

  return {
    name: review.name, // Keep names as-is
    location: review.location, // Keep locations as-is
    review: translatedReview.translatedText,
    title: translatedTitle
  };
}
