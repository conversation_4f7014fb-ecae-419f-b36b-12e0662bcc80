import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get admin users (mock data for now)
    const users = await getAdminUsers();

    return NextResponse.json({
      success: true,
      users
    });
  } catch (error) {
    console.error('Error fetching admin users:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch admin users' },
      { status: 500 }
    );
  }
}

async function getAdminUsers() {
  // In a real implementation, this would fetch from a user management system
  const mockUsers = [
    {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      role: 'super_admin' as const,
      status: 'active' as const,
      lastLogin: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago
      permissions: [
        'users.create',
        'users.read',
        'users.update',
        'users.delete',
        'cars.create',
        'cars.read',
        'cars.update',
        'cars.delete',
        'orders.create',
        'orders.read',
        'orders.update',
        'orders.delete',
        'reviews.moderate',
        'analytics.view',
        'security.manage',
        'settings.manage'
      ]
    },
    {
      id: '2',
      username: 'moderator1',
      email: '<EMAIL>',
      role: 'moderator' as const,
      status: 'active' as const,
      lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago
      permissions: [
        'cars.read',
        'cars.update',
        'orders.read',
        'orders.update',
        'reviews.moderate',
        'analytics.view'
      ]
    },
    {
      id: '3',
      username: 'viewer1',
      email: '<EMAIL>',
      role: 'viewer' as const,
      status: 'active' as const,
      lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(), // 7 days ago
      permissions: [
        'cars.read',
        'orders.read',
        'analytics.view'
      ]
    },
    {
      id: '4',
      username: 'suspended_user',
      email: '<EMAIL>',
      role: 'admin' as const,
      status: 'suspended' as const,
      lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(), // 5 days ago
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20).toISOString(), // 20 days ago
      permissions: [
        'cars.read',
        'cars.update',
        'orders.read',
        'orders.update',
        'reviews.moderate'
      ]
    }
  ];

  return mockUsers;
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { username, email, role, permissions } = await request.json();

    if (!username || !email || !role) {
      return NextResponse.json(
        { success: false, message: 'Username, email, and role are required' },
        { status: 400 }
      );
    }

    // Validate role
    const validRoles = ['super_admin', 'admin', 'moderator', 'viewer'];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { success: false, message: 'Invalid role' },
        { status: 400 }
      );
    }

    // In a real implementation, this would create the user in the database
    const newUser = {
      id: Date.now().toString(),
      username,
      email,
      role,
      status: 'active' as const,
      lastLogin: undefined,
      createdAt: new Date().toISOString(),
      permissions: permissions || getDefaultPermissions(role)
    };

    console.log('New admin user created:', newUser);

    return NextResponse.json({
      success: true,
      message: 'Admin user created successfully',
      user: newUser
    });

  } catch (error) {
    console.error('Error creating admin user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create admin user' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { userId, updates } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // In a real implementation, this would update the user in the database
    console.log('Admin user updated:', { userId, updates });

    return NextResponse.json({
      success: true,
      message: 'Admin user updated successfully'
    });

  } catch (error) {
    console.error('Error updating admin user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update admin user' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // In a real implementation, this would delete the user from the database
    console.log('Admin user deleted:', userId);

    return NextResponse.json({
      success: true,
      message: 'Admin user deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting admin user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete admin user' },
      { status: 500 }
    );
  }
}

function getDefaultPermissions(role: string): string[] {
  switch (role) {
    case 'super_admin':
      return [
        'users.create', 'users.read', 'users.update', 'users.delete',
        'cars.create', 'cars.read', 'cars.update', 'cars.delete',
        'orders.create', 'orders.read', 'orders.update', 'orders.delete',
        'reviews.moderate', 'analytics.view', 'security.manage', 'settings.manage'
      ];
    case 'admin':
      return [
        'cars.create', 'cars.read', 'cars.update', 'cars.delete',
        'orders.create', 'orders.read', 'orders.update', 'orders.delete',
        'reviews.moderate', 'analytics.view'
      ];
    case 'moderator':
      return [
        'cars.read', 'cars.update',
        'orders.read', 'orders.update',
        'reviews.moderate', 'analytics.view'
      ];
    case 'viewer':
      return [
        'cars.read', 'orders.read', 'analytics.view'
      ];
    default:
      return ['cars.read'];
  }
}
