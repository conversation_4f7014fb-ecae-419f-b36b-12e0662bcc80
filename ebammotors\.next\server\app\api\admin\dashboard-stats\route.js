(()=>{var e={};e.id=9548,e.ids=[7990,9548],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>p,Tq:()=>h,bS:()=>d,fF:()=>l,mU:()=>c});var s=r(85663),a=r(43205),i=r.n(a);let n=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await s.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),r=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(t,n,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let e=Date.now();for(let[t,r]of o.entries())e>r.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function l(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=i().verify(e,n);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let r=Date.now();return r>t.expiresAt?(o.delete(e),null):(t.lastActivity=r,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let m=new Map;function p(e){let t=Date.now(),r=m.get(e);return!r||t-r.lastAttempt>9e5?(m.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-r.lastAttempt)}:(r.count++,r.lastAttempt=t,m.set(e,r),{allowed:!0,remainingAttempts:5-r.count})}function h(e){m.delete(e)}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58655:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>w,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(77268),c=r(83376);async function d(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await l();return o.NextResponse.json(t)}catch(e){return console.error("Dashboard stats error:",e),o.NextResponse.json({success:!1,message:"Failed to fetch dashboard stats"},{status:500})}}async function l(){try{let e=await (0,c.sql)`SELECT COUNT(*) as count FROM cars`,t=parseInt(e.rows[0]?.count||"0"),r=await (0,c.sql)`
      SELECT COUNT(DISTINCT email) as count 
      FROM (
        SELECT customer_email as email FROM orders
        UNION
        SELECT customer_email as email FROM reviews WHERE customer_email IS NOT NULL
      ) as customers
    `,s=parseInt(r.rows[0]?.count||"0"),a=await (0,c.sql)`SELECT COUNT(*) as count FROM orders`,i=parseInt(a.rows[0]?.count||"0"),n=await (0,c.sql)`SELECT COUNT(*) as count FROM reviews WHERE status = 'pending'`,o=parseInt(n.rows[0]?.count||"0"),u=await (0,c.sql)`
      SELECT SUM(total_amount) as total 
      FROM orders 
      WHERE status IN ('completed', 'shipped', 'delivered')
    `,d=parseFloat(u.rows[0]?.total||"0"),l=await m(),h=await p();return{totalCars:t,totalCustomers:s,totalOrders:i,pendingReviews:o,totalRevenue:d,monthlyGrowth:12.5,systemHealth:h,recentActivity:l}}catch(e){return console.error("Error fetching dashboard stats:",e),{totalCars:0,totalCustomers:0,totalOrders:0,pendingReviews:0,totalRevenue:0,monthlyGrowth:0,systemHealth:"error",recentActivity:[]}}}async function m(){try{let e=[];return(await (0,c.sql)`
      SELECT id, customer_name, total_amount, created_at, status
      FROM orders 
      ORDER BY created_at DESC 
      LIMIT 3
    `).rows.forEach(t=>{e.push({id:`order-${t.id}`,type:"order",message:`New order from ${t.customer_name} - \xa5${t.total_amount}`,timestamp:h(t.created_at),status:"completed"===t.status?"success":"warning"})}),(await (0,c.sql)`
      SELECT id, customer_name, rating, status, created_at
      FROM reviews 
      ORDER BY created_at DESC 
      LIMIT 2
    `).rows.forEach(t=>{e.push({id:`review-${t.id}`,type:"review",message:`${"pending"===t.status?"Review pending approval":"Review approved"} - ${t.rating} stars`,timestamp:h(t.created_at),status:"approved"===t.status?"success":"warning"})}),(await (0,c.sql)`
      SELECT car_id, make, model, year, created_at
      FROM cars 
      ORDER BY created_at DESC 
      LIMIT 2
    `).rows.forEach(t=>{e.push({id:`car-${t.car_id}`,type:"car",message:`${t.make} ${t.model} ${t.year} added to inventory`,timestamp:h(t.created_at),status:"success"})}),e.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,5)}catch(e){return console.error("Error fetching recent activity:",e),[{id:"1",type:"system",message:"Dashboard initialized",timestamp:"Just now",status:"success"}]}}async function p(){try{return await (0,c.sql)`SELECT 1`,"healthy"}catch(e){return console.error("System health check failed:",e),"error"}}function h(e){let t=new Date(e),r=new Date().getTime()-t.getTime(),s=Math.floor(r/6e4),a=Math.floor(r/36e5),i=Math.floor(r/864e5);return s<1?"Just now":s<60?`${s} minute${s>1?"s":""} ago`:a<24?`${a} hour${a>1?"s":""} ago`:i<7?`${i} day${i>1?"s":""} ago`:t.toLocaleDateString()}let w=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/dashboard-stats/route",pathname:"/api/admin/dashboard-stats",filename:"route",bundlePath:"app/api/admin/dashboard-stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\dashboard-stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:y}=w;function E(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77268:(e,t,r)=>{"use strict";r.d(t,{iY:()=>a}),r(32190);var s=r(12909);function a(e,t){let r=e.headers.get("authorization"),a=e.cookies.get("admin_session")?.value,i=(0,s.fF)(r,a);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let n=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return n&&n===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7696,3376],()=>r(58655));module.exports=s})();