"use strict";(()=>{var e={};e.id=2939,e.ids=[2939],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23870:(e,s,r)=>{r.d(s,{A:()=>c});var t=r(55511);let o={randomUUID:t.randomUUID},n=new Uint8Array(256),a=n.length,u=[];for(let e=0;e<256;++e)u.push((e+256).toString(16).slice(1));let c=function(e,s,r){if(o.randomUUID&&!s&&!e)return o.randomUUID();let c=(e=e||{}).random??e.rng?.()??(a>n.length-16&&((0,t.randomFillSync)(n),a=0),n.slice(a,a+=16));if(c.length<16)throw Error("Random bytes length must be >= 16");if(c[6]=15&c[6]|64,c[8]=63&c[8]|128,s){if((r=r||0)<0||r+16>s.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)s[r+e]=c[e];return s}return function(e,s=0){return(u[e[s+0]]+u[e[s+1]]+u[e[s+2]]+u[e[s+3]]+"-"+u[e[s+4]]+u[e[s+5]]+"-"+u[e[s+6]]+u[e[s+7]]+"-"+u[e[s+8]]+u[e[s+9]]+"-"+u[e[s+10]]+u[e[s+11]]+u[e[s+12]]+u[e[s+13]]+u[e[s+14]]+u[e[s+15]]).toLowerCase()}(c)}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},60140:(e,s,r)=>{r.r(s),r.d(s,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var t={};r.r(t),r.d(t,{GET:()=>p,POST:()=>i});var o=r(96559),n=r(48088),a=r(37719),u=r(32190),c=r(6234);async function i(e){try{let{action:s,adminKey:r,...t}=await e.json();if("webhook"!==s){let e=process.env.ADMIN_PASSWORD||"admin123";if(r!==e)return u.NextResponse.json({success:!1,message:"Unauthorized"},{status:401})}switch(s){case"process_pending":return await (0,c.kA)(),u.NextResponse.json({success:!0,message:"Pending follow-ups processed successfully"});case"schedule_inactive":return await (0,c.GJ)(),u.NextResponse.json({success:!0,message:"Inactive customer follow-ups scheduled successfully"});case"abandoned_cart":if(!t.customerId||!t.cartData)return u.NextResponse.json({success:!1,message:"Customer ID and cart data are required"},{status:400});return await (0,c.yV)(t.customerId,t.cartData),u.NextResponse.json({success:!0,message:"Abandoned cart follow-up scheduled successfully"});case"order_delivered":if(!t.customerId||!t.orderId||!t.orderData)return u.NextResponse.json({success:!1,message:"Customer ID, order ID, and order data are required"},{status:400});return await (0,c._S)(t.customerId,t.orderId,t.orderData),u.NextResponse.json({success:!0,message:"Order delivery follow-up scheduled successfully"});case"webhook":let o=e.headers.get("authorization"),n=`Bearer ${process.env.CRON_SECRET||"default-cron-secret"}`;if(o!==n)return u.NextResponse.json({success:!1,message:"Unauthorized webhook"},{status:401});return await Promise.all([(0,c.kA)(),(0,c.GJ)()]),u.NextResponse.json({success:!0,message:"Webhook processed successfully"});default:return u.NextResponse.json({success:!1,message:"Invalid action"},{status:400})}}catch(e){return console.error("Error processing follow-up action:",e),u.NextResponse.json({success:!1,message:"Failed to process follow-up action"},{status:500})}}async function p(e){try{let{searchParams:s}=new URL(e.url),t=s.get("adminKey"),o=process.env.ADMIN_PASSWORD||"admin123";if(t!==o)return u.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{getPendingFollowUps:n}=await Promise.resolve().then(r.bind(r,53190)),a=await n();return u.NextResponse.json({success:!0,data:{pendingFollowUps:a.length,lastProcessed:new Date().toISOString(),nextScheduledRun:new Date(Date.now()+36e5).toISOString()}})}catch(e){return console.error("Error getting follow-up status:",e),u.NextResponse.json({success:!1,message:"Failed to get follow-up status"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/followups/process/route",pathname:"/api/followups/process",filename:"route",bundlePath:"app/api/followups/process/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\followups\\process\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:f}=d;function g(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,580,6967,1542],()=>r(60140));module.exports=t})();