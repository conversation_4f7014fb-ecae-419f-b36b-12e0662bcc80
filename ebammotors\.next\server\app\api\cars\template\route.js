(()=>{var e={};e.id=9944,e.ids=[9944],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67321:(e,t,o)=>{"use strict";o.r(t),o.d(t,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>u});var a={};o.r(a),o.d(a,{GET:()=>d,POST:()=>_});var i=o(96559),s=o(48088),r=o(37719),n=o(32190);let l=["car_id","make","model","year","price","original_price","mileage","fuel_type","transmission","engine_size","drive_type","seats","doors","body_type","body_condition","interior_condition","exterior_color","interior_color","main_image","images","image_folder","specs","features","status","stock_quantity","location","description","is_featured"],c=[{car_id:"toyota_voxy_2012_TVXYA",make:"Toyota",model:"Voxy",year:"2012",price:"300000",original_price:"320000",mileage:"85000",fuel_type:"Gasoline",transmission:"Automatic",engine_size:"2.0L",drive_type:"FWD",seats:"8",doors:"4",body_type:"Van",body_condition:"Good",interior_condition:"Good",exterior_color:"Silver",interior_color:"Black",main_image:"/car-models/toyota-voxy/toyota_voxy_2012_TVXYA/toyota_voxy_2012_TVXYA001.jpg",images:"/car-models/toyota-voxy/toyota_voxy_2012_TVXYA/toyota_voxy_2012_TVXYA001.jpg,/car-models/toyota-voxy/toyota_voxy_2012_TVXYA/toyota_voxy_2012_TVXYA002.jpg",image_folder:"/car-models/toyota-voxy/toyota_voxy_2012_TVXYA",specs:"8-seater,Automatic,85k km,Gasoline",features:"Air Conditioning,Power Steering,Electric Windows",status:"Available",stock_quantity:"1",location:"Japan",description:"Well-maintained Toyota Voxy with low mileage. Perfect family car with spacious interior.",is_featured:"false"},{car_id:"toyota_noah_2015_TNAHA",make:"Toyota",model:"Noah",year:"2015",price:"350000",original_price:"",mileage:"65000",fuel_type:"Gasoline",transmission:"Automatic",engine_size:"2.0L",drive_type:"FWD",seats:"8",doors:"4",body_type:"Van",body_condition:"Excellent",interior_condition:"Excellent",exterior_color:"White",interior_color:"Gray",main_image:"/car-models/toyota-noah/toyota_noah_2015_TNAHA/toyota_noah_2015_TNAHA001.jpg",images:"/car-models/toyota-noah/toyota_noah_2015_TNAHA/toyota_noah_2015_TNAHA001.jpg,/car-models/toyota-noah/toyota_noah_2015_TNAHA/toyota_noah_2015_TNAHA002.jpg",image_folder:"/car-models/toyota-noah/toyota_noah_2015_TNAHA",specs:"8-seater,Automatic,65k km,Gasoline",features:"Air Conditioning,Power Steering,Navigation System,Backup Camera",status:"Available",stock_quantity:"1",location:"Japan",description:"Excellent condition Toyota Noah with modern features and low mileage.",is_featured:"true"},{car_id:"toyota_sienta_2018_TSIHA",make:"Toyota",model:"Sienta",year:"2018",price:"320000",original_price:"",mileage:"45000",fuel_type:"Hybrid",transmission:"CVT",engine_size:"1.5L",drive_type:"FWD",seats:"7",doors:"4",body_type:"Van",body_condition:"Excellent",interior_condition:"Good",exterior_color:"Blue",interior_color:"Black",main_image:"/car-models/toyota-sienta/toyota_sienta_2018_TSIHA/toyota_sienta_2018_TSIHA001.jpg",images:"/car-models/toyota-sienta/toyota_sienta_2018_TSIHA/toyota_sienta_2018_TSIHA001.jpg",image_folder:"/car-models/toyota-sienta/toyota_sienta_2018_TSIHA",specs:"7-seater,CVT,45k km,Hybrid",features:"Hybrid Engine,Air Conditioning,Power Steering,Eco Mode",status:"Available",stock_quantity:"1",location:"Japan",description:"Fuel-efficient hybrid Toyota Sienta with excellent fuel economy.",is_featured:"false"}];async function d(e){try{let{searchParams:t}=new URL(e.url),o=t.get("adminKey"),a="true"===t.get("with_samples"),i=process.env.ADMIN_PASSWORD||"admin123";if(o!==i)return n.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=l.join(",")+"\n";a&&c.forEach(e=>{let t=l.map(t=>{let o=e[t]||"";return o.includes(",")||o.includes('"')||o.includes("\n")?`"${o.replace(/"/g,'""')}"`:o});s+=t.join(",")+"\n"});let r=new Headers;return r.set("Content-Type","text/csv"),r.set("Content-Disposition",`attachment; filename="car_import_template${a?"_with_samples":""}.csv"`),new n.NextResponse(s,{headers:r})}catch(e){return console.error("Error generating CSV template:",e),n.NextResponse.json({success:!1,message:"Failed to generate CSV template"},{status:500})}}async function _(e){try{let{adminKey:t}=await e.json(),o=process.env.ADMIN_PASSWORD||"admin123";if(t!==o)return n.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});return n.NextResponse.json({success:!0,template_info:{headers:l,required_fields:["car_id","make","model","year","price"],optional_fields:l.filter(e=>!["car_id","make","model","year","price"].includes(e)),validation_rules:{car_id:"Unique identifier, no spaces or special characters except underscore",make:"Car manufacturer name (e.g., Toyota, Honda)",model:"Car model name (e.g., Voxy, Noah)",year:"Manufacturing year, must be between 1990 and 2030",price:"Price in Yen, must be a positive number",mileage:"Mileage in kilometers, must be non-negative",seats:"Number of seats, must be between 1 and 50",doors:"Number of doors, must be between 1 and 10",stock_quantity:"Stock quantity, must be non-negative",is_featured:"Boolean value: true/false, 1/0, yes/no",images:"Comma-separated list of image URLs",specs:"Comma-separated list of specifications",features:"Comma-separated list of features",status:"One of: Available, Reserved, Sold, Pending",body_condition:"One of: Excellent, Good, Fair, Needs Work",fuel_type:"One of: Gasoline, Hybrid, Electric, Diesel",transmission:"One of: Automatic, Manual, CVT"},sample_data:c,tips:["Use unique car_id values to avoid duplicates","Price should be in Japanese Yen without currency symbols","Use comma-separated values for arrays (images, specs, features)","Boolean fields accept: true/false, 1/0, yes/no","Leave optional fields empty if not applicable","Ensure image URLs are accessible and properly formatted","Test with a small batch first before importing large datasets"]}})}catch(e){return console.error("Error getting template info:",e),n.NextResponse.json({success:!1,message:"Failed to get template information"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/cars/template/route",pathname:"/api/cars/template",filename:"route",bundlePath:"app/api/cars/template/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\cars\\template\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:u,serverHooks:y}=m;function g(){return(0,r.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:u})}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),a=t.X(0,[4447,580],()=>o(67321));module.exports=a})();