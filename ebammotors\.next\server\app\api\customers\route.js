(()=>{var e={};e.id=1941,e.ids=[1941],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12955:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GET:()=>a,PATCH:()=>o,POST:()=>i,syncCustomerFromOrder:()=>u});var s=r(32190),n=r(53190);async function a(e){try{let{searchParams:t}=new URL(e.url),r=t.get("adminKey"),a=t.get("id"),i=t.get("email"),o="true"===t.get("overview"),u=process.env.ADMIN_PASSWORD||"admin123";if(r!==u)return s.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(a&&o){let e=await (0,n.oP)(a);if(!e)return s.NextResponse.json({success:!1,message:"Customer not found"},{status:404});return s.NextResponse.json({success:!0,data:e})}if(a){let e=await (0,n.getCustomerById)(a);if(!e)return s.NextResponse.json({success:!1,message:"Customer not found"},{status:404});return s.NextResponse.json({success:!0,customer:e})}if(i){let e=await (0,n.getCustomerByEmail)(i);if(!e)return s.NextResponse.json({success:!1,message:"Customer not found"},{status:404});return s.NextResponse.json({success:!0,customer:e})}let c=await (0,n.Rf)();return c.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()),s.NextResponse.json({success:!0,customers:c})}catch(e){return console.error("Error fetching customers:",e),s.NextResponse.json({success:!1,message:"Failed to fetch customers"},{status:500})}}async function i(e){try{let t=await e.json();if(!t.personalInfo?.name||!t.personalInfo?.email)return s.NextResponse.json({success:!1,message:"Name and email are required"},{status:400});let a={status:"active",segment:"new",loyaltyPoints:0,membershipTier:"Bronze",totalSpent:0,totalOrders:0,averageOrderValue:0,acquisitionSource:"website",tags:[],preferences:{notifications:{email:!0,sms:!1,push:!0,marketing:!1},currency:"JPY",communicationPreference:"email"},address:{},...t},i=await (0,n.Y2)(a),{createInteraction:o}=await Promise.resolve().then(r.bind(r,53190));await o({customerId:i.id,type:"support",direction:"inbound",channel:"website",content:"Customer profile created/updated",subject:"Customer Registration",tags:["customer_registration","profile_update"],createdBy:"system"});let{scheduleAutoFollowupForCustomer:u}=await Promise.all([r.e(6967),r.e(2273)]).then(r.bind(r,62273));return await u(i.id,a),s.NextResponse.json({success:!0,message:"Customer saved successfully",customer:i})}catch(e){return console.error("Error creating/updating customer:",e),s.NextResponse.json({success:!1,message:"Failed to save customer"},{status:500})}}async function o(e){try{let{customerId:t,adminKey:a,...i}=await e.json(),o=process.env.ADMIN_PASSWORD||"admin123";if(a!==o)return s.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!t)return s.NextResponse.json({success:!1,message:"Customer ID is required"},{status:400});if(!await (0,n.Gk)(t,i))return s.NextResponse.json({success:!1,message:"Customer not found"},{status:404});let{createInteraction:u}=await Promise.resolve().then(r.bind(r,53190));return await u({customerId:t,type:"support",direction:"outbound",channel:"website",content:`Customer profile updated: ${Object.keys(i).join(", ")}`,subject:"Profile Update",tags:["profile_update","admin_action"],createdBy:"admin"}),s.NextResponse.json({success:!0,message:"Customer updated successfully"})}catch(e){return console.error("Error updating customer:",e),s.NextResponse.json({success:!1,message:"Failed to update customer"},{status:500})}}async function u(e){try{let t={personalInfo:{name:e.customerInfo.name,email:e.customerInfo.email,phone:e.customerInfo.phone},address:e.customerInfo.address,preferences:{notifications:{email:!0,sms:!1,push:!0,marketing:!1},currency:e.currency||"JPY",communicationPreference:"email"},status:"active",segment:"regular",loyaltyPoints:0,membershipTier:"Bronze",totalSpent:e.totalAmount,totalOrders:1,averageOrderValue:e.totalAmount,acquisitionSource:"order",tags:["customer"]},r=await (0,n.Y2)(t);if(r){let t=await (0,n.getCustomerById)(r.id);t&&t.totalOrders>0&&await (0,n.Gk)(r.id,{totalSpent:t.totalSpent+e.totalAmount,totalOrders:t.totalOrders+1,averageOrderValue:(t.totalSpent+e.totalAmount)/(t.totalOrders+1),lastOrderDate:new Date().toISOString()})}return r}catch(e){return console.error("Error syncing customer from order:",e),null}}},23870:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(55511);let n={randomUUID:s.randomUUID},a=new Uint8Array(256),i=a.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(n.randomUUID&&!t&&!e)return n.randomUUID();let u=(e=e||{}).random??e.rng?.()??(i>a.length-16&&((0,s.randomFillSync)(a),i=0),a.slice(i,i+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=u[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(u)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45173:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>d,routeModule:()=>o,serverHooks:()=>l,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>c});var s=r(96559),n=r(48088),a=r(37719),i=r(12955);let o=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/customers/route",pathname:"/api/customers",filename:"route",bundlePath:"app/api/customers/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\customers\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:u,workUnitAsyncStorage:c,serverHooks:l}=o;function d(){return(0,a.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:c})}},53190:(e,t,r)=>{"use strict";r.d(t,{Gk:()=>k,Gq:()=>L,HR:()=>v,Kt:()=>R,Q6:()=>j,Rf:()=>b,XL:()=>z,Y2:()=>F,_Y:()=>W,aN:()=>Z,createCustomerActivity:()=>Q,createInteraction:()=>J,dD:()=>V,fE:()=>_,getAllFollowUps:()=>G,getCustomerByEmail:()=>q,getCustomerById:()=>P,getLeadById:()=>O,getPendingFollowUps:()=>H,oP:()=>ee,qz:()=>B,sr:()=>N,tR:()=>A,tS:()=>D,updateFollowUp:()=>K});var s=r(29021),n=r(33873),a=r.n(n),i=r(23870);let o=a().join(process.cwd(),"data"),u=a().join(o,"leads.json"),c=a().join(o,"customers.json"),l=a().join(o,"interactions.json"),d=a().join(o,"followups.json"),p=a().join(o,"activities.json"),m=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,f=[],w=[],g=[],y=[],h=[];async function S(){if(!m)try{await s.promises.access(o)}catch{await s.promises.mkdir(o,{recursive:!0})}}async function I(){if(m)return f;try{await S();let e=await s.promises.readFile(u,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function x(e){if(m){f=e;return}await S(),await s.promises.writeFile(u,JSON.stringify(e,null,2))}async function A(e){let t=await I(),r={...e,id:(0,i.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await x(t),r}async function D(){return await I()}async function O(e){return(await I()).find(t=>t.id===e)||null}async function j(e,t){let r=await I(),s=r.findIndex(t=>t.id===e);return -1!==s&&(r[s]={...r[s],...t,updatedAt:new Date().toISOString()},await x(r),!0)}async function N(e){let t=await I(),r=t.filter(t=>t.id!==e);return r.length!==t.length&&(await x(r),!0)}async function v(e){return(await I()).filter(t=>t.status===e)}async function R(e){return(await I()).filter(t=>t.source===e)}async function U(){if(m)return w;try{await S();let e=await s.promises.readFile(c,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function C(e){if(m){w=e;return}await S(),await s.promises.writeFile(c,JSON.stringify(e,null,2))}async function F(e){let t=await U(),r=t.findIndex(t=>t.personalInfo.email===e.personalInfo.email);if(-1!==r)return t[r]={...t[r],...e,updatedAt:new Date().toISOString()},await C(t),t[r];{let r={...e,id:(0,i.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await C(t),r}}async function b(){return await U()}async function P(e){return(await U()).find(t=>t.id===e)||null}async function q(e){return(await U()).find(t=>t.personalInfo.email===e)||null}async function k(e,t){let r=await U(),s=r.findIndex(t=>t.id===e);return -1!==s&&(r[s]={...r[s],...t,updatedAt:new Date().toISOString()},await C(r),!0)}async function T(){if(m)return g;try{await S();let e=await s.promises.readFile(l,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function E(e){if(m){g=e;return}await S(),await s.promises.writeFile(l,JSON.stringify(e,null,2))}async function J(e){let t=await T(),r={...e,id:(0,i.A)(),createdAt:new Date().toISOString()};return t.push(r),await E(t),r}async function _(){return await T()}async function B(e){return(await T()).filter(t=>t.customerId===e)}async function L(e){return(await T()).filter(t=>t.leadId===e)}async function Y(){if(m)return y;try{await S();let e=await s.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function M(e){if(m){y=e;return}await S(),await s.promises.writeFile(d,JSON.stringify(e,null,2))}async function z(e){let t=await Y(),r={...e,id:(0,i.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await M(t),r}async function G(){return await Y()}async function V(e){return(await Y()).filter(t=>t.status===e)}async function H(){let e=await Y(),t=new Date().toISOString();return e.filter(e=>"pending"===e.status&&e.scheduledDate<=t)}async function K(e,t){let r=await Y(),s=r.findIndex(t=>t.id===e);return -1!==s&&(r[s]={...r[s],...t,updatedAt:new Date().toISOString()},await M(r),!0)}async function W(e){return(await Y()).filter(t=>t.customerId===e)}async function $(){if(m)return h;try{await S();let e=await s.promises.readFile(p,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function X(e){if(m){h=e;return}await S(),await s.promises.writeFile(p,JSON.stringify(e,null,2))}async function Q(e){let t=await $(),r={...e,id:(0,i.A)(),timestamp:new Date().toISOString()};return t.push(r),await X(t),r}async function Z(e){return(await $()).filter(t=>t.customerId===e)}async function ee(e){let t=await P(e);if(!t)return null;let r=await B(e),s=await W(e),n=await Z(e);return{customer:t,stats:{totalInteractions:r.length,pendingFollowUps:s.filter(e=>"pending"===e.status).length,recentActivities:n.filter(e=>new Date(e.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:r.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())[0]?.createdAt},recentInteractions:r.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()).slice(0,5),upcomingFollowUps:s.filter(e=>"pending"===e.status).sort((e,t)=>new Date(e.scheduledDate).getTime()-new Date(t.scheduledDate).getTime()).slice(0,3)}}},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(45173));module.exports=s})();