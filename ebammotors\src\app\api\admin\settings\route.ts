import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';

// GET - Fetch admin settings
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Return default settings (in a real app, these would come from database)
    const settings = {
      general: {
        siteName: 'EBAM Motors',
        siteDescription: 'Premium Japanese Cars for Ghana and Africa',
        contactEmail: '<EMAIL>',
        contactPhone: '+233245375692',
        businessAddress: 'Kumasi, Ghana',
        currency: 'JPY',
        timezone: 'Africa/Accra'
      },
      email: {
        provider: 'resend',
        smtpHost: 'smtp.resend.com',
        smtpPort: 587,
        smtpUser: 'resend',
        smtpPassword: '***',
        fromEmail: '<EMAIL>',
        fromName: 'EBAM Motors'
      },
      notifications: {
        emailNotifications: true,
        orderNotifications: true,
        reviewNotifications: true,
        systemAlerts: true,
        dailySummary: false
      },
      security: {
        sessionTimeout: 60,
        maxLoginAttempts: 5,
        passwordMinLength: 8,
        requireTwoFactor: false,
        allowedIPs: []
      },
      appearance: {
        theme: 'light',
        primaryColor: '#2563eb',
        logoUrl: '/logo.png',
        faviconUrl: '/favicon.ico'
      }
    };

    return NextResponse.json({
      success: true,
      settings
    });
  } catch (error) {
    console.error('Error fetching admin settings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// POST - Update admin settings
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const settings = await request.json();

    // Validate settings structure
    const requiredSections = ['general', 'email', 'notifications', 'security', 'appearance'];
    for (const section of requiredSections) {
      if (!settings[section]) {
        return NextResponse.json(
          { success: false, message: `Missing ${section} settings` },
          { status: 400 }
        );
      }
    }

    // Validate email settings
    if (settings.email.fromEmail && !isValidEmail(settings.email.fromEmail)) {
      return NextResponse.json(
        { success: false, message: 'Invalid from email address' },
        { status: 400 }
      );
    }

    // Validate security settings
    if (settings.security.sessionTimeout < 5 || settings.security.sessionTimeout > 1440) {
      return NextResponse.json(
        { success: false, message: 'Session timeout must be between 5 and 1440 minutes' },
        { status: 400 }
      );
    }

    if (settings.security.maxLoginAttempts < 1 || settings.security.maxLoginAttempts > 20) {
      return NextResponse.json(
        { success: false, message: 'Max login attempts must be between 1 and 20' },
        { status: 400 }
      );
    }

    if (settings.security.passwordMinLength < 6 || settings.security.passwordMinLength > 50) {
      return NextResponse.json(
        { success: false, message: 'Password minimum length must be between 6 and 50 characters' },
        { status: 400 }
      );
    }

    // In a real application, you would save these settings to a database
    // For now, we'll just return success
    console.log('Settings updated:', settings);

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully'
    });
  } catch (error) {
    console.error('Error updating admin settings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update settings' },
      { status: 500 }
    );
  }
}

// Helper function to validate email
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
