(()=>{var e={};e.id=9261,e.ids=[9261],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28735:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(37413),n=s(65918);function o(){return(0,r.jsx)(n.default,{})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48835:(e,t,s)=>{Promise.resolve().then(s.bind(s,59957))},59957:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var r=s(60687),n=s(43210);let o=()=>!1,i=e=>{o()&&window.gtag("event",e.action,{event_category:e.category,event_label:e.label,value:e.value,...e.custom_parameters})},a=(e,t,s)=>{i({action:"view_item",category:"ecommerce",label:t,value:s,custom_parameters:{item_id:e,item_name:t,item_category:"used_cars",currency:"JPY",value:s}})},l=(e,t)=>{i({action:"search",category:"engagement",label:e,value:t,custom_parameters:{search_term:e,results_count:t}})},c=e=>{i({action:"form_submit",category:"engagement",label:e,custom_parameters:{form_type:e}})};function d(){let[e,t]=(0,n.useState)(""),s=async e=>{try{switch(e){case"car_view":a("test-car-123","Toyota Voxy 2020",25e5),t("Car view event tracked");break;case"search":l("Toyota Voxy",5),t("Search event tracked");break;case"contact":c("test_form"),t("Contact form event tracked")}}catch(e){t(`Error: ${e}`)}};return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDD0D Analytics Testing"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("button",{onClick:()=>s("car_view"),className:"w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700",children:"Test Car View Event"}),(0,r.jsx)("button",{onClick:()=>s("search"),className:"w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700",children:"Test Search Event"}),(0,r.jsx)("button",{onClick:()=>s("contact"),className:"w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700",children:"Test Contact Form Event"})]}),e&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-gray-100 rounded text-sm",children:e}),(0,r.jsx)("div",{className:"mt-4 text-xs text-gray-600",children:"Open browser DevTools → Network tab to see analytics requests"})]})}function m(){let e=e=>{alert(`Would share on ${e} - check console for details`)};return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDCF1 Social Share Testing"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Test Social Sharing:"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>e("Facebook"),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Facebook"}),(0,r.jsx)("button",{onClick:()=>e("Twitter"),className:"bg-blue-400 text-white px-4 py-2 rounded hover:bg-blue-500",children:"Twitter"}),(0,r.jsx)("button",{onClick:()=>e("WhatsApp"),className:"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700",children:"WhatsApp"})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{children:"To test full social sharing functionality:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"Import SocialShare component"}),(0,r.jsx)("li",{children:"Add social media platform configurations"}),(0,r.jsx)("li",{children:"Test on different devices and browsers"})]})]})]})]})}function p(){let[e,t]=(0,n.useState)(""),[s,o]=(0,n.useState)(null);return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDD0D Advanced Search Testing"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Search for cars...",className:"flex-1 border border-gray-300 rounded px-3 py-2"}),(0,r.jsx)("button",{onClick:()=>{o({query:e,results:[{id:1,name:"Toyota Voxy 2020",price:25e5},{id:2,name:"Honda Freed 2019",price:22e5}],timestamp:new Date().toISOString()})},className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Search"})]}),s&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Mock Search Results:"}),(0,r.jsx)("pre",{className:"text-xs overflow-auto",children:JSON.stringify(s,null,2)})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{children:"To test full advanced search functionality:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"Import AdvancedSearch component"}),(0,r.jsx)("li",{children:"Configure search filters and sorting"}),(0,r.jsx)("li",{children:"Test autocomplete and suggestions"}),(0,r.jsx)("li",{children:"Test saved searches functionality"})]})]})]})]})}function h(){let[e,t]=(0,n.useState)({}),[s,o]=(0,n.useState)(null),i=async(e,s="GET",r)=>{o(e);try{let n={method:s,headers:{"Content-Type":"application/json"}};r&&(n.body=JSON.stringify(r));let o=await fetch(e,n),i=await o.json();t(t=>({...t,[e]:{status:o.status,data:i,timestamp:new Date().toISOString()}}))}catch(s){t(t=>({...t,[e]:{error:s instanceof Error?s.message:"Unknown error",timestamp:new Date().toISOString()}}))}finally{o(null)}};return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDD0C API Testing"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 mb-4",children:[{name:"Health Check",endpoint:"/api/admin/health",method:"GET"},{name:"Analytics",endpoint:"/api/admin/analytics",method:"GET"},{name:"Notification Stats",endpoint:"/api/notifications/send",method:"GET"},{name:"Forum Posts",endpoint:"/api/community/forum/posts",method:"GET"},{name:"Social Feed",endpoint:"/api/social/feed",method:"GET"}].map(e=>(0,r.jsx)("button",{onClick:()=>i(e.endpoint,e.method),disabled:s===e.endpoint,className:"bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 text-sm",children:s===e.endpoint?"Testing...":e.name},e.endpoint))}),(0,r.jsx)("div",{className:"space-y-2 max-h-64 overflow-auto",children:Object.entries(e).map(([e,t])=>(0,r.jsxs)("div",{className:"p-3 bg-gray-100 rounded",children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:e}),(0,r.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:t.error?(0,r.jsxs)("span",{className:"text-red-600",children:["Error: ",t.error]}):(0,r.jsxs)("span",{className:"text-green-600",children:["Status: ",t.status]})}),(0,r.jsx)("pre",{className:"text-xs mt-2 overflow-auto max-h-32",children:JSON.stringify(t.data||t.error,null,2)})]},e))})]})}function u(){let[e,t]=(0,n.useState)("Not supported"),s=async()=>{if("Notification"in window){let e=await Notification.requestPermission();t(`Permission: ${e}`),"granted"===e&&new Notification("Test Notification",{body:"This is a test notification from EBAM Motors",icon:"/favicon.ico"})}else t("Notifications not supported")};return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDD14 Push Notification Testing"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)("button",{onClick:s,className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Test Browser Notification"})}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Status:"})," ",e]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{children:"To test full push notification functionality:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"Configure VAPID keys in environment variables"}),(0,r.jsx)("li",{children:"Import NotificationSettings component"}),(0,r.jsx)("li",{children:"Test subscription management"}),(0,r.jsx)("li",{children:"Test admin notification sender"}),(0,r.jsx)("li",{children:"Test on mobile devices with HTTPS"})]})]})]})]})}function x(){let[e,t]=(0,n.useState)({});return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDCF1 PWA Status"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{onClick:()=>{t({serviceWorker:"serviceWorker"in navigator,manifest:null!==document.querySelector('link[rel="manifest"]'),isStandalone:window.matchMedia("(display-mode: standalone)").matches,isInstallable:"BeforeInstallPromptEvent"in window})},className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Check PWA Status"}),(0,r.jsx)("button",{onClick:()=>{window.open("/manifest.json","_blank")},className:"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-2",children:"View Manifest"}),Object.keys(e).length>0&&(0,r.jsxs)("div",{className:"p-3 bg-gray-100 rounded",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"PWA Status:"}),(0,r.jsx)("pre",{className:"text-xs",children:JSON.stringify(e,null,2)})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Test Steps:"})}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1 mt-2",children:[(0,r.jsx)("li",{children:"Check if service worker is registered"}),(0,r.jsx)("li",{children:"Test offline functionality (DevTools → Network → Offline)"}),(0,r.jsx)("li",{children:"Look for installation prompt (wait 5 seconds)"}),(0,r.jsx)("li",{children:"Check manifest.json accessibility"})]})]})]})]})}function b(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 p-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\uD83E\uDDEA EBAM Motors - Feature Testing Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Test all advanced features from this dashboard. Open browser DevTools to monitor network requests and console logs."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsx)(d,{}),(0,r.jsx)(m,{}),(0,r.jsx)(u,{}),(0,r.jsx)(x,{}),(0,r.jsx)(h,{}),(0,r.jsx)(p,{})]}),(0,r.jsxs)("div",{className:"mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"⚠️ Testing Notes:"}),(0,r.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,r.jsx)("li",{children:"• Make sure your .env.local file has the required environment variables"}),(0,r.jsx)("li",{children:"• Some features require HTTPS (use ngrok for local testing if needed)"}),(0,r.jsx)("li",{children:"• Push notifications need VAPID keys to be configured"}),(0,r.jsx)("li",{children:"• Check browser console for any error messages"}),(0,r.jsx)("li",{children:"• Test on different browsers and devices"})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65918:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call AnalyticsTestPanel() from the server but AnalyticsTestPanel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\TestComponents.tsx","AnalyticsTestPanel"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SocialShareTestPanel() from the server but SocialShareTestPanel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\TestComponents.tsx","SocialShareTestPanel"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SearchTestPanel() from the server but SearchTestPanel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\TestComponents.tsx","SearchTestPanel"),(0,r.registerClientReference)(function(){throw Error("Attempted to call APITestPanel() from the server but APITestPanel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\TestComponents.tsx","APITestPanel"),(0,r.registerClientReference)(function(){throw Error("Attempted to call NotificationTestPanel() from the server but NotificationTestPanel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\TestComponents.tsx","NotificationTestPanel"),(0,r.registerClientReference)(function(){throw Error("Attempted to call PWATestPanel() from the server but PWATestPanel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\TestComponents.tsx","PWATestPanel");let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\TestComponents.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\TestComponents.tsx","default")},66965:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(65239),n=s(48088),o=s(88170),i=s.n(o),a=s(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(t,l);let c={children:["",{children:["test-features",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28735)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\test-features\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\test-features\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/test-features/page",pathname:"/test-features",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72811:(e,t,s)=>{Promise.resolve().then(s.bind(s,65918))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7445,1658,5839],()=>s(66965));module.exports=r})();