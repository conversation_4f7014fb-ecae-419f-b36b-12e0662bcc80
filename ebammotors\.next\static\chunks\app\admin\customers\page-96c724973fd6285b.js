(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1016],{3730:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var a=r(5155),t=r(2115),u=r(5695);function n(){let e=(0,u.useRouter)();return(0,t.useEffect)(()=>{e.replace("/admin/dashboard?section=customers")},[e]),(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Redirecting to customer management..."})]})})}},5695:(e,s,r)=>{"use strict";var a=r(8999);r.o(a,"usePathname")&&r.d(s,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(s,{useSearchParams:function(){return a.useSearchParams}})},6006:(e,s,r)=>{Promise.resolve().then(r.bind(r,3730))}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(6006)),_N_E=e.O()}]);