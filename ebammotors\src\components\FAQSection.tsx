'use client';

// FAQ Section with chatbot integration for enhanced user experience
import { HelpCircle } from 'lucide-react';
import { getMessagesSync } from '@/lib/messages';

// FAQ functionality redirects users to chatbot for better assistance

interface FAQSectionProps {
  locale: string;
}

export default function FAQSection({ locale }: FAQSectionProps) {
  const messages = getMessagesSync(locale);
  return (
    <section className="py-16 bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-6">
          <div className="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-semibold">
            <HelpCircle className="w-4 h-4" />
            <span>{messages.faq.getInstantHelp}</span>
          </div>
          <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800 leading-tight">
            {messages.faq.needHelpChatWithUs}
          </h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            {messages.faq.chatbotAvailable24_7}
          </p>
        </div>

        {/* Chatbot CTA */}
        <div className="text-center mt-8">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-neutral-800 mb-3">
              {messages.faq.useOurChatbot}
            </h3>
            <p className="text-neutral-600 mb-4 max-w-xl mx-auto">
              {messages.faq.lookForChatIcon}
            </p>
            <div className="bg-white/80 rounded-xl p-3 border-l-4 border-primary-500">
              <p className="text-neutral-700 text-sm">
                {messages.faq.askAboutVehicles}<br/>
                {messages.faq.englishJapaneseSupport}<br/>
                {messages.faq.instantResponses}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
