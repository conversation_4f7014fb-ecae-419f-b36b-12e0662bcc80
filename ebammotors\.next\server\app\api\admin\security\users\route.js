(()=>{var e={};e.id=5213,e.ids=[5213],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,s,t)=>{"use strict";t.d(s,{Qq:()=>p,Tq:()=>g,bS:()=>c,fF:()=>l,mU:()=>d});var r=t(85663),a=t(43205),n=t.n(a);let i=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,s){try{return await r.Ay.compare(e,s)}catch(e){return console.error("Error verifying password:",e),!1}}function d(e){return o.delete(e)}async function c(e){try{let s=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),t=!1;if(!(s.startsWith("$2a$")||s.startsWith("$2b$")||s.startsWith("$2y$")?await u(e,s):e===s))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let s={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return n().sign(s,i,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),s=function(e="admin"){let s=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,t=Date.now();return o.set(s,{id:e,isAdmin:!0,createdAt:t,expiresAt:t+864e5,lastActivity:t}),function(){let e=Date.now();for(let[s,t]of o.entries())e>t.expiresAt&&o.delete(s)}(),s}();return{success:!0,token:e,sessionId:s,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function l(e,s){if(e&&e.startsWith("Bearer ")){let s=function(e){try{let s=n().verify(e,i);if(s.isAdmin)return{id:s.id,isAdmin:s.isAdmin};return null}catch(e){return null}}(e.substring(7));if(s)return{isValid:!0,adminId:s.id,message:"Token authentication successful"}}if(s){let e=function(e){let s=o.get(e);if(!s)return null;let t=Date.now();return t>s.expiresAt?(o.delete(e),null):(s.lastActivity=t,o.set(e,s),s)}(s);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let m=new Map;function p(e){let s=Date.now(),t=m.get(e);return!t||s-t.lastAttempt>9e5?(m.set(e,{count:1,lastAttempt:s}),{allowed:!0,remainingAttempts:4}):t.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(s-t.lastAttempt)}:(t.count++,t.lastAttempt=s,m.set(e,t),{allowed:!0,remainingAttempts:5-t.count})}function g(e){m.delete(e)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36850:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var r={};t.r(r),t.d(r,{DELETE:()=>p,GET:()=>d,PATCH:()=>m,POST:()=>l});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(77268);async function d(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=await c();return o.NextResponse.json({success:!0,users:s})}catch(e){return console.error("Error fetching admin users:",e),o.NextResponse.json({success:!1,message:"Failed to fetch admin users"},{status:500})}}async function c(){return[{id:"1",username:"admin",email:"<EMAIL>",role:"super_admin",status:"active",lastLogin:new Date(Date.now()-3e5).toISOString(),createdAt:new Date(Date.now()-2592e6).toISOString(),permissions:["users.create","users.read","users.update","users.delete","cars.create","cars.read","cars.update","cars.delete","orders.create","orders.read","orders.update","orders.delete","reviews.moderate","analytics.view","security.manage","settings.manage"]},{id:"2",username:"moderator1",email:"<EMAIL>",role:"moderator",status:"active",lastLogin:new Date(Date.now()-72e5).toISOString(),createdAt:new Date(Date.now()-1296e6).toISOString(),permissions:["cars.read","cars.update","orders.read","orders.update","reviews.moderate","analytics.view"]},{id:"3",username:"viewer1",email:"<EMAIL>",role:"viewer",status:"active",lastLogin:new Date(Date.now()-864e5).toISOString(),createdAt:new Date(Date.now()-6048e5).toISOString(),permissions:["cars.read","orders.read","analytics.view"]},{id:"4",username:"suspended_user",email:"<EMAIL>",role:"admin",status:"suspended",lastLogin:new Date(Date.now()-432e6).toISOString(),createdAt:new Date(Date.now()-1728e6).toISOString(),permissions:["cars.read","cars.update","orders.read","orders.update","reviews.moderate"]}]}async function l(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{username:s,email:t,role:r,permissions:a}=await e.json();if(!s||!t||!r)return o.NextResponse.json({success:!1,message:"Username, email, and role are required"},{status:400});if(!["super_admin","admin","moderator","viewer"].includes(r))return o.NextResponse.json({success:!1,message:"Invalid role"},{status:400});let n={id:Date.now().toString(),username:s,email:t,role:r,status:"active",lastLogin:void 0,createdAt:new Date().toISOString(),permissions:a||function(e){switch(e){case"super_admin":return["users.create","users.read","users.update","users.delete","cars.create","cars.read","cars.update","cars.delete","orders.create","orders.read","orders.update","orders.delete","reviews.moderate","analytics.view","security.manage","settings.manage"];case"admin":return["cars.create","cars.read","cars.update","cars.delete","orders.create","orders.read","orders.update","orders.delete","reviews.moderate","analytics.view"];case"moderator":return["cars.read","cars.update","orders.read","orders.update","reviews.moderate","analytics.view"];case"viewer":return["cars.read","orders.read","analytics.view"];default:return["cars.read"]}}(r)};return o.NextResponse.json({success:!0,message:"Admin user created successfully",user:n})}catch(e){return console.error("Error creating admin user:",e),o.NextResponse.json({success:!1,message:"Failed to create admin user"},{status:500})}}async function m(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{userId:s,updates:t}=await e.json();if(!s)return o.NextResponse.json({success:!1,message:"User ID is required"},{status:400});return o.NextResponse.json({success:!0,message:"Admin user updated successfully"})}catch(e){return console.error("Error updating admin user:",e),o.NextResponse.json({success:!1,message:"Failed to update admin user"},{status:500})}}async function p(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url);if(!s.get("userId"))return o.NextResponse.json({success:!1,message:"User ID is required"},{status:400});return o.NextResponse.json({success:!0,message:"Admin user deleted successfully"})}catch(e){return console.error("Error deleting admin user:",e),o.NextResponse.json({success:!1,message:"Failed to delete admin user"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/security/users/route",pathname:"/api/admin/security/users",filename:"route",bundlePath:"app/api/admin/security/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\security\\users\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:y}=g;function h(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77268:(e,s,t)=>{"use strict";t.d(s,{iY:()=>a}),t(32190);var r=t(12909);function a(e,s){let t=e.headers.get("authorization"),a=e.cookies.get("admin_session")?.value,n=(0,r.fF)(t,a);if(n.isValid)return{isValid:!0,adminId:n.adminId,method:"token/session"};let i=s?.adminKey||e.nextUrl.searchParams.get("adminKey");return i&&i===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,580,7696],()=>t(36850));module.exports=r})();