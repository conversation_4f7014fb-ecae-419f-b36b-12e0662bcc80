// Social Media Integration for EBAM Motors
// Handles sharing, social login, and community features

export interface SocialPlatform {
  id: string;
  name: string;
  icon: string;
  color: string;
  shareUrl: string;
  loginUrl?: string;
  apiEndpoint?: string;
}

export interface ShareData {
  url: string;
  title: string;
  description: string;
  image?: string;
  hashtags?: string[];
}

export interface SocialPost {
  id: string;
  platform: string;
  content: string;
  image?: string;
  url?: string;
  createdAt: string;
  likes: number;
  shares: number;
  comments: number;
}

export interface SocialUser {
  id: string;
  name: string;
  email: string;
  avatar: string;
  platform: string;
  socialId: string;
  followers?: number;
  verified?: boolean;
}

// Supported social media platforms
export const SOCIAL_PLATFORMS: SocialPlatform[] = [
  {
    id: 'facebook',
    name: 'Facebook',
    icon: '/icons/social/facebook.svg',
    color: '#1877F2',
    shareUrl: 'https://www.facebook.com/sharer/sharer.php',
    loginUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
  },
  {
    id: 'twitter',
    name: 'Twitter',
    icon: '/icons/social/twitter.svg',
    color: '#1DA1F2',
    shareUrl: 'https://twitter.com/intent/tweet',
  },
  {
    id: 'instagram',
    name: 'Instagram',
    icon: '/icons/social/instagram.svg',
    color: '#E4405F',
    shareUrl: 'https://www.instagram.com/',
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: '/icons/social/linkedin.svg',
    color: '#0A66C2',
    shareUrl: 'https://www.linkedin.com/sharing/share-offsite/',
    loginUrl: 'https://www.linkedin.com/oauth/v2/authorization',
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: '/icons/social/whatsapp.svg',
    color: '#25D366',
    shareUrl: 'https://wa.me/',
  },
  {
    id: 'line',
    name: 'LINE',
    icon: '/icons/social/line.svg',
    color: '#00B900',
    shareUrl: 'https://social-plugins.line.me/lineit/share',
  },
  {
    id: 'youtube',
    name: 'YouTube',
    icon: '/icons/social/youtube.svg',
    color: '#FF0000',
    shareUrl: 'https://www.youtube.com/watch',
  },
];

// Share content on social media
export const shareOnSocialMedia = (platform: string, shareData: ShareData): void => {
  const socialPlatform = SOCIAL_PLATFORMS.find(p => p.id === platform);
  if (!socialPlatform) {
    return;
  }

  let shareUrl = '';
  const encodedUrl = encodeURIComponent(shareData.url);
  const encodedTitle = encodeURIComponent(shareData.title);
  const encodedDescription = encodeURIComponent(shareData.description);
  const hashtags = shareData.hashtags?.join(',') || '';

  switch (platform) {
    case 'facebook':
      shareUrl = `${socialPlatform.shareUrl}?u=${encodedUrl}&quote=${encodedTitle}`;
      break;
    
    case 'twitter':
      const twitterText = `${shareData.title} ${shareData.description}`;
      shareUrl = `${socialPlatform.shareUrl}?text=${encodeURIComponent(twitterText)}&url=${encodedUrl}&hashtags=${hashtags}`;
      break;
    
    case 'linkedin':
      shareUrl = `${socialPlatform.shareUrl}?url=${encodedUrl}&title=${encodedTitle}&summary=${encodedDescription}`;
      break;
    
    case 'whatsapp':
      const whatsappText = `${shareData.title}\n${shareData.description}\n${shareData.url}`;
      shareUrl = `${socialPlatform.shareUrl}?text=${encodeURIComponent(whatsappText)}`;
      break;
    
    case 'line':
      shareUrl = `${socialPlatform.shareUrl}?url=${encodedUrl}&text=${encodedTitle}`;
      break;
    
    default:
      return;
  }

  // Open share dialog
  const width = 600;
  const height = 400;
  const left = (window.screen.width - width) / 2;
  const top = (window.screen.height - height) / 2;
  
  window.open(
    shareUrl,
    'share',
    `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
  );
};

// Native Web Share API (for mobile devices)
export const shareNative = async (shareData: ShareData): Promise<boolean> => {
  if (!navigator.share) {
    return false;
  }

  try {
    await navigator.share({
      title: shareData.title,
      text: shareData.description,
      url: shareData.url,
    });
    return true;
  } catch (error) {
    return false;
  }
};

// Copy link to clipboard
export const copyToClipboard = async (url: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(url);
    return true;
  } catch (error) {
    return false;
  }
};

// Track social media interactions
export const trackSocialInteraction = async (
  action: string,
  platform: string,
  content: string
): Promise<void> => {
  try {
    await fetch('/api/social/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action,
        platform,
        content,
        timestamp: new Date().toISOString(),
      }),
    });
  } catch (error) {
    // Error tracking social interaction
  }
};

// Get social media posts/feed
export const getSocialFeed = async (limit: number = 10): Promise<SocialPost[]> => {
  try {
    const response = await fetch(`/api/social/feed?limit=${limit}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch social feed');
    }

    const data = await response.json();
    return data.posts || [];
  } catch (error) {
    return [];
  }
};

// Post to social media (admin function)
export const postToSocialMedia = async (
  platforms: string[],
  content: string,
  image?: string,
  url?: string
): Promise<{ success: boolean; results: any[] }> => {
  try {
    const response = await fetch('/api/social/post', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platforms,
        content,
        image,
        url,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to post to social media');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    return { success: false, results: [] };
  }
};

// Social login functions
export const initiateSocialLogin = (platform: string, redirectUrl?: string): void => {
  const socialPlatform = SOCIAL_PLATFORMS.find(p => p.id === platform);
  if (!socialPlatform?.loginUrl) {
    return;
  }

  const clientId = process.env.NEXT_PUBLIC_SOCIAL_CLIENT_IDS?.[platform];
  if (!clientId) {
    return;
  }

  let loginUrl = '';
  const redirect = redirectUrl || `${window.location.origin}/auth/callback/${platform}`;
  const state = Math.random().toString(36).substring(7);

  switch (platform) {
    case 'facebook':
      loginUrl = `${socialPlatform.loginUrl}?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirect)}&scope=email,public_profile&response_type=code&state=${state}`;
      break;
    
    case 'linkedin':
      loginUrl = `${socialPlatform.loginUrl}?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirect)}&scope=r_liteprofile%20r_emailaddress&state=${state}`;
      break;
    
    default:
      return;
  }

  // Store state for verification
  if (typeof window !== 'undefined') {
    sessionStorage.setItem(`social_login_state_${platform}`, state);
  }
  
  // Redirect to social login
  window.location.href = loginUrl;
};

// Handle social login callback
export const handleSocialLoginCallback = async (
  platform: string,
  code: string,
  state: string
): Promise<SocialUser | null> => {
  try {
    // Verify state
    const storedState = typeof window !== 'undefined' ? sessionStorage.getItem(`social_login_state_${platform}`) : null;
    if (state !== storedState) {
      throw new Error('Invalid state parameter');
    }

    const response = await fetch('/api/auth/social/callback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform,
        code,
        state,
      }),
    });

    if (!response.ok) {
      throw new Error('Social login failed');
    }

    const data = await response.json();
    
    // Clean up state
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(`social_login_state_${platform}`);
    }
    
    return data.user;
  } catch (error) {
    console.error('Error handling social login callback:', error);
    return null;
  }
};

// Get social media statistics
export const getSocialStats = async (): Promise<{
  followers: Record<string, number>;
  engagement: Record<string, number>;
  posts: Record<string, number>;
}> => {
  try {
    const response = await fetch('/api/social/stats');
    
    if (!response.ok) {
      throw new Error('Failed to fetch social stats');
    }

    const data = await response.json();
    return data.stats;
  } catch (error) {
    console.error('Error fetching social stats:', error);
    return {
      followers: {},
      engagement: {},
      posts: {},
    };
  }
};

// Generate social media content suggestions
export const generateSocialContent = async (
  carData: any,
  platform: string
): Promise<string> => {
  const templates = {
    facebook: [
      `🚗 New arrival! ${carData.year} ${carData.brand} ${carData.model} now available for ¥${carData.price?.toLocaleString()}! Perfect condition and ready for export to Ghana. Contact us for more details! #UsedCars #JapanToGhana #EBAMMotors`,
      `✨ Quality guaranteed! This ${carData.year} ${carData.brand} ${carData.model} has been thoroughly inspected and is ready for its new home in Ghana. Starting at ¥${carData.price?.toLocaleString()}. #QualityCars #Export #Ghana`,
    ],
    twitter: [
      `🚗 ${carData.year} ${carData.brand} ${carData.model} - ¥${carData.price?.toLocaleString()}\n✅ Excellent condition\n🚢 Ready for export\n📍 Japan to Ghana\n#UsedCars #Export #Ghana #EBAMMotors`,
      `New stock alert! 🚨 ${carData.brand} ${carData.model} (${carData.year}) now available. Perfect for Ghana market! ¥${carData.price?.toLocaleString()} #JapanCars #Ghana`,
    ],
    instagram: [
      `🌟 Featured Car Alert! 🌟\n\n${carData.year} ${carData.brand} ${carData.model}\n💰 ¥${carData.price?.toLocaleString()}\n✨ Excellent condition\n🚢 Export ready\n\n#UsedCars #JapanToGhana #EBAMMotors #QualityCars #Export #Ghana #JapaneseCars`,
    ],
    linkedin: [
      `Professional vehicle export services: We're pleased to announce the availability of this ${carData.year} ${carData.brand} ${carData.model} for our Ghana market. Priced at ¥${carData.price?.toLocaleString()}, this vehicle represents excellent value for international buyers. Contact EBAM Motors for professional export services. #VehicleExport #BusinessOpportunity #Ghana`,
    ],
  };

  const platformTemplates = templates[platform as keyof typeof templates] || templates.facebook;
  const randomTemplate = platformTemplates[Math.floor(Math.random() * platformTemplates.length)];
  
  return randomTemplate;
};

// Social proof features
export const getSocialProof = async (): Promise<{
  totalShares: number;
  recentShares: SocialPost[];
  testimonials: any[];
  socialMentions: any[];
}> => {
  try {
    const response = await fetch('/api/social/proof');
    
    if (!response.ok) {
      throw new Error('Failed to fetch social proof');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching social proof:', error);
    return {
      totalShares: 0,
      recentShares: [],
      testimonials: [],
      socialMentions: [],
    };
  }
};
