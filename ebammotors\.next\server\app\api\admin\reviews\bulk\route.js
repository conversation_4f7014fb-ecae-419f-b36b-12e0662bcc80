(()=>{var e={};e.id=6600,e.ids=[6600],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>h,Tq:()=>m,bS:()=>l,fF:()=>d,mU:()=>u});var a=r(85663),s=r(43205),n=r.n(s);let i=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function c(e,t){try{return await a.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function u(e){return o.delete(e)}async function l(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),r=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await c(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return n().sign(t,i,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let e=Date.now();for(let[t,r]of o.entries())e>r.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function d(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=n().verify(e,i);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let r=Date.now();return r>t.expiresAt?(o.delete(e),null):(t.lastActivity=r,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function h(e){let t=Date.now(),r=p.get(e);return!r||t-r.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-r.lastAttempt)}:(r.count++,r.lastAttempt=t,p.set(e,r),{allowed:!0,remainingAttempts:5-r.count})}function m(e){p.delete(e)}},27910:e=>{"use strict";e.exports=require("stream")},27931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>f,serverHooks:()=>v,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>w});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>l});var s=r(96559),n=r(48088),i=r(37719),o=r(32190),c=r(77268),u=r(67462);async function l(e){try{let t;if(!(0,c.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{action:r,reviewIds:a}=await e.json();if(!r||!a||!Array.isArray(a)||0===a.length)return o.NextResponse.json({success:!1,message:"Invalid request. Action and reviewIds are required."},{status:400});let s="";switch(r){case"approve":t=await d(a,"approved"),s=`${t.count} reviews approved`;break;case"reject":t=await d(a,"rejected"),s=`${t.count} reviews rejected`;break;case"delete":t=await p(a),s=`${t.count} reviews deleted`;break;case"export":let n=await h(a);return o.NextResponse.json({success:!0,message:"Reviews exported successfully",data:n});default:return o.NextResponse.json({success:!1,message:"Invalid action"},{status:400})}return o.NextResponse.json({success:!0,message:s,affected_count:t.count})}catch(e){return console.error("Error performing bulk operation:",e),o.NextResponse.json({success:!1,message:"Failed to perform bulk operation"},{status:500})}}async function d(e,t){try{let r=await (0,u.zu)(),a=0,s=r.map(r=>e.includes(r.id)?(a++,{...r,status:t}):r);return await (0,u.CE)(s),{count:a}}catch(e){throw console.error("Error in bulk status update:",e),e}}async function p(e){try{let t=await (0,u.zu)(),r=t.filter(t=>!e.includes(t.id)),a=t.length-r.length;return await (0,u.CE)(r),{count:a}}catch(e){throw console.error("Error in bulk delete:",e),e}}async function h(e){try{return(await (0,u.zu)()).filter(t=>e.includes(t.id)).map(e=>({id:e.id,name:e.name,location:e.location,email:e.email,rating:e.rating,title:e.title||"",review:e.review,vehiclePurchased:e.vehiclePurchased||"",purchaseDate:e.purchaseDate||"",status:e.status,submittedAt:e.submittedAt,locale:e.locale}))}catch(e){throw console.error("Error in bulk export:",e),e}}async function m(e){try{if(!(0,c.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await (0,u.zu)(),r={total:t.length,byStatus:{pending:t.filter(e=>"pending"===e.status).length,approved:t.filter(e=>"approved"===e.status).length,rejected:t.filter(e=>"rejected"===e.status).length},byRating:{5:t.filter(e=>5===e.rating).length,4:t.filter(e=>4===e.rating).length,3:t.filter(e=>3===e.rating).length,2:t.filter(e=>2===e.rating).length,1:t.filter(e=>1===e.rating).length},averageRating:(()=>{let e=t.filter(e=>"approved"===e.status);return e.length>0?e.reduce((e,t)=>e+t.rating,0)/e.length:0})(),recentSubmissions:{today:t.filter(e=>(function(e){let t=new Date;return e.toDateString()===t.toDateString()})(new Date(e.submittedAt))).length,thisWeek:t.filter(e=>new Date(e.submittedAt)>=new Date(new Date().getTime()-6048e5)).length,thisMonth:t.filter(e=>(function(e){let t=new Date;return e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()})(new Date(e.submittedAt))).length},topLocations:function(e){let t={};return e.forEach(e=>{let r=e.location||"Unknown";t[r]=(t[r]||0)+1}),Object.entries(t).map(([e,t])=>({location:e,count:t})).sort((e,t)=>t.count-e.count).slice(0,10)}(t),monthlyTrend:function(e){let t={};return e.forEach(e=>{let r=new Date(e.submittedAt),a=`${r.getFullYear()}-${String(r.getMonth()+1).padStart(2,"0")}`;t[a]=(t[a]||0)+1}),Object.entries(t).map(([e,t])=>({month:e,count:t})).sort((e,t)=>e.month.localeCompare(t.month)).slice(-12)}(t)};return o.NextResponse.json({success:!0,analytics:r})}catch(e){return console.error("Error fetching review analytics:",e),o.NextResponse.json({success:!1,message:"Failed to fetch analytics"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/reviews/bulk/route",pathname:"/api/admin/reviews/bulk",filename:"route",bundlePath:"app/api/admin/reviews/bulk/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\reviews\\bulk\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:w,serverHooks:v}=f;function y(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:w})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67462:(e,t,r)=>{"use strict";r.d(t,{CE:()=>m,RV:()=>y,Ve:()=>v,WD:()=>g,kD:()=>f,zu:()=>h});var a=r(29021),s=r(33873),n=r.n(s);let i=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,o=n().join(process.cwd(),"data","reviews.json"),c=n().join(process.cwd(),"data"),u=[],l=[{id:"sample-1",name:"Kwame Mensah",location:"Accra, Ghana",email:"<EMAIL>",rating:5,title:"Excellent Service and Quality Vehicle",review:"I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!",vehiclePurchased:"Toyota Voxy 2015",purchaseDate:"2024-01",locale:"en",submittedAt:"2024-01-15T10:30:00.000Z",status:"approved",images:[],titleEn:"Excellent Service and Quality Vehicle",titleJa:"優れたサービスと高品質な車両",reviewEn:"I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!",reviewJa:"EBAM Motorsを通じてトヨタ ヴォクシーを購入しましたが、全プロセスがスムーズでプロフェッショナルでした。車は説明通りの優れた状態で到着しました。チームは私のすべての質問に迅速に対応し、配送プロセスを手間なく進めてくれました。強くお勧めします！"},{id:"sample-2",name:"Akosua Boateng",location:"Kumasi, Ghana",email:"<EMAIL>",rating:5,title:"Professional and Trustworthy",review:"EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!",vehiclePurchased:"Honda Fit 2016",purchaseDate:"2024-02",locale:"en",submittedAt:"2024-02-10T14:20:00.000Z",status:"approved",images:[],titleEn:"Professional and Trustworthy",titleJa:"プロフェッショナルで信頼できる",reviewEn:"EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!",reviewJa:"EBAM Motorsは私の日常通勤に最適なホンダ フィットを見つけるのを手伝ってくれました。彼らのチームは車両について非常に知識が豊富で、各車について詳細な情報を提供してくれました。配送は迅速で、車は完璧な状態で到着しました。全体的に素晴らしい体験でした！"}];async function d(){try{await a.promises.access(c)}catch{await a.promises.mkdir(c,{recursive:!0})}}async function p(){try{await a.promises.access(o)}catch{await d(),await a.promises.writeFile(o,JSON.stringify(l,null,2),"utf8")}}async function h(){if(i)return 0===u.length&&(u=[...l]),u;try{await p();let e=await a.promises.readFile(o,"utf8");return JSON.parse(e)}catch(e){return console.error("Error reading reviews:",e),l}}async function m(e){if(i){u=[...e];return}try{await d(),await a.promises.writeFile(o,JSON.stringify(e,null,2),"utf8")}catch(e){throw console.error("Error saving reviews:",e),Error("Failed to save reviews")}}async function f(e){let t=await h(),r={...e,id:Date.now().toString()+"-"+Math.random().toString(36).substr(2,9)};return t.push(r),await m(t),r}async function g(e,t){let r=await h(),a=r.findIndex(t=>t.id===e);return -1!==a&&(r[a].status=t,await m(r),!0)}async function w(e){return(await h()).filter(t=>t.status===e)}async function v(){return w("approved")}async function y(e){return(await h()).find(t=>t.id===e)||null}},77268:(e,t,r)=>{"use strict";r.d(t,{iY:()=>s}),r(32190);var a=r(12909);function s(e,t){let r=e.headers.get("authorization"),s=e.cookies.get("admin_session")?.value,n=(0,a.fF)(r,s);if(n.isValid)return{isValid:!0,adminId:n.adminId,method:"token/session"};let i=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return i&&i===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,7696],()=>r(27931));module.exports=a})();