(()=>{var e={};e.id=4923,e.ids=[4923,7990],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,s)=>{"use strict";s.d(t,{Qq:()=>m,Tq:()=>$,bS:()=>l,fF:()=>d,mU:()=>c});var r=s(85663),i=s(43205),a=s.n(i);let n=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await r.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function l(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),s=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return a().sign(t,n,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,s=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:s,expiresAt:s+864e5,lastActivity:s}),function(){let e=Date.now();for(let[t,s]of o.entries())e>s.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function d(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=a().verify(e,n);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let s=Date.now();return s>t.expiresAt?(o.delete(e),null):(t.lastActivity=s,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),s=p.get(e);return!s||t-s.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):s.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-s.lastAttempt)}:(s.count++,s.lastAttempt=t,p.set(e,s),{allowed:!0,remainingAttempts:5-s.count})}function $(e){p.delete(e)}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},42853:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>y,routeModule:()=>$,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>_});var r={};s.r(r),s.d(r,{DELETE:()=>m,GET:()=>l,POST:()=>d,PUT:()=>p});var i=s(96559),a=s(48088),n=s(37719),o=s(32190),u=s(77268),c=s(83376);async function l(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),r=parseInt(t.get("per_page")||"20"),i=t.get("search")||"",a=t.get("sort_by")||"created_at",n=t.get("sort_order")||"desc",l=t.get("make")||"",d=t.get("model")||"",p=t.get("status")||"",m=t.get("year_min")||"",$=t.get("year_max")||"",g=t.get("price_min")||"",_=t.get("price_max")||"",f=t.get("is_featured")||"",y=t.get("location")||"",h=["1=1"],x=[],R=1;i&&(h.push(`(
        LOWER(title) LIKE $${R} OR 
        LOWER(make) LIKE $${R} OR 
        LOWER(model) LIKE $${R} OR 
        car_id LIKE $${R}
      )`),x.push(`%${i.toLowerCase()}%`),R++),l&&(h.push(`LOWER(make) = $${R}`),x.push(l.toLowerCase()),R++),d&&(h.push(`LOWER(model) = $${R}`),x.push(d.toLowerCase()),R++),p&&(h.push(`LOWER(status) = $${R}`),x.push(p.toLowerCase()),R++),m&&(h.push(`year >= $${R}`),x.push(parseInt(m)),R++),$&&(h.push(`year <= $${R}`),x.push(parseInt($)),R++),g&&(h.push(`price >= $${R}`),x.push(parseFloat(g)),R++),_&&(h.push(`price <= $${R}`),x.push(parseFloat(_)),R++),f&&(h.push(`is_featured = $${R}`),x.push("true"===f),R++),y&&(h.push(`LOWER(location) = $${R}`),x.push(y.toLowerCase()),R++);let E=h.join(" AND "),w=["created_at","updated_at","title","make","model","year","price","status"].includes(a)?a:"created_at",q="asc"===n.toLowerCase()?"ASC":"DESC",A=`SELECT COUNT(*) as total FROM cars WHERE ${E}`,k=await c.sql.query(A,x),N=parseInt(k.rows[0]?.total||"0"),v=(s-1)*r,j=`
      SELECT 
        id,
        car_id,
        make,
        model,
        year,
        title,
        price,
        original_price,
        currency,
        status,
        mileage,
        fuel_type,
        transmission,
        body_condition,
        location,
        is_featured,
        stock_quantity,
        main_image,
        images,
        created_at,
        updated_at
      FROM cars 
      WHERE ${E}
      ORDER BY ${w} ${q}
      LIMIT $${R} OFFSET $${R+1}
    `;x.push(r,v);let I=await c.sql.query(j,x);return o.NextResponse.json({success:!0,cars:I.rows,total_count:N,page:s,per_page:r,total_pages:Math.ceil(N/r)})}catch(e){return console.error("Error fetching cars:",e),o.NextResponse.json({success:!1,message:"Failed to fetch cars"},{status:500})}}async function d(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await e.json();for(let e of["car_id","make","model","year","title","price"])if(!t[e])return o.NextResponse.json({success:!1,message:`Missing required field: ${e}`},{status:400});let s=await (0,c.sql)`
      INSERT INTO cars (
        car_id, make, model, year, title, price, original_price, currency,
        mileage, fuel_type, transmission, engine_size, drive_type, seats, doors, body_type,
        body_condition, interior_condition, exterior_color, interior_color,
        main_image, images, image_folder, specs, features,
        status, stock_quantity, location, slug, description, meta_title, meta_description,
        is_featured, import_source, import_notes
      ) VALUES (
        ${t.car_id}, ${t.make}, ${t.model}, ${t.year}, ${t.title},
        ${t.price}, ${t.original_price||null}, ${t.currency||"JPY"},
        ${t.mileage||null}, ${t.fuel_type||null}, ${t.transmission||null},
        ${t.engine_size||null}, ${t.drive_type||null}, ${t.seats||null},
        ${t.doors||null}, ${t.body_type||null}, ${t.body_condition||"Good"},
        ${t.interior_condition||"Good"}, ${t.exterior_color||null}, ${t.interior_color||null},
        ${t.main_image||null}, ${t.images||[]}, ${t.image_folder||null},
        ${t.specs||[]}, ${t.features||[]}, ${t.status||"Available"},
        ${t.stock_quantity||1}, ${t.location||"Japan"}, ${t.slug||null},
        ${t.description||null}, ${t.meta_title||null}, ${t.meta_description||null},
        ${t.is_featured||!1}, 'admin', 'Added via admin panel'
      )
      RETURNING id, car_id
    `;return o.NextResponse.json({success:!0,message:"Car added successfully",car:s.rows[0]})}catch(e){return console.error("Error adding car:",e),o.NextResponse.json({success:!1,message:"Failed to add car"},{status:500})}}async function p(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("id");if(!s)return o.NextResponse.json({success:!1,message:"Car ID is required"},{status:400});let r=await e.json(),i=await (0,c.sql)`
      UPDATE cars SET
        make = ${r.make},
        model = ${r.model},
        year = ${r.year},
        title = ${r.title},
        price = ${r.price},
        original_price = ${r.original_price||null},
        currency = ${r.currency||"JPY"},
        mileage = ${r.mileage||null},
        fuel_type = ${r.fuel_type||null},
        transmission = ${r.transmission||null},
        body_condition = ${r.body_condition||"Good"},
        status = ${r.status||"Available"},
        stock_quantity = ${r.stock_quantity||1},
        location = ${r.location||"Japan"},
        is_featured = ${r.is_featured||!1},
        description = ${r.description||null},
        updated_at = NOW()
      WHERE id = ${s}
      RETURNING id, car_id
    `;if(0===i.rows.length)return o.NextResponse.json({success:!1,message:"Car not found"},{status:404});return o.NextResponse.json({success:!0,message:"Car updated successfully",car:i.rows[0]})}catch(e){return console.error("Error updating car:",e),o.NextResponse.json({success:!1,message:"Failed to update car"},{status:500})}}async function m(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("id");if(!s)return o.NextResponse.json({success:!1,message:"Car ID is required"},{status:400});let r=await (0,c.sql)`
      DELETE FROM cars WHERE id = ${s}
      RETURNING id, car_id
    `;if(0===r.rows.length)return o.NextResponse.json({success:!1,message:"Car not found"},{status:404});return o.NextResponse.json({success:!0,message:"Car deleted successfully"})}catch(e){return console.error("Error deleting car:",e),o.NextResponse.json({success:!1,message:"Failed to delete car"},{status:500})}}let $=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/cars/route",pathname:"/api/admin/cars",filename:"route",bundlePath:"app/api/admin/cars/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\cars\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:_,serverHooks:f}=$;function y(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:_})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77268:(e,t,s)=>{"use strict";s.d(t,{iY:()=>i}),s(32190);var r=s(12909);function i(e,t){let s=e.headers.get("authorization"),i=e.cookies.get("admin_session")?.value,a=(0,r.fF)(s,i);if(a.isValid)return{isValid:!0,adminId:a.adminId,method:"token/session"};let n=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return n&&n===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,7696,3376],()=>s(42853));module.exports=r})();