"use strict";(()=>{var e={};e.id=1693,e.ids=[1693],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23870:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(55511);let a={randomUUID:n.randomUUID},i=new Uint8Array(256),s=i.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let c=function(e,t,r){if(a.randomUUID&&!t&&!e)return a.randomUUID();let c=(e=e||{}).random??e.rng?.()??(s>i.length-16&&((0,n.randomFillSync)(i),s=0),i.slice(s,s+=16));if(c.length<16)throw Error("Random bytes length must be >= 16");if(c[6]=15&c[6]|64,c[8]=63&c[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=c[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(c)}},24407:(e,t,r)=>{r.d(t,{N1:()=>u,U:()=>a,UQ:()=>d,Uv:()=>o,ZK:()=>i,_4:()=>c,gY:()=>s});let n=[{id:"bank_transfer_ghana",type:"bank_transfer",name:"Bank Transfer (Ghana)",description:"Direct bank transfer to our Ghana account",icon:"\uD83C\uDFE6",enabled:!0,config:{bankName:"Ghana Commercial Bank",accountNumber:"*************",accountName:"EBAM Motors Ghana Ltd",swiftCode:"GCBLGHAC"}},{id:"bank_transfer_japan",type:"bank_transfer",name:"Bank Transfer (Japan)",description:"Direct bank transfer to our Japan account",icon:"\uD83C\uDFE6",enabled:!0,config:{bankName:"Mizuho Bank",accountNumber:"*************",accountName:"EBAM Motors Japan KK",swiftCode:"MHCBJPJT"}},{id:"mtn_mobile_money",type:"mobile_money",name:"MTN Mobile Money",description:"Pay using MTN Mobile Money",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"mtn",number:"+************"}},{id:"vodafone_cash",type:"mobile_money",name:"Vodafone Cash",description:"Pay using Vodafone Cash",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"vodafone",number:"+************"}},{id:"airtel_money",type:"mobile_money",name:"AirtelTigo Money",description:"Pay using AirtelTigo Money",icon:"\uD83D\uDCF1",enabled:!0,config:{provider:"airtel",number:"+************"}},{id:"stripe_card",type:"stripe",name:"Credit/Debit Card",description:"Pay securely with your credit or debit card",icon:"\uD83D\uDCB3",enabled:!0,config:{publishableKey:process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}},{id:"cash_agent_kumasi",type:"cash_agent",name:"Cash Payment (Kumasi)",description:"Pay cash through our agent in Kumasi",icon:"\uD83D\uDCB5",enabled:!0,config:{agentLocations:["Kumasi Central Market","Adum Shopping Center","KNUST Campus"]}},{id:"cash_agent_accra",type:"cash_agent",name:"Cash Payment (Accra)",description:"Pay cash through our agent in Accra",icon:"\uD83D\uDCB5",enabled:!0,config:{agentLocations:["Makola Market","Osu Oxford Street","East Legon"]}}],a=[{id:"sea_freight_standard",name:"Sea Freight (Standard)",description:"Most economical option, 4-6 weeks delivery",estimatedDays:35,basePrice:15e4,pricePerKg:50,maxWeight:2e3,trackingIncluded:!0,insuranceIncluded:!0},{id:"sea_freight_express",name:"Sea Freight (Express)",description:"Faster sea shipping, 3-4 weeks delivery",estimatedDays:25,basePrice:2e5,pricePerKg:75,maxWeight:2e3,trackingIncluded:!0,insuranceIncluded:!0},{id:"air_freight",name:"Air Freight",description:"Fastest option, 1-2 weeks delivery",estimatedDays:10,basePrice:5e5,pricePerKg:200,maxWeight:500,trackingIncluded:!0,insuranceIncluded:!0}],i={compact:1200,sedan:1400,suv:1800,van:1600,truck:2500,motorcycle:200,default:1500};function s(e){return n.find(t=>t.id===e)||null}function o(e){return a.find(t=>t.id===e)||null}function c(e,t,r="ghana"){let n=o(e);if(!n)return 0;let a=i[t.toLowerCase()]||i.default,s=n.pricePerKg?a*n.pricePerKg:0,u=1;return"ghana"!==r.toLowerCase()&&(u=1.2),Math.round((n.basePrice+s)*u)}function u(e){let t=o(e);if(!t)return"";let r=new Date;return r.setDate(r.getDate()+t.estimatedDays),r.toISOString().split("T")[0]}function d(e,t,r){let n=s(e);if(!n)return"";switch(n.type){case"bank_transfer":return`Transfer \xa5${t.toLocaleString()} to:
Bank: ${n.config?.bankName}
Account: ${n.config?.accountNumber}
Name: ${n.config?.accountName}
Reference: ${r}`;case"mobile_money":return`Send \xa5${t.toLocaleString()} to ${n.config?.number}
Reference: ${r}
Network: ${n.name}`;case"cash_agent":return`Visit one of our agent locations with \xa5${t.toLocaleString()}:
${n.config?.agentLocations?.join(", ")}
Reference: ${r}`;case"stripe":return`You will be redirected to secure payment page to complete your payment of \xa5${t.toLocaleString()}.`;default:return`Payment amount: \xa5${t.toLocaleString()}
Order reference: ${r}`}}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30358:(e,t,r)=>{r.d(t,{Dq:()=>_,HQ:()=>v,Q4:()=>S,Vd:()=>b,ee:()=>k,fS:()=>w,g5:()=>I,getOrderById:()=>D,iO:()=>N,iY:()=>x});var n=r(29021),a=r(33873),i=r.n(a),s=r(23870);let o=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,c=i().join(process.cwd(),"data"),u=i().join(c,"orders.json"),d=i().join(c,"invoices.json"),p=[],l=[];async function m(){if(!o)try{await n.promises.access(c)}catch{await n.promises.mkdir(c,{recursive:!0})}}async function g(){if(o)return p;try{await m();let e=await n.promises.readFile(u,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function h(e){if(o){p=e;return}await m(),await n.promises.writeFile(u,JSON.stringify(e,null,2))}async function f(){if(o)return l;try{await m();let e=await n.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function y(e){if(o){l=e;return}await m(),await n.promises.writeFile(d,JSON.stringify(e,null,2))}async function w(e){let t=await g(),r={...e,id:(0,s.A)(),orderNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`EB${e.slice(-6)}${t}`}(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await h(t),r}async function b(){return await g()}async function D(e){return(await g()).find(t=>t.id===e)||null}async function S(e){return(await g()).filter(t=>t.customerId===e)}async function x(e,t){let r=await g(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},await h(r),!0)}async function k(e,t){let r=await D(e);if(!r)return!1;let n={...r.payment,...t};return await x(e,{payment:n})}async function v(e,t){let r=await D(e);if(!r)return!1;let n={...t,id:(0,s.A)()},a={...r.shipping,updates:[...r.shipping.updates,n]};return await x(e,{shipping:a})}async function N(e){let t=await f(),r={...e,id:(0,s.A)(),invoiceNumber:function(){let e=Date.now().toString(),t=Math.random().toString(36).substr(2,3).toUpperCase();return`INV-${e.slice(-6)}-${t}`}()};return t.push(r),await y(t),r}async function I(e){return(await f()).find(t=>t.orderId===e)||null}async function _(e,t){let r=await f(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t},await y(r),!0)}},33873:e=>{e.exports=require("path")},42455:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>h,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var n={};r.r(n),r.d(n,{GET:()=>p,PATCH:()=>m,POST:()=>l});var a=r(96559),i=r(48088),s=r(37719),o=r(32190),c=r(30358),u=r(6234),d=r(52381);async function p(e){try{var t,r;let n,{searchParams:a}=new URL(e.url),i=a.get("orderId"),s=a.get("trackingNumber");if(!i&&!s)return o.NextResponse.json({success:!1,message:"Order ID or tracking number is required"},{status:400});if(!i)return o.NextResponse.json({success:!1,message:"Search by tracking number not implemented"},{status:400});if(!(n=await (0,c.getOrderById)(i)))return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});let u=(0,d.wq)(n.shipping.method),p=u.length,l=(t=n.shipping.status,r=u,({pending:0,preparing:1,shipped:2,in_transit:3,delivered:r.length-1})[t]||0),m=Math.round(l/(p-1)*100);return o.NextResponse.json({success:!0,data:{order:{id:n.id,orderNumber:n.orderNumber,status:n.status,createdAt:n.createdAt},vehicle:{title:n.vehicle.title,image:n.vehicle.images[0]},shipping:{status:n.shipping.status,trackingNumber:n.shipping.trackingNumber,estimatedDelivery:n.shipping.estimatedDelivery,method:n.shipping.method.name,address:n.shipping.address,updates:n.shipping.updates.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime())},timeline:u.map((e,t)=>({...e,completed:t<=l,current:t===l})),progress:{percentage:m,currentStage:u[l]?.stage||"Unknown",nextStage:u[l+1]?.stage||"Delivered"}}})}catch(e){return console.error("Error getting tracking information:",e),o.NextResponse.json({success:!1,message:"Failed to get tracking information"},{status:500})}}async function l(e){try{let{orderId:t,update:r,adminKey:n}=await e.json(),a=process.env.ADMIN_PASSWORD||"admin123";if(n!==a)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!t||!r)return o.NextResponse.json({success:!1,message:"Order ID and update information are required"},{status:400});if(!r.status||!r.location||!r.description)return o.NextResponse.json({success:!1,message:"Status, location, and description are required"},{status:400});if(!await (0,c.HQ)(t,{timestamp:r.timestamp||new Date().toISOString(),status:r.status,location:r.location,description:r.description,estimatedDelivery:r.estimatedDelivery}))return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});try{let e=await (0,c.getOrderById)(t);e&&e.customer.email&&await (0,u.pG)({customerName:e.customer.name,customerEmail:e.customer.email,orderId:t,status:r.status,location:r.location,estimatedArrival:r.estimatedDelivery})}catch(e){console.error("Failed to send delivery update follow-up email:",e)}return r.shippingStatus&&await (0,c.iY)(t,{shipping:{...await (0,c.getOrderById)(t).then(e=>e?.shipping),status:r.shippingStatus,trackingNumber:r.trackingNumber||void 0}}),o.NextResponse.json({success:!0,message:"Tracking update added successfully"})}catch(e){return console.error("Error adding tracking update:",e),o.NextResponse.json({success:!1,message:"Failed to add tracking update"},{status:500})}}async function m(e){try{let{orderId:t,trackingNumber:r,shippingStatus:n,adminKey:a}=await e.json(),i=process.env.ADMIN_PASSWORD||"admin123";if(a!==i)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!t)return o.NextResponse.json({success:!1,message:"Order ID is required"},{status:400});let s=await (0,c.getOrderById)(t);if(!s)return o.NextResponse.json({success:!1,message:"Order not found"},{status:404});let u={...s.shipping,...r&&{trackingNumber:r},...n&&{status:n}};if(!await (0,c.iY)(t,{shipping:u}))return o.NextResponse.json({success:!1,message:"Failed to update order"},{status:500});return n&&n!==s.shipping.status&&await (0,c.HQ)(t,{timestamp:new Date().toISOString(),status:g(n),location:"System Update",description:`Shipping status updated to ${g(n)}`}),o.NextResponse.json({success:!0,message:"Shipping information updated successfully"})}catch(e){return console.error("Error updating shipping information:",e),o.NextResponse.json({success:!1,message:"Failed to update shipping information"},{status:500})}}function g(e){return({pending:"Order Pending",preparing:"Preparing for Shipment",shipped:"Shipped",in_transit:"In Transit",delivered:"Delivered",returned:"Returned"})[e]||e}let h=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/tracking/route",pathname:"/api/tracking",filename:"route",bundlePath:"app/api/tracking/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\tracking\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:w}=h;function b(){return(0,s.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52381:(e,t,r)=>{r.d(t,{OZ:()=>u,Qb:()=>i,TX:()=>a,WC:()=>d,gP:()=>s,hj:()=>c,wq:()=>o});var n=r(24407);function a(e,t){let r=[];for(let a of n.U){let i=e.weight||n.ZK[e.category.toLowerCase()]||n.ZK.default;if(a.maxWeight&&i>a.maxWeight)continue;let s=(0,n._4)(a.id,e.category,t.country),o=(0,n.N1)(a.id);r.push({method:a,cost:s,estimatedDelivery:o,weight:i,dimensions:e.dimensions})}return r.sort((e,t)=>e.cost-t.cost)}function i(e,t="balanced"){if(0===e.length)return null;switch(t){case"cost":default:return e[0];case"speed":return e.reduce((e,t)=>t.method.estimatedDays<e.method.estimatedDays?t:e);case"balanced":let r=e.map(e=>({calculation:e,score:e.cost/1e5+e.method.estimatedDays/10}));return r.sort((e,t)=>e.score-t.score),r[0].calculation}}function s(e,t,r=!0,n=!0){let a=r?Math.max(.02*t,1e4):0,i=25e3*!!n;return{baseShipping:e,insurance:Math.round(a),handling:i,total:Math.round(e+a+i)}}function o(e){let t=[{stage:"Order Processing",description:"Vehicle inspection and export documentation",estimatedDays:3},{stage:"Port Preparation",description:"Vehicle preparation and loading at port",estimatedDays:5}];return e.id.includes("air")?[...t,{stage:"Air Transport",description:"Flight to destination airport",estimatedDays:2},{stage:"Customs Clearance",description:"Import customs and documentation",estimatedDays:3},{stage:"Final Delivery",description:"Transport to final destination",estimatedDays:2}]:[...t,{stage:"Sea Transport",description:"Shipping to destination port",estimatedDays:e.estimatedDays-15},{stage:"Port Arrival",description:"Arrival at destination port",estimatedDays:e.estimatedDays-10},{stage:"Customs Clearance",description:"Import customs and documentation",estimatedDays:e.estimatedDays-5},{stage:"Final Delivery",description:"Transport to final destination",estimatedDays:e.estimatedDays}]}function c(e){let t=[];return e.country&&""!==e.country.trim()||t.push("Country is required"),e.city&&""!==e.city.trim()||t.push("City is required"),["ghana","nigeria","kenya","south africa","ivory coast"].includes(e.country.toLowerCase())||t.push(`Shipping to ${e.country} is not currently available`),{valid:0===t.length,errors:t}}function u(e){let t=[];switch(e.country.toLowerCase()){case"ghana":t.push("Vehicles must be less than 10 years old"),t.push("Right-hand drive vehicles only");break;case"nigeria":t.push("Vehicles must be less than 15 years old"),t.push("Additional import duties may apply");break;case"kenya":t.push("Vehicles must be less than 8 years old"),t.push("Pre-shipment inspection required");break;default:t.push("Please contact us for specific import requirements")}return t}function d(e){let t=new Date,r=new Date(t),n=new Date(t),a=e.id.includes("air")?1:3;return r.setDate(r.getDate()+e.estimatedDays-a),n.setDate(n.getDate()+e.estimatedDays+a),{earliest:r.toISOString().split("T")[0],latest:n.toISOString().split("T")[0]}}},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,580,6967,1542],()=>r(42455));module.exports=n})();