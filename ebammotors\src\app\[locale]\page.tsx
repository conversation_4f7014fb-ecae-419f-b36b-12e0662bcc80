import Link from 'next/link';
import { <PERSON>, ArrowRight, Star, CheckCircle } from 'lucide-react';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';
import AnimatedImageBackground from '@/components/AnimatedImageBackground';
import AnimatedServiceCard from '@/components/AnimatedServiceCard';
import WhatsAppChannel from '@/components/WhatsAppChannel';
import CustomerReviews from '@/components/CustomerReviews';
import FAQSection from '@/components/FAQSection';
import { getHeroImages, getServiceImages, getGalleryImages } from '@/utils/imageUtils';

export default async function HomePage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  // Get animated images for different sections
  const heroImages = getHeroImages();
  const serviceImages = getServiceImages();
  const galleryImages = getGalleryImages();

  // Simplified FAQ data - directing users to chatbot
  const faqs = [
    {
      question: messages.faq?.howCanIGetHelp || "How can I get help?",
      answer: messages.faq?.useOurChatbot || "Use our 24/7 chatbot in the bottom-right corner for instant assistance."
    }
  ];
  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <AnimatedImageBackground
        images={heroImages.length > 0 ? heroImages : ['/used cars/voxy.jpeg']}
        className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white min-h-[50vh] md:min-h-[60vh]"
        interval={5000}
      >
        <div className="absolute inset-0 bg-black/30 md:bg-black/40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20 z-10 flex items-center min-h-[50vh] md:min-h-[60vh]">
          <div className="text-center space-y-4 md:space-y-6 w-full">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-heading font-bold leading-tight animate-fadeInUp">
              {messages.homepage.heroTitle}
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-primary-100 leading-relaxed max-w-3xl mx-auto animate-fadeInUp animate-delay-200">
              {messages.homepage.heroSubtitle}
            </p>

            {/* Key Stats */}
            <div className="flex flex-wrap justify-center gap-3 sm:gap-4 lg:gap-6 animate-fadeInUp animate-delay-300">
              <div className="text-center bg-black/20 backdrop-blur-sm rounded-lg px-3 py-2 sm:bg-transparent sm:backdrop-blur-none">
                <div className="text-lg sm:text-xl lg:text-2xl font-bold text-secondary-400">1000+</div>
                <div className="text-xs text-primary-100">{messages.homepage.carsExported}</div>
              </div>
              <div className="text-center bg-black/20 backdrop-blur-sm rounded-lg px-3 py-2 sm:bg-transparent sm:backdrop-blur-none">
                <div className="text-lg sm:text-xl lg:text-2xl font-bold text-secondary-400">15+</div>
                <div className="text-xs text-primary-100">{messages.homepage.yearsExperience}</div>
              </div>
              <div className="text-center bg-black/20 backdrop-blur-sm rounded-lg px-3 py-2 sm:bg-transparent sm:backdrop-blur-none">
                <div className="text-lg sm:text-xl lg:text-2xl font-bold text-secondary-400">24/7</div>
                <div className="text-xs text-primary-100">{messages.homepage.support}</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center animate-fadeInUp animate-delay-400 px-4 sm:px-0">
              <Link
                href={`/${locale}/stock`}
                className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-5 sm:px-6 py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-200 flex items-center justify-center space-x-2 hover:scale-105 hover:shadow-lg"
              >
                <span>{messages.homepage.viewInventory}</span>
                <ArrowRight className="w-4 h-4" />
              </Link>
              <Link
                href={`/${locale}/contact`}
                className="border-2 border-white text-white hover:bg-white hover:text-primary-700 px-5 sm:px-6 py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-200 flex items-center justify-center hover:scale-105 hover:shadow-lg"
              >
                {messages.homepage.contactUs}
              </Link>
            </div>
          </div>
        </div>
      </AnimatedImageBackground>

      {/* Services Section - Enhanced with better layout and visual appeal */}
      <section className="py-24 bg-gradient-to-br from-neutral-50 to-neutral-100 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center space-y-6 mb-20">
            <div className="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-semibold">
              <Star className="w-4 h-4" />
              <span>{messages.homepage.premiumServices}</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-heading font-bold text-neutral-800 leading-tight">
              {messages.services.title}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              {messages.services.subtitle}
            </p>
          </div>

          {/* Core Services Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Used Cars */}
            <AnimatedServiceCard
              images={serviceImages.usedCars.length > 0 ? serviceImages.usedCars : ['/used cars/voxy1.png']}
              iconName=""
              title={messages.services.usedCars}
              description={messages.services.usedCarsDesc}
            />

            {/* Car Parts & Tires */}
            <AnimatedServiceCard
              images={serviceImages.carParts.length > 0 ? serviceImages.carParts : ['/car parts and tires/car parts.jpeg']}
              iconName=""
              title={messages.services.carParts}
              description={messages.services.carPartsDesc}
            />

            {/* Electronics */}
            <AnimatedServiceCard
              images={serviceImages.electronics.length > 0 ? serviceImages.electronics : ['/electronics/electronics.png']}
              iconName=""
              title={messages.services.electronics}
              description={messages.services.electronicsDesc}
            />

            {/* Furniture */}
            <AnimatedServiceCard
              images={serviceImages.furniture.length > 0 ? serviceImages.furniture : ['/furniture/furniture.png']}
              iconName=""
              title={messages.services.furniture}
              description={messages.services.furnitureDesc}
            />
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <Link
              href={`/${locale}/stock`}
              className="inline-flex items-center space-x-3 bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-lg"
            >
              <span>{messages.homepage.exploreAllStock}</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Social Proof & Trust Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6 mb-16">
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800">
              {messages.homepage.trustedByCustomers}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {messages.homepage.trustedByCustomersDesc}
            </p>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-2">{messages.homepage.qualityAssured}</h3>
              <p className="text-neutral-600 text-sm">{messages.homepage.qualityAssuredDesc}</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ArrowRight className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-2">{messages.homepage.fastDelivery}</h3>
              <p className="text-neutral-600 text-sm">{messages.homepage.fastDeliveryDesc}</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-2">{messages.homepage.expertTeam}</h3>
              <p className="text-neutral-600 text-sm">{messages.homepage.expertTeamDesc}</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="font-semibold text-neutral-800 mb-2">{messages.homepage.bestPrices}</h3>
              <p className="text-neutral-600 text-sm">{messages.homepage.bestPricesDesc}</p>
            </div>
          </div>

          {/* Partnerships */}
          <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-8 md:p-12">
            <div className="text-center space-y-4 mb-12">
              <h2 className="text-3xl lg:text-4xl font-heading font-bold text-neutral-800">
                {messages.partnerships.title}
              </h2>
              <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
                {messages.partnerships.subtitle}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-xl p-8 text-center shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Car className="w-6 h-6 text-primary-600" />
                </div>
                <h3 className="font-bold text-neutral-800 mb-3 text-lg">{messages.partnerships.toyotaHonda}</h3>
                <p className="text-neutral-600">{messages.partnerships.toyotaHondaDesc}</p>
              </div>
              <div className="bg-white rounded-xl p-8 text-center shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Car className="w-6 h-6 text-primary-600" />
                </div>
                <h3 className="font-bold text-neutral-800 mb-3 text-lg">{messages.partnerships.movingCompanies}</h3>
                <p className="text-neutral-600">{messages.partnerships.movingCompaniesDesc}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Testimonials - Dynamic Reviews */}
      <CustomerReviews locale={locale} />

      {/* Trust Stats and Review CTA */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Trust stats */}
            <div className="inline-flex items-center space-x-8 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 shadow-lg mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">4.9/5</div>
                <div className="text-sm text-neutral-600">{messages.homepage.averageRating}</div>
              </div>
              <div className="w-px h-12 bg-neutral-200"></div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">500+</div>
                <div className="text-sm text-neutral-600">{messages.homepage.happyCustomers}</div>
              </div>
              <div className="w-px h-12 bg-neutral-200"></div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">98%</div>
                <div className="text-sm text-neutral-600">{messages.homepage.satisfactionRate}</div>
              </div>
            </div>

            {/* Customer Review CTA */}
            <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-neutral-800 mb-4">
                {messages.homepage.shareYourExperience}
              </h3>
              <p className="text-neutral-600 mb-6 max-w-2xl mx-auto">
                {messages.homepage.shareExperienceDesc}
              </p>
              <div className="flex justify-center">
                <Link
                  href={`/${locale}/leave-review`}
                  className="inline-flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-xl font-bold transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  <span>{messages.homepage.leaveAReview}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection locale={locale} />

      {/* WhatsApp Channel - Enhanced */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700">
        <WhatsAppChannel locale={locale} variant="full" showQR={true} />
      </section>

      {/* Why Choose Us Section - Enhanced with better visual design */}
      <section className="py-24 bg-gradient-to-br from-neutral-50 to-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full -translate-y-48 translate-x-48 opacity-30"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-secondary-100 rounded-full translate-y-48 -translate-x-48 opacity-30"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center space-y-6 mb-20">
            <div className="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-semibold">
              <CheckCircle className="w-4 h-4" />
              <span>{messages.homepage.whyChooseEbam}</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-heading font-bold text-neutral-800 leading-tight">
              {messages.whyChoose.title}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              {messages.whyChoose.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="flex items-start space-x-6 group">
                <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary-200 transition-colors duration-300">
                  <Star className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="font-bold text-neutral-800 mb-3 text-lg">{messages.whyChoose.globalExpertise}</h3>
                  <p className="text-neutral-600 leading-relaxed">{messages.whyChoose.globalExpertiseDesc}</p>
                </div>
              </div>

              <div className="flex items-start space-x-6 group">
                <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary-200 transition-colors duration-300">
                  <CheckCircle className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="font-bold text-neutral-800 mb-3 text-lg">{messages.whyChoose.qualityAssurance}</h3>
                  <p className="text-neutral-600 leading-relaxed">{messages.whyChoose.qualityAssuranceDesc}</p>
                </div>
              </div>

              <div className="flex items-start space-x-6 group">
                <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary-200 transition-colors duration-300">
                  <Car className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="font-bold text-neutral-800 mb-3 text-lg">{messages.whyChoose.reliableShipping}</h3>
                  <p className="text-neutral-600 leading-relaxed">{messages.whyChoose.reliableShippingDesc}</p>
                </div>
              </div>

              {/* CTA Button */}
              <div className="pt-6">
                <Link
                  href={`/${locale}/about`}
                  className="inline-flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-300"
                >
                  <span>{messages.homepage.learnMoreAboutUs}</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>

            <div className="relative">
              {/* Enhanced image gallery with better layout */}
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-6">
                  <AnimatedImageBackground
                    images={galleryImages.slice(0, 2).length > 0 ? galleryImages.slice(0, 2) : ['/used cars/voxy2.png']}
                    className="h-56 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
                    interval={4000}
                  />
                  <AnimatedImageBackground
                    images={galleryImages.slice(2, 4).length > 0 ? galleryImages.slice(2, 4) : ['/car parts and tires/tires.jpg']}
                    className="h-40 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
                    interval={4500}
                  />
                </div>
                <div className="space-y-6 pt-12">
                  <AnimatedImageBackground
                    images={galleryImages.slice(4, 6).length > 0 ? galleryImages.slice(4, 6) : ['/electronics/phones and laptops.jpg']}
                    className="h-40 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
                    interval={3500}
                  />
                  <AnimatedImageBackground
                    images={galleryImages.slice(6, 8).length > 0 ? galleryImages.slice(6, 8) : ['/move-out services/move out1.jpg']}
                    className="h-56 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
                    interval={5000}
                  />
                </div>
              </div>

              {/* Floating stats card */}
              <div className="absolute -bottom-6 -left-6 bg-white rounded-2xl p-6 shadow-xl border border-neutral-100">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">98%</div>
                  <div className="text-sm text-neutral-600">{messages.homepage.customerSatisfaction}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Contact Section - Enhanced with modern design */}
      <section className="py-24 bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 text-white relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-500/20 to-secondary-500/20"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Mission & Vision */}
            <div className="space-y-10">
              <div className="space-y-6">
                <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 text-sm font-semibold">
                  <Star className="w-4 h-4 text-secondary-400" />
                  <span className="text-white/90">{messages.homepage.ourMissionVision}</span>
                </div>
                <h2 className="text-4xl lg:text-5xl font-heading font-bold leading-tight">
                  {messages.mission.title}
                </h2>
              </div>

              <div className="space-y-8">
                <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
                  <h3 className="text-2xl font-bold text-secondary-400 mb-4 flex items-center space-x-3">
                    <div className="w-8 h-8 bg-secondary-500 rounded-lg flex items-center justify-center">
                      <Star className="w-4 h-4 text-neutral-900" />
                    </div>
                    <span>{messages.mission.missionLabel}</span>
                  </h3>
                  <p className="text-neutral-300 leading-relaxed text-lg">
                    {messages.mission.missionText}
                  </p>
                </div>

                <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
                  <h3 className="text-2xl font-bold text-secondary-400 mb-4 flex items-center space-x-3">
                    <div className="w-8 h-8 bg-secondary-500 rounded-lg flex items-center justify-center">
                      <ArrowRight className="w-4 h-4 text-neutral-900" />
                    </div>
                    <span>{messages.mission.visionLabel}</span>
                  </h3>
                  <p className="text-neutral-300 leading-relaxed text-lg">
                    {messages.mission.visionText}
                  </p>
                </div>
              </div>
            </div>

            {/* Contact & CTA */}
            <div className="text-center lg:text-left space-y-10">
              <div className="space-y-6">
                <h2 className="text-4xl lg:text-5xl font-heading font-bold leading-tight">
                  {messages.mission.readyTitle}
                </h2>
                <p className="text-xl text-neutral-300 leading-relaxed">
                  {messages.mission.readySubtitle}
                </p>
              </div>



              {/* Contact Info */}
              <div className="bg-gradient-to-r from-primary-600/20 to-secondary-600/20 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
                <h3 className="text-xl font-bold text-white mb-6">{messages.homepage.getInTouch}</h3>
                <div className="space-y-4 text-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-secondary-500 rounded-lg flex items-center justify-center">
                      <Star className="w-4 h-4 text-neutral-900" />
                    </div>
                    <span>080-6985-2864</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-secondary-500 rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-neutral-900" />
                    </div>
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-secondary-500 rounded-lg flex items-center justify-center">
                      <Star className="w-4 h-4 text-neutral-900" />
                    </div>
                    <span>Japan, Saitama, Hanno City, Nagata</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link
                  href={`/${locale}/contact`}
                  className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-10 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <span>{messages.homepage.contactUs}</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
                <Link
                  href={`/${locale}/suppliers`}
                  className="border-2 border-secondary-500 text-secondary-500 hover:bg-secondary-500 hover:text-neutral-900 px-10 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 backdrop-blur-sm bg-white/10"
                >
                  {messages.homepage.becomeSupplier}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
