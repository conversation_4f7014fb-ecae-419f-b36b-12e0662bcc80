(()=>{var e={};e.id=8746,e.ids=[2833,8746],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6337:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var r={};s.r(r),s.d(r,{GET:()=>d,POST:()=>m});var n=s(96559),o=s(48088),i=s(37719),a=s(32190),u=s(16967),c=s(92833),l=s(48137);async function m(e){let t=(0,l.HL)(e);try{let s=(0,c.Tf)(e),r=(0,c.Eb)(s,"contact");if(!r.allowed)return t.finish(429,"Rate limit exceeded"),a.NextResponse.json({success:!1,message:`Too many contact form submissions. Please try again in ${Math.ceil((r.resetTime-Date.now())/6e4)} minutes.`,resetTime:r.resetTime},{status:429});let n=await e.formData(),o=n.get("name"),i=n.get("email"),m=n.get("subject"),d=n.get("message"),p=n.get("phone"),g=n.get("company");if(!o||!i||!m||!d)return t.finish(400,"Missing required fields"),a.NextResponse.json({success:!1,message:"Name, email, subject, and message are required"},{status:400});let h=(0,c.o2)(o),f=(0,c.o2)(i),w=(0,c.o2)(m),x=(0,c.o2)(d),R=p?(0,c.o2)(p):"",v=g?(0,c.o2)(g):"";if(!(0,c.B9)(f))return t.finish(400,"Invalid email format"),a.NextResponse.json({success:!1,message:"Please provide a valid email address"},{status:400});if(R&&!(0,c.BH)(R))return t.finish(400,"Invalid phone format"),a.NextResponse.json({success:!1,message:"Please provide a valid phone number"},{status:400});let q=`${h} ${f} ${w} ${x} ${v}`;if((0,c.Ww)(q))return(0,c.h4)("Suspicious content in contact form",{name:h,email:f,subject:w},e),t.finish(400,"Suspicious content detected"),a.NextResponse.json({success:!1,message:"Your submission contains invalid content. Please review and try again."},{status:400});let y={name:h.trim(),email:f.trim().toLowerCase(),subject:w.trim(),message:x.trim(),submissionDate:new Date().toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",timeZone:"UTC"})},T=y.message;R&&(T+=`

Phone: ${R.trim()}`),v&&(T+=`
Company: ${v.trim()}`),y.message=T;let b=await u.gm.sendContactFormNotification(y);if(!b.success)return(0,l.vV)("Failed to send contact form emails",{endpoint:"/api/contact",method:"POST",request:e,additionalContext:{emailError:b.error,contactData:{name:h,email:f,subject:w}}}),a.NextResponse.json({success:!1,message:"Failed to send notification emails"},{status:500});t.finish(200);let S=a.NextResponse.json({success:!0,message:"Thank you for your message! We will respond within 24 hours.",data:{submissionId:`contact_${Date.now()}`,submittedAt:y.submissionDate}}),$=(0,c.getSecurityHeaders)();return Object.entries($).forEach(([e,t])=>{S.headers.set(e,t)}),S}catch(r){(0,l.vV)(r instanceof Error?r:"Unknown error",{endpoint:"/api/contact",method:"POST",request:e,additionalContext:{action:"contact_form_submission"}}),t.finish(500,r instanceof Error?r.message:"Unknown error");let s=a.NextResponse.json({success:!1,message:"Failed to process contact form submission"},{status:500});return Object.entries((0,c.getSecurityHeaders)()).forEach(([e,t])=>{s.headers.set(e,t)}),s}}async function d(e){try{let{searchParams:t}=new URL(e.url),s=t.get("action");if("validate-email"===s){let e=t.get("email");if(!e)return a.NextResponse.json({success:!1,message:"Email parameter is required"},{status:400});let s=/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e);return a.NextResponse.json({success:!0,data:{email:e,isValid:s,message:s?"Email is valid":"Please provide a valid email address"}})}return a.NextResponse.json({success:!0,message:"Contact form API is ready",endpoints:{submit:"POST /",validateEmail:"GET /?action=validate-email&email=<EMAIL>"}})}catch(e){return console.error("Error handling contact form GET request:",e),a.NextResponse.json({success:!1,message:"Failed to process request"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\contact\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:f}=p;function w(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48137:(e,t,s)=>{"use strict";s.d(t,{AD:()=>l,HL:()=>o,PG:()=>c,Sz:()=>a,ZX:()=>u,pX:()=>m,vV:()=>i});let r=[],n=[];function o(e){let t=Date.now(),s=new URL(e.url).pathname,n=e.method;return{endpoint:s,method:n,startTime:t,finish:(o,i)=>{let a=Date.now()-t,u=e.headers.get("user-agent")||void 0,c=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||void 0,l={timestamp:Date.now(),endpoint:s,method:n,duration:a,status:o,userAgent:u,ip:c,error:i};r.push(l),r.length>1e3&&r.splice(0,r.length-1e3),a>5e3&&console.warn(`[PERFORMANCE] Slow request detected: ${n} ${s} took ${a}ms`),o>=400&&console.error(`[ERROR] ${n} ${s} returned ${o}${i?`: ${i}`:""}`)}}}function i(e,t){let s=e instanceof Error?e.message:e,r=e instanceof Error?e.stack:void 0,o={timestamp:Date.now(),error:s,stack:r,endpoint:t.endpoint||"unknown",method:t.method||"unknown",userAgent:t.request?.headers.get("user-agent")||void 0,ip:t.request?.headers.get("x-forwarded-for")||t.request?.headers.get("x-real-ip")||void 0,context:t.additionalContext};n.push(o),n.length>500&&n.splice(0,n.length-500),console.error(`[ERROR] ${s}`,{endpoint:o.endpoint,method:o.method,ip:o.ip,context:o.context})}function a(e=36e5){let t=Date.now()-e,s=r.filter(e=>e.timestamp>t);if(0===s.length)return{totalRequests:0,averageResponseTime:0,errorRate:0,slowRequests:0,endpointStats:{}};let n=s.length,o=s.reduce((e,t)=>e+t.duration,0)/n,i=s.filter(e=>e.status>=400).length,u=s.filter(e=>e.duration>5e3).length,c={};return s.forEach(e=>{c[e.endpoint]||(c[e.endpoint]={requests:0,averageTime:0,errors:0,slowRequests:0});let t=c[e.endpoint];t.requests++,t.averageTime=(t.averageTime*(t.requests-1)+e.duration)/t.requests,e.status>=400&&t.errors++,e.duration>5e3&&t.slowRequests++}),{totalRequests:n,averageResponseTime:Math.round(o),errorRate:Math.round(i/n*1e4)/100,slowRequests:u,endpointStats:c}}function u(e=50){return n.slice(-e).reverse().map(e=>({timestamp:new Date(e.timestamp).toISOString(),error:e.error,endpoint:e.endpoint,method:e.method,ip:e.ip,context:e.context}))}function c(){let e=a(3e5),t=u(10),s="healthy",r=[];return e.errorRate>10?(s="unhealthy",r.push(`High error rate: ${e.errorRate}%`)):e.errorRate>5&&(s="degraded",r.push(`Elevated error rate: ${e.errorRate}%`)),e.averageResponseTime>1e4?(s="unhealthy",r.push(`Very slow response time: ${e.averageResponseTime}ms`)):e.averageResponseTime>5e3&&("healthy"===s&&(s="degraded"),r.push(`Slow response time: ${e.averageResponseTime}ms`)),e.slowRequests>.1*e.totalRequests&&("healthy"===s&&(s="degraded"),r.push(`High number of slow requests: ${e.slowRequests}`)),{status:s,timestamp:new Date().toISOString(),uptime:process.uptime(),issues:r,stats:{requests:e.totalRequests,averageResponseTime:e.averageResponseTime,errorRate:e.errorRate,slowRequests:e.slowRequests},recentErrors:t.slice(0,3)}}async function l(){try{let e=Date.now(),{sql:t}=await Promise.all([s.e(3376),s.e(7990)]).then(s.bind(s,83376));await t`SELECT 1 as health_check`;let r=Date.now()-e;return{healthy:!0,latency:r}}catch(e){return{healthy:!1,error:e instanceof Error?e.message:"Unknown database error"}}}function m(){let e=process.memoryUsage();return{rss:Math.round(e.rss/1024/1024),heapTotal:Math.round(e.heapTotal/1024/1024),heapUsed:Math.round(e.heapUsed/1024/1024),external:Math.round(e.external/1024/1024),arrayBuffers:Math.round(e.arrayBuffers/1024/1024)}}setInterval(function(){let e=Date.now()-36e5,t=r.filter(t=>t.timestamp>e);r.splice(0,r.length,...t);let s=n.filter(t=>t.timestamp>e);n.splice(0,n.length,...s)},6e5)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},92833:(e,t,s)=>{"use strict";s.d(t,{B9:()=>u,BH:()=>c,Eb:()=>o,Tf:()=>i,Ww:()=>l,getSecurityHeaders:()=>m,h4:()=>d,o2:()=>a});let r={auth:{maxAttempts:5,windowMs:9e5},api:{maxRequests:100,windowMs:6e4},contact:{maxSubmissions:3,windowMs:36e5},review:{maxSubmissions:2,windowMs:36e5}},n=new Map;function o(e,t){let s=r[t],o=Date.now(),i=`${t}:${e}`,a=n.get(i);if(!a||o>a.resetTime){let e={count:1,resetTime:o+s.windowMs};return n.set(i,e),{allowed:!0,remaining:s.maxAttempts-1,resetTime:e.resetTime}}return a.count>=s.maxAttempts?{allowed:!1,remaining:0,resetTime:a.resetTime}:(a.count++,n.set(i,a),{allowed:!0,remaining:s.maxAttempts-a.count,resetTime:a.resetTime})}function i(e){let t=e.headers.get("x-forwarded-for"),s=e.headers.get("x-real-ip"),r=e.headers.get("remote-addr");return t?t.split(",")[0].trim():s||r||"unknown"}function a(e){return"string"!=typeof e?"":e.trim().replace(/[<>]/g,"").replace(/javascript:/gi,"").replace(/on\w+=/gi,"").substring(0,1e3)}function u(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)&&e.length<=254}function c(e){return/^[\+]?[1-9][\d]{0,15}$/.test(e.replace(/[\s\-\(\)]/g,""))}function l(e){return[/script/gi,/javascript/gi,/vbscript/gi,/onload/gi,/onerror/gi,/onclick/gi,/<iframe/gi,/<object/gi,/<embed/gi,/eval\(/gi,/document\.cookie/gi,/window\.location/gi].some(t=>t.test(e))}function m(){return{"X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY","X-XSS-Protection":"1; mode=block","Referrer-Policy":"strict-origin-when-cross-origin","Content-Security-Policy":"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"}}function d(e,t,s){let r=new Date().toISOString();console.warn(`[SECURITY] ${r} - ${e}`,{ip:i(s),userAgent:s.headers.get("user-agent")||"unknown",details:t,url:s.url})}setInterval(function(){let e=Date.now();for(let[t,s]of n.entries())e>s.resetTime&&n.delete(t)},3e5)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,6967],()=>s(6337));module.exports=r})();