'use client';

import { useState, useRef } from 'react';
import { 
  X, 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Info
} from 'lucide-react';

interface CSVImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete: () => void;
}

interface ImportResult {
  success: boolean;
  batch_id: string;
  total_rows: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
  imported_cars: any[];
}

export default function CSVImportModal({ isOpen, onClose, onImportComplete }: CSVImportModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  if (!isOpen) return null;

  const handleFileSelect = (file: File) => {
    if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
      setSelectedFile(file);
      setImportResult(null);
    } else {
      alert('Please select a CSV file');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const downloadTemplate = () => {
    const csvContent = `car_id,make,model,year,title,price,original_price,mileage,fuel_type,transmission,engine_size,drive_type,seats,doors,body_type,body_condition,interior_condition,exterior_color,interior_color,main_image,images,image_folder,specs,features,status,stock_quantity,location,description,is_featured
toyota_voxy_2012_001,Toyota,Voxy,2012,Toyota Voxy 2012 - 8 Seater Family Van,300000,350000,85000,Gasoline,CVT,2.0L,2WD,8,5,Van,Good,Good,Silver,Black,/images/toyota/voxy/2012/main.jpg,"/images/toyota/voxy/2012/1.jpg,/images/toyota/voxy/2012/2.jpg",toyota/voxy/2012,"Air Conditioning,Power Steering,Electric Windows","Family Car,Spacious,Reliable",Available,1,Japan,Excellent condition Toyota Voxy with low mileage,false
honda_freed_2015_001,Honda,Freed,2015,Honda Freed 2015 - Compact Minivan,420000,450000,65000,Hybrid,CVT,1.5L,2WD,6,5,Minivan,Excellent,Excellent,White,Gray,/images/honda/freed/2015/main.jpg,"/images/honda/freed/2015/1.jpg,/images/honda/freed/2015/2.jpg",honda/freed/2015,"Hybrid Engine,Navigation System,Backup Camera","Fuel Efficient,Compact,Modern",Available,1,Japan,Low mileage Honda Freed hybrid in excellent condition,true`;
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'car_import_template.csv';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    setImporting(true);
    try {
      const formData = new FormData();
      formData.append('csvFile', selectedFile);
      // Get admin password from localStorage or use the one from .env.local
      const adminPassword = localStorage.getItem('admin_password') || 'NDAAA5@sons&Daughters';
      formData.append('adminKey', adminPassword);
      formData.append('importedBy', 'admin');

      const response = await fetch('/api/cars/import', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      setImportResult(result);

      if (result.success) {
        onImportComplete();
      }
    } catch (error) {
      console.error('Import error:', error);
      setImportResult({
        success: false,
        batch_id: '',
        total_rows: 0,
        successful_imports: 0,
        failed_imports: 0,
        errors: ['Failed to process import: ' + (error instanceof Error ? error.message : 'Unknown error')],
        imported_cars: []
      });
    } finally {
      setImporting(false);
    }
  };

  const resetModal = () => {
    setSelectedFile(null);
    setImportResult(null);
    setImporting(false);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Bulk Import Cars from CSV</h2>
          <button
            onClick={resetModal}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {!importResult ? (
            <>
              {/* Instructions */}
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start">
                  <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-2">How to import cars:</p>
                    <ol className="list-decimal list-inside space-y-1">
                      <li>Download the CSV template below</li>
                      <li>Fill in your car data (required: car_id, make, model, year, price)</li>
                      <li>Upload the completed CSV file</li>
                      <li>Review the import results</li>
                    </ol>
                  </div>
                </div>
              </div>

              {/* Template Download */}
              <div className="mb-6">
                <button
                  onClick={downloadTemplate}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download CSV Template
                </button>
              </div>

              {/* File Upload Area */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragOver
                    ? 'border-blue-400 bg-blue-50'
                    : selectedFile
                    ? 'border-green-400 bg-green-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                {selectedFile ? (
                  <div className="flex items-center justify-center">
                    <FileText className="w-8 h-8 text-green-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-green-800">{selectedFile.name}</p>
                      <p className="text-xs text-green-600">
                        {(selectedFile.size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                  </div>
                ) : (
                  <div>
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-900 mb-2">
                      Drop your CSV file here
                    </p>
                    <p className="text-sm text-gray-600 mb-4">
                      or click to browse files
                    </p>
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Select CSV File
                    </button>
                  </div>
                )}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileSelect(file);
                  }}
                  className="hidden"
                />
              </div>

              {/* Import Button */}
              {selectedFile && (
                <div className="mt-6 flex justify-end">
                  <button
                    onClick={handleImport}
                    disabled={importing}
                    className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {importing ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <Upload className="w-4 h-4 mr-2" />
                        Import Cars
                      </>
                    )}
                  </button>
                </div>
              )}
            </>
          ) : (
            /* Import Results */
            <div className="space-y-6">
              {/* Summary */}
              <div className={`p-4 rounded-lg border ${
                importResult.success 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center">
                  {importResult.success ? (
                    <CheckCircle className="w-6 h-6 text-green-600 mr-3" />
                  ) : (
                    <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
                  )}
                  <div>
                    <h3 className={`font-medium ${
                      importResult.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {importResult.success ? 'Import Completed' : 'Import Failed'}
                    </h3>
                    <p className={`text-sm ${
                      importResult.success ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {importResult.successful_imports} of {importResult.total_rows} cars imported successfully
                    </p>
                  </div>
                </div>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">{importResult.total_rows}</p>
                  <p className="text-sm text-gray-600">Total Rows</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">{importResult.successful_imports}</p>
                  <p className="text-sm text-gray-600">Successful</p>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <p className="text-2xl font-bold text-red-600">{importResult.failed_imports}</p>
                  <p className="text-sm text-gray-600">Failed</p>
                </div>
              </div>

              {/* Errors */}
              {importResult.errors.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Import Errors:</h4>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                    {importResult.errors.map((error, index) => (
                      <p key={index} className="text-sm text-red-700 mb-1">
                        {error}
                      </p>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={resetModal}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    setSelectedFile(null);
                    setImportResult(null);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Import Another File
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
