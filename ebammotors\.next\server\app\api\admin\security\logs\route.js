(()=>{var e={};e.id=2454,e.ids=[2454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,s)=>{"use strict";s.d(t,{Qq:()=>m,Tq:()=>g,bS:()=>d,fF:()=>l,mU:()=>c});var i=s(85663),n=s(43205),r=s.n(n);let a=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await i.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),s=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return r().sign(t,a,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,s=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:s,expiresAt:s+864e5,lastActivity:s}),function(){let e=Date.now();for(let[t,s]of o.entries())e>s.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function l(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=r().verify(e,a);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let s=Date.now();return s>t.expiresAt?(o.delete(e),null):(t.lastActivity=s,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),s=p.get(e);return!s||t-s.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):s.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-s.lastAttempt)}:(s.count++,s.lastAttempt=t,p.set(e,s),{allowed:!0,remainingAttempts:5-s.count})}function g(e){p.delete(e)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69035:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>A,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var i={};s.r(i),s.d(i,{GET:()=>c,POST:()=>l});var n=s(96559),r=s(48088),a=s(37719),o=s(32190),u=s(77268);async function c(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await d();return o.NextResponse.json({success:!0,logs:t})}catch(e){return console.error("Error fetching security logs:",e),o.NextResponse.json({success:!1,message:"Failed to fetch security logs"},{status:500})}}async function d(){return[{id:"1",timestamp:new Date(Date.now()-3e5).toISOString(),action:"login",user:"admin",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",location:"Tokyo, Japan",status:"success",details:"Successful admin login"},{id:"2",timestamp:new Date(Date.now()-9e5).toISOString(),action:"failed_login",user:"unknown",ipAddress:"************",userAgent:"curl/7.68.0",location:"Unknown",status:"failed",details:"Invalid credentials provided"},{id:"3",timestamp:new Date(Date.now()-18e5).toISOString(),action:"password_change",user:"admin",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",location:"Tokyo, Japan",status:"success",details:"Password successfully changed"},{id:"4",timestamp:new Date(Date.now()-36e5).toISOString(),action:"settings_changed",user:"admin",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",location:"Tokyo, Japan",status:"success",details:"Security settings updated"},{id:"5",timestamp:new Date(Date.now()-72e5).toISOString(),action:"failed_login",user:"admin",ipAddress:"*************",userAgent:"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",location:"Unknown",status:"failed",details:"Multiple failed login attempts detected"},{id:"6",timestamp:new Date(Date.now()-108e5).toISOString(),action:"logout",user:"admin",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",location:"Tokyo, Japan",status:"success",details:"User logged out successfully"},{id:"7",timestamp:new Date(Date.now()-216e5).toISOString(),action:"login",user:"admin",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",location:"Tokyo, Japan",status:"success",details:"Successful admin login"},{id:"8",timestamp:new Date(Date.now()-432e5).toISOString(),action:"user_created",user:"admin",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",location:"Tokyo, Japan",status:"success",details:"New admin user created: moderator1"},{id:"9",timestamp:new Date(Date.now()-864e5).toISOString(),action:"failed_login",user:"unknown",ipAddress:"************",userAgent:"python-requests/2.25.1",location:"Unknown",status:"failed",details:"Automated attack attempt detected"},{id:"10",timestamp:new Date(Date.now()-1728e5).toISOString(),action:"settings_changed",user:"admin",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",location:"Tokyo, Japan",status:"success",details:"Password policy updated"}].sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime())}async function l(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{action:t,user:s,ipAddress:i,userAgent:n,details:r,status:a="success"}=await e.json();if(!t||!s||!i)return o.NextResponse.json({success:!1,message:"Missing required fields"},{status:400});let c={id:Date.now().toString(),timestamp:new Date().toISOString(),action:t,user:s,ipAddress:i,userAgent:n||"Unknown",location:"Unknown",status:a,details:r||`${t} performed by ${s}`};return o.NextResponse.json({success:!0,message:"Security log entry created",logEntry:c})}catch(e){return console.error("Error creating security log:",e),o.NextResponse.json({success:!1,message:"Failed to create security log"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/security/logs/route",pathname:"/api/admin/security/logs",filename:"route",bundlePath:"app/api/admin/security/logs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\security\\logs\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:w}=p;function A(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},77268:(e,t,s)=>{"use strict";s.d(t,{iY:()=>n}),s(32190);var i=s(12909);function n(e,t){let s=e.headers.get("authorization"),n=e.cookies.get("admin_session")?.value,r=(0,i.fF)(s,n);if(r.isValid)return{isValid:!0,adminId:r.adminId,method:"token/session"};let a=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return a&&a===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[4447,580,7696],()=>s(69035));module.exports=i})();