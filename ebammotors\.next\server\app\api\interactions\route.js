(()=>{var t={};t.id=2293,t.ids=[2293],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23870:(t,e,n)=>{"use strict";n.d(e,{A:()=>c});var r=n(55511);let a={randomUUID:r.randomUUID},i=new Uint8Array(256),s=i.length,o=[];for(let t=0;t<256;++t)o.push((t+256).toString(16).slice(1));let c=function(t,e,n){if(a.randomUUID&&!e&&!t)return a.randomUUID();let c=(t=t||{}).random??t.rng?.()??(s>i.length-16&&((0,r.randomFillSync)(i),s=0),i.slice(s,s+=16));if(c.length<16)throw Error("Random bytes length must be >= 16");if(c[6]=15&c[6]|64,c[8]=63&c[8]|128,e){if((n=n||0)<0||n+16>e.length)throw RangeError(`UUID byte range ${n}:${n+15} is out of buffer bounds`);for(let t=0;t<16;++t)e[n+t]=c[t];return e}return function(t,e=0){return(o[t[e+0]]+o[t[e+1]]+o[t[e+2]]+o[t[e+3]]+"-"+o[t[e+4]]+o[t[e+5]]+"-"+o[t[e+6]]+o[t[e+7]]+"-"+o[t[e+8]]+o[t[e+9]]+"-"+o[t[e+10]]+o[t[e+11]]+o[t[e+12]]+o[t[e+13]]+o[t[e+14]]+o[t[e+15]]).toLowerCase()}(c)}},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:t=>{"use strict";t.exports=require("path")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50974:(t,e,n)=>{"use strict";n.r(e),n.d(e,{patchFetch:()=>h,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>g});var r={};n.r(r),n.d(r,{GET:()=>u,POST:()=>d,logChatbotInteraction:()=>l,logOrderInteraction:()=>w,logWebsiteActivity:()=>p});var a=n(96559),i=n(48088),s=n(37719),o=n(32190),c=n(53190);async function u(t){try{let{searchParams:e}=new URL(t.url),n=e.get("adminKey"),r=e.get("customerId"),a=e.get("leadId"),i=process.env.ADMIN_PASSWORD||"admin123";if(n!==i)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=[];return(s=r?await (0,c.qz)(r):a?await (0,c.Gq)(a):await (0,c.fE)()).sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()),o.NextResponse.json({success:!0,interactions:s})}catch(t){return console.error("Error fetching interactions:",t),o.NextResponse.json({success:!1,message:"Failed to fetch interactions"},{status:500})}}async function d(t){try{let e=await t.json();if(!e.type||!e.content)return o.NextResponse.json({success:!1,message:"Type and content are required"},{status:400});let n={direction:"inbound",channel:"website",tags:[],createdBy:"system",...e},r=await (0,c.createInteraction)(n);return o.NextResponse.json({success:!0,message:"Interaction logged successfully",interaction:r})}catch(t){return console.error("Error creating interaction:",t),o.NextResponse.json({success:!1,message:"Failed to log interaction"},{status:500})}}async function l(t,e,n,r,a){try{return await (0,c.createInteraction)({customerId:t,leadId:e,type:"chat",direction:"inbound",channel:"website",content:n,subject:"Chatbot Conversation",tags:a?["chatbot","product_interest",a]:["chatbot"],createdBy:t||"anonymous"}),await (0,c.createInteraction)({customerId:t,leadId:e,type:"chat",direction:"outbound",channel:"website",content:r,subject:"Chatbot Response",tags:["chatbot","automated_response"],createdBy:"system"}),!0}catch(t){return console.error("Error logging chatbot interaction:",t),!1}}async function w(t,e,n,r,a){try{return await (0,c.createInteraction)({customerId:t,type:n,direction:"outbound",channel:"website",content:r,subject:`Order ${n.charAt(0).toUpperCase()+n.slice(1)} Update`,tags:["order",n,a||"update"].filter(Boolean),relatedOrderId:e,createdBy:"system"}),!0}catch(t){return console.error("Error logging order interaction:",t),!1}}async function p(t,e,r){try{let{createCustomerActivity:a}=await Promise.resolve().then(n.bind(n,53190));return await a({customerId:t,type:e,details:r}),("product_view"===e||"search"===e)&&await (0,c.createInteraction)({customerId:t,type:"website_visit",direction:"inbound",channel:"website",content:"product_view"===e?`Viewed product: ${r.productId}`:`Searched for: ${r.searchQuery}`,subject:"Website Activity",tags:["website_activity",e],createdBy:t,metadata:r}),!0}catch(t){return console.error("Error logging website activity:",t),!1}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/interactions/route",pathname:"/api/interactions",filename:"route",bundlePath:"app/api/interactions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\interactions\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:y,workUnitAsyncStorage:g,serverHooks:m}=f;function h(){return(0,s.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:g})}},53190:(t,e,n)=>{"use strict";n.d(e,{Gk:()=>T,Gq:()=>L,HR:()=>j,Kt:()=>U,Q6:()=>O,Rf:()=>E,XL:()=>G,Y2:()=>R,_Y:()=>H,aN:()=>Z,createCustomerActivity:()=>X,createInteraction:()=>B,dD:()=>z,fE:()=>J,getAllFollowUps:()=>W,getCustomerByEmail:()=>C,getCustomerById:()=>q,getLeadById:()=>v,getPendingFollowUps:()=>K,oP:()=>tt,qz:()=>P,sr:()=>x,tR:()=>D,tS:()=>b,updateFollowUp:()=>Y});var r=n(29021),a=n(33873),i=n.n(a),s=n(23870);let o=i().join(process.cwd(),"data"),c=i().join(o,"leads.json"),u=i().join(o,"customers.json"),d=i().join(o,"interactions.json"),l=i().join(o,"followups.json"),w=i().join(o,"activities.json"),p=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,f=[],y=[],g=[],m=[],h=[];async function I(){if(!p)try{await r.promises.access(o)}catch{await r.promises.mkdir(o,{recursive:!0})}}async function S(){if(p)return f;try{await I();let t=await r.promises.readFile(c,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function A(t){if(p){f=t;return}await I(),await r.promises.writeFile(c,JSON.stringify(t,null,2))}async function D(t){let e=await S(),n={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(n),await A(e),n}async function b(){return await S()}async function v(t){return(await S()).find(e=>e.id===t)||null}async function O(t,e){let n=await S(),r=n.findIndex(e=>e.id===t);return -1!==r&&(n[r]={...n[r],...e,updatedAt:new Date().toISOString()},await A(n),!0)}async function x(t){let e=await S(),n=e.filter(e=>e.id!==t);return n.length!==e.length&&(await A(n),!0)}async function j(t){return(await S()).filter(e=>e.status===t)}async function U(t){return(await S()).filter(e=>e.source===t)}async function F(){if(p)return y;try{await I();let t=await r.promises.readFile(u,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function N(t){if(p){y=t;return}await I(),await r.promises.writeFile(u,JSON.stringify(t,null,2))}async function R(t){let e=await F(),n=e.findIndex(e=>e.personalInfo.email===t.personalInfo.email);if(-1!==n)return e[n]={...e[n],...t,updatedAt:new Date().toISOString()},await N(e),e[n];{let n={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(n),await N(e),n}}async function E(){return await F()}async function q(t){return(await F()).find(e=>e.id===t)||null}async function C(t){return(await F()).find(e=>e.personalInfo.email===t)||null}async function T(t,e){let n=await F(),r=n.findIndex(e=>e.id===t);return -1!==r&&(n[r]={...n[r],...e,updatedAt:new Date().toISOString()},await N(n),!0)}async function _(){if(p)return g;try{await I();let t=await r.promises.readFile(d,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function k(t){if(p){g=t;return}await I(),await r.promises.writeFile(d,JSON.stringify(t,null,2))}async function B(t){let e=await _(),n={...t,id:(0,s.A)(),createdAt:new Date().toISOString()};return e.push(n),await k(e),n}async function J(){return await _()}async function P(t){return(await _()).filter(e=>e.customerId===t)}async function L(t){return(await _()).filter(e=>e.leadId===t)}async function M(){if(p)return m;try{await I();let t=await r.promises.readFile(l,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function $(t){if(p){m=t;return}await I(),await r.promises.writeFile(l,JSON.stringify(t,null,2))}async function G(t){let e=await M(),n={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(n),await $(e),n}async function W(){return await M()}async function z(t){return(await M()).filter(e=>e.status===t)}async function K(){let t=await M(),e=new Date().toISOString();return t.filter(t=>"pending"===t.status&&t.scheduledDate<=e)}async function Y(t,e){let n=await M(),r=n.findIndex(e=>e.id===t);return -1!==r&&(n[r]={...n[r],...e,updatedAt:new Date().toISOString()},await $(n),!0)}async function H(t){return(await M()).filter(e=>e.customerId===t)}async function Q(){if(p)return h;try{await I();let t=await r.promises.readFile(w,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function V(t){if(p){h=t;return}await I(),await r.promises.writeFile(w,JSON.stringify(t,null,2))}async function X(t){let e=await Q(),n={...t,id:(0,s.A)(),timestamp:new Date().toISOString()};return e.push(n),await V(e),n}async function Z(t){return(await Q()).filter(e=>e.customerId===t)}async function tt(t){let e=await q(t);if(!e)return null;let n=await P(t),r=await H(t),a=await Z(t);return{customer:e,stats:{totalInteractions:n.length,pendingFollowUps:r.filter(t=>"pending"===t.status).length,recentActivities:a.filter(t=>new Date(t.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:n.sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime())[0]?.createdAt},recentInteractions:n.sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()).slice(0,5),upcomingFollowUps:r.filter(t=>"pending"===t.status).sort((t,e)=>new Date(t.scheduledDate).getTime()-new Date(e.scheduledDate).getTime()).slice(0,3)}}},55511:t=>{"use strict";t.exports=require("crypto")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var e=require("../../../webpack-runtime.js");e.C(t);var n=t=>e(e.s=t),r=e.X(0,[4447,580],()=>n(50974));module.exports=r})();