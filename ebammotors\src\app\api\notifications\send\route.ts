import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import webpush from 'web-push';
import { getAdminAuth } from '@/lib/adminAuth';

// File path for storing push subscriptions
const SUBSCRIPTIONS_FILE = path.join(process.cwd(), 'data', 'push-subscriptions.json');

// Configure web-push only if valid VAPID keys are provided
const configureWebPush = () => {
  const publicKey = process.env.VAPID_PUBLIC_KEY;
  const privateKey = process.env.VAPID_PRIVATE_KEY;

  if (publicKey && privateKey && publicKey.length > 20 && privateKey.length > 20) {
    try {
      webpush.setVapidDetails(
        'mailto:<EMAIL>',
        publicKey,
        privateKey
      );
      return true;
    } catch (error) {
      console.warn('Failed to configure VAPID keys:', error);
      return false;
    }
  }
  return false;
};

const isWebPushConfigured = configureWebPush();

// Load existing subscriptions
async function loadSubscriptions() {
  try {
    const data = await fs.readFile(SUBSCRIPTIONS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

// POST - Send push notification
export async function POST(request: NextRequest) {
  try {
    // Check if web push is configured
    if (!isWebPushConfigured) {
      return NextResponse.json(
        { success: false, message: 'Push notifications not configured. Please set VAPID keys.' },
        { status: 503 }
      );
    }

    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { 
      title, 
      body, 
      icon, 
      badge, 
      image, 
      data, 
      actions, 
      tag, 
      targetUsers, 
      notificationType 
    } = await request.json();

    // Validate required fields
    if (!title || !body) {
      return NextResponse.json(
        { success: false, message: 'Title and body are required' },
        { status: 400 }
      );
    }

    // Load subscriptions
    const allSubscriptions = await loadSubscriptions();
    
    // Filter subscriptions based on target users and preferences
    let targetSubscriptions = allSubscriptions;
    
    if (targetUsers && targetUsers.length > 0) {
      targetSubscriptions = allSubscriptions.filter((sub: any) => 
        targetUsers.includes(sub.userId)
      );
    }

    // Filter by notification preferences
    if (notificationType) {
      targetSubscriptions = targetSubscriptions.filter((sub: any) => {
        const preferences = sub.preferences || {};
        switch (notificationType) {
          case 'orderUpdates':
            return preferences.orderUpdates !== false;
          case 'newStock':
            return preferences.newStock !== false;
          case 'promotions':
            return preferences.promotions === true;
          case 'priceDrops':
            return preferences.priceDrops !== false;
          case 'newsletter':
            return preferences.newsletter === true;
          case 'reminders':
            return preferences.reminders !== false;
          default:
            return true;
        }
      });
    }

    if (targetSubscriptions.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No target subscriptions found',
        sent: 0,
        failed: 0,
      });
    }

    // Prepare notification payload
    const notificationPayload = {
      title,
      body,
      icon: icon || '/icons/icon-192x192.png',
      badge: badge || '/icons/badge-72x72.png',
      image,
      data: {
        ...data,
        timestamp: Date.now(),
        url: data?.url || '/',
      },
      actions,
      tag,
      requireInteraction: false,
      silent: false,
      vibrate: [100, 50, 100],
    };

    // Send notifications
    const results = await Promise.allSettled(
      targetSubscriptions.map(async (subscription: any) => {
        try {
          await webpush.sendNotification(
            {
              endpoint: subscription.endpoint,
              keys: subscription.keys,
            },
            JSON.stringify(notificationPayload)
          );
          return { success: true, endpoint: subscription.endpoint };
        } catch (error: any) {
          console.error('Failed to send notification:', error);
          
          // Remove invalid subscriptions
          if (error.statusCode === 410 || error.statusCode === 404) {
            console.log('Removing invalid subscription:', subscription.endpoint);
            await removeInvalidSubscription(subscription.endpoint);
          }
          
          return { success: false, endpoint: subscription.endpoint, error: error.message };
        }
      })
    );

    // Count results
    const sent = results.filter(result => 
      result.status === 'fulfilled' && result.value.success
    ).length;
    
    const failed = results.filter(result => 
      result.status === 'rejected' || 
      (result.status === 'fulfilled' && !result.value.success)
    ).length;

    console.log(`Push notifications sent: ${sent} successful, ${failed} failed`);

    return NextResponse.json({
      success: true,
      message: `Notifications sent successfully`,
      sent,
      failed,
      total: targetSubscriptions.length,
    });

  } catch (error) {
    console.error('Error sending push notifications:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send notifications' },
      { status: 500 }
    );
  }
}

// Remove invalid subscription
async function removeInvalidSubscription(endpoint: string) {
  try {
    const subscriptions = await loadSubscriptions();
    const filteredSubscriptions = subscriptions.filter(
      (sub: any) => sub.endpoint !== endpoint
    );
    
    const dataDir = path.dirname(SUBSCRIPTIONS_FILE);
    try {
      await fs.access(dataDir);
    } catch {
      await fs.mkdir(dataDir, { recursive: true });
    }
    
    await fs.writeFile(SUBSCRIPTIONS_FILE, JSON.stringify(filteredSubscriptions, null, 2));
  } catch (error) {
    console.error('Error removing invalid subscription:', error);
  }
}

// GET - Get notification statistics
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const subscriptions = await loadSubscriptions();
    
    // Calculate statistics
    const stats = {
      totalSubscriptions: subscriptions.length,
      activeSubscriptions: subscriptions.filter((sub: any) => {
        const lastUsed = new Date(sub.lastUsed);
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        return lastUsed > thirtyDaysAgo;
      }).length,
      preferenceBreakdown: {
        orderUpdates: subscriptions.filter((sub: any) => sub.preferences?.orderUpdates !== false).length,
        newStock: subscriptions.filter((sub: any) => sub.preferences?.newStock !== false).length,
        promotions: subscriptions.filter((sub: any) => sub.preferences?.promotions === true).length,
        priceDrops: subscriptions.filter((sub: any) => sub.preferences?.priceDrops !== false).length,
        newsletter: subscriptions.filter((sub: any) => sub.preferences?.newsletter === true).length,
        reminders: subscriptions.filter((sub: any) => sub.preferences?.reminders !== false).length,
      },
    };

    return NextResponse.json({
      success: true,
      stats,
    });

  } catch (error) {
    console.error('Error getting notification statistics:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get statistics' },
      { status: 500 }
    );
  }
}
