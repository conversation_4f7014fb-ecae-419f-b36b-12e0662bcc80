(()=>{var e={};e.id=886,e.ids=[886,2273,4654],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>m,Tq:()=>f,bS:()=>l,fF:()=>d,mU:()=>c});var n=r(85663),a=r(43205),i=r.n(a);let s=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await n.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function l(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),r=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(t,s,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let e=Date.now();for(let[t,r]of o.entries())e>r.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function d(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=i().verify(e,s);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let r=Date.now();return r>t.expiresAt?(o.delete(e),null):(t.lastActivity=r,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),r=p.get(e);return!r||t-r.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-r.lastAttempt)}:(r.count++,r.lastAttempt=t,p.set(e,r),{allowed:!0,remainingAttempts:5-r.count})}function f(e){p.delete(e)}},18868:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var n={};r.r(n),r.d(n,{GET:()=>d,POST:()=>l});var a=r(96559),i=r(48088),s=r(37719),o=r(32190),u=r(77268),c=r(62273);async function l(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});return function(){try{(0,c.OW)()}catch(e){console.error("❌ Error initializing services:",e)}}(),o.NextResponse.json({success:!0,message:"System services initialized successfully",services:{automatedFollowups:!0,emailService:!0,crmStorage:!0}})}catch(e){return console.error("Error initializing system services:",e),o.NextResponse.json({success:!1,message:"Failed to initialize system services"},{status:500})}}async function d(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t={timestamp:new Date().toISOString(),services:{automatedFollowups:{enabled:"true"===process.env.ENABLE_AUTOMATION||!0,nextProcessing:new Date(Date.now()+36e5).toISOString()},emailService:{enabled:!!process.env.RESEND_API_KEY,provider:"resend"},smsService:{enabled:!1,provider:"none"},crmStorage:{enabled:!0,type:"file-based"}},environment:{nodeEnv:"production",automationEnabled:"true"===process.env.ENABLE_AUTOMATION}};return o.NextResponse.json({success:!0,status:t})}catch(e){return console.error("Error checking system status:",e),o.NextResponse.json({success:!1,message:"Failed to check system status"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/system/init/route",pathname:"/api/admin/system/init",filename:"route",bundlePath:"app/api/admin/system/init/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\system\\init\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:w}=p;function y(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},23870:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(55511);let a={randomUUID:n.randomUUID},i=new Uint8Array(256),s=i.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(a.randomUUID&&!t&&!e)return a.randomUUID();let u=(e=e||{}).random??e.rng?.()??(s>i.length-16&&((0,n.randomFillSync)(i),s=0),i.slice(s,s+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=u[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(u)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53190:(e,t,r)=>{"use strict";r.d(t,{Gk:()=>k,Gq:()=>C,HR:()=>F,Kt:()=>b,Q6:()=>E,Rf:()=>R,XL:()=>J,Y2:()=>U,_Y:()=>K,aN:()=>Z,createCustomerActivity:()=>Q,createInteraction:()=>M,dD:()=>z,fE:()=>L,getAllFollowUps:()=>V,getCustomerByEmail:()=>j,getCustomerById:()=>T,getLeadById:()=>x,getPendingFollowUps:()=>Y,oP:()=>ee,qz:()=>P,sr:()=>O,tR:()=>D,tS:()=>v,updateFollowUp:()=>X});var n=r(29021),a=r(33873),i=r.n(a),s=r(23870);let o=i().join(process.cwd(),"data"),u=i().join(o,"leads.json"),c=i().join(o,"customers.json"),l=i().join(o,"interactions.json"),d=i().join(o,"followups.json"),p=i().join(o,"activities.json"),m=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,f=[],w=[],y=[],g=[],h=[];async function S(){if(!m)try{await n.promises.access(o)}catch{await n.promises.mkdir(o,{recursive:!0})}}async function I(){if(m)return f;try{await S();let e=await n.promises.readFile(u,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function A(e){if(m){f=e;return}await S(),await n.promises.writeFile(u,JSON.stringify(e,null,2))}async function D(e){let t=await I(),r={...e,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await A(t),r}async function v(){return await I()}async function x(e){return(await I()).find(t=>t.id===e)||null}async function E(e,t){let r=await I(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},await A(r),!0)}async function O(e){let t=await I(),r=t.filter(t=>t.id!==e);return r.length!==t.length&&(await A(r),!0)}async function F(e){return(await I()).filter(t=>t.status===e)}async function b(e){return(await I()).filter(t=>t.source===e)}async function N(){if(m)return w;try{await S();let e=await n.promises.readFile(c,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function _(e){if(m){w=e;return}await S(),await n.promises.writeFile(c,JSON.stringify(e,null,2))}async function U(e){let t=await N(),r=t.findIndex(t=>t.personalInfo.email===e.personalInfo.email);if(-1!==r)return t[r]={...t[r],...e,updatedAt:new Date().toISOString()},await _(t),t[r];{let r={...e,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await _(t),r}}async function R(){return await N()}async function T(e){return(await N()).find(t=>t.id===e)||null}async function j(e){return(await N()).find(t=>t.personalInfo.email===e)||null}async function k(e,t){let r=await N(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},await _(r),!0)}async function $(){if(m)return y;try{await S();let e=await n.promises.readFile(l,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function q(e){if(m){y=e;return}await S(),await n.promises.writeFile(l,JSON.stringify(e,null,2))}async function M(e){let t=await $(),r={...e,id:(0,s.A)(),createdAt:new Date().toISOString()};return t.push(r),await q(t),r}async function L(){return await $()}async function P(e){return(await $()).filter(t=>t.customerId===e)}async function C(e){return(await $()).filter(t=>t.leadId===e)}async function W(){if(m)return g;try{await S();let e=await n.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function B(e){if(m){g=e;return}await S(),await n.promises.writeFile(d,JSON.stringify(e,null,2))}async function J(e){let t=await W(),r={...e,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await B(t),r}async function V(){return await W()}async function z(e){return(await W()).filter(t=>t.status===e)}async function Y(){let e=await W(),t=new Date().toISOString();return e.filter(e=>"pending"===e.status&&e.scheduledDate<=t)}async function X(e,t){let r=await W(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},await B(r),!0)}async function K(e){return(await W()).filter(t=>t.customerId===e)}async function G(){if(m)return h;try{await S();let e=await n.promises.readFile(p,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function H(e){if(m){h=e;return}await S(),await n.promises.writeFile(p,JSON.stringify(e,null,2))}async function Q(e){let t=await G(),r={...e,id:(0,s.A)(),timestamp:new Date().toISOString()};return t.push(r),await H(t),r}async function Z(e){return(await G()).filter(t=>t.customerId===e)}async function ee(e){let t=await T(e);if(!t)return null;let r=await P(e),n=await K(e),a=await Z(e);return{customer:t,stats:{totalInteractions:r.length,pendingFollowUps:n.filter(e=>"pending"===e.status).length,recentActivities:a.filter(e=>new Date(e.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:r.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())[0]?.createdAt},recentInteractions:r.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()).slice(0,5),upcomingFollowUps:n.filter(e=>"pending"===e.status).sort((e,t)=>new Date(e.scheduledDate).getTime()-new Date(t.scheduledDate).getTime()).slice(0,3)}}},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},62273:(e,t,r)=>{"use strict";r.d(t,{OW:()=>p,cz:()=>m,scheduleAutoFollowupForCustomer:()=>s,scheduleAutoFollowupForLead:()=>o});var n=r(53190),a=r(16967);let i={delayDays:3,enableEmail:!0,enableSMS:!0,emailTemplate:"customer_followup",smsTemplate:"customer_followup_sms"};async function s(e,t){try{let r=new Date;r.setDate(r.getDate()+i.delayDays),i.enableEmail&&await (0,n.XL)({type:"email",status:"pending",priority:"medium",customerId:e,title:"3-Day Customer Follow-up (Email)",description:"Automated follow-up email to check customer satisfaction and offer assistance",scheduledDate:r.toISOString(),automationRule:{trigger:"customer_created",delay:24*i.delayDays,conditions:{type:"email",template:i.emailTemplate}},createdBy:"system"}),i.enableSMS&&t.personalInfo?.phone&&await (0,n.XL)({type:"sms",status:"pending",priority:"medium",customerId:e,title:"3-Day Customer Follow-up (SMS)",description:"Automated SMS follow-up to check customer satisfaction",scheduledDate:r.toISOString(),automationRule:{trigger:"customer_created",delay:24*i.delayDays,conditions:{type:"sms",template:i.smsTemplate,phone:t.personalInfo.phone}},createdBy:"system"})}catch(e){console.error("Error scheduling auto follow-up for customer:",e)}}async function o(e,t){try{let r=new Date;r.setDate(r.getDate()+i.delayDays),i.enableEmail&&t.customerInfo?.email&&await (0,n.XL)({type:"email",status:"pending",priority:"high",leadId:e,title:"3-Day Lead Follow-up (Email)",description:"Automated follow-up email for lead nurturing and conversion",scheduledDate:r.toISOString(),automationRule:{trigger:"lead_created",delay:24*i.delayDays,conditions:{type:"email",template:"lead_followup",leadSource:t.source,productInterest:t.inquiry?.productInterest}},createdBy:"system"}),i.enableSMS&&t.customerInfo?.phone&&await (0,n.XL)({type:"sms",status:"pending",priority:"high",leadId:e,title:"3-Day Lead Follow-up (SMS)",description:"Automated SMS follow-up for lead conversion",scheduledDate:r.toISOString(),automationRule:{trigger:"lead_created",delay:24*i.delayDays,conditions:{type:"sms",template:"lead_followup_sms",phone:t.customerInfo.phone,productInterest:t.inquiry?.productInterest}},createdBy:"system"})}catch(e){console.error("Error scheduling auto follow-up for lead:",e)}}async function u(){try{let{getAllFollowUps:e,updateFollowUp:t,getCustomerById:n,getLeadById:a}=await Promise.resolve().then(r.bind(r,53190)),i=await e(),s=new Date;for(let e of i.filter(e=>"pending"===e.status&&e.automationRule&&new Date(e.scheduledDate)<=s))try{await c(e),await t(e.id,{status:"completed",completedAt:new Date().toISOString(),notes:`${e.notes||""}

Automatically processed on ${new Date().toLocaleString()}`})}catch(r){console.error(`Error processing followup ${e.id}:`,r),await t(e.id,{status:"failed",notes:`${e.notes||""}

Failed to process: ${r instanceof Error?r.message:"Unknown error"}`})}}catch(e){console.error("Error processing automated follow-ups:",e)}}async function c(e){let{getCustomerById:t,getLeadById:a}=await Promise.resolve().then(r.bind(r,53190)),i=null,s="";if(e.customerId?(i=await t(e.customerId),s="customer"):e.leadId&&(i=await a(e.leadId),s="lead"),!i)throw Error(`Recipient not found for followup ${e.id}`);let o=e.automationRule?.conditions||{};"email"===e.type?await l(i,s,o,e):"sms"===e.type&&await d(i,s,o,e),await (0,n.createInteraction)({customerId:e.customerId,leadId:e.leadId,type:"email"===e.type?"email":"sms",direction:"outbound",channel:"automation",content:`Automated ${e.type} follow-up sent: ${e.title}`,subject:e.title,tags:["automated","follow_up",e.type],createdBy:"system"})}async function l(e,t,r,n){let i="customer"===t?e.personalInfo?.email:e.customerInfo?.email;if(!i)throw Error("No email address found for recipient");let s={to:i,customerName:"customer"===t?e.personalInfo?.name:e.customerInfo?.name,followupType:n.title,description:n.description};"lead_followup"===r.template&&(s={...s,productInterest:r.productInterest||"our vehicles",leadSource:r.leadSource||"website",inquiryDetails:e.inquiry?.message||""}),await a.gm.sendFollowUpEmail(s)}async function d(e,t,r,n){if(!r.phone)throw Error("No phone number found for recipient");"customer"===t?e.personalInfo?.name:e.customerInfo?.name;"lead_followup_sms"===r.template&&r.productInterest}function p(){setInterval(u,36e5),setTimeout(u,5e3)}async function m(){try{return await u(),{processed:1,errors:0}}catch(e){return console.error("Error in manual follow-up processing:",e),{processed:0,errors:1}}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77268:(e,t,r)=>{"use strict";r.d(t,{iY:()=>a}),r(32190);var n=r(12909);function a(e,t){let r=e.headers.get("authorization"),a=e.cookies.get("admin_session")?.value,i=(0,n.fF)(r,a);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let s=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return s&&s===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,580,7696,6967],()=>r(18868));module.exports=n})();