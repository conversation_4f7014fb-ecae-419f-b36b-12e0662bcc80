import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { getOrderById } from '@/lib/orderStorage';
import { generateInvoicePDF } from '@/lib/invoiceGenerator';

export async function POST(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { orderId } = params;

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get order
    const order = await getOrderById(orderId);
    if (!order) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // Generate invoice PDF
    const pdfBuffer = await generateInvoicePDF(order);

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${order.orderNumber}.pdf"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Error generating invoice:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate invoice' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { orderId } = params;

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get order
    const order = await getOrderById(orderId);
    if (!order) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    // Return invoice data
    return NextResponse.json({
      success: true,
      invoice: {
        orderId: order.id,
        orderNumber: order.orderNumber,
        customerInfo: order.customerInfo,
        vehicle: order.vehicle,
        payment: order.payment,
        shipping: order.shipping,
        totalAmount: order.totalAmount,
        currency: order.currency,
        createdAt: order.createdAt,
        invoiceGenerated: order.invoiceGenerated
      }
    });

  } catch (error) {
    console.error('Error fetching invoice data:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch invoice data' },
      { status: 500 }
    );
  }
}
