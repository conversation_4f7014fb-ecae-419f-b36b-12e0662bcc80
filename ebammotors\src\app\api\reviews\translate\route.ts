import { NextRequest, NextResponse } from 'next/server';
import { getAllReviews, saveAllReviews } from '@/lib/reviewStorage';
import { translateReviewContent, detectLanguage } from '@/lib/translationService';
import { getAdminAuth } from '@/lib/adminMiddleware';

/**
 * API endpoint to translate existing reviews
 * This is an admin-only endpoint to add translations to existing reviews
 */
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('🔄 Starting translation of existing reviews...');

    // Get all reviews
    const reviews = await getAllReviews();
    let translatedCount = 0;
    let skippedCount = 0;

    // Process each review
    for (const review of reviews) {
      try {
        // Skip if already has translations
        if (review.reviewEn && review.reviewJa) {
          skippedCount++;
          continue;
        }

        const sourceLanguage = detectLanguage(review.review);
        const targetLanguage = sourceLanguage === 'en' ? 'ja' : 'en';

        // Translate the review content
        const translatedContent = await translateReviewContent({
          name: review.name,
          location: review.location,
          review: review.review,
          title: review.title
        }, targetLanguage);

        // Update the review with translations
        if (sourceLanguage === 'en') {
          review.reviewEn = review.review;
          review.reviewJa = translatedContent.review;
          if (review.title) {
            review.titleEn = review.title;
            review.titleJa = translatedContent.title;
          }
        } else {
          review.reviewJa = review.review;
          review.reviewEn = translatedContent.review;
          if (review.title) {
            review.titleJa = review.title;
            review.titleEn = translatedContent.title;
          }
        }

        translatedCount++;
        console.log(`✅ Translated review ${review.id} from ${sourceLanguage} to ${targetLanguage}`);

      } catch (error) {
        console.error(`❌ Failed to translate review ${review.id}:`, error);
        skippedCount++;
      }
    }

    // Save all reviews with translations
    await saveAllReviews(reviews);

    console.log(`🎉 Translation complete: ${translatedCount} translated, ${skippedCount} skipped`);

    return NextResponse.json({
      success: true,
      message: `Translation complete: ${translatedCount} reviews translated, ${skippedCount} skipped`,
      stats: {
        total: reviews.length,
        translated: translatedCount,
        skipped: skippedCount
      }
    });

  } catch (error) {
    console.error('Error translating reviews:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to translate reviews' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check translation status
 */
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const reviews = await getAllReviews();
    
    const stats = {
      total: reviews.length,
      withTranslations: reviews.filter(r => r.reviewEn && r.reviewJa).length,
      needingTranslation: reviews.filter(r => !r.reviewEn || !r.reviewJa).length
    };

    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Error checking translation status:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to check translation status' },
      { status: 500 }
    );
  }
}
