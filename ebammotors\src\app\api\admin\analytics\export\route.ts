import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { getAllOrders } from '@/lib/orderStorage';
import { getAllCustomers } from '@/lib/crmStorage';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview';
    const range = searchParams.get('range') || '30d';

    // Generate CSV data based on type
    let csvData = '';
    let filename = `analytics-${type}-${range}.csv`;

    switch (type) {
      case 'orders':
        csvData = await generateOrdersCSV(range);
        filename = `orders-report-${range}.csv`;
        break;
      case 'customers':
        csvData = await generateCustomersCSV(range);
        filename = `customers-report-${range}.csv`;
        break;
      case 'revenue':
        csvData = await generateRevenueCSV(range);
        filename = `revenue-report-${range}.csv`;
        break;
      default:
        csvData = await generateOverviewCSV(range);
        filename = `overview-report-${range}.csv`;
    }

    // Return CSV file
    return new NextResponse(csvData, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });

  } catch (error) {
    console.error('Error exporting analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to export analytics' },
      { status: 500 }
    );
  }
}

async function generateOrdersCSV(range: string): Promise<string> {
  const orders = await getAllOrders();
  
  // Filter by date range
  const filteredOrders = filterByDateRange(orders, range);
  
  const headers = [
    'Order Number',
    'Customer Name',
    'Customer Email',
    'Vehicle',
    'Order Amount',
    'Payment Status',
    'Order Status',
    'Created Date',
    'Payment Method',
    'Shipping Country'
  ];

  const rows = filteredOrders.map(order => [
    order.orderNumber,
    order.customerInfo.name,
    order.customerInfo.email,
    order.vehicle.title,
    order.totalAmount,
    order.payment.status,
    order.status,
    new Date(order.createdAt).toLocaleDateString(),
    order.payment.method.name,
    order.customerInfo.address.country
  ]);

  return generateCSV(headers, rows);
}

async function generateCustomersCSV(range: string): Promise<string> {
  const customers = await getAllCustomers();
  
  // Filter by date range
  const filteredCustomers = filterByDateRange(customers, range);
  
  const headers = [
    'Customer Name',
    'Email',
    'Phone',
    'City',
    'Country',
    'Status',
    'Segment',
    'Total Orders',
    'Total Spent',
    'Membership Tier',
    'Loyalty Points',
    'Created Date',
    'Last Order Date'
  ];

  const rows = filteredCustomers.map(customer => [
    customer.personalInfo.name,
    customer.personalInfo.email,
    customer.personalInfo.phone || '',
    customer.address.city || '',
    customer.address.country || '',
    customer.status,
    customer.segment,
    customer.totalOrders || 0,
    customer.totalSpent || 0,
    customer.membershipTier,
    customer.loyaltyPoints || 0,
    new Date(customer.createdAt).toLocaleDateString(),
    customer.lastOrderDate ? new Date(customer.lastOrderDate).toLocaleDateString() : ''
  ]);

  return generateCSV(headers, rows);
}

async function generateRevenueCSV(range: string): Promise<string> {
  const orders = await getAllOrders();
  
  // Filter by date range and completed payments
  const filteredOrders = filterByDateRange(orders, range)
    .filter(order => order.payment.status === 'completed');
  
  // Group by month
  const monthlyRevenue: Record<string, {
    revenue: number;
    orders: number;
    averageOrderValue: number;
  }> = {};

  filteredOrders.forEach(order => {
    const date = new Date(order.createdAt);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    
    if (!monthlyRevenue[monthKey]) {
      monthlyRevenue[monthKey] = { revenue: 0, orders: 0, averageOrderValue: 0 };
    }
    
    monthlyRevenue[monthKey].revenue += order.totalAmount;
    monthlyRevenue[monthKey].orders += 1;
  });

  // Calculate average order values
  Object.keys(monthlyRevenue).forEach(month => {
    const data = monthlyRevenue[month];
    data.averageOrderValue = data.orders > 0 ? data.revenue / data.orders : 0;
  });

  const headers = [
    'Month',
    'Total Revenue',
    'Total Orders',
    'Average Order Value'
  ];

  const rows = Object.entries(monthlyRevenue)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([month, data]) => [
      month,
      data.revenue,
      data.orders,
      Math.round(data.averageOrderValue)
    ]);

  return generateCSV(headers, rows);
}

async function generateOverviewCSV(range: string): Promise<string> {
  const [orders, customers] = await Promise.all([
    getAllOrders(),
    getAllCustomers()
  ]);
  
  const filteredOrders = filterByDateRange(orders, range);
  const filteredCustomers = filterByDateRange(customers, range);
  
  const completedOrders = filteredOrders.filter(o => o.payment.status === 'completed');
  const totalRevenue = completedOrders.reduce((sum, o) => sum + o.totalAmount, 0);
  const averageOrderValue = completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0;
  
  // Calculate metrics by country
  const countryStats: Record<string, {
    customers: number;
    orders: number;
    revenue: number;
  }> = {};

  filteredCustomers.forEach(customer => {
    const country = customer.address?.country || 'Unknown';
    if (!countryStats[country]) {
      countryStats[country] = { customers: 0, orders: 0, revenue: 0 };
    }
    countryStats[country].customers += 1;
  });

  filteredOrders.forEach(order => {
    const country = order.customerInfo.address.country || 'Unknown';
    if (!countryStats[country]) {
      countryStats[country] = { customers: 0, orders: 0, revenue: 0 };
    }
    countryStats[country].orders += 1;
    if (order.payment.status === 'completed') {
      countryStats[country].revenue += order.totalAmount;
    }
  });

  const headers = [
    'Metric',
    'Value'
  ];

  const overviewRows = [
    ['Total Orders', filteredOrders.length],
    ['Completed Orders', completedOrders.length],
    ['Total Customers', filteredCustomers.length],
    ['Total Revenue', totalRevenue],
    ['Average Order Value', Math.round(averageOrderValue)],
    ['Conversion Rate', filteredCustomers.length > 0 ? ((completedOrders.length / filteredCustomers.length) * 100).toFixed(2) + '%' : '0%']
  ];

  let csv = generateCSV(headers, overviewRows);
  
  // Add country breakdown
  csv += '\n\nCountry Breakdown\n';
  csv += generateCSV(
    ['Country', 'Customers', 'Orders', 'Revenue'],
    Object.entries(countryStats).map(([country, stats]) => [
      country,
      stats.customers,
      stats.orders,
      stats.revenue
    ])
  );

  return csv;
}

function filterByDateRange(data: any[], range: string) {
  const endDate = new Date();
  const startDate = new Date();
  
  switch (range) {
    case '7d':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(endDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(endDate.getDate() - 90);
      break;
    case '1y':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    default:
      startDate.setDate(endDate.getDate() - 30);
  }

  return data.filter(item => {
    const itemDate = new Date(item.createdAt);
    return itemDate >= startDate && itemDate <= endDate;
  });
}

function generateCSV(headers: string[], rows: any[][]): string {
  const csvRows = [headers, ...rows];
  return csvRows
    .map(row => 
      row.map(field => {
        // Escape quotes and wrap in quotes if necessary
        const stringField = String(field);
        if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
          return `"${stringField.replace(/"/g, '""')}"`;
        }
        return stringField;
      }).join(',')
    )
    .join('\n');
}
