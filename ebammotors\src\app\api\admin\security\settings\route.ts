import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get security settings (mock data for now)
    const settings = await getSecuritySettings();

    return NextResponse.json({
      success: true,
      settings
    });
  } catch (error) {
    console.error('Error fetching security settings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch security settings' },
      { status: 500 }
    );
  }
}

async function getSecuritySettings() {
  // In a real implementation, this would fetch from a configuration system
  return {
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      maxAge: 90 // days
    },
    sessionSettings: {
      maxDuration: 480, // 8 hours in minutes
      idleTimeout: 60, // 1 hour in minutes
      maxConcurrentSessions: 3
    },
    loginSecurity: {
      maxFailedAttempts: 5,
      lockoutDuration: 30, // minutes
      requireTwoFactor: false,
      allowedIpRanges: [
        '***********/24',
        '10.0.0.0/8'
      ]
    },
    auditSettings: {
      logRetentionDays: 365,
      enableRealTimeAlerts: true,
      alertThresholds: {
        failedLogins: 10,
        suspiciousActivity: 5
      }
    }
  };
}

export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const settings = await request.json();

    // Validate settings
    const validationResult = validateSecuritySettings(settings);
    if (!validationResult.valid) {
      return NextResponse.json(
        { success: false, message: validationResult.error },
        { status: 400 }
      );
    }

    // In a real implementation, this would save to a configuration system
    console.log('Security settings updated:', settings);

    // Log the settings change
    await logSecurityEvent('settings_changed', 'admin', 'Security settings updated');

    return NextResponse.json({
      success: true,
      message: 'Security settings updated successfully',
      settings
    });

  } catch (error) {
    console.error('Error updating security settings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update security settings' },
      { status: 500 }
    );
  }
}

function validateSecuritySettings(settings: any): { valid: boolean; error?: string } {
  // Validate password policy
  if (settings.passwordPolicy) {
    const { minLength, maxAge } = settings.passwordPolicy;
    
    if (minLength < 6 || minLength > 128) {
      return { valid: false, error: 'Password minimum length must be between 6 and 128 characters' };
    }
    
    if (maxAge < 1 || maxAge > 365) {
      return { valid: false, error: 'Password max age must be between 1 and 365 days' };
    }
  }

  // Validate session settings
  if (settings.sessionSettings) {
    const { maxDuration, idleTimeout, maxConcurrentSessions } = settings.sessionSettings;
    
    if (maxDuration < 5 || maxDuration > 1440) { // 5 minutes to 24 hours
      return { valid: false, error: 'Session max duration must be between 5 and 1440 minutes' };
    }
    
    if (idleTimeout < 5 || idleTimeout > 480) { // 5 minutes to 8 hours
      return { valid: false, error: 'Session idle timeout must be between 5 and 480 minutes' };
    }
    
    if (maxConcurrentSessions < 1 || maxConcurrentSessions > 10) {
      return { valid: false, error: 'Max concurrent sessions must be between 1 and 10' };
    }
  }

  // Validate login security
  if (settings.loginSecurity) {
    const { maxFailedAttempts, lockoutDuration } = settings.loginSecurity;
    
    if (maxFailedAttempts < 3 || maxFailedAttempts > 20) {
      return { valid: false, error: 'Max failed attempts must be between 3 and 20' };
    }
    
    if (lockoutDuration < 5 || lockoutDuration > 1440) { // 5 minutes to 24 hours
      return { valid: false, error: 'Lockout duration must be between 5 and 1440 minutes' };
    }
  }

  // Validate audit settings
  if (settings.auditSettings) {
    const { logRetentionDays, alertThresholds } = settings.auditSettings;
    
    if (logRetentionDays < 30 || logRetentionDays > 2555) { // 30 days to 7 years
      return { valid: false, error: 'Log retention must be between 30 and 2555 days' };
    }
    
    if (alertThresholds) {
      if (alertThresholds.failedLogins < 1 || alertThresholds.failedLogins > 100) {
        return { valid: false, error: 'Failed logins alert threshold must be between 1 and 100' };
      }
      
      if (alertThresholds.suspiciousActivity < 1 || alertThresholds.suspiciousActivity > 50) {
        return { valid: false, error: 'Suspicious activity alert threshold must be between 1 and 50' };
      }
    }
  }

  return { valid: true };
}

async function logSecurityEvent(action: string, user: string, details: string) {
  try {
    // In a real implementation, this would log to the security logging system
    const logEntry = {
      timestamp: new Date().toISOString(),
      action,
      user,
      ipAddress: '127.0.0.1', // Would get from request
      userAgent: 'Admin Dashboard',
      status: 'success',
      details
    };
    
    console.log('Security event logged:', logEntry);
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
}
