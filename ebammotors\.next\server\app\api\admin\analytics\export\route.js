(()=>{var t={};t.id=8767,t.ids=[8767],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(t,e,r)=>{"use strict";r.d(e,{Qq:()=>p,Tq:()=>w,bS:()=>l,fF:()=>d,mU:()=>c});var n=r(85663),a=r(43205),i=r.n(a);let s=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(t,e){try{return await n.Ay.compare(t,e)}catch(t){return console.error("Error verifying password:",t),!1}}function c(t){return o.delete(t)}async function l(t){try{let e=function(){let t=process.env.ADMIN_PASSWORD||"admin123";return t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$"),t}(),r=!1;if(!(e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$")?await u(t,e):t===e))return{success:!1,message:"Invalid credentials"};{let t=function(t="admin"){try{let e={id:t,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(e,s,{expiresIn:"24h"})}catch(t){throw console.error("Error generating token:",t),Error("Failed to generate authentication token")}}(),e=function(t="admin"){let e=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(e,{id:t,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let t=Date.now();for(let[e,r]of o.entries())t>r.expiresAt&&o.delete(e)}(),e}();return{success:!0,token:t,sessionId:e,message:"Authentication successful"}}}catch(t){return console.error("Authentication error:",t),{success:!1,message:"Authentication failed"}}}function d(t,e){if(t&&t.startsWith("Bearer ")){let e=function(t){try{let e=i().verify(t,s);if(e.isAdmin)return{id:e.id,isAdmin:e.isAdmin};return null}catch(t){return null}}(t.substring(7));if(e)return{isValid:!0,adminId:e.id,message:"Token authentication successful"}}if(e){let t=function(t){let e=o.get(t);if(!e)return null;let r=Date.now();return r>e.expiresAt?(o.delete(t),null):(e.lastActivity=r,o.set(t,e),e)}(e);if(t)return{isValid:!0,adminId:t.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let f=new Map;function p(t){let e=Date.now(),r=f.get(t);return!r||e-r.lastAttempt>9e5?(f.set(t,{count:1,lastAttempt:e}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(e-r.lastAttempt)}:(r.count++,r.lastAttempt=e,f.set(t,r),{allowed:!0,remainingAttempts:5-r.count})}function w(t){f.delete(t)}},23870:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(55511);let a={randomUUID:n.randomUUID},i=new Uint8Array(256),s=i.length,o=[];for(let t=0;t<256;++t)o.push((t+256).toString(16).slice(1));let u=function(t,e,r){if(a.randomUUID&&!e&&!t)return a.randomUUID();let u=(t=t||{}).random??t.rng?.()??(s>i.length-16&&((0,n.randomFillSync)(i),s=0),i.slice(s,s+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,e){if((r=r||0)<0||r+16>e.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let t=0;t<16;++t)e[r+t]=u[t];return e}return function(t,e=0){return(o[t[e+0]]+o[t[e+1]]+o[t[e+2]]+o[t[e+3]]+"-"+o[t[e+4]]+o[t[e+5]]+"-"+o[t[e+6]]+o[t[e+7]]+"-"+o[t[e+8]]+o[t[e+9]]+"-"+o[t[e+10]]+o[t[e+11]]+o[t[e+12]]+o[t[e+13]]+o[t[e+14]]+o[t[e+15]]).toLowerCase()}(u)}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30358:(t,e,r)=>{"use strict";r.d(e,{Dq:()=>F,HQ:()=>O,Q4:()=>D,Vd:()=>A,ee:()=>I,fS:()=>h,g5:()=>N,getOrderById:()=>S,iO:()=>x,iY:()=>v});var n=r(29021),a=r(33873),i=r.n(a),s=r(23870);let o=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,u=i().join(process.cwd(),"data"),c=i().join(u,"orders.json"),l=i().join(u,"invoices.json"),d=[],f=[];async function p(){if(!o)try{await n.promises.access(u)}catch{await n.promises.mkdir(u,{recursive:!0})}}async function w(){if(o)return d;try{await p();let t=await n.promises.readFile(c,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function m(t){if(o){d=t;return}await p(),await n.promises.writeFile(c,JSON.stringify(t,null,2))}async function y(){if(o)return f;try{await p();let t=await n.promises.readFile(l,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function g(t){if(o){f=t;return}await p(),await n.promises.writeFile(l,JSON.stringify(t,null,2))}async function h(t){let e=await w(),r={...t,id:(0,s.A)(),orderNumber:function(){let t=Date.now().toString(),e=Math.random().toString(36).substr(2,4).toUpperCase();return`EB${t.slice(-6)}${e}`}(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await m(e),r}async function A(){return await w()}async function S(t){return(await w()).find(e=>e.id===t)||null}async function D(t){return(await w()).filter(e=>e.customerId===t)}async function v(t,e){let r=await w(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await m(r),!0)}async function I(t,e){let r=await S(t);if(!r)return!1;let n={...r.payment,...e};return await v(t,{payment:n})}async function O(t,e){let r=await S(t);if(!r)return!1;let n={...e,id:(0,s.A)()},a={...r.shipping,updates:[...r.shipping.updates,n]};return await v(t,{shipping:a})}async function x(t){let e=await y(),r={...t,id:(0,s.A)(),invoiceNumber:function(){let t=Date.now().toString(),e=Math.random().toString(36).substr(2,3).toUpperCase();return`INV-${t.slice(-6)}-${e}`}()};return e.push(r),await g(e),r}async function N(t){return(await y()).find(e=>e.orderId===t)||null}async function F(t,e){let r=await y(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e},await g(r),!0)}},33873:t=>{"use strict";t.exports=require("path")},43355:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>v,routeModule:()=>h,serverHooks:()=>D,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>S});var n={};r.r(n),r.d(n,{GET:()=>d});var a=r(96559),i=r(48088),s=r(37719),o=r(32190),u=r(77268),c=r(30358),l=r(53190);async function d(t){try{if(!(0,u.iY)(t).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:e}=new URL(t.url),r=e.get("type")||"overview",n=e.get("range")||"30d",a="",i=`analytics-${r}-${n}.csv`;switch(r){case"orders":a=await f(n),i=`orders-report-${n}.csv`;break;case"customers":a=await p(n),i=`customers-report-${n}.csv`;break;case"revenue":a=await w(n),i=`revenue-report-${n}.csv`;break;default:a=await m(n),i=`overview-report-${n}.csv`}return new o.NextResponse(a,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="${i}"`}})}catch(t){return console.error("Error exporting analytics:",t),o.NextResponse.json({success:!1,message:"Failed to export analytics"},{status:500})}}async function f(t){return g(["Order Number","Customer Name","Customer Email","Vehicle","Order Amount","Payment Status","Order Status","Created Date","Payment Method","Shipping Country"],y(await (0,c.Vd)(),t).map(t=>[t.orderNumber,t.customerInfo.name,t.customerInfo.email,t.vehicle.title,t.totalAmount,t.payment.status,t.status,new Date(t.createdAt).toLocaleDateString(),t.payment.method.name,t.customerInfo.address.country]))}async function p(t){return g(["Customer Name","Email","Phone","City","Country","Status","Segment","Total Orders","Total Spent","Membership Tier","Loyalty Points","Created Date","Last Order Date"],y(await (0,l.Rf)(),t).map(t=>[t.personalInfo.name,t.personalInfo.email,t.personalInfo.phone||"",t.address.city||"",t.address.country||"",t.status,t.segment,t.totalOrders||0,t.totalSpent||0,t.membershipTier,t.loyaltyPoints||0,new Date(t.createdAt).toLocaleDateString(),t.lastOrderDate?new Date(t.lastOrderDate).toLocaleDateString():""]))}async function w(t){let e=y(await (0,c.Vd)(),t).filter(t=>"completed"===t.payment.status),r={};return e.forEach(t=>{let e=new Date(t.createdAt),n=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}`;r[n]||(r[n]={revenue:0,orders:0,averageOrderValue:0}),r[n].revenue+=t.totalAmount,r[n].orders+=1}),Object.keys(r).forEach(t=>{let e=r[t];e.averageOrderValue=e.orders>0?e.revenue/e.orders:0}),g(["Month","Total Revenue","Total Orders","Average Order Value"],Object.entries(r).sort(([t],[e])=>t.localeCompare(e)).map(([t,e])=>[t,e.revenue,e.orders,Math.round(e.averageOrderValue)]))}async function m(t){let[e,r]=await Promise.all([(0,c.Vd)(),(0,l.Rf)()]),n=y(e,t),a=y(r,t),i=n.filter(t=>"completed"===t.payment.status),s=i.reduce((t,e)=>t+e.totalAmount,0),o=i.length>0?s/i.length:0,u={};a.forEach(t=>{let e=t.address?.country||"Unknown";u[e]||(u[e]={customers:0,orders:0,revenue:0}),u[e].customers+=1}),n.forEach(t=>{let e=t.customerInfo.address.country||"Unknown";u[e]||(u[e]={customers:0,orders:0,revenue:0}),u[e].orders+=1,"completed"===t.payment.status&&(u[e].revenue+=t.totalAmount)});let d=g(["Metric","Value"],[["Total Orders",n.length],["Completed Orders",i.length],["Total Customers",a.length],["Total Revenue",s],["Average Order Value",Math.round(o)],["Conversion Rate",a.length>0?(i.length/a.length*100).toFixed(2)+"%":"0%"]]);return d+="\n\nCountry Breakdown\n",d+=g(["Country","Customers","Orders","Revenue"],Object.entries(u).map(([t,e])=>[t,e.customers,e.orders,e.revenue]))}function y(t,e){let r=new Date,n=new Date;switch(e){case"7d":n.setDate(r.getDate()-7);break;case"30d":default:n.setDate(r.getDate()-30);break;case"90d":n.setDate(r.getDate()-90);break;case"1y":n.setFullYear(r.getFullYear()-1)}return t.filter(t=>{let e=new Date(t.createdAt);return e>=n&&e<=r})}function g(t,e){return[t,...e].map(t=>t.map(t=>{let e=String(t);return e.includes(",")||e.includes('"')||e.includes("\n")?`"${e.replace(/"/g,'""')}"`:e}).join(",")).join("\n")}let h=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/analytics/export/route",pathname:"/api/admin/analytics/export",filename:"route",bundlePath:"app/api/admin/analytics/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\analytics\\export\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:A,workUnitAsyncStorage:S,serverHooks:D}=h;function v(){return(0,s.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:S})}},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53190:(t,e,r)=>{"use strict";r.d(e,{Gk:()=>T,Gq:()=>J,HR:()=>F,Kt:()=>b,Q6:()=>x,Rf:()=>$,XL:()=>W,Y2:()=>U,_Y:()=>z,aN:()=>Z,createCustomerActivity:()=>X,createInteraction:()=>M,dD:()=>B,fE:()=>q,getAllFollowUps:()=>Y,getCustomerByEmail:()=>E,getCustomerById:()=>k,getLeadById:()=>O,getPendingFollowUps:()=>K,oP:()=>tt,qz:()=>P,sr:()=>N,tR:()=>v,tS:()=>I,updateFollowUp:()=>Q});var n=r(29021),a=r(33873),i=r.n(a),s=r(23870);let o=i().join(process.cwd(),"data"),u=i().join(o,"leads.json"),c=i().join(o,"customers.json"),l=i().join(o,"interactions.json"),d=i().join(o,"followups.json"),f=i().join(o,"activities.json"),p=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,w=[],m=[],y=[],g=[],h=[];async function A(){if(!p)try{await n.promises.access(o)}catch{await n.promises.mkdir(o,{recursive:!0})}}async function S(){if(p)return w;try{await A();let t=await n.promises.readFile(u,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function D(t){if(p){w=t;return}await A(),await n.promises.writeFile(u,JSON.stringify(t,null,2))}async function v(t){let e=await S(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await D(e),r}async function I(){return await S()}async function O(t){return(await S()).find(e=>e.id===t)||null}async function x(t,e){let r=await S(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await D(r),!0)}async function N(t){let e=await S(),r=e.filter(e=>e.id!==t);return r.length!==e.length&&(await D(r),!0)}async function F(t){return(await S()).filter(e=>e.status===t)}async function b(t){return(await S()).filter(e=>e.source===t)}async function C(){if(p)return m;try{await A();let t=await n.promises.readFile(c,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function j(t){if(p){m=t;return}await A(),await n.promises.writeFile(c,JSON.stringify(t,null,2))}async function U(t){let e=await C(),r=e.findIndex(e=>e.personalInfo.email===t.personalInfo.email);if(-1!==r)return e[r]={...e[r],...t,updatedAt:new Date().toISOString()},await j(e),e[r];{let r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await j(e),r}}async function $(){return await C()}async function k(t){return(await C()).find(e=>e.id===t)||null}async function E(t){return(await C()).find(e=>e.personalInfo.email===t)||null}async function T(t,e){let r=await C(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await j(r),!0)}async function R(){if(p)return y;try{await A();let t=await n.promises.readFile(l,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function V(t){if(p){y=t;return}await A(),await n.promises.writeFile(l,JSON.stringify(t,null,2))}async function M(t){let e=await R(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString()};return e.push(r),await V(e),r}async function q(){return await R()}async function P(t){return(await R()).filter(e=>e.customerId===t)}async function J(t){return(await R()).filter(e=>e.leadId===t)}async function L(){if(p)return g;try{await A();let t=await n.promises.readFile(d,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function _(t){if(p){g=t;return}await A(),await n.promises.writeFile(d,JSON.stringify(t,null,2))}async function W(t){let e=await L(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await _(e),r}async function Y(){return await L()}async function B(t){return(await L()).filter(e=>e.status===t)}async function K(){let t=await L(),e=new Date().toISOString();return t.filter(t=>"pending"===t.status&&t.scheduledDate<=e)}async function Q(t,e){let r=await L(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await _(r),!0)}async function z(t){return(await L()).filter(e=>e.customerId===t)}async function G(){if(p)return h;try{await A();let t=await n.promises.readFile(f,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function H(t){if(p){h=t;return}await A(),await n.promises.writeFile(f,JSON.stringify(t,null,2))}async function X(t){let e=await G(),r={...t,id:(0,s.A)(),timestamp:new Date().toISOString()};return e.push(r),await H(e),r}async function Z(t){return(await G()).filter(e=>e.customerId===t)}async function tt(t){let e=await k(t);if(!e)return null;let r=await P(t),n=await z(t),a=await Z(t);return{customer:e,stats:{totalInteractions:r.length,pendingFollowUps:n.filter(t=>"pending"===t.status).length,recentActivities:a.filter(t=>new Date(t.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:r.sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime())[0]?.createdAt},recentInteractions:r.sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()).slice(0,5),upcomingFollowUps:n.filter(t=>"pending"===t.status).sort((t,e)=>new Date(t.scheduledDate).getTime()-new Date(e.scheduledDate).getTime()).slice(0,3)}}},55511:t=>{"use strict";t.exports=require("crypto")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77268:(t,e,r)=>{"use strict";r.d(e,{iY:()=>a}),r(32190);var n=r(12909);function a(t,e){let r=t.headers.get("authorization"),a=t.cookies.get("admin_session")?.value,i=(0,n.fF)(r,a);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let s=e?.adminKey||t.nextUrl.searchParams.get("adminKey");return s&&s===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:t=>{"use strict";t.exports=require("buffer")},96487:()=>{}};var e=require("../../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[4447,580,7696],()=>r(43355));module.exports=n})();