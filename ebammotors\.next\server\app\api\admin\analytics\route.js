(()=>{var t={};t.id=678,t.ids=[678,7990],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(t,e,r)=>{"use strict";r.d(e,{Qq:()=>p,Tq:()=>w,bS:()=>l,fF:()=>d,mU:()=>c});var n=r(85663),a=r(43205),i=r.n(a);let s=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(t,e){try{return await n.Ay.compare(t,e)}catch(t){return console.error("Error verifying password:",t),!1}}function c(t){return o.delete(t)}async function l(t){try{let e=function(){let t=process.env.ADMIN_PASSWORD||"admin123";return t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$"),t}(),r=!1;if(!(e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$")?await u(t,e):t===e))return{success:!1,message:"Invalid credentials"};{let t=function(t="admin"){try{let e={id:t,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(e,s,{expiresIn:"24h"})}catch(t){throw console.error("Error generating token:",t),Error("Failed to generate authentication token")}}(),e=function(t="admin"){let e=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(e,{id:t,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let t=Date.now();for(let[e,r]of o.entries())t>r.expiresAt&&o.delete(e)}(),e}();return{success:!0,token:t,sessionId:e,message:"Authentication successful"}}}catch(t){return console.error("Authentication error:",t),{success:!1,message:"Authentication failed"}}}function d(t,e){if(t&&t.startsWith("Bearer ")){let e=function(t){try{let e=i().verify(t,s);if(e.isAdmin)return{id:e.id,isAdmin:e.isAdmin};return null}catch(t){return null}}(t.substring(7));if(e)return{isValid:!0,adminId:e.id,message:"Token authentication successful"}}if(e){let t=function(t){let e=o.get(t);if(!e)return null;let r=Date.now();return r>e.expiresAt?(o.delete(t),null):(e.lastActivity=r,o.set(t,e),e)}(e);if(t)return{isValid:!0,adminId:t.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let f=new Map;function p(t){let e=Date.now(),r=f.get(t);return!r||e-r.lastAttempt>9e5?(f.set(t,{count:1,lastAttempt:e}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(e-r.lastAttempt)}:(r.count++,r.lastAttempt=e,f.set(t,r),{allowed:!0,remainingAttempts:5-r.count})}function w(t){f.delete(t)}},21820:t=>{"use strict";t.exports=require("os")},23870:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(55511);let a={randomUUID:n.randomUUID},i=new Uint8Array(256),s=i.length,o=[];for(let t=0;t<256;++t)o.push((t+256).toString(16).slice(1));let u=function(t,e,r){if(a.randomUUID&&!e&&!t)return a.randomUUID();let u=(t=t||{}).random??t.rng?.()??(s>i.length-16&&((0,n.randomFillSync)(i),s=0),i.slice(s,s+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,e){if((r=r||0)<0||r+16>e.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let t=0;t<16;++t)e[r+t]=u[t];return e}return function(t,e=0){return(o[t[e+0]]+o[t[e+1]]+o[t[e+2]]+o[t[e+3]]+"-"+o[t[e+4]]+o[t[e+5]]+"-"+o[t[e+6]]+o[t[e+7]]+"-"+o[t[e+8]]+o[t[e+9]]+"-"+o[t[e+10]]+o[t[e+11]]+o[t[e+12]]+o[t[e+13]]+o[t[e+14]]+o[t[e+15]]).toLowerCase()}(u)}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30358:(t,e,r)=>{"use strict";r.d(e,{Dq:()=>N,HQ:()=>O,Q4:()=>D,Vd:()=>A,ee:()=>I,fS:()=>h,g5:()=>F,getOrderById:()=>S,iO:()=>x,iY:()=>v});var n=r(29021),a=r(33873),i=r.n(a),s=r(23870);let o=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,u=i().join(process.cwd(),"data"),c=i().join(u,"orders.json"),l=i().join(u,"invoices.json"),d=[],f=[];async function p(){if(!o)try{await n.promises.access(u)}catch{await n.promises.mkdir(u,{recursive:!0})}}async function w(){if(o)return d;try{await p();let t=await n.promises.readFile(c,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function m(t){if(o){d=t;return}await p(),await n.promises.writeFile(c,JSON.stringify(t,null,2))}async function g(){if(o)return f;try{await p();let t=await n.promises.readFile(l,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function y(t){if(o){f=t;return}await p(),await n.promises.writeFile(l,JSON.stringify(t,null,2))}async function h(t){let e=await w(),r={...t,id:(0,s.A)(),orderNumber:function(){let t=Date.now().toString(),e=Math.random().toString(36).substr(2,4).toUpperCase();return`EB${t.slice(-6)}${e}`}(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await m(e),r}async function A(){return await w()}async function S(t){return(await w()).find(e=>e.id===t)||null}async function D(t){return(await w()).filter(e=>e.customerId===t)}async function v(t,e){let r=await w(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await m(r),!0)}async function I(t,e){let r=await S(t);if(!r)return!1;let n={...r.payment,...e};return await v(t,{payment:n})}async function O(t,e){let r=await S(t);if(!r)return!1;let n={...e,id:(0,s.A)()},a={...r.shipping,updates:[...r.shipping.updates,n]};return await v(t,{shipping:a})}async function x(t){let e=await g(),r={...t,id:(0,s.A)(),invoiceNumber:function(){let t=Date.now().toString(),e=Math.random().toString(36).substr(2,3).toUpperCase();return`INV-${t.slice(-6)}-${e}`}()};return e.push(r),await y(e),r}async function F(t){return(await g()).find(e=>e.orderId===t)||null}async function N(t,e){let r=await g(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e},await y(r),!0)}},33873:t=>{"use strict";t.exports=require("path")},34631:t=>{"use strict";t.exports=require("tls")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},53190:(t,e,r)=>{"use strict";r.d(e,{Gk:()=>$,Gq:()=>V,HR:()=>N,Kt:()=>b,Q6:()=>x,Rf:()=>q,XL:()=>P,Y2:()=>U,_Y:()=>Q,aN:()=>Z,createCustomerActivity:()=>X,createInteraction:()=>J,dD:()=>B,fE:()=>C,getAllFollowUps:()=>Y,getCustomerByEmail:()=>k,getCustomerById:()=>R,getLeadById:()=>O,getPendingFollowUps:()=>z,oP:()=>tt,qz:()=>_,sr:()=>F,tR:()=>v,tS:()=>I,updateFollowUp:()=>K});var n=r(29021),a=r(33873),i=r.n(a),s=r(23870);let o=i().join(process.cwd(),"data"),u=i().join(o,"leads.json"),c=i().join(o,"customers.json"),l=i().join(o,"interactions.json"),d=i().join(o,"followups.json"),f=i().join(o,"activities.json"),p=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,w=[],m=[],g=[],y=[],h=[];async function A(){if(!p)try{await n.promises.access(o)}catch{await n.promises.mkdir(o,{recursive:!0})}}async function S(){if(p)return w;try{await A();let t=await n.promises.readFile(u,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function D(t){if(p){w=t;return}await A(),await n.promises.writeFile(u,JSON.stringify(t,null,2))}async function v(t){let e=await S(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await D(e),r}async function I(){return await S()}async function O(t){return(await S()).find(e=>e.id===t)||null}async function x(t,e){let r=await S(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await D(r),!0)}async function F(t){let e=await S(),r=e.filter(e=>e.id!==t);return r.length!==e.length&&(await D(r),!0)}async function N(t){return(await S()).filter(e=>e.status===t)}async function b(t){return(await S()).filter(e=>e.source===t)}async function j(){if(p)return m;try{await A();let t=await n.promises.readFile(c,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function E(t){if(p){m=t;return}await A(),await n.promises.writeFile(c,JSON.stringify(t,null,2))}async function U(t){let e=await j(),r=e.findIndex(e=>e.personalInfo.email===t.personalInfo.email);if(-1!==r)return e[r]={...e[r],...t,updatedAt:new Date().toISOString()},await E(e),e[r];{let r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await E(e),r}}async function q(){return await j()}async function R(t){return(await j()).find(e=>e.id===t)||null}async function k(t){return(await j()).find(e=>e.personalInfo.email===t)||null}async function $(t,e){let r=await j(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await E(r),!0)}async function M(){if(p)return g;try{await A();let t=await n.promises.readFile(l,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function T(t){if(p){g=t;return}await A(),await n.promises.writeFile(l,JSON.stringify(t,null,2))}async function J(t){let e=await M(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString()};return e.push(r),await T(e),r}async function C(){return await M()}async function _(t){return(await M()).filter(e=>e.customerId===t)}async function V(t){return(await M()).filter(e=>e.leadId===t)}async function W(){if(p)return y;try{await A();let t=await n.promises.readFile(d,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function L(t){if(p){y=t;return}await A(),await n.promises.writeFile(d,JSON.stringify(t,null,2))}async function P(t){let e=await W(),r={...t,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return e.push(r),await L(e),r}async function Y(){return await W()}async function B(t){return(await W()).filter(e=>e.status===t)}async function z(){let t=await W(),e=new Date().toISOString();return t.filter(t=>"pending"===t.status&&t.scheduledDate<=e)}async function K(t,e){let r=await W(),n=r.findIndex(e=>e.id===t);return -1!==n&&(r[n]={...r[n],...e,updatedAt:new Date().toISOString()},await L(r),!0)}async function Q(t){return(await W()).filter(e=>e.customerId===t)}async function G(){if(p)return h;try{await A();let t=await n.promises.readFile(f,"utf-8");return JSON.parse(t)}catch(t){return[]}}async function H(t){if(p){h=t;return}await A(),await n.promises.writeFile(f,JSON.stringify(t,null,2))}async function X(t){let e=await G(),r={...t,id:(0,s.A)(),timestamp:new Date().toISOString()};return e.push(r),await H(e),r}async function Z(t){return(await G()).filter(e=>e.customerId===t)}async function tt(t){let e=await R(t);if(!e)return null;let r=await _(t),n=await Q(t),a=await Z(t);return{customer:e,stats:{totalInteractions:r.length,pendingFollowUps:n.filter(t=>"pending"===t.status).length,recentActivities:a.filter(t=>new Date(t.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:r.sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime())[0]?.createdAt},recentInteractions:r.sort((t,e)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()).slice(0,5),upcomingFollowUps:n.filter(t=>"pending"===t.status).sort((t,e)=>new Date(t.scheduledDate).getTime()-new Date(e.scheduledDate).getTime()).slice(0,3)}}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73303:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>A,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var n={};r.r(n),r.d(n,{GET:()=>f});var a=r(96559),i=r(48088),s=r(37719),o=r(32190),u=r(77268),c=r(30358),l=r(53190),d=r(83376);async function f(t){try{if(!(0,u.iY)(t).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:e}=new URL(t.url),r=e.get("range")||"30d",n=await p(r);return o.NextResponse.json(n)}catch(t){return console.error("Error fetching analytics:",t),o.NextResponse.json({success:!1,message:"Failed to fetch analytics"},{status:500})}}async function p(t){try{let e=new Date,r=new Date;switch(t){case"7d":r.setDate(e.getDate()-7);break;case"30d":default:r.setDate(e.getDate()-30);break;case"90d":r.setDate(e.getDate()-90);break;case"1y":r.setFullYear(e.getFullYear()-1)}let[n,a,i]=await Promise.all([(0,c.Vd)(),(0,l.Rf)(),w()]),s=n.filter(t=>{let n=new Date(t.createdAt);return n>=r&&n<=e}),o=a.filter(t=>{let n=new Date(t.createdAt);return n>=r&&n<=e}),u=function(t,e,r){let n=t.filter(t=>"completed"===t.payment.status).reduce((t,e)=>t+e.totalAmount,0),a=t.length,i=e.length,s=20*Math.random()-10,o=15*Math.random()-5,u=25*Math.random()-10;return{totalRevenue:n,totalOrders:a,totalCustomers:i,totalCars:r,revenueGrowth:s,orderGrowth:o,customerGrowth:u,conversionRate:i>0?a/i*100:0}}(s,o,i),d=function(t,e,r){let n=[],a=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],i={};return t.forEach(t=>{let e=new Date(t.createdAt),r=`${a[e.getMonth()]} ${e.getFullYear()}`;i[r]||(i[r]={revenue:0,orders:0,customers:new Set}),"completed"===t.payment.status&&(i[r].revenue+=t.totalAmount),i[r].orders+=1,i[r].customers.add(t.customerId)}),Object.entries(i).forEach(([t,e])=>{n.push({month:t,revenue:e.revenue,orders:e.orders,customers:e.customers.size})}),n.sort((t,e)=>{let r=new Date(t.month),n=new Date(e.month);return r.getTime()-n.getTime()})}(s,0,0),f=function(t){let e={};return t.forEach(t=>{let r=t.vehicle.id;e[r]||(e[r]={title:t.vehicle.title,sales:0,revenue:0}),e[r].sales+=1,"completed"===t.payment.status&&(e[r].revenue+=t.totalAmount)}),Object.entries(e).map(([t,e])=>({id:t,...e})).sort((t,e)=>e.revenue-t.revenue)}(s),p=function(t){let e={};t.forEach(t=>{let r=t.segment||"Regular";e[r]=(e[r]||0)+1});let r=t.length;return Object.entries(e).map(([t,e])=>({segment:t,count:e,revenue:5e5*e,percentage:r>0?e/r*100:0}))}(a),m=function(t){let e={};t.forEach(t=>{let r="Website";e[r]||(e[r]={orders:0,revenue:0}),e[r].orders+=1,"completed"===t.payment.status&&(e[r].revenue+=t.totalAmount)});let r=t.length;return Object.entries(e).map(([t,e])=>({source:t,orders:e.orders,revenue:e.revenue,percentage:r>0?e.orders/r*100:0}))}(s),g=function(t){let e={};return t.forEach(t=>{let r=t.address?.country||"Unknown";e[r]||(e[r]={customers:0,orders:0,revenue:0}),e[r].customers+=1,e[r].orders+=t.totalOrders||0,e[r].revenue+=t.totalSpent||0}),Object.entries(e).map(([t,e])=>({country:t,...e})).sort((t,e)=>e.customers-t.customers)}(a),y=function(t,e){let r=t.filter(t=>"completed"===t.payment.status),n=r.length>0?r.reduce((t,e)=>t+e.totalAmount,0)/r.length:0,a=e.length>0?e.reduce((t,e)=>t+(e.totalSpent||0),0)/e.length:0,i=e.filter(t=>(t.totalOrders||0)>1).length,s=e.length>0?i/e.length*100:0;return{averageOrderValue:n,customerLifetimeValue:a,repeatCustomerRate:s,cartAbandonmentRate:25.5,averageSessionDuration:180,bounceRate:35.2}}(n,a);return{overview:u,salesTrends:d,topProducts:f,customerSegments:p,orderSources:m,geographicData:g,performanceMetrics:y}}catch(t){throw console.error("Error calculating analytics:",t),t}}async function w(){try{let t=await (0,d.sql)`SELECT COUNT(*) as count FROM cars`;return parseInt(t.rows[0]?.count||"0")}catch(t){return console.error("Error fetching cars data:",t),0}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/analytics/route",pathname:"/api/admin/analytics",filename:"route",bundlePath:"app/api/admin/analytics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\analytics\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:h}=m;function A(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}},74075:t=>{"use strict";t.exports=require("zlib")},77268:(t,e,r)=>{"use strict";r.d(e,{iY:()=>a}),r(32190);var n=r(12909);function a(t,e){let r=t.headers.get("authorization"),a=t.cookies.get("admin_session")?.value,i=(0,n.fF)(r,a);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let s=e?.adminKey||t.nextUrl.searchParams.get("adminKey");return s&&s===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:t=>{"use strict";t.exports=require("buffer")},79551:t=>{"use strict";t.exports=require("url")},81630:t=>{"use strict";t.exports=require("http")},91645:t=>{"use strict";t.exports=require("net")},94735:t=>{"use strict";t.exports=require("events")},96487:()=>{}};var e=require("../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[4447,580,7696,3376],()=>r(73303));module.exports=n})();