(()=>{var e={};e.id=7404,e.ids=[7404],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,s)=>{"use strict";s.d(t,{Qq:()=>m,Tq:()=>f,bS:()=>l,fF:()=>d,mU:()=>c});var r=s(85663),n=s(43205),i=s.n(n);let a=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await r.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function l(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),s=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(t,a,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,s=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:s,expiresAt:s+864e5,lastActivity:s}),function(){let e=Date.now();for(let[t,s]of o.entries())e>s.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function d(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=i().verify(e,a);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let s=Date.now();return s>t.expiresAt?(o.delete(e),null):(t.lastActivity=s,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),s=p.get(e);return!s||t-s.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):s.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-s.lastAttempt)}:(s.count++,s.lastAttempt=t,p.set(e,s),{allowed:!0,remainingAttempts:5-s.count})}function f(e){p.delete(e)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35117:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var r={};s.r(r),s.d(r,{POST:()=>c});var n=s(96559),i=s(48088),a=s(37719),o=s(32190),u=s(12909);async function c(e){try{let{password:t}=await e.json(),s=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",r=(0,u.Qq)(s);if(!r.allowed)return o.NextResponse.json({success:!1,message:`Too many authentication attempts. Please try again in ${Math.ceil((r.lockoutTime||0)/6e4)} minutes.`,lockoutTime:r.lockoutTime},{status:429});if(!t||"string"!=typeof t)return o.NextResponse.json({success:!1,message:"Password is required"},{status:400});let n=await (0,u.bS)(t);if(!n.success)return o.NextResponse.json({success:!1,message:n.message,remainingAttempts:r.remainingAttempts-1},{status:401});{(0,u.Tq)(s);let e=o.NextResponse.json({success:!0,message:n.message,token:n.token,expiresIn:"24h"});return e.cookies.set("admin_session",n.sessionId,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:86400,path:"/"}),e}}catch(e){return console.error("Error in admin authentication:",e),o.NextResponse.json({success:!1,message:"Authentication failed"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/auth/route",pathname:"/api/admin/auth",filename:"route",bundlePath:"app/api/admin/auth/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\auth\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:m}=l;function f(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,7696],()=>s(35117));module.exports=r})();