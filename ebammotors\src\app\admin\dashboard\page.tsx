'use client';

import { Suspense } from 'react';
import AdminDashboardLayout from '@/components/admin/AdminDashboardLayout';

function AdminDashboardContent() {
  return <AdminDashboardLayout />;
}

export default function AdminDashboard() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    }>
      <AdminDashboardContent />
    </Suspense>
  );
}