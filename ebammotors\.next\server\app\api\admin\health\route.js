(()=>{var e={};e.id=3100,e.ids=[3100],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>m,Tq:()=>h,bS:()=>d,fF:()=>c,mU:()=>l});var s=r(85663),n=r(43205),a=r.n(n);let i=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await s.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function l(e){return o.delete(e)}async function d(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),r=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return a().sign(t,i,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,r=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:r,expiresAt:r+864e5,lastActivity:r}),function(){let e=Date.now();for(let[t,r]of o.entries())e>r.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function c(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=a().verify(e,i);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let r=Date.now();return r>t.expiresAt?(o.delete(e),null):(t.lastActivity=r,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let p=new Map;function m(e){let t=Date.now(),r=p.get(e);return!r||t-r.lastAttempt>9e5?(p.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):r.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-r.lastAttempt)}:(r.count++,r.lastAttempt=t,p.set(e,r),{allowed:!0,remainingAttempts:5-r.count})}function h(e){p.delete(e)}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},42935:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>d});var n=r(96559),a=r(48088),i=r(37719),o=r(32190),u=r(77268),l=r(48137);async function d(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url);t.get("detailed");let s=parseInt(t.get("timeRange")||"3600000"),[n,a,i,d,c]=await Promise.all([(0,l.PG)(),(0,l.Sz)(s),(0,l.ZX)(20),(0,l.AD)(),(0,l.pX)()]),p={status:n.status,timestamp:n.timestamp,uptime:n.uptime,issues:n.issues,stats:n.stats,database:d,memory:c,recentErrors:i,performance:a,system:{nodeVersion:process.version,platform:process.platform,uptime:process.uptime(),pid:process.pid}},m=o.NextResponse.json(p),{getSecurityHeaders:h}=await r.e(2833).then(r.bind(r,92833)),g=h();return Object.entries(g).forEach(([e,t])=>{m.headers.set(e,t)}),m}catch(s){console.error("Health check error:",s);let e=o.NextResponse.json({success:!1,message:"Health check failed",error:s instanceof Error?s.message:"Unknown error"},{status:500}),{getSecurityHeaders:t}=await r.e(2833).then(r.bind(r,92833));return Object.entries(t()).forEach(([t,r])=>{e.headers.set(t,r)}),e}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/health/route",pathname:"/api/admin/health",filename:"route",bundlePath:"app/api/admin/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:h}=c;function g(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48137:(e,t,r)=>{"use strict";r.d(t,{AD:()=>d,HL:()=>a,PG:()=>l,Sz:()=>o,ZX:()=>u,pX:()=>c,vV:()=>i});let s=[],n=[];function a(e){let t=Date.now(),r=new URL(e.url).pathname,n=e.method;return{endpoint:r,method:n,startTime:t,finish:(a,i)=>{let o=Date.now()-t,u=e.headers.get("user-agent")||void 0,l=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||void 0,d={timestamp:Date.now(),endpoint:r,method:n,duration:o,status:a,userAgent:u,ip:l,error:i};s.push(d),s.length>1e3&&s.splice(0,s.length-1e3),o>5e3&&console.warn(`[PERFORMANCE] Slow request detected: ${n} ${r} took ${o}ms`),a>=400&&console.error(`[ERROR] ${n} ${r} returned ${a}${i?`: ${i}`:""}`)}}}function i(e,t){let r=e instanceof Error?e.message:e,s=e instanceof Error?e.stack:void 0,a={timestamp:Date.now(),error:r,stack:s,endpoint:t.endpoint||"unknown",method:t.method||"unknown",userAgent:t.request?.headers.get("user-agent")||void 0,ip:t.request?.headers.get("x-forwarded-for")||t.request?.headers.get("x-real-ip")||void 0,context:t.additionalContext};n.push(a),n.length>500&&n.splice(0,n.length-500),console.error(`[ERROR] ${r}`,{endpoint:a.endpoint,method:a.method,ip:a.ip,context:a.context})}function o(e=36e5){let t=Date.now()-e,r=s.filter(e=>e.timestamp>t);if(0===r.length)return{totalRequests:0,averageResponseTime:0,errorRate:0,slowRequests:0,endpointStats:{}};let n=r.length,a=r.reduce((e,t)=>e+t.duration,0)/n,i=r.filter(e=>e.status>=400).length,u=r.filter(e=>e.duration>5e3).length,l={};return r.forEach(e=>{l[e.endpoint]||(l[e.endpoint]={requests:0,averageTime:0,errors:0,slowRequests:0});let t=l[e.endpoint];t.requests++,t.averageTime=(t.averageTime*(t.requests-1)+e.duration)/t.requests,e.status>=400&&t.errors++,e.duration>5e3&&t.slowRequests++}),{totalRequests:n,averageResponseTime:Math.round(a),errorRate:Math.round(i/n*1e4)/100,slowRequests:u,endpointStats:l}}function u(e=50){return n.slice(-e).reverse().map(e=>({timestamp:new Date(e.timestamp).toISOString(),error:e.error,endpoint:e.endpoint,method:e.method,ip:e.ip,context:e.context}))}function l(){let e=o(3e5),t=u(10),r="healthy",s=[];return e.errorRate>10?(r="unhealthy",s.push(`High error rate: ${e.errorRate}%`)):e.errorRate>5&&(r="degraded",s.push(`Elevated error rate: ${e.errorRate}%`)),e.averageResponseTime>1e4?(r="unhealthy",s.push(`Very slow response time: ${e.averageResponseTime}ms`)):e.averageResponseTime>5e3&&("healthy"===r&&(r="degraded"),s.push(`Slow response time: ${e.averageResponseTime}ms`)),e.slowRequests>.1*e.totalRequests&&("healthy"===r&&(r="degraded"),s.push(`High number of slow requests: ${e.slowRequests}`)),{status:r,timestamp:new Date().toISOString(),uptime:process.uptime(),issues:s,stats:{requests:e.totalRequests,averageResponseTime:e.averageResponseTime,errorRate:e.errorRate,slowRequests:e.slowRequests},recentErrors:t.slice(0,3)}}async function d(){try{let e=Date.now(),{sql:t}=await Promise.all([r.e(3376),r.e(7990)]).then(r.bind(r,83376));await t`SELECT 1 as health_check`;let s=Date.now()-e;return{healthy:!0,latency:s}}catch(e){return{healthy:!1,error:e instanceof Error?e.message:"Unknown database error"}}}function c(){let e=process.memoryUsage();return{rss:Math.round(e.rss/1024/1024),heapTotal:Math.round(e.heapTotal/1024/1024),heapUsed:Math.round(e.heapUsed/1024/1024),external:Math.round(e.external/1024/1024),arrayBuffers:Math.round(e.arrayBuffers/1024/1024)}}setInterval(function(){let e=Date.now()-36e5,t=s.filter(t=>t.timestamp>e);s.splice(0,s.length,...t);let r=n.filter(t=>t.timestamp>e);n.splice(0,n.length,...r)},6e5)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77268:(e,t,r)=>{"use strict";r.d(t,{iY:()=>n}),r(32190);var s=r(12909);function n(e,t){let r=e.headers.get("authorization"),n=e.cookies.get("admin_session")?.value,a=(0,s.fF)(r,n);if(a.isValid)return{isValid:!0,adminId:a.adminId,method:"token/session"};let i=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return i&&i===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7696],()=>r(42935));module.exports=s})();