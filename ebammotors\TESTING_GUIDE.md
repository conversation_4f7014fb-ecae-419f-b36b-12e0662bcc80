# 🧪 Testing Guide - EBAM Motors Advanced Features

This guide will help you test all the implemented advanced features step by step.

## 🔧 Prerequisites

### 1. Environment Setup
First, make sure your `.env.local` file has the required variables:

```bash
# Google Analytics (Optional for testing)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Push Notifications (Required for push notification testing)
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your-vapid-public-key

# Social Media (Optional for social login testing)
NEXT_PUBLIC_SOCIAL_CLIENT_IDS={"facebook":"your-fb-client-id","linkedin":"your-linkedin-client-id"}

# Admin Authentication (Required for admin features)
ADMIN_PASSWORD=your-admin-password

# Database (If using)
POSTGRES_URL=your-postgres-url
```

### 2. Generate VAPID Keys for Push Notifications
```bash
cd ebammotors
npx web-push generate-vapid-keys
```
Copy the generated keys to your `.env.local` file.

### 3. Install Dependencies
```bash
npm install
```

### 4. Start Development Server
```bash
npm run dev
```

## 🧪 Feature Testing

### ✅ 1. Google Analytics 4 Testing

**What to test**: Analytics tracking and event firing

**Steps**:
1. Open browser developer tools (F12)
2. Go to Network tab, filter by "collect" or "analytics"
3. Navigate through your website:
   - Visit different pages
   - Search for cars
   - Click on car details
   - Use contact forms
   - Interact with chatbot
4. **Expected**: You should see network requests to Google Analytics
5. **Verify**: Check Google Analytics Real-Time reports (if GA_ID is configured)

**Test URLs**:
- `http://localhost:3000/en` - Homepage
- `http://localhost:3000/en/stock` - Stock page
- `http://localhost:3000/en/contact` - Contact page

### ✅ 2. PWA Features Testing

**What to test**: Progressive Web App functionality

**Steps**:
1. **Installation Prompt**:
   - Open in Chrome/Edge
   - Wait 5 seconds on homepage
   - **Expected**: Installation prompt should appear
   - Click "Install" to test installation

2. **Manifest File**:
   - Visit `http://localhost:3000/manifest.json`
   - **Expected**: JSON manifest with app details

3. **Service Worker**:
   - Open DevTools → Application → Service Workers
   - **Expected**: Service worker should be registered
   - Check Cache Storage for cached files

4. **Offline Testing**:
   - Go offline (DevTools → Network → Offline)
   - Navigate to cached pages
   - **Expected**: Pages should load from cache

**Mobile Testing**:
- Test on mobile device or use DevTools mobile emulation
- Check for "Add to Home Screen" prompt

### ✅ 3. Push Notifications Testing

**What to test**: Subscription, preferences, and notifications

**Steps**:
1. **Subscription**:
   - Add notification settings component to a page
   - Click "Enable Notifications"
   - **Expected**: Browser permission prompt
   - Grant permission

2. **Admin Notification Sender**:
   - Go to admin panel: `http://localhost:3000/admin`
   - Login with admin password
   - Find notification sender component
   - Send test notification
   - **Expected**: Notification appears on device

3. **Preferences**:
   - Toggle different notification types
   - **Expected**: Settings save successfully

**Test Component Integration**:
Add to any page for testing:
```tsx
import NotificationSettings from '@/components/NotificationSettings';

// In your component
<NotificationSettings />
```

### ✅ 4. Advanced Search Testing

**What to test**: Search functionality, filters, and suggestions

**Steps**:
1. **Basic Search**:
   - Add AdvancedSearch component to stock page
   - Type search queries (Toyota, Voxy, etc.)
   - **Expected**: Search suggestions appear

2. **Filters**:
   - Open filter panel
   - Set price range, year range, brand filters
   - Apply filters
   - **Expected**: Search parameters update

3. **Saved Searches**:
   - Perform a search with filters
   - Save the search with a name
   - **Expected**: Search saves and appears in saved searches

**Test Component Integration**:
```tsx
import AdvancedSearch from '@/components/AdvancedSearch';

// In your component
<AdvancedSearch 
  onSearch={(query, filters, sort) => {
    console.log('Search:', { query, filters, sort });
  }}
/>
```

### ✅ 5. Social Media Integration Testing

**What to test**: Social sharing and login functionality

**Steps**:
1. **Social Sharing**:
   - Add SocialShare component to car detail pages
   - Test sharing on different platforms
   - **Expected**: Share dialogs open correctly

2. **Copy Link**:
   - Click copy link button
   - **Expected**: Link copied to clipboard

3. **Social Login** (if configured):
   - Test Facebook/LinkedIn login buttons
   - **Expected**: Redirects to social platform

**Test Component Integration**:
```tsx
import SocialShare from '@/components/SocialShare';

// In your component
<SocialShare 
  shareData={{
    url: 'http://localhost:3000/car/123',
    title: 'Toyota Voxy 2020',
    description: 'Excellent condition car from Japan',
    hashtags: ['UsedCars', 'Toyota']
  }}
  variant="buttons"
  showLabels={true}
/>
```

### ✅ 6. Real-time Tracking Testing

**What to test**: Tracking functionality and updates

**Steps**:
1. **Tracking API**:
   - Test tracking endpoints:
   ```bash
   # Get tracking info
   curl http://localhost:3000/api/tracking/realtime/order-123
   
   # Add tracking event
   curl -X POST http://localhost:3000/api/tracking/events \
     -H "Content-Type: application/json" \
     -d '{"orderId":"order-123","status":"shipped","title":"Shipped","description":"Vehicle shipped from Japan"}'
   ```

2. **GPS Updates**:
   - Test GPS location updates
   - **Expected**: Location data saves correctly

3. **Notifications**:
   - Test delivery notifications
   - **Expected**: Notifications send successfully

### ✅ 7. Community Features Testing

**What to test**: Forums, loyalty program, and user content

**Steps**:
1. **Forum Posts**:
   - Test forum API endpoints:
   ```bash
   # Get forum posts
   curl http://localhost:3000/api/community/forum/posts
   
   # Create forum post
   curl -X POST http://localhost:3000/api/community/forum/posts \
     -H "Content-Type: application/json" \
     -d '{"title":"Test Post","content":"Test content","category":"general"}'
   ```

2. **Loyalty Program**:
   - Test loyalty status API:
   ```bash
   curl http://localhost:3000/api/community/loyalty/user-123
   ```

3. **User Generated Content**:
   - Test UGC submission and retrieval
   - **Expected**: Content saves and displays correctly

### ✅ 8. Advanced Analytics Testing

**What to test**: Enhanced analytics and reporting

**Steps**:
1. **Admin Analytics**:
   - Go to: `http://localhost:3000/admin/analytics`
   - Login with admin credentials
   - **Expected**: Enhanced analytics dashboard loads

2. **API Testing**:
   ```bash
   # Get analytics data
   curl http://localhost:3000/api/admin/analytics?range=30d \
     -H "Authorization: Bearer admin-token"
   ```

3. **Performance Metrics**:
   - Check system health: `http://localhost:3000/api/admin/health`
   - **Expected**: System status and metrics display

## 🔍 Debugging Common Issues

### Issue: Push notifications not working
**Solution**: 
- Check VAPID keys are correctly set
- Ensure HTTPS (use ngrok for local testing)
- Check browser permissions

### Issue: PWA not installing
**Solution**:
- Ensure manifest.json is accessible
- Check service worker registration
- Test on supported browsers (Chrome, Edge, Firefox)

### Issue: Analytics not tracking
**Solution**:
- Verify GA_ID is correct
- Check network requests in DevTools
- Ensure analytics code is properly imported

### Issue: Social sharing not working
**Solution**:
- Check URL encoding
- Test with different platforms
- Verify share data format

## 📱 Mobile Testing

### Test on Real Devices
1. **PWA Installation**: Test on Android/iOS
2. **Push Notifications**: Test on mobile browsers
3. **Social Sharing**: Test native share API
4. **Responsive Design**: Test all components

### Use Browser DevTools
1. Toggle device emulation
2. Test different screen sizes
3. Check touch interactions
4. Verify mobile-specific features

## 🚀 Production Testing Checklist

Before deploying to production:

- [ ] All environment variables configured
- [ ] Google Analytics property set up
- [ ] VAPID keys generated and configured
- [ ] PWA icons added to public/icons/
- [ ] Social media apps configured (if using social login)
- [ ] Database connections tested
- [ ] All API endpoints responding correctly
- [ ] Mobile responsiveness verified
- [ ] Performance metrics acceptable
- [ ] Error handling working correctly

## 📊 Performance Testing

### Lighthouse Testing
1. Open DevTools → Lighthouse
2. Run audit for:
   - Performance
   - Accessibility
   - Best Practices
   - SEO
   - PWA

**Expected Scores**:
- Performance: 90+
- Accessibility: 95+
- Best Practices: 95+
- SEO: 95+
- PWA: 100 (if PWA features enabled)

### Load Testing
Test with multiple concurrent users:
```bash
# Install artillery for load testing
npm install -g artillery

# Create test script and run
artillery quick --count 10 --num 5 http://localhost:3000
```

## 🎯 Next Steps After Testing

1. **Fix any issues** found during testing
2. **Optimize performance** based on Lighthouse results
3. **Configure production environment** variables
4. **Set up monitoring** and error tracking
5. **Deploy to staging** environment first
6. **Conduct user acceptance testing**
7. **Deploy to production**

---

**💡 Tip**: Test features incrementally and keep detailed notes of any issues for easier debugging.
