import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { initializeServices } from '@/lib/startup';

// POST - Initialize system services
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('🔧 System initialization requested by admin');
    
    // Initialize all services
    initializeServices();

    return NextResponse.json({
      success: true,
      message: 'System services initialized successfully',
      services: {
        automatedFollowups: true,
        emailService: true,
        crmStorage: true
      }
    });

  } catch (error) {
    console.error('Error initializing system services:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to initialize system services' },
      { status: 500 }
    );
  }
}

// GET - Check system status
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check various system components
    const systemStatus = {
      timestamp: new Date().toISOString(),
      services: {
        automatedFollowups: {
          enabled: process.env.ENABLE_AUTOMATION === 'true' || process.env.NODE_ENV === 'production',
          nextProcessing: new Date(Date.now() + 60 * 60 * 1000).toISOString()
        },
        emailService: {
          enabled: !!process.env.RESEND_API_KEY,
          provider: 'resend'
        },
        smsService: {
          enabled: false, // TODO: Implement SMS service
          provider: 'none'
        },
        crmStorage: {
          enabled: true,
          type: 'file-based'
        }
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        automationEnabled: process.env.ENABLE_AUTOMATION === 'true'
      }
    };

    return NextResponse.json({
      success: true,
      status: systemStatus
    });

  } catch (error) {
    console.error('Error checking system status:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to check system status' },
      { status: 500 }
    );
  }
}
