(()=>{var e={};e.id=2677,e.ids=[2677],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23870:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(55511);let a={randomUUID:n.randomUUID},i=new Uint8Array(256),s=i.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(a.randomUUID&&!t&&!e)return a.randomUUID();let u=(e=e||{}).random??e.rng?.()??(s>i.length-16&&((0,n.randomFillSync)(i),s=0),i.slice(s,s+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=u[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(u)}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45938:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>D,routeModule:()=>y,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var n={};r.r(n),r.d(n,{GET:()=>c,PATCH:()=>d,POST:()=>l,createAbandonedCartFollowUp:()=>p,createInactivityFollowUp:()=>f,createOrderDeliveryFollowUp:()=>w});var a=r(96559),i=r(48088),s=r(37719),o=r(32190),u=r(53190);async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("adminKey"),n=t.get("status"),a=t.get("customerId"),i="true"===t.get("pending"),s=process.env.ADMIN_PASSWORD||"admin123";if(r!==s)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let c=[];return(c=i?await (0,u.getPendingFollowUps)():a?await (0,u._Y)(a):n?await (0,u.dD)(n):await (0,u.getAllFollowUps)()).sort((e,t)=>new Date(e.scheduledDate).getTime()-new Date(t.scheduledDate).getTime()),o.NextResponse.json({success:!0,followups:c})}catch(e){return console.error("Error fetching follow-ups:",e),o.NextResponse.json({success:!1,message:"Failed to fetch follow-ups"},{status:500})}}async function l(e){try{let t=await e.json();if(!t.title||!t.scheduledDate)return o.NextResponse.json({success:!1,message:"Title and scheduled date are required"},{status:400});let r={type:"task",status:"pending",priority:"medium",createdBy:"admin",...t},n=await (0,u.XL)(r);return o.NextResponse.json({success:!0,message:"Follow-up created successfully",followup:n})}catch(e){return console.error("Error creating follow-up:",e),o.NextResponse.json({success:!1,message:"Failed to create follow-up"},{status:500})}}async function d(e){try{let{followupId:t,adminKey:r,...n}=await e.json(),a=process.env.ADMIN_PASSWORD||"admin123";if(r!==a)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});if(!t)return o.NextResponse.json({success:!1,message:"Follow-up ID is required"},{status:400});if("completed"!==n.status||n.completedDate||(n.completedDate=new Date().toISOString()),!await (0,u.updateFollowUp)(t,n))return o.NextResponse.json({success:!1,message:"Follow-up not found"},{status:404});return o.NextResponse.json({success:!0,message:"Follow-up updated successfully"})}catch(e){return console.error("Error updating follow-up:",e),o.NextResponse.json({success:!1,message:"Failed to update follow-up"},{status:500})}}async function p(e,t){try{let r={type:"email",status:"pending",priority:"medium",customerId:e,title:"Abandoned Cart Follow-up",description:`Customer left items in cart: ${t.items?.map(e=>e.title).join(", ")}`,scheduledDate:new Date(Date.now()+864e5).toISOString(),template:"abandoned_cart",emailConfig:{subject:"Complete Your Purchase - Items Still Available",body:"Hi there! We noticed you left some great items in your cart. Complete your purchase now before they're gone!"},automationRule:{trigger:"abandoned_cart",delay:24,conditions:{cartValue:t.totalValue}},createdBy:"system"};return await (0,u.XL)(r)}catch(e){return console.error("Error creating abandoned cart follow-up:",e),null}}async function w(e,t){try{let r={type:"email",status:"pending",priority:"low",customerId:e,orderId:t,title:"Order Delivery Follow-up",description:"Follow up on order delivery and request review",scheduledDate:new Date(Date.now()+6048e5).toISOString(),template:"order_delivered",emailConfig:{subject:"How was your recent purchase?",body:"We hope you're enjoying your recent purchase! Please let us know how everything went and consider leaving a review."},automationRule:{trigger:"order_delivered",delay:168},createdBy:"system"};return await (0,u.XL)(r)}catch(e){return console.error("Error creating order delivery follow-up:",e),null}}async function f(e,t){try{let r={type:"email",status:"pending",priority:"low",customerId:e,title:"Customer Re-engagement",description:`Customer inactive for ${t} days`,scheduledDate:new Date().toISOString(),template:"customer_reengagement",emailConfig:{subject:"We miss you! Check out our latest vehicles",body:"It's been a while since your last visit. Come back and see our latest vehicle arrivals!"},automationRule:{trigger:"no_activity",delay:0,conditions:{daysSinceLastActivity:t}},createdBy:"system"};return await (0,u.XL)(r)}catch(e){return console.error("Error creating inactivity follow-up:",e),null}}let y=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/followups/route",pathname:"/api/followups",filename:"route",bundlePath:"app/api/followups/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\followups\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:h}=y;function D(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},53190:(e,t,r)=>{"use strict";r.d(t,{Gk:()=>T,Gq:()=>B,HR:()=>O,Kt:()=>U,Q6:()=>j,Rf:()=>C,XL:()=>X,Y2:()=>b,_Y:()=>G,aN:()=>Z,createCustomerActivity:()=>Q,createInteraction:()=>_,dD:()=>Y,fE:()=>L,getAllFollowUps:()=>H,getCustomerByEmail:()=>k,getCustomerById:()=>E,getLeadById:()=>F,getPendingFollowUps:()=>$,oP:()=>ee,qz:()=>J,sr:()=>x,tR:()=>A,tS:()=>v,updateFollowUp:()=>z});var n=r(29021),a=r(33873),i=r.n(a),s=r(23870);let o=i().join(process.cwd(),"data"),u=i().join(o,"leads.json"),c=i().join(o,"customers.json"),l=i().join(o,"interactions.json"),d=i().join(o,"followups.json"),p=i().join(o,"activities.json"),w=process.env.VERCEL||process.env.NETLIFY||process.env.AWS_LAMBDA_FUNCTION_NAME,f=[],y=[],g=[],m=[],h=[];async function D(){if(!w)try{await n.promises.access(o)}catch{await n.promises.mkdir(o,{recursive:!0})}}async function S(){if(w)return f;try{await D();let e=await n.promises.readFile(u,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function I(e){if(w){f=e;return}await D(),await n.promises.writeFile(u,JSON.stringify(e,null,2))}async function A(e){let t=await S(),r={...e,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await I(t),r}async function v(){return await S()}async function F(e){return(await S()).find(t=>t.id===e)||null}async function j(e,t){let r=await S(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},await I(r),!0)}async function x(e){let t=await S(),r=t.filter(t=>t.id!==e);return r.length!==t.length&&(await I(r),!0)}async function O(e){return(await S()).filter(t=>t.status===e)}async function U(e){return(await S()).filter(t=>t.source===e)}async function N(){if(w)return y;try{await D();let e=await n.promises.readFile(c,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function R(e){if(w){y=e;return}await D(),await n.promises.writeFile(c,JSON.stringify(e,null,2))}async function b(e){let t=await N(),r=t.findIndex(t=>t.personalInfo.email===e.personalInfo.email);if(-1!==r)return t[r]={...t[r],...e,updatedAt:new Date().toISOString()},await R(t),t[r];{let r={...e,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await R(t),r}}async function C(){return await N()}async function E(e){return(await N()).find(t=>t.id===e)||null}async function k(e){return(await N()).find(t=>t.personalInfo.email===e)||null}async function T(e,t){let r=await N(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},await R(r),!0)}async function q(){if(w)return g;try{await D();let e=await n.promises.readFile(l,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function P(e){if(w){g=e;return}await D(),await n.promises.writeFile(l,JSON.stringify(e,null,2))}async function _(e){let t=await q(),r={...e,id:(0,s.A)(),createdAt:new Date().toISOString()};return t.push(r),await P(t),r}async function L(){return await q()}async function J(e){return(await q()).filter(t=>t.customerId===e)}async function B(e){return(await q()).filter(t=>t.leadId===e)}async function M(){if(w)return m;try{await D();let e=await n.promises.readFile(d,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function W(e){if(w){m=e;return}await D(),await n.promises.writeFile(d,JSON.stringify(e,null,2))}async function X(e){let t=await M(),r={...e,id:(0,s.A)(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),await W(t),r}async function H(){return await M()}async function Y(e){return(await M()).filter(t=>t.status===e)}async function $(){let e=await M(),t=new Date().toISOString();return e.filter(e=>"pending"===e.status&&e.scheduledDate<=t)}async function z(e,t){let r=await M(),n=r.findIndex(t=>t.id===e);return -1!==n&&(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},await W(r),!0)}async function G(e){return(await M()).filter(t=>t.customerId===e)}async function K(){if(w)return h;try{await D();let e=await n.promises.readFile(p,"utf-8");return JSON.parse(e)}catch(e){return[]}}async function V(e){if(w){h=e;return}await D(),await n.promises.writeFile(p,JSON.stringify(e,null,2))}async function Q(e){let t=await K(),r={...e,id:(0,s.A)(),timestamp:new Date().toISOString()};return t.push(r),await V(t),r}async function Z(e){return(await K()).filter(t=>t.customerId===e)}async function ee(e){let t=await E(e);if(!t)return null;let r=await J(e),n=await G(e),a=await Z(e);return{customer:t,stats:{totalInteractions:r.length,pendingFollowUps:n.filter(e=>"pending"===e.status).length,recentActivities:a.filter(e=>new Date(e.timestamp)>=new Date(Date.now()-6048e5)).length,lastInteraction:r.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())[0]?.createdAt},recentInteractions:r.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()).slice(0,5),upcomingFollowUps:n.filter(e=>"pending"===e.status).sort((e,t)=>new Date(e.scheduledDate).getTime()-new Date(t.scheduledDate).getTime()).slice(0,3)}}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,580],()=>r(45938));module.exports=n})();