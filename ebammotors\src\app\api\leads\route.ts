import { NextRequest, NextResponse } from 'next/server';
import {
  createLead,
  getAllLeads,
  getLeadById,
  updateLead,
  deleteLead,
  getLeadsByStatus,
  getLeadsBySource
} from '@/lib/crmStorage';
import { notifyAdminNewLead } from '@/lib/adminNotifications';
import { Lead } from '@/types/payment';

// GET - Fetch leads
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');
    const leadId = searchParams.get('id');
    const status = searchParams.get('status') as Lead['status'];
    const source = searchParams.get('source') as Lead['source'];

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get specific lead
    if (leadId) {
      const lead = await getLeadById(leadId);
      if (!lead) {
        return NextResponse.json(
          { success: false, message: 'Lead not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, lead });
    }

    // Get leads by status
    if (status) {
      const leads = await getLeadsByStatus(status);
      return NextResponse.json({ success: true, leads });
    }

    // Get leads by source
    if (source) {
      const leads = await getLeadsBySource(source);
      return NextResponse.json({ success: true, leads });
    }

    // Get all leads
    const leads = await getAllLeads();
    return NextResponse.json({ success: true, leads });

  } catch (error) {
    console.error('Error fetching leads:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch leads' },
      { status: 500 }
    );
  }
}

// POST - Create new lead
export async function POST(request: NextRequest) {
  try {
    const leadData = await request.json();

    // Validate required fields
    if (!leadData.customerInfo?.name || !leadData.inquiry?.message) {
      return NextResponse.json(
        { success: false, message: 'Name and message are required' },
        { status: 400 }
      );
    }

    // Set defaults
    const newLeadData = {
      source: 'manual',
      status: 'new',
      priority: 'medium',
      tags: [],
      ...leadData,
    } as Omit<Lead, 'id' | 'createdAt' | 'updatedAt'>;

    const lead = await createLead(newLeadData);

    // Log interaction for lead creation
    const { createInteraction } = await import('@/lib/crmStorage');
    await createInteraction({
      leadId: lead.id,
      type: 'chat',
      direction: 'inbound',
      channel: leadData.source === 'chatbot' ? 'website' : 'manual',
      content: leadData.inquiry.message,
      subject: leadData.inquiry.subject || 'New Lead Inquiry',
      tags: ['lead_creation'],
      createdBy: 'system',
    });

    // Schedule automated follow-ups (3 days)
    const { scheduleAutoFollowupForLead } = await import('@/lib/automatedFollowup');
    await scheduleAutoFollowupForLead(lead.id, newLeadData);

    // Send admin notification for new lead
    try {
      await notifyAdminNewLead({
        leadId: lead.id,
        customerName: lead.customerInfo.name,
        customerEmail: lead.customerInfo.email || 'No email provided',
        source: lead.source,
        priority: lead.priority,
        inquiry: lead.inquiry.message
      });
    } catch (emailError) {
      console.error('Failed to send admin lead notification:', emailError);
      // Don't fail the lead creation if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Lead created successfully',
      lead
    });

  } catch (error) {
    console.error('Error creating lead:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create lead' },
      { status: 500 }
    );
  }
}

// PATCH - Update lead
export async function PATCH(request: NextRequest) {
  try {
    const { leadId, adminKey, ...updates } = await request.json();

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!leadId) {
      return NextResponse.json(
        { success: false, message: 'Lead ID is required' },
        { status: 400 }
      );
    }

    const success = await updateLead(leadId, updates);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Lead not found' },
        { status: 404 }
      );
    }

    // Log interaction for lead update
    const { createInteraction } = await import('@/lib/crmStorage');
    await createInteraction({
      leadId,
      type: 'support',
      direction: 'outbound',
      channel: 'website',
      content: `Lead updated: ${Object.keys(updates).join(', ')}`,
      subject: 'Lead Status Update',
      tags: ['lead_update', 'admin_action'],
      createdBy: 'admin',
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Lead updated successfully' 
    });

  } catch (error) {
    console.error('Error updating lead:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update lead' },
      { status: 500 }
    );
  }
}

// DELETE - Delete lead
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const leadId = searchParams.get('id');
    const adminKey = searchParams.get('adminKey');

    // Verify admin authentication
    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';
    if (adminKey !== validAdminKey) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!leadId) {
      return NextResponse.json(
        { success: false, message: 'Lead ID is required' },
        { status: 400 }
      );
    }

    const success = await deleteLead(leadId);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Lead not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Lead deleted successfully' 
    });

  } catch (error) {
    console.error('Error deleting lead:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete lead' },
      { status: 500 }
    );
  }
}
