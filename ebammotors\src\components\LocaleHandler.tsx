'use client';

import { useEffect } from 'react';

interface LocaleHandlerProps {
  locale: string;
}

export default function LocaleHandler({ locale }: LocaleHandlerProps) {
  useEffect(() => {
    // Update the html lang attribute when locale changes
    if (typeof document !== 'undefined') {
      document.documentElement.lang = locale;
    }
  }, [locale]);

  return null; // This component doesn't render anything
}
