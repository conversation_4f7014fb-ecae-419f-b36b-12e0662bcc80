'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Off, Setting<PERSON>, Check, X } from 'lucide-react';
import { 
  subscribeToPush, 
  unsubscribeFromPush, 
  getSubscriptionStatus,
  updateNotificationPreferences,
  sendTestNotification,
  NotificationPreferences,
  DEFAULT_PREFERENCES
} from '@/lib/pushNotifications';

export default function NotificationSettings() {
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState<NotificationPreferences>(DEFAULT_PREFERENCES);
  const [permission, setPermission] = useState<NotificationPermission>('default');

  useEffect(() => {
    checkSubscriptionStatus();
  }, []);

  const checkSubscriptionStatus = async () => {
    try {
      const status = await getSubscriptionStatus();
      setIsSubscribed(status.isSubscribed);
      setPermission(status.permission);
      
      if (status.subscription?.preferences) {
        setPreferences(status.subscription.preferences);
      }
    } catch (error) {
      // Error checking subscription status
    }
  };

  const handleSubscribe = async () => {
    setIsLoading(true);
    try {
      const subscription = await subscribeToPush();
      if (subscription) {
        setIsSubscribed(true);
        setPermission('granted');
      }
    } catch (error) {
      // Error subscribing to notifications
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnsubscribe = async () => {
    setIsLoading(true);
    try {
      const success = await unsubscribeFromPush();
      if (success) {
        setIsSubscribed(false);
      }
    } catch (error) {
      // Error unsubscribing from notifications
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreferenceChange = async (key: keyof NotificationPreferences, value: boolean) => {
    const newPreferences = { ...preferences, [key]: value };
    setPreferences(newPreferences);
    
    try {
      await updateNotificationPreferences({ [key]: value });
    } catch (error) {
      // Revert on error
      setPreferences(preferences);
    }
  };

  const handleTestNotification = async () => {
    try {
      await sendTestNotification();
    } catch (error) {
      // Error sending test notification
    }
  };

  if (permission === 'denied') {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <BellOff className="w-5 h-5 text-yellow-600" />
          <div>
            <h3 className="font-medium text-yellow-800">Notifications Blocked</h3>
            <p className="text-sm text-yellow-700 mt-1">
              Please enable notifications in your browser settings to receive updates.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <Bell className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900">Push Notifications</h3>
        </div>
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="text-gray-500 hover:text-gray-700"
        >
          <Settings className="w-4 h-4" />
        </button>
      </div>

      <div className="space-y-4">
        {/* Subscription Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium text-gray-900">Enable Notifications</p>
            <p className="text-sm text-gray-600">
              Get updates about your orders and new cars
            </p>
          </div>
          <button
            onClick={isSubscribed ? handleUnsubscribe : handleSubscribe}
            disabled={isLoading}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              isSubscribed ? 'bg-blue-600' : 'bg-gray-200'
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                isSubscribed ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Notification Preferences */}
        {isSubscribed && showSettings && (
          <div className="border-t pt-4 space-y-3">
            <h4 className="font-medium text-gray-900 mb-3">Notification Preferences</h4>
            
            {Object.entries({
              orderUpdates: 'Order Updates',
              newStock: 'New Stock Alerts',
              promotions: 'Promotions & Deals',
              priceDrops: 'Price Drop Alerts',
              newsletter: 'Newsletter',
              reminders: 'Reminders',
            }).map(([key, label]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="text-sm text-gray-700">{label}</span>
                <button
                  onClick={() => handlePreferenceChange(
                    key as keyof NotificationPreferences, 
                    !preferences[key as keyof NotificationPreferences]
                  )}
                  className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                    preferences[key as keyof NotificationPreferences] ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      preferences[key as keyof NotificationPreferences] ? 'translate-x-5' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            ))}

            {/* Test Notification */}
            <div className="pt-3 border-t">
              <button
                onClick={handleTestNotification}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium transition-colors"
              >
                Send Test Notification
              </button>
            </div>
          </div>
        )}

        {/* Status Indicator */}
        <div className="flex items-center space-x-2 text-sm">
          {isSubscribed ? (
            <>
              <Check className="w-4 h-4 text-green-600" />
              <span className="text-green-700">Notifications enabled</span>
            </>
          ) : (
            <>
              <X className="w-4 h-4 text-gray-400" />
              <span className="text-gray-600">Notifications disabled</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Admin component for sending notifications
export function AdminNotificationSender() {
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [formData, setFormData] = useState({
    title: '',
    body: '',
    notificationType: 'general',
    targetUsers: '',
  });

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/notifications/send');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      // Error fetching notification stats
    }
  };

  const handleSendNotification = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          body: formData.body,
          notificationType: formData.notificationType,
          targetUsers: formData.targetUsers ? formData.targetUsers.split(',').map(id => id.trim()) : undefined,
          data: {
            url: '/',
            type: formData.notificationType,
          },
        }),
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Notification sent successfully! ${result.sent} sent, ${result.failed} failed.`);
        setFormData({ title: '', body: '', notificationType: 'general', targetUsers: '' });
        fetchStats(); // Refresh stats
      } else {
        throw new Error('Failed to send notification');
      }
    } catch (error) {
      alert('Failed to send notification');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Send Push Notification</h3>
      
      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-600">Total Subscribers</p>
            <p className="text-xl font-bold text-blue-900">{stats.totalSubscriptions}</p>
          </div>
          <div className="bg-green-50 p-3 rounded-lg">
            <p className="text-sm text-green-600">Active (30d)</p>
            <p className="text-xl font-bold text-green-900">{stats.activeSubscriptions}</p>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg">
            <p className="text-sm text-purple-600">Order Updates</p>
            <p className="text-xl font-bold text-purple-900">{stats.preferenceBreakdown.orderUpdates}</p>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg">
            <p className="text-sm text-orange-600">New Stock</p>
            <p className="text-xl font-bold text-orange-900">{stats.preferenceBreakdown.newStock}</p>
          </div>
        </div>
      )}

      {/* Send Form */}
      <form onSubmit={handleSendNotification} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Title
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Message
          </label>
          <textarea
            value={formData.body}
            onChange={(e) => setFormData({ ...formData, body: e.target.value })}
            rows={3}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Type
          </label>
          <select
            value={formData.notificationType}
            onChange={(e) => setFormData({ ...formData, notificationType: e.target.value })}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="general">General</option>
            <option value="orderUpdates">Order Updates</option>
            <option value="newStock">New Stock</option>
            <option value="promotions">Promotions</option>
            <option value="priceDrops">Price Drops</option>
          </select>
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Sending...' : 'Send Notification'}
        </button>
      </form>
    </div>
  );
}
