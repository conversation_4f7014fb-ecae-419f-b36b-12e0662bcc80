(()=>{var e={};e.id=8225,e.ids=[8225],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,s)=>{"use strict";s.d(t,{Qq:()=>p,Tq:()=>g,bS:()=>m,fF:()=>d,mU:()=>c});var r=s(85663),n=s(43205),i=s.n(n);let a=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-in-production",o=new Map;async function u(e,t){try{return await r.Ay.compare(e,t)}catch(e){return console.error("Error verifying password:",e),!1}}function c(e){return o.delete(e)}async function m(e){try{let t=function(){let e=process.env.ADMIN_PASSWORD||"admin123";return e.startsWith("$2a$")||e.startsWith("$2b$")||e.startsWith("$2y$"),e}(),s=!1;if(!(t.startsWith("$2a$")||t.startsWith("$2b$")||t.startsWith("$2y$")?await u(e,t):e===t))return{success:!1,message:"Invalid credentials"};{let e=function(e="admin"){try{let t={id:e,isAdmin:!0,iat:Math.floor(Date.now()/1e3)};return i().sign(t,a,{expiresIn:"24h"})}catch(e){throw console.error("Error generating token:",e),Error("Failed to generate authentication token")}}(),t=function(e="admin"){let t=`admin_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,s=Date.now();return o.set(t,{id:e,isAdmin:!0,createdAt:s,expiresAt:s+864e5,lastActivity:s}),function(){let e=Date.now();for(let[t,s]of o.entries())e>s.expiresAt&&o.delete(t)}(),t}();return{success:!0,token:e,sessionId:t,message:"Authentication successful"}}}catch(e){return console.error("Authentication error:",e),{success:!1,message:"Authentication failed"}}}function d(e,t){if(e&&e.startsWith("Bearer ")){let t=function(e){try{let t=i().verify(e,a);if(t.isAdmin)return{id:t.id,isAdmin:t.isAdmin};return null}catch(e){return null}}(e.substring(7));if(t)return{isValid:!0,adminId:t.id,message:"Token authentication successful"}}if(t){let e=function(e){let t=o.get(e);if(!t)return null;let s=Date.now();return s>t.expiresAt?(o.delete(e),null):(t.lastActivity=s,o.set(e,t),t)}(t);if(e)return{isValid:!0,adminId:e.id,message:"Session authentication successful"}}return{isValid:!1,message:"Authentication required"}}let l=new Map;function p(e){let t=Date.now(),s=l.get(e);return!s||t-s.lastAttempt>9e5?(l.set(e,{count:1,lastAttempt:t}),{allowed:!0,remainingAttempts:4}):s.count>=5?{allowed:!1,remainingAttempts:0,lockoutTime:9e5-(t-s.lastAttempt)}:(s.count++,s.lastAttempt=t,l.set(e,s),{allowed:!0,remainingAttempts:5-s.count})}function g(e){l.delete(e)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77268:(e,t,s)=>{"use strict";s.d(t,{iY:()=>n}),s(32190);var r=s(12909);function n(e,t){let s=e.headers.get("authorization"),n=e.cookies.get("admin_session")?.value,i=(0,r.fF)(s,n);if(i.isValid)return{isValid:!0,adminId:i.adminId,method:"token/session"};let a=t?.adminKey||e.nextUrl.searchParams.get("adminKey");return a&&a===(process.env.ADMIN_PASSWORD||"admin123")?{isValid:!0,adminId:"admin",method:"legacy"}:{isValid:!1,method:"none"}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},83791:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>p});var r={};s.r(r),s.d(r,{GET:()=>c,POST:()=>m});var n=s(96559),i=s(48088),a=s(37719),o=s(32190),u=s(77268);async function c(e){try{if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});return o.NextResponse.json({success:!0,settings:{general:{siteName:"EBAM Motors",siteDescription:"Premium Japanese Cars for Ghana and Africa",contactEmail:"<EMAIL>",contactPhone:"+233245375692",businessAddress:"Kumasi, Ghana",currency:"JPY",timezone:"Africa/Accra"},email:{provider:"resend",smtpHost:"smtp.resend.com",smtpPort:587,smtpUser:"resend",smtpPassword:"***",fromEmail:"<EMAIL>",fromName:"EBAM Motors"},notifications:{emailNotifications:!0,orderNotifications:!0,reviewNotifications:!0,systemAlerts:!0,dailySummary:!1},security:{sessionTimeout:60,maxLoginAttempts:5,passwordMinLength:8,requireTwoFactor:!1,allowedIPs:[]},appearance:{theme:"light",primaryColor:"#2563eb",logoUrl:"/logo.png",faviconUrl:"/favicon.ico"}}})}catch(e){return console.error("Error fetching admin settings:",e),o.NextResponse.json({success:!1,message:"Failed to fetch settings"},{status:500})}}async function m(e){try{var t;if(!(0,u.iY)(e).isValid)return o.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=await e.json();for(let e of["general","email","notifications","security","appearance"])if(!s[e])return o.NextResponse.json({success:!1,message:`Missing ${e} settings`},{status:400});if(s.email.fromEmail&&(t=s.email.fromEmail,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)))return o.NextResponse.json({success:!1,message:"Invalid from email address"},{status:400});if(s.security.sessionTimeout<5||s.security.sessionTimeout>1440)return o.NextResponse.json({success:!1,message:"Session timeout must be between 5 and 1440 minutes"},{status:400});if(s.security.maxLoginAttempts<1||s.security.maxLoginAttempts>20)return o.NextResponse.json({success:!1,message:"Max login attempts must be between 1 and 20"},{status:400});if(s.security.passwordMinLength<6||s.security.passwordMinLength>50)return o.NextResponse.json({success:!1,message:"Password minimum length must be between 6 and 50 characters"},{status:400});return o.NextResponse.json({success:!0,message:"Settings updated successfully"})}catch(e){return console.error("Error updating admin settings:",e),o.NextResponse.json({success:!1,message:"Failed to update settings"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/settings/route",pathname:"/api/admin/settings",filename:"route",bundlePath:"app/api/admin/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\app\\api\\admin\\settings\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:p,serverHooks:g}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:p})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,7696],()=>s(83791));module.exports=r})();