exports.id=5839,exports.ids=[5839],exports.modules={17151:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},22300:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,51767,23)),Promise.resolve().then(r.t.bind(r,50893,23)),Promise.resolve().then(r.t.bind(r,48182,23)),Promise.resolve().then(r.t.bind(r,47429,23)),Promise.resolve().then(r.bind(r,98046)),Promise.resolve().then(r.bind(r,29131))},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\contexts\\AuthContext.tsx","useAuth")},32028:(e,t,r)=>{Promise.resolve().then(r.bind(r,88741)),Promise.resolve().then(r.bind(r,96959)),Promise.resolve().then(r.bind(r,81012)),Promise.resolve().then(r.t.bind(r,79167,23)),Promise.resolve().then(r.bind(r,45016)),Promise.resolve().then(r.bind(r,63213))},35303:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},45016:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(60687),o=r(43210),n=r(17581),i=r(11860),a=r(31158);function l(){let[e,t]=(0,o.useState)(null),[r,l]=(0,o.useState)(!1),[d,c]=(0,o.useState)(!1),[m,u]=(0,o.useState)(!1),[h,p]=(0,o.useState)(!1),x=async()=>{if(e)try{await e.prompt();let{outcome:r}=await e.userChoice;t(null),l(!1)}catch(e){console.error("Error during installation:",e)}},f=()=>{l(!1)};return d?null:m&&!h?(0,s.jsx)("div",{className:"fixed bottom-4 left-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 max-w-sm mx-auto",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(n.A,{className:"w-6 h-6 text-blue-600 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Install EBAM Motors"}),(0,s.jsxs)("p",{className:"text-xs text-gray-600 mt-1",children:["Tap ",(0,s.jsx)("span",{className:"font-mono bg-gray-100 px-1 rounded",children:"⎘"}),' then "Add to Home Screen"']})]})]}),(0,s.jsx)("button",{onClick:f,className:"text-gray-400 hover:text-gray-600 flex-shrink-0",children:(0,s.jsx)(i.A,{className:"w-4 h-4"})})]})}):r&&e?(0,s.jsx)("div",{className:"fixed bottom-4 left-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 max-w-sm mx-auto",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"bg-blue-100 p-2 rounded-full",children:(0,s.jsx)(a.A,{className:"w-5 h-5 text-blue-600"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Install EBAM Motors"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Get the app for faster access and offline browsing"}),(0,s.jsxs)("div",{className:"flex space-x-2 mt-3",children:[(0,s.jsx)("button",{onClick:x,className:"bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-blue-700 transition-colors",children:"Install"}),(0,s.jsx)("button",{onClick:f,className:"text-gray-600 px-3 py-1 rounded text-xs hover:text-gray-800 transition-colors",children:"Not now"})]})]})]}),(0,s.jsx)("button",{onClick:f,className:"text-gray-400 hover:text-gray-600 flex-shrink-0",children:(0,s.jsx)(i.A,{className:"w-4 h-4"})})]})}):null}},61135:()=>{},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>i});var s=r(60687),o=r(43210);let n=(0,o.createContext)(void 0);function i({children:e}){let[t,r]=(0,o.useState)(null),[i,a]=(0,o.useState)(!0),[l,d]=(0,o.useState)([]),[c,m]=(0,o.useState)([]),[u,h]=(0,o.useState)([]),p=async(e,t)=>{a(!0);try{await new Promise(e=>setTimeout(e,1e3));let t={id:"1",email:e,name:e.split("@")[0],joinDate:new Date().toISOString(),loyaltyPoints:1250,membershipTier:"Silver",preferences:{notifications:{email:!0,sms:!1,push:!0,marketing:!1},language:"en",currency:"JPY"}};return r(t),!0}catch(e){return console.error("Login error:",e),!1}finally{a(!1)}},x=async e=>{a(!0);try{await new Promise(e=>setTimeout(e,1e3));let t={id:Date.now().toString(),email:e.email,name:e.name,phone:e.phone,location:e.location,joinDate:new Date().toISOString(),loyaltyPoints:100,membershipTier:"Bronze",preferences:{notifications:{email:!0,sms:!1,push:!0,marketing:!1},language:"en",currency:"JPY"}};return r(t),!0}catch(e){return console.error("Registration error:",e),!1}finally{a(!1)}},f=async e=>{if(!t)return!1;try{let s={...t,...e};return r(s),!0}catch(e){return console.error("Profile update error:",e),!1}},b=(e,s)=>{if(!t)return;let o=t.loyaltyPoints+e,n=t.membershipTier;n=o>=1e4?"Platinum":o>=5e3?"Gold":o>=2e3?"Silver":"Bronze",r(e=>e?{...e,loyaltyPoints:o,membershipTier:n}:null)};return(0,s.jsx)(n.Provider,{value:{user:t,isLoading:i,login:p,register:x,logout:()=>{r(null),d([]),m([]),h([])},updateProfile:f,favorites:l,addToFavorites:(e,r)=>{if(!t)return;let s={id:Date.now().toString(),vehicleId:e,addedAt:new Date().toISOString(),notes:r};d(e=>[...e,s]),b(5,"Added to favorites")},removeFromFavorites:e=>{d(t=>t.filter(t=>t.vehicleId!==e))},isFavorite:e=>l.some(t=>t.vehicleId===e),savedSearches:c,saveSearch:(e,r)=>{if(!t)return;let s={id:Date.now().toString(),name:e,filters:r,createdAt:new Date().toISOString(),lastUsed:new Date().toISOString()};m(e=>[...e,s])},removeSavedSearch:e=>{m(t=>t.filter(t=>t.id!==e))},purchaseHistory:u,addPurchase:e=>{if(!t)return;let r={...e,id:Date.now().toString(),orderDate:new Date().toISOString()};h(e=>[...e,r]),b(Math.floor((parseInt(e.price.replace(/[¥,]/g,""))||0)/1e3),`Purchase: ${e.vehicleTitle}`)},addLoyaltyPoints:b,redeemPoints:(e,s)=>!!t&&!(t.loyaltyPoints<e)&&(r(t=>t?{...t,loyaltyPoints:t.loyaltyPoints-e}:null),!0)},children:e})}function a(){let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>m});var s=r(37413),o=r(35759),n=r.n(o),i=r(58854),a=r.n(i);r(61135);var l=r(29131),d=r(23487),c=r(98046);let m={title:"EBAM MOTORS - Quality Used Goods from Japan to Ghana",description:"EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa.",keywords:"used cars Japan, export Ghana, EBAM Motors, Japanese cars, used electronics, furniture export, trading company, sustainable trade",authors:[{name:"EBAM MOTORS"}],creator:"EBAM MOTORS",publisher:"EBAM MOTORS",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:"https://ebammotors.com",title:"EBAM MOTORS - Quality Used Goods from Japan to Ghana",description:"EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa.",siteName:"EBAM MOTORS"},twitter:{card:"summary_large_image",title:"EBAM MOTORS - Quality Used Goods from Japan to Ghana",description:"EBAM Motors specializes in sourcing, recycling, and exporting high-demand used goods including automobiles, electronics, furniture from Japan to Ghana and Africa."},verification:{google:"your-google-verification-code"},manifest:"/manifest.json"};function u({children:e}){return(0,s.jsx)("html",{lang:"en",className:`${n().variable} ${a().variable}`,children:(0,s.jsxs)("body",{className:"font-inter antialiased",suppressHydrationWarning:!0,children:[(0,s.jsxs)(l.AuthProvider,{children:[e,(0,s.jsx)(c.default,{})]}),(0,s.jsx)(d.GoogleAnalytics,{gaId:"your-google-analytics-4-id"})]})})}},98046:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\PWAInstaller.tsx","default");(0,s.registerClientReference)(function(){throw Error("Attempted to call PWAStatus() from the server but PWAStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\website\\ebammotors\\src\\components\\PWAInstaller.tsx","PWAStatus")}};