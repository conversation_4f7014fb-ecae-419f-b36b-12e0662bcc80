"use strict";exports.id=6967,exports.ids=[6967],exports.modules={16967:(e,t,r)=>{r.d(t,{gm:()=>R});var i=Object.defineProperty,o=Object.defineProperties,n=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,d=(e,t,r)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))a.call(t,r)&&d(e,r,t[r]);if(s)for(var r of s(t))l.call(t,r)&&d(e,r,t[r]);return e},u=(e,t)=>o(e,n(t)),h=(e,t,r)=>new Promise((i,o)=>{var n=e=>{try{a(r.next(e))}catch(e){o(e)}},s=e=>{try{a(r.throw(e))}catch(e){o(e)}},a=e=>e.done?i(e.value):Promise.resolve(e.value).then(n,s);a((r=r.apply(e,t)).next())}),p=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},m=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function g(e){return{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var v=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){let i=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(3794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}i.push(g(t))}return yield this.resend.post("/emails/batch",i,t)})}},y=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(3794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return h(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return h(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return h(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},f=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.get(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}update(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},b=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",{name:e.name,region:e.region,custom_return_path:e.customReturnPath},t)})}list(){return h(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return h(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},w=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(3794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",g(e),t)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return h(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},x="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",M="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.6.0",A=class{constructor(e){if(this.key=e,this.apiKeys=new p(this),this.audiences=new m(this),this.batch=new v(this),this.broadcasts=new y(this),this.contacts=new f(this),this.domains=new b(this),this.emails=new w(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":M,"Content-Type":"application/json"})}fetchRequest(e){return h(this,arguments,function*(e,t={}){try{let r=yield fetch(`${x}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:u(c({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return h(this,arguments,function*(e,t,r={}){let i=new Headers(this.headers);r.idempotencyKey&&i.set("Idempotency-Key",r.idempotencyKey);let o=c({method:"POST",headers:i,body:JSON.stringify(t)},r);return this.fetchRequest(e,o)})}get(e){return h(this,arguments,function*(e,t={}){let r=c({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return h(this,arguments,function*(e,t,r={}){let i=c({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,i)})}patch(e,t){return h(this,arguments,function*(e,t,r={}){let i=c({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,i)})}delete(e,t){return h(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}};let E=`
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
    .container { max-width: 600px; margin: 0 auto; background-color: white; }
    .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }
    .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
    .content { padding: 30px; }
    .vehicle-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin: 20px 0; }
    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
    .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
    .status-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
    .status-confirmed { background-color: #dcfce7; color: #166534; }
    .divider { height: 1px; background-color: #e5e7eb; margin: 20px 0; }
  </style>
`;class T{static generateOrderConfirmationHTML(e){return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Confirmation - ${e.orderNumber}</title>
        ${E}
      </head>
      <body>
        <div class="container">
          <!-- Header -->
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Your trusted partner for quality vehicles from Japan to Ghana</p>
          </div>

          <!-- Content -->
          <div class="content">
            <h1>Order Confirmation</h1>
            <p>Dear ${e.customerName},</p>
            <p>Thank you for your order! We're excited to help you get your new vehicle.</p>

            <div class="highlight">
              <h3>Order Details</h3>
              <p><strong>Order Number:</strong> ${e.orderNumber}</p>
              <p><strong>Order Date:</strong> ${e.orderDate}</p>
              <p><strong>Status:</strong> <span class="status-badge status-confirmed">Confirmed</span></p>
            </div>

            <!-- Vehicle Details -->
            <div class="vehicle-card">
              <h3>Vehicle Information</h3>
              <img src="${e.vehicle.image}" alt="${e.vehicle.title}" style="width: 100%; max-width: 300px; height: 200px; object-fit: cover; border-radius: 6px; margin-bottom: 15px;">
              <h4>${e.vehicle.title}</h4>
              <p><strong>Price:</strong> ${e.vehicle.price}</p>
            </div>

            <!-- Shipping Information -->
            <div class="highlight">
              <h3>Shipping Address</h3>
              <p>
                ${e.shippingAddress.street}<br>
                ${e.shippingAddress.city}, ${e.shippingAddress.state}<br>
                ${e.shippingAddress.country} ${e.shippingAddress.postalCode}
              </p>
              <p><strong>Estimated Delivery:</strong> ${e.estimatedDelivery}</p>
            </div>

            <!-- Order Summary -->
            <div class="divider"></div>
            <div style="text-align: right;">
              <h3>Order Total: ${e.total}</h3>
            </div>

            <!-- Next Steps -->
            <div class="highlight">
              <h3>What's Next?</h3>
              <ul>
                <li>We'll prepare your vehicle for shipping</li>
                <li>You'll receive tracking information once shipped</li>
                <li>Our team will contact you for any updates</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/tracking?order=${e.orderNumber}" class="button">Track Your Order</a>
            </div>

            <p>If you have any questions, please don't hesitate to contact us:</p>
            <ul>
              <li>📧 Email: <EMAIL></li>
              <li>📱 WhatsApp: +233245375692</li>
              <li>📍 Location: Kumasi, Ghana</li>
            </ul>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>\xa9 2024 EBAM Motors. All rights reserved.</p>
            <p>This email was sent to confirm your order. Please keep this for your records.</p>
          </div>
        </div>
      </body>
      </html>
    `}static generateOrderConfirmationText(e){return`
ORDER CONFIRMATION - EBAM Motors

Dear ${e.customerName},

Thank you for your order! We're excited to help you get your new vehicle.

ORDER DETAILS:
- Order Number: ${e.orderNumber}
- Order Date: ${e.orderDate}
- Status: Confirmed

VEHICLE:
- ${e.vehicle.title}
- Price: ${e.vehicle.price}

SHIPPING ADDRESS:
${e.shippingAddress.street}
${e.shippingAddress.city}, ${e.shippingAddress.state}
${e.shippingAddress.country} ${e.shippingAddress.postalCode}

Estimated Delivery: ${e.estimatedDelivery}

ORDER TOTAL: ${e.total}

WHAT'S NEXT:
- We'll prepare your vehicle for shipping
- You'll receive tracking information once shipped
- Our team will contact you for any updates

Track your order: https://yourdomain.com/tracking?order=${e.orderNumber}

CONTACT US:
- Email: <EMAIL>
- WhatsApp: +233245375692
- Location: Kumasi, Ghana

Thank you for choosing EBAM Motors!
\xa9 2024 EBAM Motors. All rights reserved.
    `}static generateReviewNotificationHTML(e){let t="⭐".repeat(e.rating)+"☆".repeat(5-e.rating);return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Review Submitted</title>
        ${E}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors Admin</div>
            <p>New Review Notification</p>
          </div>

          <div class="content">
            <h1>New Review Submitted</h1>
            
            <div class="highlight">
              <h3>Review Details</h3>
              <p><strong>Customer:</strong> ${e.customerName}</p>
              <p><strong>Vehicle:</strong> ${e.vehicleTitle}</p>
              <p><strong>Rating:</strong> ${t} (${e.rating}/5)</p>
              <p><strong>Date:</strong> ${e.reviewDate}</p>
            </div>

            <div class="vehicle-card">
              <h3>Review Content</h3>
              <p>"${e.review}"</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/admin/reviews" class="button">Review & Approve</a>
            </div>
          </div>

          <div class="footer">
            <p>EBAM Motors Admin Panel</p>
            <p>Please review and approve/reject this review in the admin panel.</p>
          </div>
        </div>
      </body>
      </html>
    `}static generateReviewApprovalHTML(e){let t="⭐".repeat(e.rating)+"☆".repeat(5-e.rating);return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Review Approved</title>
        ${E}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Thank you for your feedback!</p>
          </div>

          <div class="content">
            <h1>Your Review Has Been Approved!</h1>
            <p>Dear ${e.customerName},</p>
            <p>Thank you for taking the time to review your experience with us. Your review has been approved and is now live on our website!</p>

            <div class="highlight">
              <h3>Your Review</h3>
              <p><strong>Vehicle:</strong> ${e.vehicleTitle}</p>
              <p><strong>Rating:</strong> ${t} (${e.rating}/5)</p>
              <p><strong>Review:</strong> "${e.review}"</p>
            </div>

            <p>Your feedback helps other customers make informed decisions and helps us improve our services.</p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/reviews" class="button">View All Reviews</a>
            </div>
          </div>

          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>\xa9 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `}static generateContactFormAdminHTML(e){return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Contact Form Submission</title>
        ${E}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors Admin</div>
            <p>New Contact Form Submission</p>
          </div>

          <div class="content">
            <h1>New Contact Form Submission</h1>
            
            <div class="highlight">
              <h3>Contact Details</h3>
              <p><strong>Name:</strong> ${e.name}</p>
              <p><strong>Email:</strong> ${e.email}</p>
              <p><strong>Subject:</strong> ${e.subject}</p>
              <p><strong>Submitted:</strong> ${e.submissionDate}</p>
            </div>

            <div class="vehicle-card">
              <h3>Message</h3>
              <p>${e.message.replace(/\n/g,"<br>")}</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="mailto:${e.email}?subject=Re: ${e.subject}" class="button">Reply to Customer</a>
            </div>
          </div>

          <div class="footer">
            <p>EBAM Motors Admin Panel</p>
            <p>Please respond to this customer inquiry promptly.</p>
          </div>
        </div>
      </body>
      </html>
    `}static generateAbandonedCartHTML(e){return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Complete Your Purchase</title>
        ${E}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Don't miss out on your perfect vehicle!</p>
          </div>

          <div class="content">
            <h1>Complete Your Purchase</h1>
            <p>Hi ${e.customerName},</p>
            <p>We noticed you were interested in some amazing vehicles but didn't complete your purchase. Don't worry - we've saved your items!</p>

            <div class="highlight">
              <h3>Your Saved Items</h3>
              ${e.data?.items?.map(e=>`
                <div class="vehicle-card">
                  <h4>${e.title}</h4>
                  <p><strong>Price:</strong> ${e.price}</p>
                  <p>Quantity: ${e.quantity}</p>
                </div>
              `).join("")||"<p>Your selected vehicles are waiting for you!</p>"}
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/stock" class="button">Complete Your Purchase</a>
            </div>

            <div class="highlight">
              <h3>Why Choose EBAM Motors?</h3>
              <ul>
                <li>✅ Quality guaranteed vehicles from Japan</li>
                <li>✅ Competitive pricing with transparent costs</li>
                <li>✅ Reliable shipping to Ghana and Africa</li>
                <li>✅ Expert support throughout the process</li>
              </ul>
            </div>

            <p>Need help deciding? Our team is here to assist you:</p>
            <ul>
              <li>📱 WhatsApp: +233245375692</li>
              <li>📧 Email: <EMAIL></li>
            </ul>
          </div>

          <div class="footer">
            <p>This offer won't last forever!</p>
            <p>\xa9 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `}static generateDeliveryUpdateHTML(e){return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Delivery Update</title>
        ${E}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Your vehicle is on its way!</p>
          </div>

          <div class="content">
            <h1>Delivery Update</h1>
            <p>Hi ${e.customerName},</p>
            <p>Great news! We have an update on your vehicle delivery.</p>

            <div class="highlight">
              <h3>Delivery Status</h3>
              <p><strong>Current Status:</strong> ${e.data?.status||"In Transit"}</p>
              <p><strong>Location:</strong> ${e.data?.location||"En route to destination"}</p>
              <p><strong>Estimated Arrival:</strong> ${e.data?.estimatedArrival||"To be confirmed"}</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/tracking?order=${e.data?.orderId}" class="button">Track Your Order</a>
            </div>

            <div class="vehicle-card">
              <h3>What to Expect Next</h3>
              <ul>
                <li>We'll notify you 24 hours before delivery</li>
                <li>Our delivery team will contact you directly</li>
                <li>Ensure someone is available to receive the vehicle</li>
                <li>Have your ID and order confirmation ready</li>
              </ul>
            </div>
          </div>

          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>\xa9 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `}static generateFeedbackRequestHTML(e){return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>How was your experience?</title>
        ${E}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>We'd love to hear from you!</p>
          </div>

          <div class="content">
            <h1>How Was Your Experience?</h1>
            <p>Hi ${e.customerName},</p>
            <p>We hope you're enjoying your new vehicle! Your feedback helps us improve our services and helps other customers make informed decisions.</p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/reviews" class="button">Leave a Review</a>
            </div>

            <div class="highlight">
              <h3>Share Your Experience</h3>
              <p>Tell us about:</p>
              <ul>
                <li>🚗 Vehicle quality and condition</li>
                <li>📦 Shipping and delivery experience</li>
                <li>👥 Customer service quality</li>
                <li>💰 Value for money</li>
                <li>🌟 Overall satisfaction</li>
              </ul>
            </div>

            <div class="vehicle-card">
              <h3>Why Your Review Matters</h3>
              <ul>
                <li>Helps other customers make confident decisions</li>
                <li>Helps us improve our services</li>
                <li>Builds trust in our community</li>
                <li>Takes less than 2 minutes to complete</li>
              </ul>
            </div>

            <p>As a thank you, customers who leave reviews get priority support and exclusive offers!</p>
          </div>

          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>\xa9 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `}static generateContactFormCustomerHTML(e){return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Thank you for contacting us</title>
        ${E}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Thank you for reaching out!</p>
          </div>

          <div class="content">
            <h1>Thank You for Contacting Us!</h1>
            <p>Dear ${e.name},</p>
            <p>We've received your message and appreciate you taking the time to contact us. Our team will review your inquiry and respond within 24 hours.</p>

            <div class="highlight">
              <h3>Your Message Summary</h3>
              <p><strong>Subject:</strong> ${e.subject}</p>
              <p><strong>Submitted:</strong> ${e.submissionDate}</p>
              <p><strong>Reference ID:</strong> #${Date.now().toString().slice(-6)}</p>
            </div>

            <div class="vehicle-card">
              <h3>What to Expect Next</h3>
              <ul>
                <li>Our team will review your inquiry</li>
                <li>You'll receive a response within 24 hours</li>
                <li>For urgent matters, contact us on WhatsApp</li>
              </ul>
            </div>

            <p>In the meantime, feel free to:</p>
            <ul>
              <li>Browse our latest vehicle inventory</li>
              <li>Check out customer reviews</li>
              <li>Learn more about our services</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/stock" class="button">Browse Vehicles</a>
            </div>

            <p><strong>Need immediate assistance?</strong></p>
            <ul>
              <li>📱 WhatsApp: +233245375692</li>
              <li>📧 Email: <EMAIL></li>
              <li>📍 Location: Kumasi, Ghana</li>
            </ul>
          </div>

          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>\xa9 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `}}let $=new A(process.env.RESEND_API_KEY),k={from:process.env.RESEND_FROM_EMAIL||"EBAM Motors <<EMAIL>>",adminEmail:process.env.ADMIN_EMAIL||"<EMAIL>",supportEmail:process.env.SUPPORT_EMAIL||"<EMAIL>",noReplyEmail:process.env.NO_REPLY_EMAIL||"<EMAIL>"};class C{constructor(){this.resend=$}async sendEmail(e){try{if(!process.env.RESEND_API_KEY)return console.warn("Resend API key not configured. Email not sent."),{success:!1,error:"Resend API key not configured"};let t=await this.resend.emails.send({from:k.from,to:e.to,subject:e.subject,html:e.html,text:e.text});if(t.error)return console.error("Resend email error:",t.error),{success:!1,error:t.error.message};return{success:!0,messageId:t.data?.id}}catch(e){return console.error("Email service error:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async sendOrderConfirmation(e,t){let r={to:e,subject:`Order Confirmation - ${t.orderNumber} | EBAM Motors`,html:this.generateOrderConfirmationHTML(t),text:this.generateOrderConfirmationText(t)};return await this.sendEmail(r)}async sendReviewNotificationToAdmin(e){let t={to:k.adminEmail,subject:`New Review Submitted - ${e.vehicleTitle} | EBAM Motors`,html:this.generateReviewNotificationHTML(e),text:this.generateReviewNotificationText(e)};return await this.sendEmail(t)}async sendReviewApprovalNotification(e,t){let r={to:e,subject:"Your Review Has Been Approved | EBAM Motors",html:this.generateReviewApprovalHTML(t),text:this.generateReviewApprovalText(t)};return await this.sendEmail(r)}async sendContactFormNotification(e){let t={to:k.adminEmail,subject:`New Contact Form Submission - ${e.subject} | EBAM Motors`,html:this.generateContactFormAdminHTML(e),text:this.generateContactFormAdminText(e)},r={to:e.email,subject:"Thank you for contacting EBAM Motors",html:this.generateContactFormCustomerHTML(e),text:this.generateContactFormCustomerText(e)},[i,o]=await Promise.all([this.sendEmail(t),this.sendEmail(r)]);return{success:i.success&&o.success,error:i.error||o.error}}async sendFollowUpEmail(e){try{let t=e.productInterest||e.leadSource,r=t?`Following up on your ${e.productInterest||"vehicle"} inquiry - EBAM Motors`:"How are you enjoying your EBAM Motors experience?",i={to:e.to,subject:r,html:this.generateFollowUpHTML(e,t),text:this.generateFollowUpText(e,t)};return await this.sendEmail(i)}catch(e){return console.error("Error sending follow-up email:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async sendAdminNotification(e){let t={to:k.adminEmail,subject:`${e.title} | EBAM Motors Admin`,html:this.generateAdminNotificationHTML(e),text:this.generateAdminNotificationText(e)};return await this.sendEmail(t)}async sendFollowUpEmail(e){let t="",r="",i="";switch(e.type){case"abandoned_cart":t="Complete Your Purchase - Items Still Available | EBAM Motors",r=this.generateAbandonedCartHTML(e),i=this.generateAbandonedCartText(e);break;case"delivery_update":t="Delivery Update for Your Order | EBAM Motors",r=this.generateDeliveryUpdateHTML(e),i=this.generateDeliveryUpdateText(e);break;case"feedback_request":t="How was your experience with EBAM Motors?",r=this.generateFeedbackRequestHTML(e),i=this.generateFeedbackRequestText(e);break;case"maintenance_reminder":t="Vehicle Maintenance Reminder | EBAM Motors",r=this.generateMaintenanceReminderHTML(e),i=this.generateMaintenanceReminderText(e);break;default:return{success:!1,error:"Unknown follow-up type"}}let o={to:e.customerEmail,subject:t,html:r,text:i};return await this.sendEmail(o)}generateOrderConfirmationHTML(e){return T.generateOrderConfirmationHTML(e)}generateOrderConfirmationText(e){return T.generateOrderConfirmationText(e)}generateReviewNotificationHTML(e){return T.generateReviewNotificationHTML(e)}generateReviewNotificationText(e){return`New Review from ${e.customerName} for ${e.vehicleTitle} - Rating: ${e.rating}/5`}generateReviewApprovalHTML(e){return T.generateReviewApprovalHTML(e)}generateReviewApprovalText(e){return`Your review for ${e.vehicleTitle} has been approved. Thank you ${e.customerName}!`}generateContactFormAdminHTML(e){return T.generateContactFormAdminHTML(e)}generateContactFormAdminText(e){return`New Contact Form from ${e.name} (${e.email}) - Subject: ${e.subject}`}generateContactFormCustomerHTML(e){return T.generateContactFormCustomerHTML(e)}generateContactFormCustomerText(e){return`Thank you for contacting EBAM Motors, ${e.name}. We received your message about "${e.subject}" and will respond within 24 hours.`}generateAdminNotificationHTML(e){return`<h1>${e.title}</h1><p>${e.message}</p>`}generateAdminNotificationText(e){return`${e.title}: ${e.message}`}generateAbandonedCartHTML(e){return T.generateAbandonedCartHTML(e)}generateAbandonedCartText(e){return`Hi ${e.customerName}! You left some amazing vehicles in your cart. Complete your purchase at EBAM Motors and get them shipped to Ghana. Don't miss out!`}generateFollowUpHTML(e,t){return t?`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">EBAM Motors</h1>
            <p style="color: #f0f0f0; margin: 10px 0 0 0;">Premium Japanese Cars for Ghana & Africa</p>
          </div>

          <div style="padding: 30px; background: #ffffff;">
            <h2 style="color: #333; margin-bottom: 20px;">Hi ${e.customerName}!</h2>

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              I hope this email finds you well. I wanted to follow up on your recent inquiry about
              <strong>${e.productInterest||"our vehicles"}</strong>.
            </p>

            ${e.inquiryDetails?`
              <div style="background: #f8f9fa; padding: 15px; border-left: 4px solid #667eea; margin: 20px 0;">
                <p style="margin: 0; color: #555; font-style: italic;">"${e.inquiryDetails}"</p>
              </div>
            `:""}

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              We have some excellent options that might interest you:
            </p>

            <ul style="color: #666; line-height: 1.8; margin-bottom: 25px;">
              <li>🚗 <strong>Premium Quality:</strong> All vehicles inspected and certified</li>
              <li>📋 <strong>Complete Documentation:</strong> Full import paperwork handled</li>
              <li>🚚 <strong>Delivery Service:</strong> Direct shipping to Ghana</li>
              <li>💰 <strong>Competitive Pricing:</strong> Best value for Japanese imports</li>
              <li>🛡️ <strong>Warranty Options:</strong> Peace of mind with every purchase</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://websit-ebam1s-projects.vercel.app/en/stock"
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                View Our Latest Stock
              </a>
            </div>

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              Have any questions or need more information? I'm here to help! You can:
            </p>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="margin: 0 0 10px 0; color: #333;"><strong>📞 Call/WhatsApp:</strong> +233245375692</p>
              <p style="margin: 0 0 10px 0; color: #333;"><strong>📧 Email:</strong> <EMAIL></p>
              <p style="margin: 0; color: #333;"><strong>🌐 Website:</strong> ebammotors.com</p>
            </div>

            <p style="color: #666; line-height: 1.6;">
              Thank you for considering EBAM Motors for your vehicle needs. We look forward to helping you find the perfect car!
            </p>

            <p style="color: #666; margin-top: 30px;">
              Best regards,<br>
              <strong>EBAM Motors Team</strong><br>
              <em>Your trusted partner for Japanese cars in Ghana</em>
            </p>
          </div>

          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px; margin: 0;">
              This is an automated follow-up email. If you no longer wish to receive these emails, please contact us.
            </p>
          </div>
        </div>
      `:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">EBAM Motors</h1>
            <p style="color: #f0f0f0; margin: 10px 0 0 0;">Premium Japanese Cars for Ghana & Africa</p>
          </div>

          <div style="padding: 30px; background: #ffffff;">
            <h2 style="color: #333; margin-bottom: 20px;">Hi ${e.customerName}!</h2>

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              I hope you're enjoying your experience with EBAM Motors! It's been a few days since we last connected,
              and I wanted to check in to see how everything is going.
            </p>

            <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
              At EBAM Motors, your satisfaction is our top priority. Whether you have questions about:
            </p>

            <ul style="color: #666; line-height: 1.8; margin-bottom: 25px;">
              <li>🚗 Vehicle specifications or features</li>
              <li>📋 Documentation and import process</li>
              <li>🚚 Shipping and delivery updates</li>
              <li>💰 Payment options or financing</li>
              <li>🔧 Maintenance tips and recommendations</li>
            </ul>

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              We're here to help! Don't hesitate to reach out if you need any assistance.
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://websit-ebam1s-projects.vercel.app/en/stock"
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Browse Our Latest Arrivals
              </a>
            </div>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="margin: 0 0 10px 0; color: #333;"><strong>📞 Call/WhatsApp:</strong> +233245375692</p>
              <p style="margin: 0 0 10px 0; color: #333;"><strong>📧 Email:</strong> <EMAIL></p>
              <p style="margin: 0; color: #333;"><strong>🌐 Website:</strong> ebammotors.com</p>
            </div>

            <p style="color: #666; line-height: 1.6;">
              Thank you for choosing EBAM Motors. We appreciate your business and look forward to serving you again!
            </p>

            <p style="color: #666; margin-top: 30px;">
              Best regards,<br>
              <strong>EBAM Motors Team</strong><br>
              <em>Your trusted partner for Japanese cars in Ghana</em>
            </p>
          </div>

          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px; margin: 0;">
              This is an automated follow-up email. If you no longer wish to receive these emails, please contact us.
            </p>
          </div>
        </div>
      `}generateFollowUpText(e,t){return t?`Hi ${e.customerName}! Following up on your inquiry about ${e.productInterest||"our vehicles"}. We have excellent options available. Contact us at +233245375692 or visit ebammotors.com. - EBAM Motors`:`Hi ${e.customerName}! Hope you're enjoying your EBAM Motors experience. Need any assistance? We're here to help! Contact us at +233245375692. - EBAM Motors`}generateDeliveryUpdateHTML(e){return T.generateDeliveryUpdateHTML(e)}generateDeliveryUpdateText(e){return`Delivery update for ${e.customerName}: ${e.data?.status||"Your vehicle is on its way"}. Track your order at ebammotors.com`}generateFeedbackRequestHTML(e){return T.generateFeedbackRequestHTML(e)}generateFeedbackRequestText(e){return`Hi ${e.customerName}! How was your experience with EBAM Motors? We'd love to hear your feedback. Leave a review at ebammotors.com/reviews`}generateMaintenanceReminderHTML(e){return`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Vehicle Maintenance Reminder</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }
          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
          .content { padding: 30px; }
          .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
          .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Keep your vehicle in perfect condition</p>
          </div>
          <div class="content">
            <h1>Vehicle Maintenance Reminder</h1>
            <p>Hi ${e.customerName},</p>
            <p>It's time for your vehicle's scheduled maintenance to keep it running smoothly and safely.</p>
            <div class="highlight">
              <h3>Recommended Maintenance</h3>
              <p><strong>Vehicle:</strong> ${e.data?.vehicleTitle||"Your vehicle"}</p>
              <p><strong>Mileage:</strong> ${e.data?.currentMileage||"Check your odometer"}</p>
              <p><strong>Service Due:</strong> ${e.data?.serviceType||"Regular maintenance"}</p>
            </div>
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/contact" class="button">Schedule Service</a>
            </div>
            <p>Regular maintenance helps ensure:</p>
            <ul>
              <li>🔧 Optimal performance and fuel efficiency</li>
              <li>🛡️ Safety and reliability</li>
              <li>💰 Prevention of costly repairs</li>
              <li>📈 Maintained resale value</li>
            </ul>
          </div>
          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>\xa9 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `}generateMaintenanceReminderText(e){return`Hi ${e.customerName}! It's time for your vehicle's scheduled maintenance. Contact EBAM Motors to schedule service and keep your vehicle running smoothly.`}}let R=new C}};