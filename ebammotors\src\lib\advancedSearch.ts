// Advanced search functionality for EBAM Motors
// Includes filters, sorting, autocomplete, and intelligent suggestions

export interface SearchFilters {
  priceRange: {
    min: number;
    max: number;
  };
  yearRange: {
    min: number;
    max: number;
  };
  mileageRange: {
    min: number;
    max: number;
  };
  brands: string[];
  models: string[];
  fuelTypes: string[];
  transmissions: string[];
  colors: string[];
  conditions: string[];
  locations: string[];
  features: string[];
}

export interface SortOption {
  field: string;
  direction: 'asc' | 'desc';
  label: string;
}

export interface SearchResult {
  id: string;
  title: string;
  brand: string;
  model: string;
  year: number;
  price: number;
  mileage: number;
  fuelType: string;
  transmission: string;
  color: string;
  condition: string;
  location: string;
  features: string[];
  images: string[];
  description: string;
  relevanceScore: number;
}

export interface SavedSearch {
  id: string;
  name: string;
  query: string;
  filters: Partial<SearchFilters>;
  sortBy: SortOption;
  createdAt: string;
  lastUsed: string;
  alertsEnabled: boolean;
}

export interface SearchSuggestion {
  text: string;
  type: 'brand' | 'model' | 'feature' | 'location' | 'query';
  count: number;
  category?: string;
}

// Default search filters
export const DEFAULT_FILTERS: SearchFilters = {
  priceRange: { min: 0, max: 10000000 },
  yearRange: { min: 1990, max: new Date().getFullYear() },
  mileageRange: { min: 0, max: 500000 },
  brands: [],
  models: [],
  fuelTypes: [],
  transmissions: [],
  colors: [],
  conditions: [],
  locations: [],
  features: [],
};

// Available sort options
export const SORT_OPTIONS: SortOption[] = [
  { field: 'relevance', direction: 'desc', label: 'Most Relevant' },
  { field: 'price', direction: 'asc', label: 'Price: Low to High' },
  { field: 'price', direction: 'desc', label: 'Price: High to Low' },
  { field: 'year', direction: 'desc', label: 'Year: Newest First' },
  { field: 'year', direction: 'asc', label: 'Year: Oldest First' },
  { field: 'mileage', direction: 'asc', label: 'Mileage: Low to High' },
  { field: 'mileage', direction: 'desc', label: 'Mileage: High to Low' },
  { field: 'createdAt', direction: 'desc', label: 'Recently Added' },
  { field: 'popularity', direction: 'desc', label: 'Most Popular' },
];

// Filter options
export const FILTER_OPTIONS = {
  brands: ['Toyota', 'Honda', 'Nissan', 'Mazda', 'Subaru', 'Mitsubishi', 'Suzuki', 'Daihatsu'],
  fuelTypes: ['Gasoline', 'Hybrid', 'Electric', 'Diesel', 'LPG'],
  transmissions: ['Automatic', 'Manual', 'CVT'],
  colors: ['White', 'Black', 'Silver', 'Red', 'Blue', 'Gray', 'Green', 'Yellow', 'Brown'],
  conditions: ['Excellent', 'Good', 'Fair', 'Needs Repair'],
  locations: ['Tokyo', 'Osaka', 'Nagoya', 'Yokohama', 'Kobe', 'Kyoto', 'Fukuoka', 'Sapporo'],
  features: [
    'Air Conditioning', 'Power Steering', 'Power Windows', 'Central Locking',
    'ABS', 'Airbags', 'Navigation System', 'Backup Camera', 'Sunroof',
    'Leather Seats', 'Heated Seats', 'Bluetooth', 'USB Port', 'Cruise Control'
  ],
};

// Search cars with advanced filters and sorting
export const searchCars = async (
  query: string,
  filters: Partial<SearchFilters> = {},
  sortBy: SortOption = SORT_OPTIONS[0],
  page: number = 1,
  limit: number = 20
): Promise<{
  results: SearchResult[];
  total: number;
  page: number;
  totalPages: number;
  filters: SearchFilters;
}> => {
  try {
    const searchParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      limit: limit.toString(),
      sortField: sortBy.field,
      sortDirection: sortBy.direction,
      ...Object.entries(filters).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null) {
          acc[key] = JSON.stringify(value);
        }
        return acc;
      }, {} as Record<string, string>),
    });

    const response = await fetch(`/api/search/cars?${searchParams}`);
    
    if (!response.ok) {
      throw new Error('Search request failed');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
};

// Get search suggestions for autocomplete
export const getSearchSuggestions = async (
  query: string,
  limit: number = 10
): Promise<SearchSuggestion[]> => {
  try {
    const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}&limit=${limit}`);
    
    if (!response.ok) {
      throw new Error('Failed to get suggestions');
    }

    const data = await response.json();
    return data.suggestions || [];
  } catch (error) {
    return [];
  }
};

// Save a search for later use
export const saveSearch = async (
  name: string,
  query: string,
  filters: Partial<SearchFilters>,
  sortBy: SortOption,
  alertsEnabled: boolean = false
): Promise<SavedSearch> => {
  try {
    const savedSearch: Omit<SavedSearch, 'id'> = {
      name,
      query,
      filters,
      sortBy,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
      alertsEnabled,
    };

    const response = await fetch('/api/search/saved', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(savedSearch),
    });

    if (!response.ok) {
      throw new Error('Failed to save search');
    }

    const data = await response.json();
    return data.savedSearch;
  } catch (error) {
    throw error;
  }
};

// Get saved searches
export const getSavedSearches = async (): Promise<SavedSearch[]> => {
  try {
    const response = await fetch('/api/search/saved');
    
    if (!response.ok) {
      throw new Error('Failed to get saved searches');
    }

    const data = await response.json();
    return data.savedSearches || [];
  } catch (error) {
    return [];
  }
};

// Delete a saved search
export const deleteSavedSearch = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/search/saved/${id}`, {
      method: 'DELETE',
    });

    return response.ok;
  } catch (error) {
    return false;
  }
};

// Update saved search last used timestamp
export const updateSavedSearchUsage = async (id: string): Promise<void> => {
  try {
    await fetch(`/api/search/saved/${id}/use`, {
      method: 'PATCH',
    });
  } catch (error) {
    // Error updating saved search usage
  }
};

// Get popular searches
export const getPopularSearches = async (limit: number = 10): Promise<string[]> => {
  try {
    const response = await fetch(`/api/search/popular?limit=${limit}`);
    
    if (!response.ok) {
      throw new Error('Failed to get popular searches');
    }

    const data = await response.json();
    return data.searches || [];
  } catch (error) {
    return [];
  }
};

// Track search query for analytics
export const trackSearch = async (
  query: string,
  filters: Partial<SearchFilters>,
  resultsCount: number
): Promise<void> => {
  try {
    await fetch('/api/search/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        filters,
        resultsCount,
        timestamp: new Date().toISOString(),
      }),
    });
  } catch (error) {
    // Error tracking search
  }
};

// Build search query string for URL
export const buildSearchUrl = (
  query: string,
  filters: Partial<SearchFilters>,
  sortBy: SortOption,
  page: number = 1
): string => {
  const params = new URLSearchParams();
  
  if (query) params.set('q', query);
  if (page > 1) params.set('page', page.toString());
  if (sortBy.field !== 'relevance') {
    params.set('sort', `${sortBy.field}_${sortBy.direction}`);
  }

  // Add filters to URL
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value) && value.length > 0) {
        params.set(key, value.join(','));
      } else if (typeof value === 'object' && value !== null) {
        const obj = value as any;
        if (obj.min !== undefined || obj.max !== undefined) {
          params.set(key, `${obj.min || ''}-${obj.max || ''}`);
        }
      }
    }
  });

  return `/search?${params.toString()}`;
};

// Parse search URL parameters
export const parseSearchUrl = (searchParams: URLSearchParams): {
  query: string;
  filters: Partial<SearchFilters>;
  sortBy: SortOption;
  page: number;
} => {
  const query = searchParams.get('q') || '';
  const page = parseInt(searchParams.get('page') || '1');
  
  // Parse sort option
  const sortParam = searchParams.get('sort');
  let sortBy = SORT_OPTIONS[0];
  if (sortParam) {
    const [field, direction] = sortParam.split('_');
    const foundSort = SORT_OPTIONS.find(
      opt => opt.field === field && opt.direction === direction
    );
    if (foundSort) sortBy = foundSort;
  }

  // Parse filters
  const filters: Partial<SearchFilters> = {};
  
  // Price range
  const priceRange = searchParams.get('priceRange');
  if (priceRange) {
    const [min, max] = priceRange.split('-').map(v => parseInt(v) || undefined);
    if (min !== undefined || max !== undefined) {
      filters.priceRange = { min: min || 0, max: max || 10000000 };
    }
  }

  // Year range
  const yearRange = searchParams.get('yearRange');
  if (yearRange) {
    const [min, max] = yearRange.split('-').map(v => parseInt(v) || undefined);
    if (min !== undefined || max !== undefined) {
      filters.yearRange = { min: min || 1990, max: max || new Date().getFullYear() };
    }
  }

  // Array filters
  ['brands', 'models', 'fuelTypes', 'transmissions', 'colors', 'conditions', 'locations', 'features'].forEach(key => {
    const value = searchParams.get(key);
    if (value) {
      (filters as any)[key] = value.split(',').filter(Boolean);
    }
  });

  return { query, filters, sortBy, page };
};

// Calculate search relevance score
export const calculateRelevanceScore = (
  item: any,
  query: string,
  filters: Partial<SearchFilters>
): number => {
  let score = 0;
  const queryLower = query.toLowerCase();
  
  // Title match (highest weight)
  if (item.title?.toLowerCase().includes(queryLower)) {
    score += 100;
  }
  
  // Brand/model match
  if (item.brand?.toLowerCase().includes(queryLower)) {
    score += 80;
  }
  if (item.model?.toLowerCase().includes(queryLower)) {
    score += 80;
  }
  
  // Description match
  if (item.description?.toLowerCase().includes(queryLower)) {
    score += 40;
  }
  
  // Features match
  if (item.features?.some((feature: string) => 
    feature.toLowerCase().includes(queryLower)
  )) {
    score += 30;
  }
  
  // Exact matches get bonus
  if (item.brand?.toLowerCase() === queryLower) {
    score += 50;
  }
  if (item.model?.toLowerCase() === queryLower) {
    score += 50;
  }
  
  // Recent items get slight boost
  const daysSinceAdded = (Date.now() - new Date(item.createdAt || 0).getTime()) / (1000 * 60 * 60 * 24);
  if (daysSinceAdded < 7) {
    score += 10;
  }
  
  return score;
};
