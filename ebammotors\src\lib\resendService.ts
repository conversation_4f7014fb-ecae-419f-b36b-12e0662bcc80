import { Resend } from 'resend';
import { EmailTemplates } from './emailTemplates';

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Email configuration
export const EMAIL_CONFIG = {
  from: process.env.RESEND_FROM_EMAIL || 'EBAM Motors <<EMAIL>>',
  adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',
  supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
  noReplyEmail: process.env.NO_REPLY_EMAIL || '<EMAIL>',
};

// Email template interfaces
export interface EmailTemplate {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
}

export interface OrderConfirmationData {
  customerName: string;
  orderNumber: string;
  orderDate: string;
  vehicle: {
    title: string;
    price: string;
    image: string;
  };
  total: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  estimatedDelivery: string;
}

export interface ReviewNotificationData {
  customerName: string;
  vehicleTitle: string;
  rating: number;
  review: string;
  reviewDate: string;
  isApproval?: boolean;
}

export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
  submissionDate: string;
}

export interface AdminNotificationData {
  type: 'new_order' | 'new_review' | 'contact_form' | 'low_stock' | 'system_alert';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
}

export interface FollowUpData {
  customerName: string;
  customerEmail: string;
  type: 'abandoned_cart' | 'delivery_update' | 'feedback_request' | 'maintenance_reminder';
  data?: any;
}

// Base email service class
export class ResendEmailService {
  private resend: Resend;

  constructor() {
    this.resend = resend;
  }

  /**
   * Send a generic email
   */
  async sendEmail(template: EmailTemplate): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      if (!process.env.RESEND_API_KEY) {
        console.warn('Resend API key not configured. Email not sent.');
        return { success: false, error: 'Resend API key not configured' };
      }

      const result = await this.resend.emails.send({
        from: EMAIL_CONFIG.from,
        to: template.to,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      if (result.error) {
        console.error('Resend email error:', result.error);
        return { success: false, error: result.error.message };
      }

      console.log('Email sent successfully:', result.data?.id);
      return { success: true, messageId: result.data?.id };
    } catch (error) {
      console.error('Email service error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send order confirmation email
   */
  async sendOrderConfirmation(customerEmail: string, data: OrderConfirmationData): Promise<{ success: boolean; error?: string }> {
    const template: EmailTemplate = {
      to: customerEmail,
      subject: `Order Confirmation - ${data.orderNumber} | EBAM Motors`,
      html: this.generateOrderConfirmationHTML(data),
      text: this.generateOrderConfirmationText(data),
    };

    const result = await this.sendEmail(template);
    return result;
  }

  /**
   * Send review notification to admin
   */
  async sendReviewNotificationToAdmin(data: ReviewNotificationData): Promise<{ success: boolean; error?: string }> {
    const template: EmailTemplate = {
      to: EMAIL_CONFIG.adminEmail,
      subject: `New Review Submitted - ${data.vehicleTitle} | EBAM Motors`,
      html: this.generateReviewNotificationHTML(data),
      text: this.generateReviewNotificationText(data),
    };

    const result = await this.sendEmail(template);
    return result;
  }

  /**
   * Send review approval notification to customer
   */
  async sendReviewApprovalNotification(customerEmail: string, data: ReviewNotificationData): Promise<{ success: boolean; error?: string }> {
    const template: EmailTemplate = {
      to: customerEmail,
      subject: `Your Review Has Been Approved | EBAM Motors`,
      html: this.generateReviewApprovalHTML(data),
      text: this.generateReviewApprovalText(data),
    };

    const result = await this.sendEmail(template);
    return result;
  }

  /**
   * Send contact form submission notification
   */
  async sendContactFormNotification(data: ContactFormData): Promise<{ success: boolean; error?: string }> {
    // Send to admin
    const adminTemplate: EmailTemplate = {
      to: EMAIL_CONFIG.adminEmail,
      subject: `New Contact Form Submission - ${data.subject} | EBAM Motors`,
      html: this.generateContactFormAdminHTML(data),
      text: this.generateContactFormAdminText(data),
    };

    // Send confirmation to customer
    const customerTemplate: EmailTemplate = {
      to: data.email,
      subject: `Thank you for contacting EBAM Motors`,
      html: this.generateContactFormCustomerHTML(data),
      text: this.generateContactFormCustomerText(data),
    };

    const [adminResult, customerResult] = await Promise.all([
      this.sendEmail(adminTemplate),
      this.sendEmail(customerTemplate),
    ]);

    return {
      success: adminResult.success && customerResult.success,
      error: adminResult.error || customerResult.error,
    };
  }

  /**
   * Send automated follow-up email
   */
  async sendFollowUpEmail(data: {
    to: string;
    customerName: string;
    followupType: string;
    description: string;
    productInterest?: string;
    leadSource?: string;
    inquiryDetails?: string;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const isLeadFollowup = data.productInterest || data.leadSource;

      const subject = isLeadFollowup
        ? `Following up on your ${data.productInterest || 'vehicle'} inquiry - EBAM Motors`
        : 'How are you enjoying your EBAM Motors experience?';

      const template: EmailTemplate = {
        to: data.to,
        subject,
        html: this.generateFollowUpHTML(data, isLeadFollowup),
        text: this.generateFollowUpText(data, isLeadFollowup),
      };

      const result = await this.sendEmail(template);
      console.log(`📧 Follow-up email sent to ${data.to}`);
      return result;
    } catch (error) {
      console.error('Error sending follow-up email:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send admin notification
   */
  async sendAdminNotification(data: AdminNotificationData): Promise<{ success: boolean; error?: string }> {
    const template: EmailTemplate = {
      to: EMAIL_CONFIG.adminEmail,
      subject: `${data.title} | EBAM Motors Admin`,
      html: this.generateAdminNotificationHTML(data),
      text: this.generateAdminNotificationText(data),
    };

    const result = await this.sendEmail(template);
    return result;
  }

  /**
   * Send follow-up email
   */
  async sendFollowUpEmail(data: FollowUpData): Promise<{ success: boolean; error?: string }> {
    let subject = '';
    let html = '';
    let text = '';

    switch (data.type) {
      case 'abandoned_cart':
        subject = 'Complete Your Purchase - Items Still Available | EBAM Motors';
        html = this.generateAbandonedCartHTML(data);
        text = this.generateAbandonedCartText(data);
        break;
      case 'delivery_update':
        subject = 'Delivery Update for Your Order | EBAM Motors';
        html = this.generateDeliveryUpdateHTML(data);
        text = this.generateDeliveryUpdateText(data);
        break;
      case 'feedback_request':
        subject = 'How was your experience with EBAM Motors?';
        html = this.generateFeedbackRequestHTML(data);
        text = this.generateFeedbackRequestText(data);
        break;
      case 'maintenance_reminder':
        subject = 'Vehicle Maintenance Reminder | EBAM Motors';
        html = this.generateMaintenanceReminderHTML(data);
        text = this.generateMaintenanceReminderText(data);
        break;
      default:
        return { success: false, error: 'Unknown follow-up type' };
    }

    const template: EmailTemplate = {
      to: data.customerEmail,
      subject,
      html,
      text,
    };

    const result = await this.sendEmail(template);
    return result;
  }

  // HTML template generators using EmailTemplates class
  private generateOrderConfirmationHTML(data: OrderConfirmationData): string {
    return EmailTemplates.generateOrderConfirmationHTML(data);
  }

  private generateOrderConfirmationText(data: OrderConfirmationData): string {
    return EmailTemplates.generateOrderConfirmationText(data);
  }

  private generateReviewNotificationHTML(data: ReviewNotificationData): string {
    return EmailTemplates.generateReviewNotificationHTML(data);
  }

  private generateReviewNotificationText(data: ReviewNotificationData): string {
    return `New Review from ${data.customerName} for ${data.vehicleTitle} - Rating: ${data.rating}/5`;
  }

  private generateReviewApprovalHTML(data: ReviewNotificationData): string {
    return EmailTemplates.generateReviewApprovalHTML(data);
  }

  private generateReviewApprovalText(data: ReviewNotificationData): string {
    return `Your review for ${data.vehicleTitle} has been approved. Thank you ${data.customerName}!`;
  }

  private generateContactFormAdminHTML(data: ContactFormData): string {
    return EmailTemplates.generateContactFormAdminHTML(data);
  }

  private generateContactFormAdminText(data: ContactFormData): string {
    return `New Contact Form from ${data.name} (${data.email}) - Subject: ${data.subject}`;
  }

  private generateContactFormCustomerHTML(data: ContactFormData): string {
    return EmailTemplates.generateContactFormCustomerHTML(data);
  }

  private generateContactFormCustomerText(data: ContactFormData): string {
    return `Thank you for contacting EBAM Motors, ${data.name}. We received your message about "${data.subject}" and will respond within 24 hours.`;
  }

  private generateAdminNotificationHTML(data: AdminNotificationData): string {
    return `<h1>${data.title}</h1><p>${data.message}</p>`;
  }

  private generateAdminNotificationText(data: AdminNotificationData): string {
    return `${data.title}: ${data.message}`;
  }

  private generateAbandonedCartHTML(data: FollowUpData): string {
    return EmailTemplates.generateAbandonedCartHTML(data);
  }

  private generateAbandonedCartText(data: FollowUpData): string {
    return `Hi ${data.customerName}! You left some amazing vehicles in your cart. Complete your purchase at EBAM Motors and get them shipped to Ghana. Don't miss out!`;
  }

  private generateFollowUpHTML(data: any, isLeadFollowup: boolean): string {
    if (isLeadFollowup) {
      return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">EBAM Motors</h1>
            <p style="color: #f0f0f0; margin: 10px 0 0 0;">Premium Japanese Cars for Ghana & Africa</p>
          </div>

          <div style="padding: 30px; background: #ffffff;">
            <h2 style="color: #333; margin-bottom: 20px;">Hi ${data.customerName}!</h2>

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              I hope this email finds you well. I wanted to follow up on your recent inquiry about
              <strong>${data.productInterest || 'our vehicles'}</strong>.
            </p>

            ${data.inquiryDetails ? `
              <div style="background: #f8f9fa; padding: 15px; border-left: 4px solid #667eea; margin: 20px 0;">
                <p style="margin: 0; color: #555; font-style: italic;">"${data.inquiryDetails}"</p>
              </div>
            ` : ''}

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              We have some excellent options that might interest you:
            </p>

            <ul style="color: #666; line-height: 1.8; margin-bottom: 25px;">
              <li>🚗 <strong>Premium Quality:</strong> All vehicles inspected and certified</li>
              <li>📋 <strong>Complete Documentation:</strong> Full import paperwork handled</li>
              <li>🚚 <strong>Delivery Service:</strong> Direct shipping to Ghana</li>
              <li>💰 <strong>Competitive Pricing:</strong> Best value for Japanese imports</li>
              <li>🛡️ <strong>Warranty Options:</strong> Peace of mind with every purchase</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://websit-ebam1s-projects.vercel.app/en/stock"
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                View Our Latest Stock
              </a>
            </div>

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              Have any questions or need more information? I'm here to help! You can:
            </p>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="margin: 0 0 10px 0; color: #333;"><strong>📞 Call/WhatsApp:</strong> +233245375692</p>
              <p style="margin: 0 0 10px 0; color: #333;"><strong>📧 Email:</strong> <EMAIL></p>
              <p style="margin: 0; color: #333;"><strong>🌐 Website:</strong> ebammotors.com</p>
            </div>

            <p style="color: #666; line-height: 1.6;">
              Thank you for considering EBAM Motors for your vehicle needs. We look forward to helping you find the perfect car!
            </p>

            <p style="color: #666; margin-top: 30px;">
              Best regards,<br>
              <strong>EBAM Motors Team</strong><br>
              <em>Your trusted partner for Japanese cars in Ghana</em>
            </p>
          </div>

          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px; margin: 0;">
              This is an automated follow-up email. If you no longer wish to receive these emails, please contact us.
            </p>
          </div>
        </div>
      `;
    } else {
      return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">EBAM Motors</h1>
            <p style="color: #f0f0f0; margin: 10px 0 0 0;">Premium Japanese Cars for Ghana & Africa</p>
          </div>

          <div style="padding: 30px; background: #ffffff;">
            <h2 style="color: #333; margin-bottom: 20px;">Hi ${data.customerName}!</h2>

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              I hope you're enjoying your experience with EBAM Motors! It's been a few days since we last connected,
              and I wanted to check in to see how everything is going.
            </p>

            <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
              At EBAM Motors, your satisfaction is our top priority. Whether you have questions about:
            </p>

            <ul style="color: #666; line-height: 1.8; margin-bottom: 25px;">
              <li>🚗 Vehicle specifications or features</li>
              <li>📋 Documentation and import process</li>
              <li>🚚 Shipping and delivery updates</li>
              <li>💰 Payment options or financing</li>
              <li>🔧 Maintenance tips and recommendations</li>
            </ul>

            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              We're here to help! Don't hesitate to reach out if you need any assistance.
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://websit-ebam1s-projects.vercel.app/en/stock"
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Browse Our Latest Arrivals
              </a>
            </div>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="margin: 0 0 10px 0; color: #333;"><strong>📞 Call/WhatsApp:</strong> +233245375692</p>
              <p style="margin: 0 0 10px 0; color: #333;"><strong>📧 Email:</strong> <EMAIL></p>
              <p style="margin: 0; color: #333;"><strong>🌐 Website:</strong> ebammotors.com</p>
            </div>

            <p style="color: #666; line-height: 1.6;">
              Thank you for choosing EBAM Motors. We appreciate your business and look forward to serving you again!
            </p>

            <p style="color: #666; margin-top: 30px;">
              Best regards,<br>
              <strong>EBAM Motors Team</strong><br>
              <em>Your trusted partner for Japanese cars in Ghana</em>
            </p>
          </div>

          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px; margin: 0;">
              This is an automated follow-up email. If you no longer wish to receive these emails, please contact us.
            </p>
          </div>
        </div>
      `;
    }
  }

  private generateFollowUpText(data: any, isLeadFollowup: boolean): string {
    if (isLeadFollowup) {
      return `Hi ${data.customerName}! Following up on your inquiry about ${data.productInterest || 'our vehicles'}. We have excellent options available. Contact us at +233245375692 or visit ebammotors.com. - EBAM Motors`;
    } else {
      return `Hi ${data.customerName}! Hope you're enjoying your EBAM Motors experience. Need any assistance? We're here to help! Contact us at +233245375692. - EBAM Motors`;
    }
  }

  private generateDeliveryUpdateHTML(data: FollowUpData): string {
    return EmailTemplates.generateDeliveryUpdateHTML(data);
  }

  private generateDeliveryUpdateText(data: FollowUpData): string {
    return `Delivery update for ${data.customerName}: ${data.data?.status || 'Your vehicle is on its way'}. Track your order at ebammotors.com`;
  }

  private generateFeedbackRequestHTML(data: FollowUpData): string {
    return EmailTemplates.generateFeedbackRequestHTML(data);
  }

  private generateFeedbackRequestText(data: FollowUpData): string {
    return `Hi ${data.customerName}! How was your experience with EBAM Motors? We'd love to hear your feedback. Leave a review at ebammotors.com/reviews`;
  }

  private generateMaintenanceReminderHTML(data: FollowUpData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Vehicle Maintenance Reminder</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }
          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
          .content { padding: 30px; }
          .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
          .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚗 EBAM Motors</div>
            <p>Keep your vehicle in perfect condition</p>
          </div>
          <div class="content">
            <h1>Vehicle Maintenance Reminder</h1>
            <p>Hi ${data.customerName},</p>
            <p>It's time for your vehicle's scheduled maintenance to keep it running smoothly and safely.</p>
            <div class="highlight">
              <h3>Recommended Maintenance</h3>
              <p><strong>Vehicle:</strong> ${data.data?.vehicleTitle || 'Your vehicle'}</p>
              <p><strong>Mileage:</strong> ${data.data?.currentMileage || 'Check your odometer'}</p>
              <p><strong>Service Due:</strong> ${data.data?.serviceType || 'Regular maintenance'}</p>
            </div>
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://yourdomain.com/contact" class="button">Schedule Service</a>
            </div>
            <p>Regular maintenance helps ensure:</p>
            <ul>
              <li>🔧 Optimal performance and fuel efficiency</li>
              <li>🛡️ Safety and reliability</li>
              <li>💰 Prevention of costly repairs</li>
              <li>📈 Maintained resale value</li>
            </ul>
          </div>
          <div class="footer">
            <p>Thank you for choosing EBAM Motors!</p>
            <p>© 2024 EBAM Motors. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateMaintenanceReminderText(data: FollowUpData): string {
    return `Hi ${data.customerName}! It's time for your vehicle's scheduled maintenance. Contact EBAM Motors to schedule service and keep your vehicle running smoothly.`;
  }
}

// Export singleton instance
export const emailService = new ResendEmailService();
