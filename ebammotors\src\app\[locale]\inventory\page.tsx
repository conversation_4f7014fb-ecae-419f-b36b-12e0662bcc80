import Link from 'next/link';
import { getMessages } from '@/lib/messages';
import Navigation from '@/components/Navigation';
import WhatsAppChannel from '@/components/WhatsAppChannel';
import InventoryWrapper from './InventoryWrapper';
import { generateInventoryFromFileSystem } from './utils/inventoryGenerator';

export default async function InventoryPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const messages = await getMessages(locale);

  // Generate inventory data dynamically from file system
  const inventoryItems = await generateInventoryFromFileSystem();

  const categories = [
    { id: 'all', name: locale === 'en' ? 'All Items' : '全商品', count: inventoryItems.length },
    { id: 'cars', name: messages.services.usedCars, count: inventoryItems.filter(item => item.category === 'cars').length },
    { id: 'electronics', name: messages.services.electronics, count: inventoryItems.filter(item => item.category === 'electronics').length },
    { id: 'furniture', name: messages.services.furniture, count: inventoryItems.filter(item => item.category === 'furniture').length }
  ];

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-heading font-bold mb-6">
            {messages.navigation.inventory}
          </h1>
          <p className="text-xl lg:text-2xl text-primary-100 max-w-4xl mx-auto">
            {locale === 'en' 
              ? 'Browse our current selection of quality used goods ready for export to Ghana and Africa'
              : '現在ガーナ・アフリカへの輸出準備が整った高品質中古品をご覧ください'
            }
          </p>
        </div>
      </section>

      {/* Filters and Search */}
      <section className="py-8 bg-neutral-50 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col gap-6">
            {/* Categories */}
            <div className="w-full">
              <h3 className="text-sm font-medium text-neutral-700 mb-3">
                {locale === 'en' ? 'Filter by Category' : 'カテゴリーで絞り込み'}
              </h3>
              <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    className="px-3 py-2 sm:px-4 sm:py-2 rounded-full bg-white border border-neutral-300 hover:border-primary-500 hover:text-primary-600 transition-colors duration-200 text-xs sm:text-sm font-medium whitespace-nowrap"
                  >
                    {category.name} ({category.count})
                  </button>
                ))}
              </div>
            </div>

            {/* Search */}
            <div className="w-full">
              <h3 className="text-sm font-medium text-neutral-700 mb-3">
                {locale === 'en' ? 'Search Stock' : '在庫検索'}
              </h3>
              <div className="flex flex-col sm:flex-row gap-3 w-full">
                <input
                  type="text"
                  placeholder={locale === 'en' ? 'Search stock...' : '在庫を検索...'}
                  className="flex-1 px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-base"
                />
                <button className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium text-base whitespace-nowrap">
                  {locale === 'en' ? 'Search' : '検索'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Toyota Voxy Section */}
      <section className="py-12 bg-primary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-heading font-bold text-primary-800 mb-4">
              {locale === 'en' ? 'Featured: Toyota Voxy 8-Seater' : '特集：トヨタ ヴォクシー 8人乗り'}
            </h2>
            <p className="text-lg text-primary-600 mb-2">
              {locale === 'en' ? 'All Toyota Voxy models: 8-seater capacity' : '全てのトヨタ ヴォクシー：8人乗り'}
            </p>
            <p className="text-2xl font-bold text-primary-800">
              ¥295,000-¥300,000 {locale === 'en' ? '(2010-2012 models)' : '（2010-2012年モデル）'}
            </p>
          </div>
        </div>
      </section>

      {/* Inventory Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Inventory Grid with Client Components */}
          <InventoryWrapper items={inventoryItems} locale={locale} />

          {/* Pricing Summary */}
          <div className="mt-16 bg-neutral-50 rounded-2xl p-8">
            <h3 className="text-2xl font-heading font-bold text-center mb-8">
              {locale === 'en' ? 'Car Model Pricing Guide' : '車種別価格ガイド'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <div className="bg-white rounded-lg p-6 text-center shadow-sm">
                <h4 className="font-semibold text-lg mb-2">Toyota Voxy</h4>
                <p className="text-sm text-neutral-600 mb-2">8-seater MPV</p>
                <p className="text-xl font-bold text-primary-600">¥295,000-¥300,000</p>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm">
                <h4 className="font-semibold text-lg mb-2">Toyota Noah</h4>
                <p className="text-sm text-neutral-600 mb-2">8-seater MPV</p>
                <p className="text-xl font-bold text-primary-600">¥350,000</p>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm">
                <h4 className="font-semibold text-lg mb-2">Toyota Sienta</h4>
                <p className="text-sm text-neutral-600 mb-2">7-seater Compact MPV</p>
                <p className="text-xl font-bold text-primary-600">¥320,000</p>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm">
                <h4 className="font-semibold text-lg mb-2">Toyota Vitz</h4>
                <p className="text-sm text-neutral-600 mb-2">5-seater Compact</p>
                <p className="text-xl font-bold text-primary-600">¥325,000</p>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm">
                <h4 className="font-semibold text-lg mb-2">Toyota Yaris</h4>
                <p className="text-sm text-neutral-600 mb-2">5-seater Compact</p>
                <p className="text-xl font-bold text-primary-600">¥550,000</p>
              </div>
            </div>
            <div className="mt-6 text-center">
              <p className="text-sm text-neutral-600">
                {locale === 'en'
                  ? 'All prices in Japanese Yen (¥). Shipping and import duties not included.'
                  : '全ての価格は日本円（¥）表示です。送料・輸入関税は含まれません。'
                }
              </p>
            </div>
          </div>

          {/* WhatsApp Channel */}
          <div className="mt-12">
            <WhatsAppChannel locale={locale} variant="compact" showQR={true} />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-heading font-bold mb-6">
            {locale === 'en' ? "Don't See What You're Looking For?" : 'お探しの商品が見つかりませんか？'}
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
            {locale === 'en'
              ? 'Contact us with your specific requirements. We source new stock regularly and can help you find exactly what you need.'
              : 'ご希望の商品についてお問い合わせください。定期的に新しい在庫を調達しており、お客様のニーズに合った商品を見つけるお手伝いをいたします。'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href={`/${locale}/contact`}
              className="bg-secondary-500 hover:bg-secondary-600 text-neutral-900 px-8 py-4 rounded-lg font-semibold transition-colors duration-200"
            >
              {messages.navigation.contact}
            </Link>
            <Link
              href={`/${locale}/buyers`}
              className="border-2 border-white text-white hover:bg-white hover:text-primary-800 px-8 py-4 rounded-lg font-semibold transition-colors duration-200"
            >
              {messages.navigation.buyers}
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">EM</span>
                </div>
                <span className="font-heading font-bold text-xl">EBAM MOTORS</span>
              </div>
              <p className="text-neutral-300">
                {messages.about?.mission || 'Promote sustainable trade by giving used goods a second life where they are needed most.'}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">Contact Info</h3>
              <div className="space-y-2 text-neutral-300">
                <p>{messages.about?.phone || '080-6985-2864'}</p>
                <p>{messages.about?.email || '<EMAIL>'}</p>
                <p>{messages.about?.location || 'Japan, Saitama, Hanno City, Nagata'}</p>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
              <div className="space-y-2">
                <Link href={`/${locale}/services`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.services}
                </Link>
                <Link href={`/${locale}/how-it-works`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.howItWorks}
                </Link>
                <Link href={`/${locale}/contact`} className="block text-neutral-300 hover:text-white transition-colors">
                  {messages.navigation.contact}
                </Link>
              </div>
            </div>
          </div>
          <div className="border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 EBAM MOTORS. All rights reserved.</p>
          </div>
        </div>
      </footer>


    </div>
  );
}
