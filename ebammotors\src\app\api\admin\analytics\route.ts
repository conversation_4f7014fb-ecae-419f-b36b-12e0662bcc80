import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { getAllOrders } from '@/lib/orderStorage';
import { getAllCustomers } from '@/lib/crmStorage';
import { sql } from '@vercel/postgres';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    // Get analytics data
    const analytics = await getAnalyticsData(range);

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}

async function getAnalyticsData(range: string) {
  try {
    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (range) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Get data from various sources
    const [orders, customers, cars] = await Promise.all([
      getAllOrders(),
      getAllCustomers(),
      getCarsData()
    ]);

    // Filter data by date range
    const filteredOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= startDate && orderDate <= endDate;
    });

    const filteredCustomers = customers.filter(customer => {
      const customerDate = new Date(customer.createdAt);
      return customerDate >= startDate && customerDate <= endDate;
    });

    // Calculate overview metrics
    const overview = calculateOverviewMetrics(filteredOrders, filteredCustomers, cars);

    // Calculate sales trends
    const salesTrends = calculateSalesTrends(filteredOrders, startDate, endDate);

    // Calculate top products
    const topProducts = calculateTopProducts(filteredOrders);

    // Calculate customer segments
    const customerSegments = calculateCustomerSegments(customers);

    // Calculate order sources
    const orderSources = calculateOrderSources(filteredOrders);

    // Calculate geographic data
    const geographicData = calculateGeographicData(customers);

    // Calculate performance metrics
    const performanceMetrics = calculatePerformanceMetrics(orders, customers);

    return {
      overview,
      salesTrends,
      topProducts,
      customerSegments,
      orderSources,
      geographicData,
      performanceMetrics
    };
  } catch (error) {
    console.error('Error calculating analytics:', error);
    throw error;
  }
}

async function getCarsData() {
  try {
    const result = await sql`SELECT COUNT(*) as count FROM cars`;
    return parseInt(result.rows[0]?.count || '0');
  } catch (error) {
    console.error('Error fetching cars data:', error);
    return 0;
  }
}

function calculateOverviewMetrics(orders: any[], customers: any[], totalCars: number) {
  const totalRevenue = orders
    .filter(o => o.payment.status === 'completed')
    .reduce((sum, o) => sum + o.totalAmount, 0);
  
  const totalOrders = orders.length;
  const totalCustomers = customers.length;

  // Calculate growth rates (simplified - comparing with previous period)
  const revenueGrowth = Math.random() * 20 - 10; // Mock data
  const orderGrowth = Math.random() * 15 - 5; // Mock data
  const customerGrowth = Math.random() * 25 - 10; // Mock data
  
  // Calculate conversion rate (simplified)
  const conversionRate = totalCustomers > 0 ? (totalOrders / totalCustomers) * 100 : 0;

  return {
    totalRevenue,
    totalOrders,
    totalCustomers,
    totalCars,
    revenueGrowth,
    orderGrowth,
    customerGrowth,
    conversionRate
  };
}

function calculateSalesTrends(orders: any[], startDate: Date, endDate: Date) {
  const trends = [];
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  // Group orders by month
  const monthlyData: Record<string, { revenue: number; orders: number; customers: Set<string> }> = {};
  
  orders.forEach(order => {
    const orderDate = new Date(order.createdAt);
    const monthKey = `${monthNames[orderDate.getMonth()]} ${orderDate.getFullYear()}`;
    
    if (!monthlyData[monthKey]) {
      monthlyData[monthKey] = { revenue: 0, orders: 0, customers: new Set() };
    }
    
    if (order.payment.status === 'completed') {
      monthlyData[monthKey].revenue += order.totalAmount;
    }
    monthlyData[monthKey].orders += 1;
    monthlyData[monthKey].customers.add(order.customerId);
  });

  // Convert to array format
  Object.entries(monthlyData).forEach(([month, data]) => {
    trends.push({
      month,
      revenue: data.revenue,
      orders: data.orders,
      customers: data.customers.size
    });
  });

  return trends.sort((a, b) => {
    const dateA = new Date(a.month);
    const dateB = new Date(b.month);
    return dateA.getTime() - dateB.getTime();
  });
}

function calculateTopProducts(orders: any[]) {
  const productStats: Record<string, { title: string; sales: number; revenue: number }> = {};
  
  orders.forEach(order => {
    const productId = order.vehicle.id;
    if (!productStats[productId]) {
      productStats[productId] = {
        title: order.vehicle.title,
        sales: 0,
        revenue: 0
      };
    }
    
    productStats[productId].sales += 1;
    if (order.payment.status === 'completed') {
      productStats[productId].revenue += order.totalAmount;
    }
  });

  return Object.entries(productStats)
    .map(([id, stats]) => ({ id, ...stats }))
    .sort((a, b) => b.revenue - a.revenue);
}

function calculateCustomerSegments(customers: any[]) {
  const segments: Record<string, number> = {};
  
  customers.forEach(customer => {
    const segment = customer.segment || 'Regular';
    segments[segment] = (segments[segment] || 0) + 1;
  });

  const total = customers.length;
  return Object.entries(segments).map(([segment, count]) => ({
    segment,
    count,
    revenue: count * 500000, // Mock revenue calculation
    percentage: total > 0 ? (count / total) * 100 : 0
  }));
}

function calculateOrderSources(orders: any[]) {
  const sources: Record<string, { orders: number; revenue: number }> = {};
  
  orders.forEach(order => {
    const source = 'Website'; // Simplified - all orders from website
    if (!sources[source]) {
      sources[source] = { orders: 0, revenue: 0 };
    }
    
    sources[source].orders += 1;
    if (order.payment.status === 'completed') {
      sources[source].revenue += order.totalAmount;
    }
  });

  const totalOrders = orders.length;
  return Object.entries(sources).map(([source, data]) => ({
    source,
    orders: data.orders,
    revenue: data.revenue,
    percentage: totalOrders > 0 ? (data.orders / totalOrders) * 100 : 0
  }));
}

function calculateGeographicData(customers: any[]) {
  const countries: Record<string, { customers: number; orders: number; revenue: number }> = {};
  
  customers.forEach(customer => {
    const country = customer.address?.country || 'Unknown';
    if (!countries[country]) {
      countries[country] = { customers: 0, orders: 0, revenue: 0 };
    }
    
    countries[country].customers += 1;
    countries[country].orders += customer.totalOrders || 0;
    countries[country].revenue += customer.totalSpent || 0;
  });

  return Object.entries(countries)
    .map(([country, data]) => ({ country, ...data }))
    .sort((a, b) => b.customers - a.customers);
}

function calculatePerformanceMetrics(orders: any[], customers: any[]) {
  const completedOrders = orders.filter(o => o.payment.status === 'completed');
  
  const averageOrderValue = completedOrders.length > 0
    ? completedOrders.reduce((sum, o) => sum + o.totalAmount, 0) / completedOrders.length
    : 0;

  const customerLifetimeValue = customers.length > 0
    ? customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0) / customers.length
    : 0;

  const repeatCustomers = customers.filter(c => (c.totalOrders || 0) > 1).length;
  const repeatCustomerRate = customers.length > 0 ? (repeatCustomers / customers.length) * 100 : 0;

  // Mock metrics for cart abandonment, session duration, and bounce rate
  const cartAbandonmentRate = 25.5;
  const averageSessionDuration = 180; // seconds
  const bounceRate = 35.2;

  return {
    averageOrderValue,
    customerLifetimeValue,
    repeatCustomerRate,
    cartAbandonmentRate,
    averageSessionDuration,
    bounceRate
  };
}
